<?php

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        // app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        //
        // $assets = ['asset view', 'asset create', 'asset edit', 'asset delete'];
        //
        // $pages = ['page view', 'page create', 'page edit', 'page delete'];
        //
        // $eliterature = ['eliterature view', 'eliterature create', 'eliterature edit', 'eliterature delete'];
        //
        // $users = ['user view', 'user create', 'user edit', 'user delete'];
        //
        // $catalogs = ['catalog view', 'catalog create', 'catalog edit', 'catalog delete'];
        //
        // $software = ['software upgrade view', 'software upgrade create', 'software upgrade edit', 'software upgrade delete', 'software upgrade publish'];
        //
        // $server = ['server info view', 'server logs view'];
        //
        // $permissions = ['assets', 'pages', 'elit', 'users', 'catalogs', 'software', 'server'];
        //
        // foreach ($permissions as $key) {
        //     foreach ($$key as $value) {
        //         // echo $value . ', ';
        //         Permission::create(['name' => $value]);
        //     }
        // }
        //
        // // create roles and assign created permissions
        // $role = Role::create(['name' => 'superadmin'])
        //     ->givePermissionTo(Permission::all());
        //
        // $role = Role::create(['name' => 'software update manager'])
        //     ->givePermissionTo($software);
        //
        // $role = Role::create(['name' => 'software update developer'])
        //     ->givePermissionTo(['software upgrade view', 'software upgrade create', 'software upgrade edit', 'software upgrade delete']);
        //
        // $role = Role::create(['name' => 'eliterature manager'])
        //     ->givePermissionTo($elit);
        //
        // $role = Role::create(['name' => 'eliterature contributor'])
        //     ->givePermissionTo(['eliterature view', 'eliterature create', 'eliterature edit']);
        //
        // $role = Role::create(['name' => 'catalog manager'])
        //     ->givePermissionTo($catalogs);
        //
        // $role = Role::create(['name' => 'catalog contributor'])
        //     ->givePermissionTo(['catalog view', 'catalog create', 'catalog edit']);

        // $user = User::find('1');
        // $user->assignRole('superadmin');

        // Kurt
        // $user = User::find('12');
        // $user->assignRole('eliterature manager');
        //
        // // Wendy
        // $user = User::find('8');
        // $user->assignRole('eliterature contributor');
        //
        // // Shavaughn
        // $user = User::find('11');
        // $user->assignRole('eliterature contributor');

        // Tyler
        $user = User::create([
            'username' => 'tylerdurfee',
            'email' => strtolower('<EMAIL>'),
            'password' => bcrypt(Str::uuid()),
            'active' => '1',
            'name_first' => 'Tyler',
            'name_last' => 'Durfee',
            'department' => 'Guidance Technologies',
        ])->assignRole('software update manager');

        // Kelly
        $user = User::create([
            'username' => 'kellyharper',
            'email' => strtolower('<EMAIL>'),
            'password' => bcrypt(Str::uuid()),
            'active' => '1',
            'name_first' => 'Kelly',
            'name_last' => 'Harper',
            'department' => 'Guidance Technologies',
        ])->assignRole('software update developer');

        // Brian
        $user = User::create([
            'username' => 'briantanner',
            'email' => strtolower('<EMAIL>'),
            'password' => bcrypt(Str::uuid()),
            'active' => '1',
            'name_first' => 'Brian',
            'name_last' => 'Tanner',
            'department' => 'Guidance Technologies',
        ])->assignRole('software update developer');
    }
}

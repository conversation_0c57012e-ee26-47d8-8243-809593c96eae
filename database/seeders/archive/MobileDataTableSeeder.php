<?php

use Illuminate\Database\Seeder;

class MobileDataTableSeeder extends Seeder
{
    public function run()
    {

        $records = [
            [
                'id' => '101',
                'label' => 'Featured',
                'back_to_label' => 'Featured',
                'tab_bar_icon' => 'FeaturedStandard',
                'tab_bar_label' => 'Featured',
            ],
            [
                'id' => '102',
                'label' => 'Product Categories',
                'back_to_label' => 'Categories',
                'tab_bar_icon' => 'products.png',
                'tab_bar_label' => 'Products',
            ],
            [
                'id' => '103',
                'label' => 'Videos',
                'back_to_label' => 'Videos',
                'tab_bar_icon' => 'videos.png',
                'tab_bar_label' => 'Videos',
            ],
            [
                'id' => '104',
                'label' => 'Catalog',
                'back_to_label' => 'Catalog',
                'tab_bar_icon' => 'search.png',
                'tab_bar_label' => 'Catalog',
            ],
            [
                'id' => '105',
                'label' => 'Bard Access Systems',
                'back_to_label' => null,
                'tab_bar_icon' => 'contact.png',
                'tab_bar_label' => 'Contact',
            ],
        ];

        foreach ($records as $record) {

            DB::connection('sqlite_ios_1')->table('Tab')->insert([
                'id' => $record['id'],
                'label' => $record['label'],
                'back_to_label' => $record['back_to_label'],
                'tab_bar_icon' => $record['tab_bar_icon'],
                'tab_bar_label' => $record['tab_bar_label'],
                'type' => null,
                'version' => 1,
            ]);

        }

        DB::connection('sqlite_ios_1')->table('Contact_Info')->insert([
            'id' => '1',
            'email' => '<EMAIL>',
            'fax' => '(*************',
            'label' => 'Bard Access Systems',
            'label_two' => 'Customer Service',
            'note' => 'Hours: 7:00 a.m. - 4:30 p.m. Monday-Friday (Mountain Time Zone)',
            'phone' => '(*************',
            'product_note' => 'Concerning product:',
            'thumbnail' => 'http://www.bardaccess.com/assets/images/mobile/icons/bard_b_icon.png',
            'website' => 'http://www.bardaccess.com/',
        ]);

    }
}

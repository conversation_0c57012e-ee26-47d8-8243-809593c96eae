<?php

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionsTableSeeder extends Seeder
{
    public function run()
    {

        DB::table('permissions')->delete();
        $permissions = ['create', 'read', 'update', 'delete', 'forceDelete'];

        foreach ($permissions as $permission) {

            Permission::create([
                'title' => $permission,
            ]);

        }

    }
}

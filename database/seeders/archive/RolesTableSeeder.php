<?php

use App\Models\Role;
use Config;
use Illuminate\Database\Seeder;

class RolesTableSeeder extends Seeder
{
    public function run()
    {

        DB::table('roles')->delete();
        $roles = [Config::get('settings.url_prefixes.literature_prefix'), Config::get('settings.url_prefixes.elabeling_prefix'), Config::get('settings.url_prefixes.video_prefix'), Config::get('settings.url_prefixes.catalog_prefix'), Config::get('settings.url_prefixes.page_prefix'), Config::get('settings.url_prefixes.user_prefix'), Config::get('settings.url_prefixes.server_prefix')];

        foreach ($roles as $role) {

            Role::create([
                'title' => $role,
            ]);

        }

    }
}

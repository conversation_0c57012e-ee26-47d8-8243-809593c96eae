<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInternalFlagToSystemSoftwareVersions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('gt_system_software_versions', function (Blueprint $table) {
            $table->tinyInteger('internal')->nullable()->after('prod');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('gt_system_software_versions', function (Blueprint $table) {
            $table->dropColumn('internal');
        });
    }
}

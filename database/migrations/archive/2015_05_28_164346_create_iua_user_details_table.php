<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateIuaUserDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('iua_user_details', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', '50')->nullable();
            $table->string('title', '50')->nullable();
            $table->string('organization', '100')->nullable();
            $table->string('department', '50')->nullable();
            $table->string('email', '50')->nullable();
            $table->string('phone', '50')->nullable();
            $table->tinyInteger('ismanager')->unsigned()->nullable();
            $table->tinyInteger('optin')->unsigned()->nullable();

            $table->timestamps();
        });

        Schema::create('iua_user_details_uat', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', '50')->nullable();
            $table->string('title', '50')->nullable();
            $table->string('organization', '100')->nullable();
            $table->string('department', '50')->nullable();
            $table->string('email', '50')->nullable();
            $table->string('phone', '50')->nullable();
            $table->tinyInteger('ismanager')->unsigned()->nullable();
            $table->tinyInteger('optin')->unsigned()->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('iua_user_details');
        Schema::drop('iua_user_details_uat');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateSalesContactDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales_contact_details', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name_first', 25);
            $table->string('name_last', 25);
            $table->string('email', 50);
            $table->string('title', 50);
            $table->string('employer', 50);
            $table->string('emp_address', 50);
            $table->string('emp_city', 25);
            $table->string('emp_state', 25);
            $table->string('emp_zipcode', 10);
            $table->string('phone_day', 25);
            $table->string('contact_via', 10);
            $table->string('contact_time', 25);
            $table->string('comments', 500);
            $table->string('products', 255);
            $table->tinyInteger('opt_in');
            $table->tinyInteger('terms_privacy');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('sales_contact_details');
    }
}

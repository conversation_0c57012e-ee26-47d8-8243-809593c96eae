<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class ExtendSitePages extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('site_pages', function (Blueprint $table) {
            $table->string('related_areas')->nullable()->after('content');
            $table->text('extra_menu')->nullable()->after('content');
            $table->text('logo')->nullable()->after('content');
            $table->text('slogan')->nullable()->after('content');
            $table->text('slogan_img')->nullable()->after('content');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('site_pages', function ($table) {
            $table->dropColumn('related_areas');
            $table->dropColumn('extra_menu');
            $table->dropColumn('logo');
            $table->dropColumn('slogan');
            $table->dropColumn('slogan_img');
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateMobileTablesForSqlite extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('sqlite_ios_1')->create('Tab', function (Blueprint $table) {
            $table->increments('id');
            $table->text('label');
            $table->text('back_to_label')->nullable();
            $table->text('tab_bar_icon');
            $table->text('tab_bar_label');
            $table->text('type')->nullable();
            $table->integer('version');
        });
        Schema::connection('sqlite_ios_1')->create('Contact_Info', function (Blueprint $table) {
            $table->increments('id');
            $table->string('email');
            $table->string('fax');
            $table->string('label');
            $table->string('label_two');
            $table->string('note');
            $table->string('phone');
            $table->string('product_note');
            $table->string('thumbnail');
            $table->string('website');
        });
        /*		Schema::connection('sqlite_ios_1')->create('site_pages_sections', function(Blueprint $table)
                {
                    $table->increments('id');
                    $table->integer('page_id')->unsigned();
                    $table->foreign('page_id')->references('id')->on('site_pages')->onDelete('cascade');
                    $table->string('title');
                    $table->string('slug');
                    $table->text('content')->nullable();
                    $table->text('content_html')->nullable();
                    $table->softDeletes();
                    $table->nullableTimestamps();

                });
                Schema::connection('sqlite_ios_1')->create('site_page_site_section', function(Blueprint $table)
                {
                    $table->increments('id');
                    $table->integer('page_id')->unsigned()->index();
                    $table->foreign('page_id')->references('id')->on('site_pages')->onDelete('cascade');
                    $table->integer('section_id')->unsigned()->index();
                    $table->foreign('section_id')->references('id')->on('site_pages_sections')->onDelete('cascade');
                    $table->nullableTimestamps();
                });
        */
        Schema::connection('sqlite_ios_1')->create('site_videos', function (Blueprint $table) {
            $table->increments('id');
            $table->string('unique_id')->unique()->nullable();
            $table->string('tracking_number')->nullable();
            $table->string('title')->nullable();
            $table->string('description')->nullable();
            $table->string('playtime')->nullable();
            $table->string('poster_width')->nullable();
            $table->string('poster_height')->nullable();
            $table->tinyInteger('active')->default(0);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->nullableTimestamps();
        });
        Schema::connection('sqlite_ios_1')->create('site_pages_site_videos', function (Blueprint $table) {
            $table->integer('page_id')->unsigned()->index();
            $table->foreign('page_id')->references('id')->on('site_pages')->onDelete('cascade');
            $table->integer('video_id')->unsigned()->index();
            $table->foreign('video_id')->references('id')->on('site_videos')->onDelete('cascade');
        });
        Schema::connection('sqlite_ios_1')->create('site_video_files', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('video_id')->unsigned()->index();
            $table->string('filename')->nullable();
            $table->string('fileformat')->nullable();
            $table->string('filesize')->nullable();
            $table->string('width')->nullable();
            $table->string('height')->nullable();
            $table->string('language')->nullable();
            $table->tinyInteger('active')->default(1);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->nullableTimestamps();
            $table->foreign('video_id')->references('id')->on('site_videos')->onDelete('cascade');
        });
        Schema::connection('sqlite_ios_1')->create('site_literature', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('literature_type_id')->unsigned()->nullable();
            $table->string('tracking_number')->nullable();
            $table->string('title')->nullable();
            $table->string('filename')->nullable();
            $table->string('fileformat')->nullable();
            $table->string('filesize')->nullable();
            $table->string('language', '2')->nullable();
            $table->string('uploaded_by', '50')->nullable();
            $table->integer('sort_order')->unsigned()->nullable();
            $table->tinyInteger('active')->default('1')->nullable();
            $table->softDeletes();
            $table->nullableTimestamps();
            $table->foreign('literature_type_id')->references('id')->on('site_literature_types');
        });
        Schema::connection('sqlite_ios_1')->create('site_literature_site_page', function (Blueprint $table) {
            $table->integer('literature_id')->unsigned()->index();
            $table->foreign('literature_id')->references('id')->on('site_literature')->onDelete('cascade');
            $table->integer('page_id')->unsigned()->index();
            $table->foreign('page_id')->references('id')->on('site_pages')->onDelete('cascade');
        });
        Schema::connection('sqlite_ios_1')->create('site_literature_types', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title')->nullable();
            $table->string('short_name')->nullable();
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->nullableTimestamps();
        });
        Schema::connection('sqlite_ios_1')->create('site_pages', function (Blueprint $table) {
            $table->increments('id')->index();
            $table->integer('category_id')->unsigned()->index();
            $table->integer('subcategory_id')->unsigned()->index();
            $table->string('title');
            $table->string('sections');
            $table->string('content');
            $table->string('hero_image');
            $table->string('thumbnail_image');
            $table->string('url');
            $table->string('category_name');
            $table->string('related_areas')->nullable()->after('content');
            $table->text('extra_menu')->nullable()->after('content');
            $table->text('logo')->nullable()->after('content');
            $table->text('slogan')->nullable()->after('content');
            $table->text('slogan_img')->nullable()->after('content');
            $table->tinyInteger('product_page')->nullable();
            $table->tinyInteger('new_product')->nullable();
            $table->tinyInteger('featured')->unsigned()->nullable();
            $table->tinyInteger('featured_sort_order')->unsigned()->nullable();
            $table->tinyInteger('active')->default(0);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->timestamp('published_on');
            $table->nullableTimestamps();
            $table->foreign('category_id')->references('id')->on('site_categories')->onDelete('cascade');
        });
        Schema::connection('sqlite_ios_1')->create('site_subcategories', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('category_id')->unsigned()->index();
            $table->string('title');
            $table->tinyInteger('active')->default(1);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->nullableTimestamps();
            $table->foreign('category_id')->references('id')->on('site_categories')->onDelete('cascade');
        });
        Schema::connection('sqlite_ios_1')->create('site_categories', function (Blueprint $table) {
            $table->increments('id')->index();
            $table->string('title');
            $table->string('short_name');
            $table->tinyInteger('product_category')->nullable();
            $table->tinyInteger('active')->default(0);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->nullableTimestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('sqlite_ios_1')->drop('Tab');
        Schema::connection('sqlite_ios_1')->drop('Contact_Info');
        /*	Schema::connection('sqlite_ios_1')->drop('site_pages_sections');
            Schema::connection('sqlite_ios_1')->drop('site_page_site_section');
        */ Schema::connection('sqlite_ios_1')->drop('site_videos');
        Schema::connection('sqlite_ios_1')->drop('site_page_site_videos');
        Schema::connection('sqlite_ios_1')->drop('site_video_files');
        Schema::connection('sqlite_ios_1')->drop('site_literature');
        Schema::connection('sqlite_ios_1')->drop('site_literature_site_page');
        Schema::connection('sqlite_ios_1')->drop('site_literature_types');
        Schema::connection('sqlite_ios_1')->drop('site_pages');
        Schema::connection('sqlite_ios_1')->drop('site_subcategories');
        Schema::connection('sqlite_ios_1')->drop('site_categories');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSystemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gt_systems', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->unique()->index();

            $table->string('system_model');
            $table->string('system_name');

            $table->tinyInteger('dev')->nullable();
            $table->tinyInteger('staging')->nullable();
            $table->tinyInteger('prod')->nullable();

            $table->tinyInteger('internal')->nullable();
            $table->tinyInteger('international')->nullable();

            $table->tinyInteger('active')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        Schema::create('gt_system_software_versions', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->unique()->index();
            $table->integer('system_id')->unsigned()->index();

            $table->string('name');
            $table->string('version');

            $table->tinyInteger('dev')->nullable();
            $table->tinyInteger('staging')->nullable();
            $table->tinyInteger('prod')->nullable();

            $table->tinyInteger('active')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('system_id')->references('id')->on('gt_systems')->onDelete('cascade');
        });

        Schema::create('gt_system_software_files', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->unique()->index();
            $table->integer('version_id')->unsigned()->index();

            $table->string('name');
            $table->string('filename');
            $table->integer('filesize');
            $table->string('checksum'); // md5

            $table->tinyInteger('dev')->nullable();
            $table->tinyInteger('staging')->nullable();
            $table->tinyInteger('prod')->nullable();

            $table->tinyInteger('active')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('version_id')->references('id')->on('gt_system_software_versions')->onDelete('cascade');
        });

        Schema::create('gt_operating_system_files', function (Blueprint $table) {
            $table->increments('id');
            $table->uuid('uuid')->unique()->index();

            $table->string('name');
            $table->string('filename');
            $table->integer('filesize');
            $table->string('checksum'); // md5

            $table->tinyInteger('dev')->nullable();
            $table->tinyInteger('staging')->nullable();
            $table->tinyInteger('prod')->nullable();

            $table->tinyInteger('active')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gt_systems');
        Schema::dropIfExists('gt_system_software_versions');
        Schema::dropIfExists('gt_system_software_files');
        Schema::dropIfExists('gt_operating_system_files');
    }
}

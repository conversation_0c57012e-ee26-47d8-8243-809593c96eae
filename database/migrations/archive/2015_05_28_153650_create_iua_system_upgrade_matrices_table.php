<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateIuaSystemUpgradeMatricesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('iua_system_upgrade_matrices', function (Blueprint $table) {
            $table->increments('id');
            $table->string('system_id', '10')->nullable();
            $table->string('system_name', '255')->nullable();
            $table->string('ultrasound', '255')->nullable();
            $table->string('sherlock', '255')->nullable();
            $table->string('shell', '255')->nullable();
            $table->string('dicom', '255')->nullable();
            $table->string('source_filename', '255')->nullable();
            $table->string('download_filename', '255')->nullable();
            $table->string('product_image', '255')->nullable();
            $table->string('installation_instructions', '255')->nullable();

            $table->timestamps();
        });

        Schema::create('iua_system_upgrade_matrices_uat', function (Blueprint $table) {
            $table->increments('id');
            $table->string('system_id', '10')->nullable();
            $table->string('system_name', '255')->nullable();
            $table->string('ultrasound', '255')->nullable();
            $table->string('sherlock', '255')->nullable();
            $table->string('shell', '255')->nullable();
            $table->string('dicom', '255')->nullable();
            $table->string('source_filename', '255')->nullable();
            $table->string('download_filename', '255')->nullable();
            $table->string('product_image', '255')->nullable();
            $table->string('installation_instructions', '255')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('iua_system_upgrade_matrices');
        Schema::drop('iua_system_upgrade_matrices_uat');
    }
}

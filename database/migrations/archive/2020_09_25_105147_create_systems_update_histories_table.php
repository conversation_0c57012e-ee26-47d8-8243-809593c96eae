<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSystemsUpdateHistoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gt_systems_update_history', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->uuid('uuid')->unique()->index();

            $table->string('serial_number')->nullable();
            $table->string('system_name')->nullable();
            $table->string('system_model')->nullable();
            $table->string('current_version')->nullable();
            $table->string('previous_version')->nullable();
            $table->boolean('success')->nullable();
            $table->string('log_file')->nullable();
            $table->string('update_duration')->nullable();
            $table->dateTime('update_datetime')->nullable();

            $table->tinyInteger('dev')->nullable();
            $table->tinyInteger('staging')->nullable();
            $table->tinyInteger('prod')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gt_systems_update_history');
    }
}

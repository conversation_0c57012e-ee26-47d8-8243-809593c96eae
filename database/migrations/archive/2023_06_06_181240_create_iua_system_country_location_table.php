<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('iua_system_country_location', function (Blueprint $table) {
            $table->id();
            $table->string('product_family')->nullable();
            $table->string('serial_number')->nullable();
            $table->string('country_code')->nullable();
            $table->timestamps();
        });

        Schema::create('iua_system_country_location_uat', function (Blueprint $table) {
            $table->id();
            $table->string('product_family')->nullable();
            $table->string('serial_number')->nullable();
            $table->string('country_code')->nullable();
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('iua_system_country_location');
        Schema::dropIfExists('iua_system_country_location_uat');
    }
};

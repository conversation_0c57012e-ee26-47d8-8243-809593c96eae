<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateEifuImport extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('eifu_import', function (Blueprint $table) {
            $table->increments('id');
            $table->text('name');
            $table->text('filename')->nullable();
            $table->text('database_table')->nullable();
            $table->tinyInteger('first_record_title')->default('1')->nullable();
            $table->text('backup_table_prefix')->nullable();
            $table->integer('backup_count')->unsigned()->nullable();
            $table->date('notify_email')->nullable();
            $table->text('notify_from_email')->nullable();
            $table->integer('sort_order')->unsigned()->nullable();
            $table->tinyInteger('active')->default('1')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('eifu_import');
    }
}

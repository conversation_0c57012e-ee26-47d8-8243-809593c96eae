<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateIuaUserSerialNumbersDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('iua_user_serial_numbers_details', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('pid')->unsigned()->nullable();
            $table->string('serial_number', '25')->nullable();
            $table->string('system_name', '50')->nullable();
            $table->string('ultrasound', '25')->nullable();
            $table->string('sherlock', '25')->nullable();
            $table->string('shell', '25')->nullable();
            $table->string('dicom', '25')->nullable();
            $table->string('contact', '50')->nullable();
            $table->string('department', '50')->nullable();
            $table->string('location', '50')->nullable();
            $table->string('email', '100')->nullable();

            $table->timestamps();

            $table->foreign('pid')->references('id')->on('iua_user_details');
        });

        Schema::create('iua_user_serial_numbers_details_uat', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('pid')->unsigned()->nullable();
            $table->string('serial_number', '25')->nullable();
            $table->string('system_name', '50')->nullable();
            $table->string('ultrasound', '25')->nullable();
            $table->string('sherlock', '25')->nullable();
            $table->string('shell', '25')->nullable();
            $table->string('dicom', '25')->nullable();
            $table->string('contact', '50')->nullable();
            $table->string('department', '50')->nullable();
            $table->string('location', '50')->nullable();
            $table->string('email', '100')->nullable();

            $table->timestamps();

            $table->foreign('pid')->references('id')->on('iua_user_details_uat');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('iua_user_serial_numbers_details');
        Schema::drop('iua_user_serial_numbers_details_uat');
    }
}

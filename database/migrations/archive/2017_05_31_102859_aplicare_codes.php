<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AplicareCodes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('aplicare_codes', function (Blueprint $table) {
            $table->increments('id');
            $table->string('product_code', 25)->nullable();
            $table->string('product_name', 255)->nullable();
            $table->string('0100479', 128)->nullable();
            $table->string('0704237', 128)->nullable();
            $table->string('7601393', 128)->nullable();
            $table->string('7170029', 128)->nullable();
            $table->string('7170030', 128)->nullable();
            $table->string('7170041', 128)->nullable();
            $table->string('0739580', 128)->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateCatalogOptionsMap extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::create('catalog_options_map', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('option_id')->unsigned()->index();
            $table->integer('product_id')->unsigned()->index();
            $table->tinyInteger('active')->default(1);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->timestamps();
            $table->foreign('option_id')->references('id')->on('catalog_field_options')->onDelete('cascade');
            $table->foreign('product_id')->references('id')->on('catalog_product_codes')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('catalog_options_map');
    }
}

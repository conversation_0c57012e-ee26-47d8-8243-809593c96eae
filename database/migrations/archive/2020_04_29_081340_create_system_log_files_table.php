<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSystemLogFilesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gt_system_log_files', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->uuid('uuid')->unique()->index();

            // $table->string('serial_number')->nullable();
            // $table->string('system_name')->nullable();
            // $table->string('current_version')->nullable();
            // $table->string('previous_version')->nullable();
            // $table->boolean('success')->nullable();
            // $table->boolean('log_file')->nullable();
            // $table->string('update_duration')->nullable();
            // $table->dateTime('update_datetime')->nullable();

            $table->timestamps();

            $table->foreign('log_id')->references('id')->on('gt_systems_update_history')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('system_log_files');
    }
}

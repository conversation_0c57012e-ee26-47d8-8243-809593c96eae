<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateSiteRegions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('site_regions', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title')->nullable();
            $table->string('slug')->nullable();
            $table->integer('sort_order')->unsigned()->nullable();
            $table->tinyInteger('active')->default('1')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        // Create Pivot/lookup table for site literature and site pages
        Schema::create('site_page_site_region', function (Blueprint $table) {
            $table->integer('region_id')->unsigned()->index();
            $table->foreign('region_id')->references('id')->on('site_regions')->onDelete('cascade');

            $table->integer('page_id')->unsigned()->index();
            $table->foreign('page_id')->references('id')->on('site_pages')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

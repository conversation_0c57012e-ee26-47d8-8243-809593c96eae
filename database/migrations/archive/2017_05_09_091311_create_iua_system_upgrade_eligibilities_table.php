<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateIuaSystemUpgradeEligibilitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('iua_system_upgrade_eligibility', function (Blueprint $table) {
            $table->increments('id');
            $table->string('system_id', '10')->nullable();
            $table->string('serial_number', '25')->nullable();
            $table->timestamps();
        });

        Schema::create('iua_system_upgrade_eligibility_uat', function (Blueprint $table) {
            $table->increments('id');
            $table->string('system_id', '10')->nullable();
            $table->string('serial_number', '25')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('iua_system_upgrade_eligibility');
        Schema::dropIfExists('iua_system_upgrade_eligibility_uat');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateLanguagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('languages', function (Blueprint $table) {
            $table->increments('id');
            $table->string('language_family')->nullable();
            $table->string('language_name')->nullable();
            $table->string('native_name')->nullable();
            $table->string('iso_639_1', 2)->nullable();
            $table->tinyInteger('active', false, true)->unsigned()->default(0);
            $table->smallInteger('order', false, true)->unsigned()->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('languages');
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateAllPointsContactTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('allpoints_contact_details', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 75);
            $table->string('email', 75);
            $table->string('title', 75);
            $table->string('department', 75);
            $table->string('hospital', 75);
            $table->string('zipcode', 10);
            $table->tinyInteger('opt_in');
            $table->tinyInteger('terms_privacy');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

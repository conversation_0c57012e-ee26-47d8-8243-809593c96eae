<?php

use Illuminate\Database\Migrations\Migration;

class ReformatPartNumberColumnOnLibraryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Let's drop the part number column (as an int)
        Schema::table('eifu_library', function ($table) {
            $table->dropColumn('part_number');
        });

        // And add it back as a varchar
        Schema::table('eifu_library', function ($table) {
            $table->string('part_number', 12)->after('id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Let's drop the part number column (as an varchar)
        Schema::table('eifu_library', function ($table) {
            $table->dropColumn('part_number');
        });

        // And add it back as a integer
        Schema::table('eifu_library', function ($table) {
            $table->integer('part_number')->unsigned()->nullable();
        });
    }
}

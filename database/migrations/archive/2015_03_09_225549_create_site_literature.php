<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateSiteLiterature extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('site_literature', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('literature_type_id')->unsigned()->nullable();
            $table->string('tracking_number')->nullable();
            $table->string('title')->nullable();
            $table->string('filename')->nullable();
            $table->string('fileformat')->nullable();
            $table->string('filesize')->nullable();
            $table->string('language', '2')->nullable();
            $table->string('uploaded_by', '50')->nullable();
            $table->integer('sort_order')->unsigned()->nullable();
            $table->tinyInteger('active')->default('1')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('literature_type_id')->references('id')->on('site_literature_types');
        });

        // Create Pivot/lookup table for site literature and site pages
        Schema::create('site_literature_site_page', function (Blueprint $table) {
            $table->integer('literature_id')->unsigned()->index();
            $table->foreign('literature_id')->references('id')->on('site_literature')->onDelete('cascade');

            $table->integer('page_id')->unsigned()->index();
            $table->foreign('page_id')->references('id')->on('site_pages')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}

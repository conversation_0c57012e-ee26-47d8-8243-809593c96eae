<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('iua_system_upgrade_country_eligibility_matrices', function (Blueprint $table) {
            $table->id();
            $table->string('system_id')->nullable();
            $table->string('system_name')->nullable();
            $table->string('country_code')->nullable();
            $table->string('country_name')->nullable();
            $table->string('ultrasound')->nullable();
            $table->string('sherlock')->nullable();
            $table->string('shell')->nullable();
            $table->string('dicom')->nullable();
            $table->string('source_filename')->nullable();
            $table->string('download_filename')->nullable();
            $table->string('product_image')->nullable();
            $table->string('installation_instructions')->nullable();
            $table->timestamps();
        });

        Schema::create('iua_system_upgrade_country_eligibility_matrices_uat', function (Blueprint $table) {
            $table->id();
            $table->string('system_id')->nullable();
            $table->string('system_name')->nullable();
            $table->string('country_code')->nullable();
            $table->string('country_name')->nullable();
            $table->string('ultrasound')->nullable();
            $table->string('sherlock')->nullable();
            $table->string('shell')->nullable();
            $table->string('dicom')->nullable();
            $table->string('source_filename')->nullable();
            $table->string('download_filename')->nullable();
            $table->string('product_image')->nullable();
            $table->string('installation_instructions')->nullable();
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('iua_system_upgrade_country_eligibility_matrices');
        Schema::dropIfExists('iua_system_upgrade_country_eligibility_matrices_uat');
    }
};

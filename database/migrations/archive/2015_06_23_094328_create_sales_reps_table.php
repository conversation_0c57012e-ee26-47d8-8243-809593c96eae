<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateSalesRepsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales_reps', function (Blueprint $table) {
            $table->increments('id');
            $table->string('zip', 5)->index();
            $table->integer('territory');
            $table->string('name', 50);
            $table->string('email', 50);
            $table->integer('district');
            $table->string('dm_name', 50);
            $table->string('dm_email', 50);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('sales_reps');
    }
}

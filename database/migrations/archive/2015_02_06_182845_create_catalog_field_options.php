<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateFieldOptions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        Schema::create('catalog_field_options', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('field_id')->unsigned()->index();
            $table->string('title');
            $table->tinyInteger('active')->default(1);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->timestamps();
            $table->foreign('field_id')->references('id')->on('category_fields')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('catalog_options');
    }
}

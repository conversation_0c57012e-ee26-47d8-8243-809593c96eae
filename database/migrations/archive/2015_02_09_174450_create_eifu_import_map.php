<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateEifuImportMap extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('eifu_import_map', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('import_id')->unsigned()->nullable();
            $table->text('name');
            $table->mediumInteger('column_index')->unsigned()->nullable();
            $table->text('database_column')->nullable();
            $table->integer('sort_order')->unsigned()->nullable();
            $table->tinyInteger('active')->default('1')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('eifu_import_map');
    }
}

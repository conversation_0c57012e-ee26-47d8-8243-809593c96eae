<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateSiteVideosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('site_videos', function (Blueprint $table) {
            $table->increments('id');
            $table->string('unique_id')->unique()->nullable();
            $table->string('tracking_number')->nullable();
            $table->string('title')->nullable();
            $table->string('description')->nullable();
            $table->string('playtime')->nullable();
            $table->string('poster_width')->nullable();
            $table->string('poster_height')->nullable();
            $table->tinyInteger('active')->default(0);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });

        // Create Pivot/lookup table for site videos and site pages
        Schema::create('site_pages_site_videos', function (Blueprint $table) {
            $table->integer('page_id')->unsigned()->index();
            $table->foreign('page_id')->references('id')->on('site_pages')->onDelete('cascade');

            $table->integer('video_id')->unsigned()->index();
            $table->foreign('video_id')->references('id')->on('site_videos')->onDelete('cascade');
        });

        Schema::create('site_video_files', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('video_id')->unsigned()->index();
            $table->string('filename')->nullable();
            $table->string('fileformat')->nullable();
            $table->string('filesize')->nullable();
            $table->string('width')->nullable();
            $table->string('height')->nullable();
            $table->string('language')->nullable();
            $table->tinyInteger('active')->default(1);
            $table->tinyInteger('sort_order')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('video_id')->references('id')->on('site_videos')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('site_videos');
    }
}

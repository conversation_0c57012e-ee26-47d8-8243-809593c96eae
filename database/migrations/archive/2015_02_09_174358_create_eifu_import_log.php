<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateEifuImportLog extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('eifu_import_log', function (Blueprint $table) {
            $table->increments('id');
            $table->text('logged_filename');
            $table->mediumInteger('successful')->unsigned()->nullable();
            $table->mediumInteger('errors')->unsigned()->nullable();
            $table->integer('starting_sequence_number')->unsigned()->nullable();
            $table->integer('ending_sequence_number')->unsigned()->nullable();
            $table->integer('sort_order')->unsigned()->nullable();
            $table->tinyInteger('active')->default('1')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('eifu_import_log');
    }
}

<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Custom Site Settings
    |--------------------------------------------------------------------------
    |
    | Usage Notes:
    | Be sure to include the Config file in the use block at the beginnnig of a class:
    | use Config;
    |
    | Access them via:
    | Config::get('settings.site_settings.admin_prefix');
    |
    */

    'url_prefixes' => [
        'admin_prefix' => 'maestro',
        'site_prefix' => 'site',
        'literature_prefix' => 'literature',
        'elabeling_prefix' => 'elit',
        'video_prefix' => 'video',
        'catalog_prefix' => 'catalog',
        'page_prefix' => 'page',
        'user_prefix' => 'user',
        'server_prefix' => 'server',
    ],

    'elabeling' => [
        'temp_dir' => normalizeFilePath(storage_path('app/uploads/elabeling/temp/')),
        'target_dir' => normalizeFilePath(storage_path('app/public/elabeling/pdfs')),
        'ftp_directory' => normalizeFilePath(storage_path('ftp/')),
        'import_processed_archive_directory' => normalizeFilePath(storage_path('app/uploads/elabeling/processed/')),
    ],

    'videos' => [
        '' => '',
    ],

    'literature' => [
        'temp_dir' => storage_path('/app/uploads/literature/temp'),
        'target_dir' => storage_path('/app/public/literature'),
    ],

];

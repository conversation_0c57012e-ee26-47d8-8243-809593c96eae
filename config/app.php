<?php

use Illuminate\Support\Facades\Facade;
use Illuminate\Support\ServiceProvider;

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
    |
    */

    'name' => env('APP_NAME', 'BD'),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services the application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => (bool) env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
    */

    'url' => env('APP_URL', 'http://localhost'),

    'asset_url' => env('ASSET_URL'),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
    */

    'timezone' => 'UTC',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
    */

    'locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
    */

    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Faker Locale
    |--------------------------------------------------------------------------
    |
    | This locale will be used by the Faker PHP library when generating fake
    | data for your database seeds. For example, this will be used to get
    | localized telephone numbers, street address information and more.
    |
    */

    'faker_locale' => 'en_US',

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the Illuminate encrypter service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
    */

    'key' => env('APP_KEY'),

    'cipher' => 'AES-256-CBC',

    /*
    |--------------------------------------------------------------------------
    | Hiding Environment Variables From Debug Pages
    |--------------------------------------------------------------------------
    |
    | When an exception is uncaught and the APP_DEBUG environment variable is true,
    | the debug page will show all environment variables and their contents. In some
    | cases you may want to obscure certain variables. You may do this by updating
    | the debug_blacklist option in your config/app.php configuration file.
    |
    | Some variables are available in both the environment variables and the server /
    | request data. Therefore, you may need to blacklist them for both $_ENV and $_SERVER:    |
    */

    'debug_blacklist' => [
        '_ENV' => [
            'APP_KEY',
            'DB_HOST',
            'DB_PORT',
            'DB_DATABASE',
            'DB_USERNAME',
            'DB_PASSWORD',
            'SERVER_ADDR',
            'MAIL_USERNAME',
            'MAIL_PASSWORD',
            'JWT_SIGNATURE_ISSUER',
            'JWT_SIGNATURE_KEY',
            'HASH_HMAC_KEY',
            'GOOGLE_MAPS_API_KEY',
            'GOOGLE_MAPS_IP_ADDRESS_API_KEY',
            'AZURE_AD_TENANT_ID',
            'AZURE_AD_CLIENT_ID',
            'AZURE_AD_CLIENT_SECRET',
            'AZURE_KEY_VAULT_NAME',
        ],

        '_SERVER' => [
            'APP_KEY',
            'DB_HOST',
            'DB_PORT',
            'DB_DATABASE',
            'DB_USERNAME',
            'DB_PASSWORD',
            'SERVER_ADDR',
            'MAIL_USERNAME',
            'MAIL_PASSWORD',
            'JWT_SIGNATURE_ISSUER',
            'JWT_SIGNATURE_KEY',
            'HASH_HMAC_KEY',
            'GOOGLE_MAPS_API_KEY',
            'GOOGLE_MAPS_IP_ADDRESS_API_KEY',
            'MICROSOFT_CLIENT_SECRET',
            'MICROSOFT_CLIENT_ID',
            'MICROSOFT_REDIRECT_URI',
            'MICROSOFT_TENANT_ID',
            'DEFAULT_ADMIN_USERS',
            'AZURE_AD_TENANT_ID',
            'AZURE_AD_CLIENT_ID',
            'AZURE_AD_CLIENT_SECRET',
            'AZURE_KEY_VAULT_NAME',
        ],

        '_POST' => [
            'password',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode Driver
    |--------------------------------------------------------------------------
    |
    | These configuration options determine the driver used to determine and
    | manage Laravel's "maintenance mode" status. The "cache" driver will
    | allow maintenance mode to be controlled across multiple machines.
    |
    | Supported drivers: "file", "cache"
    |
    */

    'maintenance' => [
        'driver' => 'file',
        // 'store'  => 'redis',
    ],

    /*
    |--------------------------------------------------------------------------
    | Autoloaded Service Providers
    |--------------------------------------------------------------------------
    |
    | The service providers listed here will be automatically loaded on the
    | request to your application. Feel free to add your own services to
    | this array to grant expanded functionality to your applications.
    |
    */

    'providers' => ServiceProvider::defaultProviders()->merge([
        /*
         * Application Service Providers...
         */
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        // App\Providers\BroadcastServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        App\Providers\RouteServiceProvider::class,

        /*
         * Package Service Providers...
         */
        Arcanedev\LogViewer\LogViewerServiceProvider::class,
        LaravelLux\Html\HtmlServiceProvider::class,
        // Intervention\Image\ImageServiceProvider::class,
        Laracasts\Flash\FlashServiceProvider::class,
        Maatwebsite\Excel\ExcelServiceProvider::class,
        Spatie\Permission\PermissionServiceProvider::class,
        SocialiteProviders\Manager\ServiceProvider::class,

    ])->toArray(),

    /*
    |--------------------------------------------------------------------------
    | Class Aliases
    |--------------------------------------------------------------------------
    |
    | This array of class aliases will be registered when this application
    | is started. However, feel free to register as many as you wish as
    | the aliases are "lazy" loaded so they don't hinder performance.
    |
    */

    'aliases' => Facade::defaultAliases()->merge([
        'Excel' => Maatwebsite\Excel\Facades\Excel::class,
        'Flash' => Laracasts\Flash\Flash::class,
        'Form' => LaravelLux\Html\FormFacade::class,
        'Html' => LaravelLux\Html\HtmlFacade::class,
        'HTML' => LaravelLux\Html\HtmlFacade::class,
        // 'HTMLMin'   => HTMLMin\HTMLMin\Facades\HTMLMin::class,
        'Image' => Intervention\Image\Facades\Image::class,
    ])->toArray(),

];

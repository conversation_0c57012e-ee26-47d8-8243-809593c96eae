<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        'App\Console\Commands\ElabelingImport',
        'App\Console\Commands\ImagingUpgradeReportingCommand',
        // 'App\Console\Commands\CronjobTestCommand',
        // 'App\Console\Commands\ImagingUpgradeWeeklyReportingCommand',
    ];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Run the elabeling import
        $schedule->command('utility:elabelingimport')->withoutOverlapping()->hourly();

        // Run imaging report
        $schedule->command('utility:imagingreporting')->monthlyOn(1, '5:00'); // 6am MST

        // Run the elabeling import
        // $schedule->command('app:cronjob-test')->everyMinute();

        // Run imaging report
        // $schedule->command('utility:imagingweeklyreporting')->weeklyOn(1, '5:00'); // 6am MST
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}

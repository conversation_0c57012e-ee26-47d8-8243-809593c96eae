<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Log;

class ImagingUpgradeWeeklyReportingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'utility:imagingweeklyreporting';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Report Imaging Upgrades for the previous week.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle(ImagingUpgradeWeeklyReportingJob $runReport)
    {
        Log::info('The Imaging Weekly Upgrades report has started.');

        // call the indexing off the job
        $runJob = $runReport->handle();
        if ($runJob) {
            Log::info('The Imaging Weekly Upgrades Report job ran well.');
        } else {
            Log::info('The Imaging Weekly Upgrades Report job did not run well.');
        }

        return $runJob;
    }
}

<?php

namespace App\Console\Commands;

use App\Jobs\ImagingUpgradeReportingJob;
use Illuminate\Console\Command;
use Log;

class ImagingUpgradeReportingCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'utility:imagingreporting';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Report Imaging Upgrades for the previous month';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle(ImagingUpgradeReportingJob $runReport)
    {
        Log::info('The Imaging Upgrades report has started.');

        // call the indexing off the job
        $runJob = $runReport->handle();
        if ($runJob) {
            Log::info('The Imaging Upgrades Report job ran well.');
        } else {
            Log::info('The Imaging Upgrades Report job did not run well.');
        }

        return $runJob;
    }
}

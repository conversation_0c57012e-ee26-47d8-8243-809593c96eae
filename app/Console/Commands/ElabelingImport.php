<?php

namespace App\Console\Commands;

use App\Jobs\ImportElabelingData;
use Illuminate\Console\Command;
use Log;

class ElabelingImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'utility:elabelingimport';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the JDE & MFG Pro Import';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle(ImportElabelingData $importData)
    {
        Log::info('The Elabeling import job has begun to fire.');

        // call the indexing off the job
        $runJob = $importData->handle();
        if ($runJob) {
            Log::info('The Elabeling import job ran well.');
        } else {
            Log::info('The Elabeling import job did not run well.');
        }

        return $runJob;
    }
}

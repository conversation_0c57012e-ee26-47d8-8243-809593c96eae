<?php

namespace App\Services;

// use Purifier;

class SanitizeService
{
    /**
     * Sanitize
     *
     * Sanitize incoming variable
     *
     * @param  string  $input
     * @return string
     */
    public static function sanitize($input)
    {
        if (is_array($input)) {
            $clean = self::sanitizeArray($input);
        } else {

            // $clean = Purifier::clean( trim($input) );
            $clean = trim($input);

            $clean = self::filter($clean);

            $clean = self::removeInvisibleCharacters($clean, false);
        }

        return $clean;
    }

    /**
     * filter
     *
     * filter input for character list
     *
     * @param  string  $input
     * @return string
     */
    public static function filter($input)
    {
        $filtered = str_replace(['      ', '     ', '    ', '   ', '  ', '../', '..', './', '^', '\\'], [' ', ' ', ' ', ' ', ' ', '', '', '', '', ''], $input); // remove extra whitespace and remove directory pathes ../

        return $filtered;
    }

    /**
     * Sanitize Array
     *
     * Loop through array and sanitize each $value
     *
     * @param  array  $array
     * @return array $array
     */
    public static function sanitizeArray($array)
    {
        return array_map(['Self', 'sanitize'], $array);
    }

    /**
     * Filename Security
     *
     * @param	string
     * @param 	bool
     * @return string
     */
    public static function sanitizeFilename($input, $relative_path = false)
    {
        $bad = [
            '../',
            './',
            '<!--',
            '-->',
            '<',
            '>',
            "'",
            '"',
            '&',
            '$',
            '#',
            '{',
            '}',
            '[',
            ']',
            '=',
            ';',
            '?',
            '%20',
            '%22',
            '%3c',		// <
            '%253c',		// <
            '%3e',		// >
            '%0e',		// >
            '%28',		// (
            '%29',		// )
            '%2528',		// (
            '%26',		// &
            '%24',		// $
            '%3f',		// ?
            '%3b',		// ;
            '%3d',		// =
        ];

        if (! $relative_path) {
            $bad[] = './';
            $bad[] = '/';
        }

        $input = self::removeInvisibleCharacters($input, false);

        $input = self::sanitize($input);

        return stripslashes(str_replace($bad, '', $input));
    }

    /**
     * Remove Invisible Characters
     *
     * This prevents sandwiching null characters
     * between ascii characters, like Java\0script.
     *
     * @param  string  $input
     * @param  bool  $url_encoded
     * @return string
     */
    public static function removeInvisibleCharacters($input, $url_encoded = true)
    {
        $non_displayables = [];

        // every control character except newline (dec 10)
        // carriage return (dec 13), and horizontal tab (dec 09)

        if ($url_encoded) {
            $non_displayables[] = '/%0[0-8bcef]/';	// url encoded 00-08, 11, 12, 14, 15
            $non_displayables[] = '/%1[0-9a-f]/';	// url encoded 16-31
        }

        $non_displayables[] = '/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]+/S';	// 00-08, 11, 12, 14-31, 127

        $input = preg_replace($non_displayables, '', $input);

        return $input;
    }
}

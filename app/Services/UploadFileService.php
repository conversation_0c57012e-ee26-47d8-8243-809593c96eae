<?php

namespace App\Services;

// ini_set('display_errors', 1);

/**
 * UploadManager Class based in part on code from: plupload by Moxiecode Systems AB
 *
 * Copyright 2009, Moxiecode Systems AB
 * Released under GPL License.
 *
 * License: http://www.plupload.com/license
 * Contributing: http://www.plupload.com/contributing
 * Extended by <PERSON><PERSON><PERSON>
 */

use File;
use Illuminate\Http\Response;
use Request;

/*
    if ( ! File::isDirectory($path) and $path) @File::makeDirectory($path, 0777, true);
    File::move($path, $file);
*/

class UploadFileService
{
    // Protected Vars
    protected $allowed_file_types = [];

    protected $temp_dir;

    protected $target_dir;

    // Initial Settings
    public $error = [];

    public $message = [];

    public $max_file_age = '3600'; // Temp file age in seconds

    public $time_limit = '300';  // code execution time limit in seconds

    /**
     * This method sets the default language to be used
     * on the ELabeling pages.
     *
     * @param  array  $data
     * @return User
     */
    public function __construct($request, $files, $target_dir, $temp_dir, array $allowed_file_types)
    {

        // 5 minutes execution time
        @set_time_limit($this->time_limit);

        // Set the files param
        $this->files = $files;
        $this->allowed_file_types = $allowed_file_types;

        // Source File information from request parameters
        $this->source_filename = isset($request['src_filename']) ? $request['src_filename'] : '';
        $this->source_filename_clean = $this->sanitizeFilename($this->source_filename); // Clean the filename for security reasons
        $this->source_filename_extension = File::extension(strtolower($this->source_filename_clean));
        $this->source_filesize = $request['filesize'] ?? '';

        // Chuck file information
        $chunk_filename = isset($request['name']) ? $request['name'] : exit('{"jsonrpc" : "2.0", "error" : {"code": 100, "message": "Unique upload filename is not set."}, "id" : "id"}');

        $this->chunk_filename = $this->sanitizeFilename($chunk_filename); // Clean the filename for security reasons
        $this->chunk_file_extension = File::extension(strtolower($chunk_filename));

        // Set Directory parameters
        $this->temp_dir = (isset($temp_dir) && $temp_dir != '') ? $temp_dir : storage_path().'/app/uploads/temp';
        $this->target_dir = (isset($target_dir) && $target_dir != '') ? $target_dir : storage_path().'/app/uploads/files';
        $this->target_file_path = $this->target_dir.DIRECTORY_SEPARATOR.$this->source_filename_clean; // final target file path.

        // Perform the output check if isset
        $upload_check = (isset($_POST['upload_check'])) ? $_POST['upload_check'] : '';
        if ($upload_check == 1) {
            // initial Check to see if the file already exists and if the file extension is allowed
            $this->preFlightCheck();
        }
    }

    /**
     * Uploads the file
     *
     * @return array source_filename, stored_filename
     */
    public function uploadFile()
    {
        // Check each chuck to see if the source file already exists and if the chunk file extension is allowed
        $this->inFlightCheck();

        // HTTP headers for no cache etc
        header('Content-type: text/plain; charset=UTF-8');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: '.gmdate('D, d M Y H:i:s').' GMT');
        header('Cache-Control: no-store, no-cache, must-revalidate');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');

        // 5 minutes execution time
        @set_time_limit($this->time_limit);

        // Get parameters
        $chunk = $_REQUEST['chunk'] ?? 0;
        $chunks = $_REQUEST['chunks'] ?? 1;

        // Make sure the filename is unique
        //if (file_exists($this->temp_dir . DIRECTORY_SEPARATOR . $filename)) {
        $uid_chunk_name = pathinfo($this->chunk_filename, PATHINFO_FILENAME);

        $count = 0;
        $count += 10000;
        while (File::exists($this->temp_dir.DIRECTORY_SEPARATOR.$uid_chunk_name.'_'.$count.'.'.$this->chunk_file_extension)) {
            $count++;
        }

        $uid_chunk_filename = $uid_chunk_name.'_'.$count.'.'.$this->chunk_file_extension;
        //}

        // Create target dir
        if (! File::exists($this->temp_dir)) {
            File::makeDirectory($this->temp_dir, $mode = 493, $recursive = true);
        }
        if (! File::exists($this->target_dir)) {
            File::makeDirectory($this->target_dir, $mode = 493, $recursive = true);
        }

        // Look for the content type header
        if (isset($_SERVER['HTTP_CONTENT_TYPE'])) {
            $contentType = $_SERVER['HTTP_CONTENT_TYPE'];
        }
        if (isset($_SERVER['CONTENT_TYPE'])) {
            $contentType = $_SERVER['CONTENT_TYPE'];
        }

        // Check to see if it's uploaded at once
        if (strpos($contentType, 'multipart') !== false) {
            if (isset($this->files['file']['tmp_name']) && is_uploaded_file($this->files['file']['tmp_name'])) {
                // Open temp file
                $out = fopen($this->temp_dir.DIRECTORY_SEPARATOR.$uid_chunk_filename, $chunk == 0 ? 'wb' : 'ab');

                //die('{"jsonrpc" : "2.0", "error" : {"code": 107, "message": "'.$this->temp_dir . DIRECTORY_SEPARATOR . $uid_chunk_filename.'"}, "id" : "id"}');

                if ($out) {
                    // Read binary input stream and append it to temp file
                    $in = fopen($this->files['file']['tmp_name'], 'rb');

                    if ($in) {
                        while ($buff = fread($in, 4096)) {
                            fwrite($out, $buff);
                        }
                    } else {
                        exit('{"jsonrpc" : "2.0", "error" : {"code": 101, "message": "Failed to open input stream."}, "id" : "id"}');
                    }

                    fclose($out);

                    // If we reach the last chunk
                    if (($chunk + 1) == $chunks) { // $chunk position starts at zero.

                        // Make an array of all the files
                        $temp_file_chunks_array = File::glob($this->temp_dir.DIRECTORY_SEPARATOR.$uid_chunk_name.'*.*');

                        // If the chunk number matches the number of file chunks received.
                        if ($chunks == count($temp_file_chunks_array)) {

                            // Loop through the array
                            foreach ($temp_file_chunks_array as $key => $temp_file_chunk_name) {
                                $file_contents = File::get($temp_file_chunk_name);

                                // PUT THE FILE BACK TOGETHER
                                if ($key == 0) {
                                    //$this->checkFileMimeType($temp_file_chunk_name);
                                    // Use Put for first chunk
                                    File::put($this->target_file_path, $file_contents);
                                } else {
                                    // Then append there after
                                    File::append($this->target_file_path, $file_contents);
                                }
                            } // End for each

                            // SET THE TARGET SIZE OF THE FILE
                            $target_filesize = File::size($this->target_file_path);

                            if (File::exists($this->target_file_path) && $this->source_filesize == $target_filesize) {

                                // Clean up after the upload and delete the temporary files.
                                $this->cleanUpTempFiles($temp_file_chunks_array);
                                //Get the MD5 hash of the file at the given path.
                                $checksum = File::hash($this->target_file_path);
                                $filesize = File::size($this->target_file_path);

                                return ['success' => true, 'source_filename' => $this->source_filename, 'stored_filename' => $this->source_filename_clean, 'file_extension' => $this->source_filename_extension, 'filesize' => $filesize, 'checksum' => $checksum];

                                // Return JSON-RPC response
                                //die('{"jsonrpc" : "2.0", "result" : "Your file '.$this->source_filename.' was successfully uploaded."}');
                            } else {
                                File::delete($this->target_file_path);
                                // Return JSON-RPC response
                                exit('{"jsonrpc" : "2.0", "result" : "Sorry, but there was a problem uploading your file '.$this->source_filename.'"}');
                            }
                        }
                    }
                } else {
                    exit('{"jsonrpc" : "2.0", "error" : {"code": 102a, "message": "Failed to open output stream. '.$uid_chunk_filename.'"}, "id" : "id"}');
                }
            } else {
                exit('{"jsonrpc" : "2.0", "error" : {"code": 103, "message": "Failed to move uploaded file."}, "id" : "id"}');
            }
        }
    }

    /**
     * Destructor
     *
     * Clean up any files left from a previous upload when we're done
     */
    public function __destruct()
    {
        $this->removeOldTempFiles();
    }

    /////--------------------------------------------------------------------------
    ///// 							Helper FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * Check if file exists
     *
     * @return void
     */
    protected function preFlightCheck()
    {
        if (in_array($this->source_filename_extension, $this->allowed_file_types) == false) {
            //		die('{"jsonrpc" : "2.0", "error" :  "Sorry, but that is not the right file type. "}');
            $this->error[] = 'Sorry, but that is not the right file type.';
        }

        if (File::exists($this->target_file_path)) {
            /*
            return \Response::json(array("jsonrpc" => "2.0", "error" =>  "Sorry, but the '.$this->source_filename.' file already exists."));
            die('{"jsonrpc" : "2.0", "error" :  "Sorry, but the '.$this->source_filename.' file already exists."}');
*/
            $this->error[] = 'Sorry, but the '.$this->source_filename.' file already exists.';
        } else {

            // 			die('{"jsonrpc" : "2.0", "msg" :  "File '.$this->source_filename.' does not exist yet. It is cleared for upload"}');
            $this->message[] = 'File '.$this->source_filename.' does not exist yet. It is cleared for upload';
        }
    }

    /**
     * Check if file exists
     *
     * @return void
     */
    protected function inFlightCheck()
    {
        if (in_array($this->source_filename_extension, $this->allowed_file_types) == false || in_array($this->chunk_file_extension, $this->allowed_file_types) == false) {
            exit('{"jsonrpc" : "2.0", "error" :  "Sorry, but that is not the right file type. "}');
        }

        if (File::exists($this->target_file_path)) {
            //return \Response::json(array("jsonrpc" => "2.0", "error" =>  "Sorry, but the '.$this->source_filename.' file already exists."));
            exit('{"jsonrpc" : "2.0", "error" :  "Sorry, but the '.$this->source_filename.' file already exists."}');
        }
    }

    /**
     * Check if file exists
     *
     * @return void
     */
    protected function checkFileMimeType($file)
    {
        //$finfo = finfo_open(FILEINFO_MIME_TYPE); // return mime type ala mimetype extension
        //    echo finfo_file($finfo, $file) . "\n";
        //finfo_close($finfo);
        //if (File::mimeType($file) == "pdf")
        //{
        //return \Response::json(array("jsonrpc" => "2.0", "error" =>  "Sorry, but the '.$this->source_filename.' file already exists."));
        //die('{"jsonrpc" : "2.0", "error" :  "Sorry, but the '.File::mimeType($file).' is not a PDF."}');
        //}
    }

    /**
     * remove old temp upload files.
     *
     * @return void
     */
    private function removeOldTempFiles()
    {
        // Get all the files in the directory and loop through them
        $files = File::files($this->temp_dir);
        if (is_array($files)) {
            foreach ($files as $file) {
                // Check the time on the file and unlink it it's too old
                if (File::lastModified($file) < time() - $this->max_file_age) {
                    File::delete($file);
                }
            } // END FOR EACH
        } // END IF $FILES
    }

    /**
     * Delete temp upload files.
     *
     * @return void
     */
    private function cleanUpTempFiles(array $files)
    {
        // Get all the files in the directory and loop through them
        if (is_array($files)) {
            foreach ($files as $file) {
                File::delete($file);
            } // END FOR EACH
        } // END IF $FILES
    }

    /**
     * Sanatizes a file name
     *
     * @return string
     */
    private function sanitizeFilename($filename)
    {
        $filename = str_replace(['.php', 'php', '../', '...', '..', './', '/', '*', '?', '^', ' '], ['', '', '', '', '', '', '', '', '_'], $filename); // remove attempts to pass a php or js file. remove directory pathes ../
        $filename = str_replace(['______', '_____', '____', '___', '__', '------', '-----', '----', '---', '--'], ['_', '_', '_', '_', '_', '_', '_', '_', '_', '_'], $filename); // remove multiple underscores and dashes
        $filename = preg_replace('/[^0-9a-zA-Z_\-.]/', '', $filename);  // Whitelist english alphanumeric, underscore, dash and period only.

        return $filename;
    }

    // end class
}

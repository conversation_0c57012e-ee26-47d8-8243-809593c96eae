<?php

namespace App\Services;

// <AUTHOR> <<EMAIL>>
// @modified <PERSON><PERSON><PERSON>
// @version 1.3
// @desc A simple object for processing download operation , support section downloading
// Please send me an email if you find some bug or it doesn't work with download manager.
// I've tested it with
// 	- Reget
// 	- FDM
// 	- FlashGet
// 	- GetRight
// 	- DAP
//
// @copyright It's free as long as you keep this header .
// @example
//
// 1: File Download
// $download = new DownloadFileService;
// 	$download->setByFile($filename); //Download from a file
// 	$download->use_resume = true; //Enable Resume Mode
// 	$download->download(); //Download File
//
// 2: Data Download
// $download = new DownloadFileService;
// 	$download->setByData($data); //Download from php data
// 	$download->use_resume = true; //Enable Resume Mode
// 	$download->set_filename($filename); //Set download name
// 	$download->$mime = $mime; //File MIME/Content-Type (Default: application/otect-stream)
// $download->disposition = $disposition; //File Content-Disposition (Default: attachment)
// 	$download->download(); //Download File
//
// 3: Manual Download
// $download = new DownloadFileService;
// 	$download->set_filename($filename);
// $download->downloadEx($size);
// //output your data here , remember to use $this->seek_start and $this->seek_end value :)
//

class DownloadFileService
{
    public $data = null;

    public $data_len = 0;

    public $data_mod = 0;

    public $data_type = 0;

    public $data_section = 0; //section download

    /**
     * @var ObjectHandler
     **/
    public $handler = ['auth' => null];

    public $use_resume = true;

    public $use_autoexit = false;

    public $use_auth = false;

    public $filename = null;

    public $output_filename = null;

    public $mime = null;

    public $disposition = null;

    public $bufsize = 2048;

    public $seek_start = 0;

    public $seek_end = -1;

    /**
     * Total bandwidth has been used for this download
     *
     * @var int
     */
    public $bandwidth = 0;

    /**
     * Speed limit
     *
     * @var float
     */
    public $speed = 0;

    /*-------------------
    | Download Function |
    -------------------*/
    /**
     * Check authentication and get seek position
     *
     * @return bool
     **/
    public function initialize()
    {
        global $HTTP_SERVER_VARS;

        if ($this->use_auth) { //use authentication
            if (! $this->_auth()) { //no authentication
                header('WWW-Authenticate: Basic realm="Please enter your username and password"');
                header('HTTP/1.0 401 Unauthorized');
                header('status: 401 Unauthorized');
                if ($this->use_autoexit) {
                    exit();
                }

                return false;
            }
        }
        if ($this->mime == null) {
            $this->mime = 'application/octet-stream';
        } //default mime
        if ($this->disposition == null) {
            $this->disposition = 'attachment';
        } //default mime

        if (isset($_SERVER['HTTP_RANGE']) || isset($HTTP_SERVER_VARS['HTTP_RANGE'])) {
            if (isset($HTTP_SERVER_VARS['HTTP_RANGE'])) {
                $seek_range = substr($HTTP_SERVER_VARS['HTTP_RANGE'], strlen('bytes='));
            } else {
                $seek_range = substr($_SERVER['HTTP_RANGE'], strlen('bytes='));
            }

            $range = explode('-', $seek_range);

            if ($range[0] > 0) {
                $this->seek_start = intval($range[0]);
            }

            if ($range[1] > 0) {
                $this->seek_end = intval($range[1]);
            } else {
                $this->seek_end = -1;
            }

            if (! $this->use_resume) {
                $this->seek_start = 0;

                //header("HTTP/1.0 404 Bad Request");
                //header("Status: 400 Bad Request");

                //exit;

                //return false;
            } else {
                $this->data_section = 1;
            }
        } else {
            $this->seek_start = 0;
            $this->seek_end = -1;
        }

        return true;
    }

    /**
     * Send download information header
     **/
    public function header($size, $seek_start = null, $seek_end = null)
    {
        header('Content-type: '.$this->mime);
        header('Content-Disposition: '.$this->disposition.'; filename="'.$this->output_filename.'"');
        header('Last-Modified: '.date('D, d M Y H:i:s \G\M\T', $this->data_mod));
        header('Cache-Control: public');
        header('Pragma: public');

        if ($this->data_section && $this->use_resume) {
            header('HTTP/1.0 206 Partial Content');
            header('Status: 206 Partial Content');
            header('Accept-Ranges: bytes');
            header("Content-Range: bytes $seek_start-$seek_end/$size");
            header('Content-Length: '.($seek_end - $seek_start + 1));
        } else {
            header("Content-Length: $size");
        }
    }

    public function downloadEx($size)
    {
        if (! $this->initialize()) {
            return false;
        }
        ignore_user_abort(true);
        //Use seek end here
        if ($this->seek_start > ($size - 1)) {
            $this->seek_start = 0;
        }
        if ($this->seek_end <= 0) {
            $this->seek_end = $size - 1;
        }
        $this->header($size, $seek, $this->seek_end);
        $this->data_mod = time();

        return true;
    }

    /**
     * Start download
     *
     * @return bool
     **/
    public function download()
    {
        if (! $this->initialize()) {
            return false;
        }

        $seek = $this->seek_start;
        $speed = $this->speed;
        $bufsize = $this->bufsize;
        $packet = 1;

        //do some clean up
        @ob_end_clean();
        $old_status = ignore_user_abort(true);
        @set_time_limit(0);
        $this->bandwidth = 0;

        $size = $this->data_len;

        if ($this->data_type == 0) { //download from a file

            $size = filesize($this->data);

            if ($seek > ($size - 1)) {
                $seek = 0;
            }
            if ($this->filename == null) {
                $this->filename = basename($this->data);
            }

            $res = fopen($this->data, 'rb');
            if ($seek) {
                fseek($res, $seek);
            }
            if ($this->seek_end < $seek) {
                $this->seek_end = $size - 1;
            }

            $this->header($size, $seek, $this->seek_end); //always use the last seek
            $size = $this->seek_end - $seek + 1;

            while (! (connection_aborted() || connection_status() == 1) && $size > 0) {
                if ($size < $bufsize) {
                    echo fread($res, $size);
                    $this->bandwidth += $size;
                } else {
                    echo fread($res, $bufsize);
                    $this->bandwidth += $bufsize;
                }

                $size -= $bufsize;
                flush();

                if ($speed > 0 && ($this->bandwidth > $speed * $packet * 1024)) {
                    sleep(1);
                    $packet++;
                }
            }
            fclose($res);
        } elseif ($this->data_type == 1) { //download from a string

            if ($this->data_section && $this->use_resume) {
                if ($seek > ($size - 1)) {
                    $seek = 0;
                }
                if ($this->seek_end < $seek) {
                    $this->seek_end = $this->data_len - 1;
                }
                $this->data = substr($this->data, $seek, $this->seek_end - $seek + 1);
                if ($this->filename == null) {
                    $this->filename = time();
                }
                $size = strlen($this->data);
                $this->header($this->data_len, $seek, $this->seek_end);
                while (! connection_aborted() && $size > 0) {
                    if ($size < $bufsize) {
                        $this->bandwidth += $size;
                    } else {
                        $this->bandwidth += $bufsize;
                    }

                    echo substr($this->data, 0, $bufsize);
                    $this->data = substr($this->data, $bufsize);

                    $size -= $bufsize;
                    flush();

                    if ($speed > 0 && ($this->bandwidth > $speed * $packet * 1024)) {
                        sleep(1);
                        $packet++;
                    }
                }
            } else {
                $this->header($this->data_len, $seek_start = null, $seek_end = null);
                echo $this->data;
            }
        } elseif ($this->data_type == 2) {
            //just send a redirect header
            header('location: '.$this->data);
        }

        if ($this->use_autoexit) {
            exit();
        }

        //restore old status
        ignore_user_abort($old_status);
        set_time_limit(ini_get('max_execution_time'));

        return true;
    }

    public function setByFile($dir)
    {
        if (is_readable($dir) && is_file($dir)) {
            $this->data_len = 0;
            $this->data = $dir;
            $this->data_type = 0;
            $this->data_mod = filemtime($dir);

            return true;
        } else {
            return false;
        }
    }

    public function setByData($data)
    {
        if ($data == '') {
            return false;
        }
        $this->data = $data;
        $this->data_len = strlen($data);
        $this->data_type = 1;
        $this->data_mod = time();

        return true;
    }

    public function setByUrl($data)
    {
        $this->data = $data;
        $this->data_len = 0;
        $this->data_type = 2;

        return true;
    }

    public function setLastModTime($time)
    {
        $time = intval($time);
        if ($time <= 0) {
            $time = time();
        }
        $this->data_mod = $time;
    }

    /**
     * Check authentication
     *
     * @return bool
     **/
    public function _auth()
    {
        if (! isset($_SERVER['PHP_AUTH_USER'])) {
            return false;
        }
        if (isset($this->handler['auth']) && function_exists($this->handler['auth'])) {
            return $this->handler['auth']('auth', $_SERVER['PHP_AUTH_USER'], $_SERVER['PHP_AUTH_PW']);
        } else {
            return true;
        } //you must use a handler
    }
}

<?php

namespace App\Services;

use App\Models\Language;

class SetLanguageService
{
    /**
     * This method sets the default language to be used
     * on the ELabeling pages.
     *
     * @param  array  $data
     * @return User
     */
    public function setLanguage($default_language = '')
    {

        // FIRST, check if there's an input, as this will be a selection and override everything
        if (isset($default_language) && ($default_language != '')) {

            // Set it to the session var
            $_SESSION['selected_language'] = $default_language;

            // Set it to the var we'll return
            $selected_language = $default_language;

            // IF no Input...
        } else {

            // Then, check if there's a variable in the session
            if (isset($_SESSION['selected_language'])) {

                // Set it to the var we'll return
                $selected_language = $_SESSION['selected_language'];

                // ELSE, if there is no cookie variable set
            } else {

                // Detect if their browser language as a default -> it may match an enabled language
                $http_accept_language = isset($_SERVER['HTTP_ACCEPT_LANGUAGE']) ? $_SERVER['HTTP_ACCEPT_LANGUAGE'] : '';
                $browser_language = substr($http_accept_language, 0, 2);

                // Check to see if the browser language is set
                if (isset($browser_language) && ($browser_language != '')) {

                    // check the db for the language
                    $find_language = Language::where('active', '!=', '0')->where('iso_639_1', $browser_language)->first();

                    // if it's there, set it.. otherwise set default
                    if (isset($find_language)) {

                        // Set it to the var we'll return
                        $selected_language = $find_language->iso_639_1;

                        // else if there is no returned result from the DB Query
                    } else {

                        // Set it to the var we'll return
                        $selected_language = 'en';
                    }

                    // else if there is no browser language detetched (and no session var, and no input)...
                } else {

                    // Set it to the var we'll return
                    $selected_language = 'en';
                }
            } // end if no cookie set
        } // end if no input

        // Set the Laravel Localization
        \App::setLocale($selected_language);

        return $selected_language;
    }
}

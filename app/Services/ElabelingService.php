<?php

namespace App\Services;

use App\Models\Elabeling\ElabelingLibraryFile;
use App\Models\Elabeling\ElabelingLibraryRecord;
use App\Models\Elabeling\ElabelingResourceTypes;
use App\Models\Language;

class ElabelingService
{
    /*
     * ELabeling ADMIN HELPERS
     */

    // Display how many files are left to be uploaded until each record has a file for each activated language
    public function displayMissingFileWarning($id)
    {
        // Get the number of files with this id
        $file_count = ElabelingLibraryFile::where('parent_id', $id)->count();

        // Get the available languages in the website from the database
        $enabled_languages = Language::where('active', '1')->count();

        // Logic for missing file count
        $missing_file_count = $enabled_languages - $file_count;

        return ($missing_file_count > 0) ? ''.$missing_file_count.'' : '';
    }

    // Display how many files are left to be uploaded until each record has a file for each activated language
    public function listMissingLanguages($id)
    {
        // Get the number of files with this id
        $file_languages = ElabelingLibraryFile::where('parent_id', $id)->pluck('language')->toArray();

        // Get the available languages in the website from the database
        $enabled_languages = Language::where('active', '1')->pluck('iso_639_1')->toArray();

        // missing languages
        return array_diff($enabled_languages, $file_languages);
    }

    // Get the Resource Type
    public function listMissingLanguagesAsList($id)
    {
        $missing_languages_array = [];

        // missing languages
        $missing_langauges = $this->listMissingLanguages($id);

        foreach ($missing_langauges as $missing_language) {
            $fullname = $this->getLanguageName($missing_language);
            $missing_languages_array[] = $fullname;
        }

        return implode(', ', $missing_languages_array);
    }

    // Get the Resource Type
    public function getResourceType($id)
    {
        // Get the Resource type
        return $filetype = ElabelingResourceTypes::where('id', $id)->value('name');
    }

    // Get the Resource Type
    public function getLanguageName($short_name)
    {
        // Get the Resource type
        return $language = Language::where('iso_639_1', $short_name)->value('language_name');
    }

    // GET ALL OF THE RELATED DOCUMENTS
    public function getResourceItem($part_number, $language = 'en', $trashed = false)
    {
        if ($trashed) {
            $relatedItem = ElabelingLibraryRecord::join('eifu_library_files', 'eifu_library_files.parent_id', '=', 'eifu_library.id')
                ->join('eifu_resource_types', 'eifu_library.resource_type', '=', 'eifu_resource_types.id')
                ->select('eifu_library.part_number', 'eifu_library.title', 'eifu_library.replacement_part_number', 'eifu_library.replacement_notification_message', 'eifu_library.replacement_rationale_notes', 'eifu_library.replacement_date', 'eifu_library_files.filename', 'eifu_library_files.filesize', 'eifu_resource_types.name', 'eifu_resource_types.order')
                ->where('eifu_library.part_number', $part_number)
                ->where('eifu_library_files.language', $language)
                ->where('eifu_library.active', 1)
                ->where('eifu_library_files.active', 1)
                                            //->orWhere('eifu_library_files.language', 'en')
                ->orderBy('eifu_library.part_number', 'asc')
                ->get();
        } else {
            $relatedItem = ElabelingLibraryRecord::join('eifu_library_files', 'eifu_library_files.parent_id', '=', 'eifu_library.id')
                ->join('eifu_resource_types', 'eifu_library.resource_type', '=', 'eifu_resource_types.id')
                ->select('eifu_library.part_number', 'eifu_library.title', 'eifu_library.replacement_part_number', 'eifu_library.replacement_notification_message', 'eifu_library.replacement_rationale_notes', 'eifu_library.replacement_date', 'eifu_library_files.filename', 'eifu_library_files.filesize', 'eifu_resource_types.name', 'eifu_resource_types.order')
                ->where('eifu_library.part_number', $part_number)
                ->whereNull('eifu_library.deleted_at')
                ->where('eifu_library_files.language', $language)
                ->where('eifu_library.active', 1)
                ->where('eifu_library_files.active', 1)
                                        //->orWhere('eifu_library_files.language', 'en')
                ->orderBy('eifu_library.part_number', 'asc')
                ->first();
        }

        if (isset($relatedItem) > 0) {
            // If a part number/approval number has been replaced by a new part then get the replacement part number info instead.
            if (isset($relatedItem->replacement_part_number) && ($relatedItem->replacement_part_number != '')) {
                // Loop through self to get and return the associative result set
                return $this->getResourceItem($relatedItem->replacement_part_number, $language);
            } else {
                // return associative result set
                return $relatedItem;
            }
        } else {
            return false;
        }
    }

    // Get all the literature items and sort them by type eifu_resource_types.order
    public function getSortedResourceItems($search_results, $language = 'en', $confirmation = false)
    {
        $items = [];

        foreach ($search_results as $result) {
            if ($confirmation) {
                $item = $this->getResourceItem($result->part_number, $language, true);
            } else {
                $item = $this->getResourceItem($result->part_number, $language, false);
            }

            if (isset($item) && $item != false) {
                $items[] = $item;
            }
        }

        $collection = collect($items);
        $sorted = $collection->sortBy('order');

        return $sorted;
    }

    //end class
}

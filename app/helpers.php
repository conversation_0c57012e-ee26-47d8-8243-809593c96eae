<?php

/****************************
 * Roles and Permissions Helpers
 *****************************/

function get_persmission_number_for($action)
{
    $permission = \DB::table('permissions')->whereTitle($action)->first();

    return $permission->id;
}

function get_role_based_on_route_path($routePath)
{
    $role = \DB::table('roles')->whereTitle($routePath)->first();

    return $role->id;
}

function get_user_roles_as_list($roles, $withLabels = false)
{
    $output = [];
    foreach ($roles as $role) {
        if ($withLabels) {
            $output[] .= '<span class="label">'.$role->title.'</span>';
        } else {
            $output[] .= $role->title;
        }
    }
    if ($withLabels) {
        return implode($output, ' ');
    } else {
        return implode($output, ', ');
    }
}

function get_route_path_for_this_request($request)
{
    // remove the number of characters in the admin prefix + 1 for the directory structure
    $routePath = substr_replace($request, '', 0, strlen(\Config::get('settings.url_prefixes.admin_prefix')) + 1);

    // get first X of the route
    $firstLetters = substr($routePath, 0, strlen(\Config::get('settings.url_prefixes.site_prefix')));

    // check if it is site prefix
    if ($routePath !== $firstLetters) {
        // check to see if it begins with sitepath
        if (\Config::get('settings.url_prefixes.site_prefix') == $firstLetters) {
            $routePath = substr_replace($routePath, '', 0, strlen(\Config::get('settings.url_prefixes.site_prefix')) + 1);
        }
    }

    $firstPartOfRoutePath = explode('/', $routePath);

    return $firstPartOfRoutePath[0];
}

function generate_random_string($name_length = 8)
{
    $alpha_numeric = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

    return substr(str_shuffle($alpha_numeric), 0, $name_length);
}

/****************************
 * User Helpers
 *****************************/

// Takes a size and class, returns the authenticated users avatar
function getUserAvatar($size = '50', $class = 'thumbnail media-object')
{
    return '<img src="https://www.gravatar.com/avatar/'.md5(Auth::user()->email).'?s='.$size.'&d=identicon&r=PG&d=retro" class="'.$class.'" style="float: left;display: inline-block;margin-top: -3px;">';
}

/****************************
 * Comments: These helper functions can be used throughout the site as they are loaded into the global scope
 *****************************/

/** YUI Drop-Down Menu - Creates YUI Top Nav Drop-Down Menu and Menu items              **/
// function topnav_menu($target, $a = '', $b = '', $c = '', $d = '', $e = '', $f = '', $g = '', $h = '', $i = '', $j = '', $k = '', $l = '', $m = '', $n = '', $o = '', $p = '', $q = '', $r = '', $s = '', $t = '')
// { // ?category=name, "name" must match the recieving Category page "category" name

//     $menu_items = array_filter([$a, $b, $c, $d, $e, $f, $g, $h, $i, $j, $k, $l, $m, $n, $o, $p, $q, $r, $s, $t]);

//     while ([$key, $val] = each($menu_items)) {
//         if (!empty($val)) {
//             ($key == 0) ? $li = '<li class="yuimenuitem first-of-type">' : $li = '<li class="yuimenuitem">';

//             if (is_array($val)) {
//                 foreach ($val as $k => $v) {
//                     echo $li . '<a class="yuimenuitem" href="' . $v . '">' . $k . '</a></li>';
//                 }
//             } else {
//                 echo $li . '<a class="yuimenuitemlabel" href="/' . $target . '/' . $val . '">' . $val . '</a></li>';
//             }
//         }
//     }
// }

/** Category Menu - Creates menu and selected category              **/
function category_menu($a = '', $b = '', $c = '', $d = '', $e = '', $f = '', $g = '', $h = '', $i = '', $j = '', $k = '', $l = '', $m = '', $n = '', $o = '', $p = '', $q = '', $r = '', $s = '', $t = '')
{ // ?category=name || ?category=int
    global $page;

    $menu_items = [$a, $b, $c, $d, $e, $f, $g, $h, $i, $j, $k, $l, $m, $n, $o, $p, $q, $r, $s, $t];

    /*echo '<script src="assets/js/switch_content-pc.js" type="text/javascript"></script>';*/

    $page = str_replace('.', '_', $_SERVER['PHP_SELF']);

    if (isset($_GET['section'])) {
        $catid = ($_GET['section']) - 1;
    } else {
        $catid = str_replace('|', '', $_COOKIE[$page]);
        $catid = str_replace('cat', '', $catid);
    }

    $category_menu = '<ul class="yui-nav">';

    $count = 0;

    while ([$key, $val] = each($menu_items)) {
        if (! empty($val)) {
            ($catid == $key) ? $li = '<li class="selected">' : $li = '<li>';

            $category_menu .= $li.'<a href="'.$_SERVER['PHP_SELF'].'#cat" onclick="expandcontent(this, \'cat'.$key.'\'); Effect.ScrollTo(\'cat\'); return false; "><span class="showstate"></span><span class="showcolor">'.$val.'</span></a></li>';
        }
        $count++;
    }

    echo $category_menu .= '</ul>';
}

/** Page Menu - Creates jquery tab menu and selected section              **/
function side_menu(array $menu_items, $safety_info = true)
{ // ?section=name || ?section=int

    $menu = '<ul class="side-nav">';

    foreach ($menu_items as $key => $val) {

        // check that $val is not an array before you try to apply the filters.
        if (is_array($val) == false) {
            $anchor = strtolower(str_replace(' ', '_', $val));
        }

        // if key is not numeric and is set to a text value than use the text based key as the anchor link.
        if (! empty($key) && ! is_numeric($key)) {
            $menu .= '<li data-tab-name="'.$key.'"><a href="'.URL::current().'#'.$key.'">'.$val.'</a></li>';
        } elseif (! empty($val)) {

            // use an array to build a hard link.
            if (is_array($val)) {
                foreach ($val as $name => $path) {
                    $menu .= '<li data-tab-name="'.str_slug($name).'" data-href="'.url($path).'"><a href="'.url($path).'">'.$name.'</a></li>';
                }
            } else {
                $menu .= '<li data-tab-name="'.str_slug($val).'"><a href="'.URL::current().'#'.$anchor.'">'.$val.'</a></li>';
            }
        }
    }
    echo $menu .= '</ul>';

    if ($safety_info == true) {
        echo '<ul class="select">
					<li><a href="'.URL::current().'#literature" data-open-tab="literature" class="open-tab">Safety Information</a></li>
				  </ul>';
    }
}

function consult_statement()
{
    echo '<p class="red disclaimer">Please consult product labels, IFU, and package inserts for any indications, contraindications, hazards, warnings, cautions, and instructions for use.</p>';
}
function safety_statement()
{
    echo '<p class="red disclaimer">Please refer below to the Instructions for Use (IFUs) for any indications, contraindications, hazards, warnings, and cautions.</p>';
}
function safety_menu_item()
{
    echo '<ul class="select">
            	<li><a href="'.URL::current().'#resources" class="open-tab">Safety Information</a></li>
              </ul>';
}

//sherlock 3CG disclaimer
function sherlock_disclaimer()
{
    return '<p class="red disclaimer">Sherlock 3CG&#174; TCS is indicated for use as an alternative to chest x-ray and fluoroscopy for PICC tip placement confirmation in adult patients. Any alterations of cardiac rhythms that change the normal presentation of the P-wave limit the use of ECG tip confirmation technology. In these instances, confirm PICC tip location using an alternative method. Please consult instructions for use for additional safety information. </p>';
}

/**
 * get file modified time of blade view.
 *
 * @param  string  $view_params
 * @return string
 */
function getViewFilemtime($view_params)
{
    $view_path = realpath(base_path().'/resources/views/'.str_replace('.', '/', $view_params).'.blade.php');

    if ($view_path) {
        return date('Y-m-d', filemtime($view_path));
    } else {
        return '';
    }
}

/**
 * Normalize Storage Path for Windows and Linux.
 *
 * @return string
 */
function normalizeFilePath($file_path)
{
    return str_replace(['\\', '/', '\\\\', '//', '\/\/'], [DIRECTORY_SEPARATOR, DIRECTORY_SEPARATOR, DIRECTORY_SEPARATOR, DIRECTORY_SEPARATOR, DIRECTORY_SEPARATOR], $file_path);
}

/**
 * Product Catalog option builder.
 *
 * @param  array  $options
 * @param  array  $fieldname
 * @return string
 */
function buildOptionsList($options, $fieldname = '')
{
    if (is_array($fieldname)) {
        foreach ($options as $val => $display) {
            $selected = (in_array($val, $fieldname)) ? 'selected="selected"' : '';
            echo '<option value="'.$val.'" '.$selected.'> '.$display.'</option>';
        }
    } else {
        foreach ($options as $val => $display) {
            $selected = ($fieldname == $val) ? 'selected="selected"' : '';
            echo '<option value="'.$val.'" '.$selected.'> '.$display.'</option>';
        }
    }
}

function required_field(&$problems, $field, $type = 'cell')
{
    if (! is_array($problems)) {
        echo '';
    } elseif (array_key_exists($field, $problems)) {
        echo ' class="error"';
    }
}

function date_mysql_to_julian($date)
{
    if (strlen($date) == 14) { // timestamp
        if (substr($date, 0, 8) == '00000000') {
            return '';
        } else {
            $year = substr($date, 0, 4);
            $month = intval(substr($date, 4, 2));
            $day = intval(substr($date, 6, 2));

            return "$month/$day/$year";
        }
    }

    if ($date == '' || substr($date, 0, 10) == '0000-00-00') {
        return '';
    }

    return date('m/d/Y', strtotime($date));
}

function formatFileSize($size, $precision = 2)
{
    $base = log($size, 1024);
    $suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];

    return round(pow(1024, $base - floor($base)), $precision).' '.$suffixes[floor($base)];
}

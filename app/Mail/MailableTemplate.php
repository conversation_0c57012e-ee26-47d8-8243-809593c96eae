<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

/**
 * Reusable Email Template
 *
 *      $attachment = [
 *           'file' => $report,
 *           'filename' => $filename,
 *           'mime' => 'text/csv'
 *       ];
 */
class MailableTemplate extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * any public property defined on your mailable class will automatically be made available to the view
     */
    public $view_name;

    public $data;

    public $subject;

    public $attachment;

    public $attachment_type; // none, data, file

    /**
     * Create a new message instance.
     */
    public function __construct($view_name, $data, $subject, $attachment = [], $attachment_type = 'none')
    {
        $this->view_name = $view_name;
        $this->data = $data;
        $this->subject = $subject;
        $this->attachment = $attachment;
        $this->attachment_type = $attachment_type;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: $this->view_name,
            with: [
                'data' => $this->data,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        if (isset($this->attachment) && ! empty($this->attachment)) {

            switch ($this->attachment_type) {
                // Raw Data Attachments
                case 'data':
                    return [
                        Attachment::fromData(fn () => $this->attachment['file'], $this->attachment['filename'])
                            ->withMime($this->attachment['mime']),
                    ];
                    break;

                    // Attaching Files From Disk
                case 'file':
                    return [
                        // $this->attachment['file'] = '/path/to/file'
                        // Attachment::fromStorage('/path/to/file')
                        //     ->as('name.pdf')
                        //     ->withMime('application/pdf'),
                        Attachment::fromPath($this->attachment['file'])
                            ->as($this->attachment['filename'])
                            ->withMime($this->attachment['mime']),
                    ];
                    break;

                    // Attaching Files From Storage Disk
                case 'storage':
                    return [
                        // $this->attachment['file'] = '/path/to/file'
                        // Attachment::fromStorageDisk('s3', '/path/to/file')
                        //     ->as('name.pdf')
                        //     ->withMime('application/pdf'),
                        Attachment::fromStorageDisk($this->attachment['disk'], $this->attachment['file'])
                            ->as($this->attachment['filename'])
                            ->withMime($this->attachment['mime']),
                    ];
                    break;

                case 'none':
                default:
                    return [];
                    break;
            }
        } else {
            return [];
        }
    }
}

<?php

namespace App\Imports;

use App\Models\ImagingUpgrade\IuaSystemUpgradeCountryEligibilityMatrixUat;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithUpserts;
use Illuminate\Support\Facades\Log;

class IuaSystemUpgradeCountryEligibilityMatrixUatImport implements 
    ToModel, 
    WithBatchInserts, 
    WithChunkReading, 
    WithHeadingRow, 
    WithValidation,
    WithUpserts
{
    private $rowCount = 0;

    public function uniqueBy()
    {
        return ['system_id', 'country_code'];
    }

    public function model(array $row)
    {
        $this->rowCount++;
        // Removed per-row logging
        try {
            return new IuaSystemUpgradeCountryEligibilityMatrixUat([
                'system_id' => $row['system_id'] ?? null,
                'system_name' => $row['system_name'] ?? null,
                'country_code' => $row['country_code'] ?? null,
                'country_name' => $row['country_name'] ?? null,
                'ultrasound' => $row['ultrasound'] ?? null,
                'sherlock' => $row['sherlock'] ?? null,
                'shell' => $row['shell'] ?? null,
                'dicom' => $row['dicom'] ?? null,
                'source_filename' => $row['source_filename'] ?? null,
                'download_filename' => $row['download_filename'] ?? null,
                'product_image' => $row['product_image'] ?? null,
                'installation_instructions' => $row['installation_instructions'] ?? null,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to process import: " . $e->getMessage());
            throw $e;
        }
    }

    public function rules(): array
    {
        return [
            'system_id' => 'required',
            'system_name' => 'required',
            'country_code' => 'required',
            'country_name' => 'nullable',
        ];
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}
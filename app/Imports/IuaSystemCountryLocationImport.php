<?php

namespace App\Imports;

use App\Models\ImagingUpgrade\IuaSystemCountryLocation;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithUpserts;
use Illuminate\Support\Facades\Log;

class IuaSystemCountryLocationImport implements 
    ToModel, 
    WithBatchInserts, 
    WithChunkReading, 
    WithHeadingRow, 
    WithValidation,
    WithUpserts
{
    private $rowCount = 0;

    public function uniqueBy()
    {
        return 'serial_number';
    }

    public function model(array $row)
    {
        $this->rowCount++;
        // Remove the per-row logging
        
        try {
            return new IuaSystemCountryLocation([
                'product_family' => $row['product_family'] ?? null,
                'serial_number' => strtoupper($row['serial_number'] ?? ''),
                'country_code' => $row['country_code'] ?? null,
            ]);
        } catch (\Exception $e) {
            Log::error("Failed to process import: " . $e->getMessage());
            throw $e;
        }
    }

    public function rules(): array
    {
        return [
            'product_family' => 'required',
            'serial_number' => 'required',
            'country_code' => 'nullable', // Changed from 'required' to 'nullable'
        ];
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 1000;
    }
}

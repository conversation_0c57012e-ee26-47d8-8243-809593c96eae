<?php

namespace App\Models\ProductCatalog;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductCode extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'catalog_product_codes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['category_id', 'product_code', 'description', 'packaging_configuration', 'minimum_quantity', 'price', 'image1', 'image2', 'image3'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    public function category()
    {
        return $this->belongsTo('App\Models\ProductCatalog\CatalogCategory', 'id');
    }

    ////////////////////////////////////////////////////
    /// Mutators

    /**
     * Trim whitespace from value.
     *
     * @param  string  $value
     */
    public function setProductCodeAttribute($value)
    {
        $this->attributes['product_code'] = trim($value);
    }

    /**
     * Trim whitespace from value.
     *
     * @param  string  $value
     */
    public function setDescriptionAttribute($value)
    {
        $this->attributes['description'] = trim($value);
    }
}

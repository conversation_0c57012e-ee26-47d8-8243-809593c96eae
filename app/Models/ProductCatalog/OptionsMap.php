<?php

namespace App\Models\ProductCatalog;

use Illuminate\Database\Eloquent\Model;

class OptionsMap extends Model
{
    //use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'catalog_options_map';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['product_id', 'option_id'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    public function optionField()
    {
        // return $this->belongsTo('App\Models\ModelName', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\ProductCatalog\FieldOption', 'field_id');
    }
}

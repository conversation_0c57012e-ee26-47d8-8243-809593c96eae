<?php

namespace App\Models\ProductCatalog;

use Illuminate\Database\Eloquent\Model;

class CategoryField extends Model
{
    //use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'catalog_category_fields';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['category_id', 'short_name', 'title', 'additional', 'active', 'sort_order'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    public function fieldOptions()
    {
        return $this->hasMany('App\Models\ProductCatalog\FieldOption', 'field_id')->orderBy('sort_order', 'ASC');
    }

    ////////////////////////////////////////////////////
    /// Mutators

    /**
     * Uppercase the first character of each word in the title
     *
     * @param  string  $value
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = trim(ucwords($value));
    }

    /**
     * Uppercase the first character of each word in the title
     *
     * @param  string  $value
     */
    public function setShortNameAttribute($value)
    {
        $this->attributes['short_name'] = trim(str_slug($value, '_'));
    }
}

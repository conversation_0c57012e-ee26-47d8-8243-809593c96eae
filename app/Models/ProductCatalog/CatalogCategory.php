<?php

namespace App\Models\ProductCatalog;

use Illuminate\Database\Eloquent\Model;

class CatalogCategory extends Model
{
    //use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'catalog_categories';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    public function products()
    {
        return $this->hasMany('App\Models\ProductCatalog\ProductCode', 'category_id', 'id');
    }
}

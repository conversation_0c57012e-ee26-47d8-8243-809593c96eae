<?php

namespace App\Models\ProductCatalog;

use Illuminate\Database\Eloquent\Model;

class FieldOption extends Model
{
    //use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'catalog_field_options';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['field_id', 'title', 'active', 'sort_order'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = ['created_at', 'updated_at'];

    public function categoryField()
    {
        return $this->belongsTo('App\Models\ProductCatalog\CategoryField', 'field_id');
    }

    public function optionsMap()
    {
        return $this->hasMany('App\Models\ProductCatalog\OptionsMap', 'option_id');
    }

    ////////////////////////////////////////////////////
    /// Mutators

    /**
     * Uppercase the first character of each word in the title
     *
     * @param  string  $value
     */
    public function setTitleAttribute($value)
    {
        $this->attributes['title'] = trim(ucfirst($value));
    }
}

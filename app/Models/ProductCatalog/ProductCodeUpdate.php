<?php

namespace App\Models\ProductCatalog;

use Illuminate\Database\Eloquent\Model;

class ProductCodeUpdate extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'catalog_product_code_updates';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['category_id', 'product_code', 'description', 'packaging_configuration', 'minimum_quantity', 'price', 'image1', 'image2', 'image3'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];
}

<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;

class SiteLiterature extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'site_literature';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['literature_type_id', 'tracking_number', 'title', 'filename', 'fileformat', 'filesize', 'uploaded_by', 'active'];

    /**
     * Get the pages associated with a given literature item
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function pages()
    {
        return $this->belongsToMany('App\Models\Site\SitePage', 'site_literature_site_page', 'literature_id', 'page_id');
    }

    /**
     * Get the pages associated with a given literature item
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function types()
    {

        // return $this->belongsTo('App\Models\ModelName', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\Site\SiteLiteratureTypes', 'literature_type_id');
    }
}

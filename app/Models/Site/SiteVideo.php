<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;

class SiteVideo extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'site_videos';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['unique_id', 'tracking_number', 'title', 'description', 'playtime', 'poster_width', 'poster_height', 'active', 'sort_order'];

    /**
     * Get the pages associated with a given literature item
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    //
    public function pages()
    {

        return $this->belongsToMany('App\Models\Site\SitePage', 'site_pages_site_videos', 'video_id', 'page_id');

    }

    /**
     * Get the pages associated with a given literature item
     *
     * @return \illuminate\Database\Eloquent\Relations\hasMany
     */
    //
    public function files()
    {

        // return $this->hasMany('App\Comment', 'foreign_key', 'local_key');
        return $this->hasMany('App\Models\Site\SiteVideoFile', 'video_id', 'id');

    }
}

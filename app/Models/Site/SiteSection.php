<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;

class SiteSection extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'site_pages_sections';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['title', 'slug', 'page_id', 'content', 'content_html'];

    /**
     * Get the pages associated with a given literature item
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    //
    public function pages()
    {

        return $this->belongsToMany('App\Models\Site\SitePage', 'site_page_site_section', 'section_id', 'page_id');

    }
}

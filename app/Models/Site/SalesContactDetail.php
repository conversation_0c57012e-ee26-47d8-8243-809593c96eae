<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;

class SalesContactDetail extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'sales_contact_details';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name_first',
        'name_last',
        'email',
        'title',
        'employer',
        'emp_address',
        'emp_city',
        'emp_state',
        'emp_zipcode',
        'phone_day',
        'contact_via',
        'contact_time',
        'comments',
        'products',
        'opt_in',
        'terms_privacy',
    ];
}

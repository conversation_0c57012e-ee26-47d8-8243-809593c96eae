<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SiteCategory extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'site_categories';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['title', 'short_name', 'product_category', 'active', 'sort_order'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    public function subcategories()
    {
        //return $this->hasMany('App\Models\ModelName', 'foreign_key', 'local_key');
        return $this->hasMany('App\Models\Site\SiteSubcategory', 'category_id', 'id');
    }

    public function pages()
    {
        //return $this->hasMany('App\Models\ModelName', 'foreign_key', 'local_key');
        return $this->hasMany('App\Models\Site\SitePage', 'category_id')->where('active', '1');
    }

    public function productPages()
    {
        //return $this->hasMany('App\Models\ModelName', 'foreign_key', 'local_key');
        return $this->hasMany('App\Models\Site\SitePage', 'category_id')->where('active', '1')->where('product_page', '1');
    }
}

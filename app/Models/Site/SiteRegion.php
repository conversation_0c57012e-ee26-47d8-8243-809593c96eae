<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;

class SiteRegion extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'site_regions';

    /**
     * Get the pages associated with a given re
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function pages()
    {

        return $this->belongsToMany('App\Models\Site\SitePage', 'site_page_site_region', 'region_id', 'page_id')->where('active', 1);

    }

    /**
     * Get the pages associated with a given re
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function scopePages()
    {

        return $this->belongsToMany('App\Models\Site\SitePage', 'site_page_site_region', 'region_id', 'page_id')->where('active', 1);

    }
}

<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SitePage extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'site_pages';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['category_id', 'subcategory_id', 'title', 'sections', 'content', 'hero_image', 'thumbnail_image', 'url', 'redirect', 'category_name', 'product_page', 'new_product', 'featured', 'featured_sort_order', 'active', 'sort_order', 'published_on'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    public function category()
    {
        // return $this->belongsTo('App\Models\ModelName', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\Site\SiteCategory', 'category_id', 'id');
    }

    public function subcategory()
    {
        // return $this->belongsTo('App\Models\ModelName', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\Site\SiteSubcategory', 'subcategory_id', 'id');
    }

    /**
     * Get the region associated with a given page
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function regions()
    {
        return $this->belongsToMany('App\Models\Site\SiteRegion', 'site_page_site_region', 'page_id', 'region_id');
    }

    /**
     * Get the literature associated with a given page
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function literature()
    {
        return $this->belongsToMany('App\Models\Site\SiteLiterature', 'site_literature_site_page', 'page_id', 'literature_id')->join('site_literature_types', 'site_literature.literature_type_id', '=', 'site_literature_types.id')->where('active', '1')->orderBy('site_literature_types.sort_order');
    }

    /**
     * Get the patient guides associated with a given page
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function patientGuides()
    {
        return $this->belongsToMany('App\Models\Site\SiteLiterature', 'site_literature_site_page', 'page_id', 'literature_id')->where('literature_type_id', 1);
    }

    /**
     * Get the videos associated with a given page
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function videos()
    {
        return $this->belongsToMany('App\Models\Site\SiteVideo', 'site_pages_site_videos', 'page_id', 'video_id')->where('active', 1);
    }

    /**
     * Get the content sections associated with a given page
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function siteSections()
    {
        return $this->belongsToMany('App\Models\Site\SiteSection', 'site_page_site_section', 'page_id', 'section_id');
    }
}

<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;

class Patent extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'patents';

    /**
     * Query to only include active/unexpired patents.
     * Overrides Illuminate\Database\Eloquent\Model 's query()
     *
     * @return mixed
     */
    public function newQuery($excludeDeleted = true)
    {

        return parent::newQuery($excludeDeleted)->where('expiration_date', '>=', \Carbon\Carbon::now())->orWhere('expiration_date', '=', null);
    }
}

<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;

class SiteVideoFile extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'site_video_files';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['video_id', 'filename', 'fileformat', 'filesize', 'width', 'height', 'language', 'active', 'sort_order'];

    /**
     * Get the video associated with the files
     *
     * @return \illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    //
    public function video()
    {

        // return $this->belongsTo('App\User', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\Site\SiteVideo', 'video_id', 'id');

    }
}

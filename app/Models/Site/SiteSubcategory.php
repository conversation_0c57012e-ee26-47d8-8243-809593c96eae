<?php

namespace App\Models\Site;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SiteSubcategory extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'site_subcategories';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['category_id', 'title', 'active', 'sort_order'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    public function category()
    {
        // return $this->belongsTo('App\Models\ModelName', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\Site\SiteCategory', 'category_id', 'id');
    }

    public function pages()
    {
        //return $this->hasMany('App\Models\ModelName', 'foreign_key', 'local_key');
        return $this->hasMany('App\Models\Site\SitePage', 'subcategory_id', 'id');
    }

    public function productPages()
    {
        //return $this->hasMany('App\Models\ModelName', 'foreign_key', 'local_key');
        return $this->hasMany('App\Models\Site\SitePage', 'category_id')->where('active', '1')->where('product_page', '1');
    }
}

<?php

namespace App\Models\GuidanceTechnologies;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class SystemModels extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'gt_system_models';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uuid',
        'system_name',
        'system_model',
        'manufacturer_regex',
        'manufacture_year_regex',
        'manufacture_month_regex',
        'system_model_regex',
        'lot_number_regex',
        'international',
        'active',
    ];

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = Str::uuid();
            $model->active = 1;
        });
    }

    ////////////////////////////////////////////////////
    /// Mutators

    public function setSystemModelAttribute($value)
    {
        $this->attributes['system_model'] = strtoupper($value);
    }

    public function setManufacturerRegexAttribute($value)
    {
        $this->attributes['manufacturer_regex'] = strtoupper($value);
    }

    public function setManufactureYearRegexAttribute($value)
    {
        $this->attributes['manufacture_year_regex'] = strtoupper($value);
    }

    public function setManufactureMonthRegexAttribute($value)
    {
        $this->attributes['manufacture_month_regex'] = strtoupper($value);
    }

    public function setSystemModelRegexAttribute($value)
    {
        $this->attributes['system_model_regex'] = strtoupper($value);
    }

    public function setLotNumberRegexAttribute($value)
    {
        $this->attributes['lot_number_regex'] = strtoupper($value);
    }
}

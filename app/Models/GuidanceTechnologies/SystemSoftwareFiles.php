<?php

namespace App\Models\GuidanceTechnologies;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class SystemSoftwareFiles extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'gt_system_software_files';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['uuid', 'system_software_id', 'name', 'filename', 'filesize', 'checksum', 'dev', 'staging', 'prod', 'active'];

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    public function system()
    {
        // return $this->belongsTo('App\Models\ModelName', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\GuidanceTechnologies\SystemSoftwareVersions', 'system_software_id', 'id');
    }

    public function version()
    {
        // return $this->belongsTo('App\Models\ModelName', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\GuidanceTechnologies\SystemSoftwareVersions', 'version_id', 'id');
    }
}

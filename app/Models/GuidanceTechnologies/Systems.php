<?php

namespace App\Models\GuidanceTechnologies;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Systems extends Model
{
    use SoftDeletes;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'gt_systems';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['uuid', 'system_model', 'system_name', 'dev', 'staging', 'prod', 'internal', 'active'];

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    public function scopeVersions()
    {
        //return $this->hasMany('App\Models\ModelName', 'foreign_key', 'local_key');
        return $this->hasMany('App\Models\GuidanceTechnologies\SystemSoftwareVersions', 'system_id', 'id')->orderBy('version', 'desc');
    }

    ////////////////////////////////////////////////////
    /// Mutators

    /**
     * Uppercase the first character of each word in the title
     *
     * @param  string  $value
     */
    public function setSystemIdAttribute($value)
    {
        $this->attributes['system_model'] = strtoupper($value);
    }
}

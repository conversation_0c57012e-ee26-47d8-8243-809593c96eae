<?php

namespace App\Models\GuidanceTechnologies;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class SystemUpdateHistory extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'gt_systems_update_history';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'uuid',
        'serial_number',
        'system_name',
        'system_model',
        'current_version',
        'previous_version',
        'success',
        'log_file',
        'update_duration',
        'update_datetime',
        'dev',
        'staging',
        'prod',
    ];

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }

    // Mutators
    public function setSerialNumberAttribute($value)
    {
        $this->attributes['serial_number'] = strtoupper($value);
    }

    public function setSystemIdAttribute($value)
    {
        $this->attributes['system_model'] = strtoupper($value);
    }
}

<?php

namespace App\Models\GuidanceTechnologies;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class SystemUpgradeEligibility extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'gt_system_upgrade_eligibility';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['uuid', 'system_model', 'serial_number', 'internal', 'international', 'active'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = Str::uuid();
        });
    }
}

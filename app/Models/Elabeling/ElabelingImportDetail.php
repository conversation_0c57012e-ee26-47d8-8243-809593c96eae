<?php

namespace App\Models\Elabeling;

use Illuminate\Database\Eloquent\Model;

class ElabelingImportDetail extends Model
{
    /**
     * The dates protected and treated as Carbon instances
     *
     * @var array
     */
    protected $dates = ['deleted_at'];

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'eifu_import';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['', '', '', '', '', '', '', '', '', '', '', '', '', '', ''];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    public function map()
    {
        //return $this->hasMany('App\Models\ModelName', 'foreign_key', 'local_key');
        return $this->hasMany('App\Models\Elabeling\ElabelingImportMap', 'import_id', 'id');
    }
}

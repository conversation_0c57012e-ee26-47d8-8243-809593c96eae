<?php

namespace App\Models\Elabeling;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ElabelingLibraryFile extends Model
{
    use SoftDeletes;

    /**
     * The dates protected and treated as Carbon instances
     *
     * @var array
     */
    protected $dates = ['deleted_at'];

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'eifu_library_files';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['parent_id', 'filename', 'fileformat', 'filesize', 'language', 'uploaded_by', 'sort_order', 'active'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];
}

<?php

namespace App\Models\Elabeling;

use Illuminate\Database\Eloquent\Model;

class ElabelingImportMap extends Model
{
    /**
     * The dates protected and treated as Carbon instances
     *
     * @var array
     */
    protected $dates = ['deleted_at'];

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'eifu_import_map';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['', '', '', '', '', '', '', '', '', '', '', '', '', '', ''];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];
}

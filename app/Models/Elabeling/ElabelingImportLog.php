<?php

namespace App\Models\Elabeling;

use Illuminate\Database\Eloquent\Model;

class ElabelingImportLog extends Model
{
    /**
     * The dates protected and treated as Carbon instances
     *
     * @var array
     */
    protected $dates = ['deleted_at'];

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'eifu_import_log';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['logged_filename', 'successful', 'errors', 'starting_sequence_number', 'ending_sequence_number'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];
}

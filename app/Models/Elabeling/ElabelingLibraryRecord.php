<?php

namespace App\Models\Elabeling;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ElabelingLibraryRecord extends Model
{
    use SoftDeletes;

    /**
     * The dates protected and treated as Carbon instances
     *
     * @var array
     */
    protected $dates = ['deleted_at'];

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'eifu_library';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['part_number', 'title', 'description', 'resource_type', 'description', 'replacement_part_number', 'replacement_notification_message', 'replacement_date', 'created_by', 'active'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    /**
     * Maps the relationship between Library Records and Instruction types
     *
     * @var string
     */
    public function instruction_types()
    {
        // return $this->belongsTo('App\Models\ModelName', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\Elabeling\ElabelingResourceTypes', 'resource_type', 'id');
    }
}

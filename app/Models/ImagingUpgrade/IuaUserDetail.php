<?php

namespace App\Models\ImagingUpgrade;

use Illuminate\Database\Eloquent\Model;

class IuaUserDetail extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'iua_user_details';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'title', 'organization', 'department', 'email', 'phone', 'ismanager', 'optin'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    /**
     * Get the serial numbers detail tied to the user detail
     *
     * @return \illuminate\Database\Eloquent\Relations\hasMany
     */
    public function serialNumbersDetail()
    {
        //return $this->hasMany('App\Models\ModelName', 'foreign_key', 'local_key');
        return $this->hasMany('App\Models\ImagingUpgrade\IuaUserSerialNumbersDetail', 'pid', 'id');
    }
}

<?php

namespace App\Models\ImagingUpgrade;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IuaSystemUpgradeCountryEligibilityMatrixUat extends Model
{
    use HasFactory;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'iua_system_upgrade_country_eligibility_matrices_uat';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'system_id',
        'system_name',
        'country_code',
        'country_name',
        'ultrasound',
        'sherlock',
        'shell',
        'dicom',
        'source_filename',
        'download_filename',
        'product_image',
        'installation_instructions',
        'created_at',
        'updated_at',
    ];

    /**
     * The dates protected and treated as Carbon instances
     *
     * @var array
     */
    protected $dates = ['created_at', 'updated_at'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];
}

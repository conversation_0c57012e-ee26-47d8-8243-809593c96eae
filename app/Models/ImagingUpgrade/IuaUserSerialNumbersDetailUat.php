<?php

namespace App\Models\ImagingUpgrade;

use Illuminate\Database\Eloquent\Model;

class IuaUserSerialNumbersDetailUat extends Model
{
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'iua_user_serial_numbers_details_uat';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['serial_number', 'system_name', 'ultrasound', 'sherlock', 'shell', 'dicom', 'contact', 'department', 'email', 'location'];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [];

    public function userDetail()
    {
        // return $this->belongsTo('App\Models\ModelName', 'local_key', 'parent_key');
        return $this->belongsTo('App\Models\ImagingUpgrade\IuaUserDetailUat', 'pid', 'id');
    }
}

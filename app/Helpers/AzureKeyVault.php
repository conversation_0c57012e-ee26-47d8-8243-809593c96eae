<?php

namespace App\Helpers;

use GuzzleHttp\Client;
// use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class AzureKeyVault
{
    private const CACHE_KEY = 'azure_keyvault_token';

    private string $tenantId;

    private string $clientId;

    private string $clientSecret;

    private string $vault;

    public function __construct($tenantId, $clientId, $clientSecret, $vault)
    {
        $this->tenantId = $tenantId;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->vault = $vault;
    }

    private function getAuthToken()
    {
        if (Cache::has(self::CACHE_KEY)) {
            return Cache::get(self::CACHE_KEY);
        }

        $client = new Client;

        $response = $client->post(
            "https://login.microsoftonline.com/{$this->tenantId}/oauth2/token", [
                'form_params' => [
                    'client_id' => $this->clientId,
                    'client_secret' => $this->clientSecret,
                    'resource' => 'https://vault.azure.net',
                    'grant_type' => 'client_credentials',
                ]]
        );

        if ($response->getStatusCode() !== 200) {
            throw new \Exception(
                json_decode((string) $response->getBody())->error_description,
                // $response->json()['error_description'],
                $response->getStatusCode()
            );
        }

        $response = json_decode((string) $response->getBody());
        $token = $response->access_token;
        $expiry = now()->addSeconds((int) $response->expires_in);

        Cache::put(self::CACHE_KEY, $token, $expiry);

        return $token;
    }

    private function vaultUrl(): string
    {
        return "https://{$this->vault}.vault.azure.net/";
    }

    public function secret(string $name, ?string $default = null): ?string
    {
        $client = new Client;

        $response = $client->get(
            $this->vaultUrl()."secrets/$name", [
                'headers' => [
                    'Accept' => 'application/json',
                    'Authorization' => 'Bearer '.$this->getAuthToken(),
                ],
                'query' => [
                    'api-version' => '7.1',
                ],
            ]
        );

        if ($response->getStatusCode() !== 200) {
            throw new \Exception(
                json_decode((string) $response->getBody())->error_description,
                $response->getStatusCode()
            );
        }

        $response = json_decode((string) $response->getBody());

        return $response->value;
    }
}

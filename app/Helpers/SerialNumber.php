<?php

namespace App\Helpers;

use App\Models\GuidanceTechnologies\SystemModels;
use App\Models\GuidanceTechnologies\SystemUpgradeEligibility;

// use App\Models\GuidanceTechnologies\InternationalSystemUpgradeEligibility;

class SerialNumber
{
    public $manufacturer;

    public $manufacture_year;

    public $manufacture_month;

    public $system_model;

    public $lot_number;

    public $internal_serial_number = false;

    public $international_model = false;

    /**
     * Split serial number into its schema
     *
     * @param  string  $serial_number
     * @param  array  $position_length
     * @return array $split_serial_number_array
     */
    public function split($serial_number)
    {
        $keys = [
            'manufacturer',
            'manufacture_year',
            'manufacture_month',
            'system_model',
            'lot_number',
        ];

        $position_length = [];
        $split_serial_number_array = [];

        switch (strlen($serial_number)) {
            case 8:
                $position_length = [2, 1, 1, 1, 3];
                break;

            case 9:
                $position_length = [2, 1, 1, 2, 3];
                break;

            default:
                return false;
                break;
        }

        $pos = 0;

        foreach ($position_length as &$value) {
            $split_serial_number_array[] = substr($serial_number, $pos, $value);
            $pos += $value;
        }

        // combine the keys with the values and then cast it to an object
        return (object) array_combine($keys, $split_serial_number_array);
    }

    /**
     * Verify serial number format
     *
     * @param  string  $serial_number
     * @return bool
     */
    public function verifyFormat($serial_number)
    {
        $serial_number = strtoupper($serial_number);
        $sn = $this->split($serial_number);

        if ($sn && is_object($sn)) {
            $this->manufacturer = $sn->manufacturer;
            $this->manufacture_year = $sn->manufacture_year;
            $this->manufacture_month = $sn->manufacture_month;
            $this->system_model = $sn->system_model;
            $this->lot_number = $sn->lot_number;

            $model = SystemModels::where('system_model', $sn->system_model)->first();

            if ($model) {

                // Allowed Character Sets:
                $manufacturer = (preg_match("/^($model->manufacturer_regex)+$/i", $sn->manufacturer));
                $manufacture_year = (preg_match("/^($model->manufacture_year_regex)+$/i", $sn->manufacture_year));
                $manufacture_month = (preg_match("/^($model->manufacture_month_regex)+$/i", $sn->manufacture_month));
                $system_model = (preg_match("/^($model->system_model_regex)+$/i", $sn->system_model));
                $lot_number = (preg_match("/^($model->lot_number_regex)+$/i", $sn->lot_number));

                $this->international_model = $model->international;
            } else {
                return false;
            }

            return ($manufacturer == true && $manufacture_month == true && $manufacture_year == true && $system_model == true && $lot_number == true) ? true : false; // True == Valid Serial number
        } else {
            return false;
        }
    }

    /**
     * Validate serial numbers and eligibility
     *
     * @param  string  $serial_number
     * @return bool
     */
    public function validate($serial_number)
    {
        $serial_number = strtoupper($serial_number);

        $verification = $this->verifyFormat($serial_number);

        if ($verification == true) {
            $this->internal_serial_number = $this->SystemUpgradeEligibility($this->system_model, $serial_number, true, null);
        }

        if ($verification == true && $this->international_model) {
            $international_model = $this->SystemUpgradeEligibility($this->system_model, $serial_number, null, $this->international_model);

            return ($verification == true && $international_model == true) ? true : false; // True == Valid Serial number
        }

        return ($verification == true) ? true : false; // True == Valid Serial number
    }

    /**
     * validate system upgrade eligibility
     * string $system_model
     *
     * @return bool
     */
    public function SystemUpgradeEligibility($system_model, $serial_number, $internal = null, $international = null)
    {
        if ($internal && $international) {
            return SystemUpgradeEligibility::where('system_model', $system_model)->where('serial_number', $serial_number)->where('internal', 1)->where('international', 1)->where('active', 1)->exists();
        } elseif ($internal) {
            return SystemUpgradeEligibility::where('system_model', $system_model)->where('serial_number', $serial_number)->where('internal', 1)->where('active', 1)->exists();
        } elseif ($international) {
            return SystemUpgradeEligibility::where('system_model', $system_model)->where('serial_number', $serial_number)->where('international', 1)->where('active', 1)->exists();
        }

        return SystemUpgradeEligibility::where('system_model', $system_model)->where('serial_number', $serial_number)->where('active', 1)->exists();
    }
}

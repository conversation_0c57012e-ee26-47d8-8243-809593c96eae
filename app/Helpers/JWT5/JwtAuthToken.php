<?php
/**
* JwtAuthToken
*
* wrapper to encode, decode and validate JWT tokens
* for use with <PERSON><PERSON>bucci\JWT version 5.0+
*
* @version	2.0.3
*
* <AUTHOR>
*/

namespace App\Helpers;

use Carbon\Carbon;
use DateTimeZone;
use Exception;
use <PERSON><PERSON>bu<PERSON>\Clock\SystemClock;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Configuration;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac\Sha256;
// use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac\Sha512;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Key\InMemory;
use Lcobucci\JWT\Validation\Constraint\IdentifiedBy;
use Lcobucci\JWT\Validation\Constraint\LooseValidAt;
use Lcobucci\JWT\Validation\Constraint\SignedWith;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\RequiredConstraintsViolated;
use Log;

class JwtAuthToken
{
    private Configuration $config;

    private $key;

    private $jti;

    private $issuer;

    private $expiration = '+1 hour';

    public $errors;

    public function __construct($key, $jti, $issuer)
    {
        $this->key = $key;
        $this->jti = $jti;
        $this->issuer = $issuer;

        $this->config = Configuration::forSymmetricSigner(
            // You may use any HMAC variations (256, 384, and 512)
            new Sha256,
            // replace the value below with a key of your own!
            InMemory::plainText($key)
        );
    }

    /**
     * Encode JWT Token
     *
     * @param  string  $expiration  ex: '+10 minutes', '+1 hour', '+1 week'
     */
    public function encode($payload = null, $expiration = null)
    {
        $expiration_time = $expiration ?? $this->expiration;

        $now = new \DateTimeImmutable;

        $token = $this->config->builder()
                      // Configures the issuer (iss claim)
            ->issuedBy($this->issuer)
                      // Configures the audience (aud claim)
            ->permittedFor($this->issuer)
                      // Configures the id (jti claim)
            ->identifiedBy($this->jti)
                      // Configures the time that the token was issue (iat claim)
            ->issuedAt($now)
                      // Configures the time that the token can be used (nbf claim)
            ->canOnlyBeUsedAfter($now)
                      // Configures the expiration time of the token (exp claim)
            ->expiresAt($now->modify($expiration_time))
                      // Configures a new claim, called "uid"
            ->withClaim('payload', $payload)
                      // Configures a new header, called "foo"
                      // ->withHeader('foo', 'bar')
                      // Builds a new token
            ->getToken($this->config->signer(), $this->config->signingKey());

        return $token->toString();
    }

    /**
     * Decode JWT Token
     *
     * new \Lcobucci\JWT\Validation\Constraint\IdentifiedBy($this->jti); // Verify that the jwt id matches
     * new \Lcobucci\JWT\Validation\Constraint\IssuedBy($this->issuer); // Verify that the issuer parameters match
     * new \Lcobucci\JWT\Validation\Constraint\PermittedFor($this->issuer); // Verify that the audience parameters match
     * new \Lcobucci\JWT\Validation\Constraint\RelatedTo($this->sub); verifies if the claim sub matches the expected value
     * new \Lcobucci\JWT\Validation\Constraint\SignedWith($this->config->signer(), $this->config->signingKey()); // Verify that the token has been signed with the expected signer and key
     * new \Lcobucci\JWT\Validation\Constraint\LooseValidAt($now); // Verification requires iat, nbf and exp (supports room for configuration)
     */
    public function decode($jwt_token)
    {
        if ($jwt_token) {
            try {
                $token = $this->config->parser()->parse($jwt_token); // Parses from a string
                // dd($token);
                // dd($token->headers()); // Retrieves the token header
                // dd($token->claims()); // Retrieves the token claims
                // dd($token->claims()->get('payload')); // Retrieves the token claims payload

                // All these constraints have to validate as true to pass
                $this->config->setValidationConstraints(
                    new LooseValidAt(new SystemClock(new DateTimeZone('UTC'))),
                    new IdentifiedBy($this->jti),
                    new SignedWith($this->config->signer(), $this->config->signingKey()),
                );

                $constraints = $this->config->validationConstraints();

                if ($this->validate($jwt_token)) {
                    $payload = $token->claims()->get('payload');

                    if ($payload) {
                        return $payload;
                    } else {
                        return null;
                    }
                } else {
                    return false;
                }
            } catch (Exception $e) {
                return false;
            }
        } else {
            return false;
        }

        return false;
    }

    /**
     * Validate JWT Token
     */
    public function validate($jwt_token)
    {
        if ($jwt_token) {
            try {
                $token = $this->config->parser()->parse($jwt_token); // Parses from a string

                // All these constraints have to validate as true to pass
                $this->config->setValidationConstraints(
                    new LooseValidAt(new SystemClock(new DateTimeZone('UTC'))),
                    new IdentifiedBy($this->jti),
                    new SignedWith($this->config->signer(), $this->config->signingKey()),
                );

                $constraints = $this->config->validationConstraints();

                try {
                    /* This method goes through every single constraint in the set,
                       groups all the violations, and throws an exception with the
                       grouped violations: */
                    $this->config->validator()->assert($token, ...$constraints);

                    return true;
                } catch (RequiredConstraintsViolated $e) {
                    $this->errors = str_replace(["\n"], [' '], $e->getMessage());

                    // Log JWT token
                    Log::error($jwt_token);
                    // Log the list of constraints violation exceptions:
                    Log::error('Timestamp: '.Carbon::now()->timezone('UTC')->timestamp.' - '.$this->errors);
                    // referrer
                    Log::error($_SERVER['HTTP_REFERER'] ?? null);

                    return false;
                }
            } catch (Exception $e) {
                return false;
            }
        } else {
            return false;
        }
    }
}

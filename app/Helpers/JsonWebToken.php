<?php
/**
* JsonWebToken
*
* wrapper to encode, decode and validate JWT tokens
* for use with L<PERSON>bucci\JWT version 4.0+
*
* @version	2.0
*
* <AUTHOR>
*/

namespace App\Helpers;

class JsonWebToken extends JwtAuthToken
{
    public function __construct()
    {
        $this->key = config('jwt.JWT_SIGNATURE_KEY');
        $this->jti = config('jwt.JWT_SIGNATURE_ISSUER');
        $this->issuer = config('app.url');

        parent::__construct($this->key, $this->jti, $this->issuer);
    }
}

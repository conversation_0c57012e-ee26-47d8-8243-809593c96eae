<?php

namespace App\Http\Middleware;

use Closure;

class VerifyRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int|string  $role
     * @return mixed
     */
    public function handle($request, Closure $next, $role)
    {
        if ($request->user()->hasRole($role)) {
            return $next($request);
        } else {
            flash('Sorry, it doesn\'t look like you have permission to do that.')->error();

            return redirect()->route('home');
        }
    }
}

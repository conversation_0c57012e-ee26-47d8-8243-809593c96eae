<?php

namespace App\Http\Middleware;

use Closure;

class VerifyPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int|string  $permission
     * @return mixed
     */
    public function handle($request, Closure $next, $permission)
    {
        if ($request->user()->hasPermissionTo($permission)) {
            return $next($request);
        } else {
            flash('Sorry, it doesn\'t look like you have permission to do that.')->error();

            return redirect()->route('home');
        }
    }
}

<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        'https://www.bardaccess.com/products/imaging/upgrade/detail/create',
        'https://www.bardaccess.com/products/imaging/upgrade/uat/detail/create',
    ];
}

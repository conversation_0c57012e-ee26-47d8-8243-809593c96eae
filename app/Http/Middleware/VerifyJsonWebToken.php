<?php

namespace App\Http\Middleware;

use App\Helpers\JsonWebToken;
use Closure;

class VerifyJsonWebToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        $jwt = new JsonWebToken;

        if ($request->access_token && $jwt->validate($request->access_token)) {
            return $next($request);
        } else {

            return response()->json([
                'api' => 'jwt access token verification',
                'is_valid' => false,
            ], $status = 401);
        }

    }
}

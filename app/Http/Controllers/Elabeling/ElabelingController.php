<?php

namespace App\Http\Controllers\Elabeling;

use App\Http\Controllers\Controller;
use App\Http\Requests\ElabelingLanguageRequest;
use App\Http\Requests\ElabelingRequest;
use App\Models\Language;
use App\Services\ElabelingService;
use App\Services\SetLanguageService;

class ElabelingController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | ELabeling Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the ELabeling pages, including the searchable
    | database and the posts for database requests.
    |
    */

    protected $language_setter;

    protected $searched_term;

    protected $search_results;

    protected $product_info;

    protected $product_description;

    protected $enabled_languages;

    protected $selected_language;

    protected $input;

    /**
     * Create a new ELabeling controller instance.
     *
     * @return void
     */
    public function __construct(SetLanguageService $language_setter)
    {
        //ini_set('display_errors', 1);

        // Track Sessions
        session_start();

        // Get the available languages in the website from the database
        $this->enabled_languages = Language::where('active', '1')->orderBy('language_name', 'asc')->get()->mapWithKeys(function ($item) {
            if ($item->iso_639_1 == 'en') {
                return [$item->iso_639_1 => $item->language_name];
            } else {
                return [$item->iso_639_1 => $item->language_name.'  ('.$item->native_name.')'];
            }
        });

        // Save the language setter to a variable so it can be called later
        $this->language_setter = $language_setter;

        // Throw the language to a service provider which handles the logic for determining the language
        $this->selected_language = $this->language_setter->setLanguage();
    }

    /**
     * The main ELabeling page -> contains the place where searches are made and Loads Results if shown
     *
     * @return view
     */
    public function index()
    {

        // Send them to their view
        if (view()->exists('site.elabeling.index')) {
            return view('site.elabeling.index', ['enabled_languages' => $this->enabled_languages, 'selected_language' => $this->selected_language, 'search_results' => $this->search_results, 'product_info' => $this->product_info, 'product_description' => $this->product_description, 'searched_term' => $this->searched_term, 'confirmation' => false]);
        } else {
            return view('errors.404');
        }
    }

    /**
     * The main ELabeling page -> contains the place where searches are made and Loads Results if shown
     *
     * @return view
     */
    public function search(ElabelingRequest $request, ElabelingService $ElabelingService)
    {
        // Get the input
        $this->input = $request->all();

        // Get the old input if there is any
        $oldifu = \Request::old('ifu_search');

        // Get the input, set it to the searched term
        if (isset($oldifu) && ($oldifu != '')) {
            $this->searched_term = $oldifu;
        } elseif (isset($this->input['ifu_search']) && ($this->input['ifu_search'] != '')) {
            $this->searched_term = $this->input['ifu_search'];
        } else {
            $this->searched_term = '';
        }

        // If there is input, perform the search
        if (isset($this->searched_term) && ($this->searched_term != '')) {
            $this->lotNumberSearch($this->searched_term);
            // else if there is no info... Send to the view with no default info
        } else {
            $this->search_results = $this->product_info = $this->input = $this->product_description = '';
        }

        if ($this->search_results) {
            $result_items = $ElabelingService->getSortedResourceItems($this->search_results, $this->selected_language, false);
        }

        // Send them to their view
        if (view()->exists('site.elabeling.index')) {
            return view('site.elabeling.index', ['enabled_languages' => $this->enabled_languages, 'selected_language' => $this->selected_language, 'search_results' => $this->search_results, 'result_items' => $result_items, 'product_info' => $this->product_info, 'product_description' => $this->product_description, 'searched_term' => $this->searched_term, 'elabeling_service' => $ElabelingService, 'confirmation' => false]);
        } else {
            return view('errors.404');
        }
    }

    /**
     * The main ELabeling page -> contains the place where searches are made and Loads Results if shown
     *
     * @return view
     */
    public function getSearch(ElabelingService $ElabelingService, $lot_number, $language = null, $type = null)
    {
        // Get the url parameter, set it to the searched term
        if (isset($lot_number) && ($lot_number != '')) {
            $this->searched_term = $lot_number;
        } else {
            $this->searched_term = '';
        }

        // Set language if set
        if (isset($language) && ($language != '')) {
            $this->selected_language = $this->language_setter->setLanguage($language);
        }

        // If there is input, perform the search
        if (isset($this->searched_term) && ($this->searched_term != '')) {
            $this->lotNumberSearch($this->searched_term);

            // else if there is no info... Send to the view with no default info
        } else {
            $this->search_results = $this->product_info = $this->input = $this->product_description = '';
        }

        if ($this->search_results) {
            $result_items = $ElabelingService->getSortedResourceItems($this->search_results, $this->selected_language, false);
        }

        if ($type == 'json') {
            if (is_array((array) $result_items)) {
                foreach ($result_items as $key => $object) {
                    $object->filename = url('assets/elabeling/pdfs/'.$object->filename);
                    $object->type = $object->name;

                    unset($object->replacement_part_number);
                    unset($object->replacement_notification_message);
                    unset($object->replacement_rationale_notes);
                    unset($object->replacement_date);
                    unset($object->order);
                    unset($object->name);
                }
            }

            $product_info = [];

            if (is_object($this->product_info)) {
                $product_info = $this->product_info;
                if ($this->product_description) {
                    $product_info->product_description = $this->product_description->description;
                }
                unset($product_info->id);
                unset($product_info->ref);
                unset($product_info->resource_type);
                unset($product_info->sequence_number);
                unset($product_info->deleted_at);
                unset($product_info->created_at);
                unset($product_info->updated_at);
            }

            return \Response::json(['product_info' => $product_info, 'result_items' => $result_items]);
        } elseif (view()->exists('site.elabeling.index')) {
            // Send them to their view
            return view('site.elabeling.index', ['enabled_languages' => $this->enabled_languages, 'selected_language' => $this->selected_language, 'search_results' => $this->search_results, 'result_items' => $result_items, 'product_info' => $this->product_info, 'product_description' => $this->product_description, 'searched_term' => $this->searched_term, 'elabeling_service' => $ElabelingService, 'confirmation' => false]);
        } else {
            return view('errors.404');
        }
    }

    /**
     * Lot Number search
     *
     * @return view
     */
    public function lotNumberSearch($lot_number)
    {

        // If there is input, perform the search
        if (isset($lot_number) && ($lot_number != '')) {
            // Remove "CE" Mark at the end for CE marked product codes
            $lot_number = $lot_number;
            // $lot_number = rtrim($this->searched_term, 'CE');

            // Remove "R" at the end for refurbished product codes
            // $lot_number = rtrim($lot_number, 'R');

            // GET THE SEARCH RESULTS FOR JDE
            // $jde_results = \DB::table('eifu_lot_numbers_jde')->where('lot_number', $lot_number)->orWhere('product_code', $lot_number);
            $jde_results = \DB::table('eifu_lot_numbers_jde')->where('lot_number', $lot_number)->groupBy('part_number');

            // GET THE SEARCH RESULTS FOR SOFTWARE RELEASES
            $software_results = \DB::table('eifu_lot_numbers_software')->where('lot_number', $lot_number)->groupBy('part_number');

            // UNION THEM WITH THE RESULTS FROM MFGPRO FOR ALL RESULTS
            // $this->search_results = \DB::table('eifu_lot_numbers_mfgpro')->where('lot_number', $lot_number)->orWhere('product_code', $lot_number)->union($jde_results)->orderby('manufacture_date', 'desc')->groupBy('part_number')->get();
            $this->search_results = \DB::table('eifu_lot_numbers_mfgpro')->where('lot_number', $lot_number)->union($jde_results)->union($software_results)->orderby('manufacture_date', 'desc')->groupBy('part_number')->get();

            // UNION THEM WITH THE RESULTS FROM MFGPRO FOR SINGLE RESULT TO USE AS SOURCE FOR PRODUCT DESCRIPTIONS
            // $this->product_info = \DB::table('eifu_lot_numbers_mfgpro')->where('lot_number', $lot_number)->orWhere('product_code', $lot_number)->union($jde_results)->orderby('manufacture_date', 'desc')->groupBy('part_number')->first();
            $this->product_info = $this->search_results->first();

            if ($this->product_info) {
                // Get the product description
                $this->product_description = \DB::table('catalog_product_codes')->where('product_code', $this->product_info->product_code)->where('active', 1)->first();
            } else {
                $this->product_description = '';
            }

            // else if there is no info... Send to the view with no default info
        } else {
            $this->search_results = $this->product_info = $this->input = $this->product_description = '';
        }
    }

    /**
     * The main ELabeling page -> contains the place where searches are made and Loads Results if shown
     *
     * @return view
     */
    public function confirmation()
    {

        // Send them to their view
        if (view()->exists('site.elabeling.index')) {
            return view('site.elabeling.index', ['enabled_languages' => $this->enabled_languages, 'selected_language' => $this->selected_language, 'search_results' => $this->search_results, 'product_info' => $this->product_info, 'product_description' => $this->product_description, 'searched_term' => $this->searched_term, 'confirmation' => true]);
        } else {
            return view('errors.404');
        }
    }

    /**
     * The post from any Elabeling page, sets the language and returns back to the page
     *
     * @return void
     */
    public function changeLanguage(ElabelingLanguageRequest $request)
    {

        // Throw the language to a service provider which handles the logic for determining the language
        $selected_language = $this->language_setter->setLanguage($request->language_selection);

        // Send them back to their view
        return redirect()->back()->withInput()->with(['enabled_languages' => $this->enabled_languages, 'selected_language' => $this->selected_language, 'search_results' => $this->search_results, 'product_info' => $this->product_info, 'product_description' => $this->product_description, 'searched_term' => $this->searched_term]);
    }
}

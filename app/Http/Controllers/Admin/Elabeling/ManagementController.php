<?php

namespace App\Http\Controllers\Admin\Elabeling;

use App\Http\Controllers\Controller;
use App\Models\Elabeling\ElabelingEmail;
use App\Models\Elabeling\ElabelingLibraryFile;
use App\Models\Elabeling\ElabelingLibraryRecord;
use App\Models\Elabeling\ElabelingResourceTypes;
use App\Models\Language;
use App\Services\ElabelingService;
use App\Services\UploadFileService;
use Auth;
use File;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Storage;
use View;

class ManagementController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Admin ELabeling Management Controller
    |--------------------------------------------------------------------------
    */

    // protected $upload_file_service;
    protected $target_dir;

    protected $temp_dir;

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->target_dir = storage_path().'/app/public/elabeling/pdfs';
        $this->temp_dir = storage_path().'/app/uploads/elabeling/temp';
        $this->user = Auth::user();
    }

    /**
     * Show the Elabeling management dashboard to the logged in user.
     *
     * @return View
     */
    public function index(ElabelingService $elabeling_service)
    {
        $user = $this->user;

        // Load the part numbers
        $part_numbers = ElabelingLibraryRecord::withTrashed()->orderBy('part_number')->with('instruction_types')->get();

        // Send them to their view
        return view('admin.elabeling.index', compact('part_numbers', 'user', 'elabeling_service'));
    }

    /**
     * Show the Elabeling management dashboard to the logged in user.
     *
     * @return View
     */
    public function emails()
    {
        $user = $this->user;

        // Load the part numbers
        // $email_addresses = ElabelingEmail::find('1');
        $email_addresses = '<EMAIL>';

        // Send them to their view
        return view('admin.elabeling.emails', compact('email_addresses', 'user'));
    }

    /**
     * Show the Elabeling management dashboard to the logged in user.
     *
     * @return View
     */
    public function emailsUpdate()
    {

        // Get the input
        $input = \Request::all();
        $user = $this->user;

        $email_addresses = $input['email'];

        \Session::flash('message', 'Your email settings were updated.');

        // Send them to their view
        return view('admin.elabeling.emails', compact('email_addresses', 'user'));
    }

    /**
     * Show the form to add a new document listing.
     *
     * @return View
     */
    public function create()
    {

        // Get the Resource Types
        $resource_types = ElabelingResourceTypes::orderBy('order')->pluck('name', 'id')->all();

        // Send them to their view
        return view('admin.elabeling.create', compact('resource_types'));
    }

    /**
     * Save the record to the database.
     *
     * @return Redirect
     */
    public function store(Request $request, ElabelingLibraryRecord $ElabelingLibraryRecord)
    {
        // Get the input
        $input = \Request::all();

        // ltrim the input to remove the "BAW" or "PK" numbers
        $trimmedPartNumber = ltrim($input['part_number'], 'BAWPK');

        $record = $ElabelingLibraryRecord->where('part_number', $trimmedPartNumber)->exists();

        if (! $record) {
            // Save the info to the DB
            $new_record = $ElabelingLibraryRecord->create([
                'part_number' => $trimmedPartNumber,
                'title' => $input['title'],
                'resource_type' => $input['resource_type'],
                'created_by' => $request->user()->id,
            ]);

            // Send a flash message
            \Session::flash('message', 'A new record was created for '.$new_record->part_number.'');

            // Send them to their view -> in this case, the upload file page view
            return redirect()->route('elabeling.record.upload', [$new_record->id]);
        } else {
            // Send a flash message
            \Session::flash('message', 'Part Number: '.$request->part_number.' already exists.');

            // Send them to their view -> in this case, the upload file page view
            return redirect()->back();
        }
    }

    /**
     * Show upload page.
     *
     * @return View
     */
    public function upload($id, ElabelingService $elabeling_service)
    {
        // Get the record
        $eifu_record = ElabelingLibraryRecord::withTrashed()->find($id);
        $missing_languages = $elabeling_service->listMissingLanguagesAsList($eifu_record->id);

        // Get the files
        $files = ElabelingLibraryFile::where('parent_id', $id)->get();

        // Send them to their view
        return view('admin.elabeling.upload', compact('eifu_record', 'files', 'missing_languages', 'elabeling_service'));
    }

    /**
     * Save the file to your site's storage and database - each file is sent in a separate request!
     *
     * @return View
     */
    public function storeUpload(Request $request)
    {

        // Okay - let's grab all the form input and put it in array, which is basically the name, size and token
        //$request = \Request::all();

        // Let's grab the Language ISO codes which are enabled
        $enabled_languages = Language::pluck('iso_639_1')->all();

        // Let's separate out the files into their own array
        $files = $_FILES;

        // Send the request to a Upload file Service Provider
        $upload = new UploadFileService($request, $files, $this->target_dir, $this->temp_dir, ['pdf']);

        // Part of preflight check

        // $this->error[] and $this->message[] available to us through PreFlightCheck() in UploadFileService
        // First, check that the chunk_file_extension is a PDF => $upload->chunk_file_extension; gives "pdf" (as does $upload->source_filename_extension)
        if ($upload->chunk_file_extension !== 'pdf') {
            $upload->error[] = 'Sorry, but '.$upload->source_filename.' must be a pdf. Please check that it is formatted according to SOP guidelines and try again.';
        } else {

            // CONTINUE WITH OTHER CHECKS

            // Second, check that the soure_filename is formatted correctly begins with the same numbers as the parent_id => $upload->source_filename; gives "1234_xx.pdf" (as does $upload->source_filename_clean)
            $filename_parts = explode('_', pathinfo($upload->source_filename, PATHINFO_FILENAME));

            if (count($filename_parts) !== 2) {
                $upload->error[] = 'Sorry, but '.$upload->source_filename.' is not named correctly. Please check that it is named according to SOP guidelines and try again.';
            } else {

                // CONTINUE WITH OTHER CHECKS

                // check first part of the file name to make sure it matches tha parent artwork part number
                if ($filename_parts[0] !== $request['part_number']) {
                    $upload->error[] = 'Sorry, the filename prefix ('.$filename_parts[0].') of file '.$upload->source_filename.' <strong>must</strong> match the parent Artwork part number ('.$request['part_number'].'). The file naming convention should be like '.$request['part_number'].'_en.pdf. Please see the SOP guidelines for more information.';
                }

                // Finally, check that the language code is in the approved languages => $upload->source_filename; gives "1234_xx.pdf" (as does $upload->source_filename_clean)
                if (! in_array($filename_parts[1], $enabled_languages)) {
                    $upload->error[] = 'Sorry, but the language code for file '.$upload->source_filename.' either does not exist or does not match the list of active languages. Please check that all of the active languages are correct and that the file is named correctly.';
                }
            } // END OF SECOND CHECK
        } // END OF FIRST CHECK

        if (! empty($upload->error)) {
            return \Response::json(['error' => $upload->error]);
        } elseif (! empty($upload->message)) {
            return \Response::json(['message' => $upload->message]);
        }

        // End Preflight Check

        // Save the info to the DB
        $record = ElabelingLibraryFile::where('filename', $upload->source_filename)->first();

        if (! $record) {
            try {
                $new_record = ElabelingLibraryFile::create([
                    'parent_id' => $request['parent_id'],
                    'filename' => $upload->source_filename,
                    'fileformat' => $upload->source_filename_extension,
                    'filesize' => $upload->source_filesize,
                    'language' => $filename_parts[1],
                    'uploaded_by' => $request->user()->id,
                ]);
            } catch (Exception $e) {
                $upload->error[] = 'Sorry, but there was an error creating the database record for this file.';
            } // end try/catch
        } // end if record

        // Upload checks passed, let the file through
        $result = $upload->uploadFile();

        // Now we'll return the AJAX response in JSON format back to the browser
        if (is_array($result)) {
            return \Response::json(['output' => 'Your file was uploaded, playa!']);
        }
    }

    /**
     * Show the record.
     *
     * @return Redirect
     */
    public function getFiles($id, ElabelingService $elabeling_service)
    {
        $token = csrf_token();

        // Get the record and the corresponding files
        $eifu_record = ElabelingLibraryRecord::find($id);
        $missing_languages = $elabeling_service->listMissingLanguagesAsList($eifu_record->id);

        $files = ElabelingLibraryFile::where('parent_id', $id)->get();

        // Return it back to them
        return \Response::json(['output' => view('admin.elabeling.includes.uploaded-files-list', compact('eifu_record', 'files', 'missing_languages', 'elabeling_service'))->render()]);
    }

    /**
     * Show the edit form for a given document.
     *
     * @return View
     */
    public function edit($part_number)
    {
        // Get the Resource Types
        $resource_types = ElabelingResourceTypes::orderBy('order')->pluck('name', 'id')->all();

        // Get the Record
        $eifu_record = ElabelingLibraryRecord::withTrashed()->find($part_number);

        // Send them to their view
        return view('admin.elabeling.edit', compact('resource_types', 'eifu_record'));
    }

    /**
     * Save the udpdated file to the Site's storage.
     *
     * @return View
     */
    public function update(Request $request, ElabelingService $elabeling_service)
    {

        // Save the info to the DB
        $eifu_record = $record = ElabelingLibraryRecord::withTrashed()->find($request->id);
        $record->part_number = $request->part_number;
        $record->title = $request->title;
        $record->resource_type = $request->resource_type;
        $record->save();

        $files = ElabelingLibraryFile::where('parent_id', $record->id)->get();

        // Send a flash message
        \Session::flash('message', 'Your record was updated!');

        // Send them to their view -> in this case, the upload file page view
        return view('admin.elabeling.upload', compact('eifu_record', 'files', 'elabeling_service'));
    }

    /**
     * Change the status of a Record.
     *
     * @return View
     */
    public function changeStatus($part_number)
    {
        try {
            // Save the info to the DB
            $record = ElabelingLibraryRecord::withTrashed()->find($part_number);
            if ($record->active == 1) {
                $record->active = 0;
                $record->save();
            } else {
                $record->active = 1;
                $record->save();
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            //			return \Response::json(['status' => 'fail']);
        }

        return \Response::json(['status' => 'success', 'record_id' => $record->id, 'record_title' => $record->title]);
    }

    /**
     * Delete the record permanately.
     *
     * @return View
     */
    public function forceDelete($id)
    {
        $file_count = 0;
        $file_delete_count = 0;

        try {
            // Save the info to the DB
            $record = ElabelingLibraryRecord::withTrashed()->find($id);
            $title = $record->title;

            if ($record) {
                $files = ElabelingLibraryFile::where('parent_id', $id)->get();
                if ($files) {
                    $file_count = count($files);

                    foreach ($files as $file) {
                        $filepath = $this->target_dir.'/'.$file->filename;
                        if ($this->deleteFile($filepath) == true) {
                            $file->forcedelete();
                            $file_delete_count++;
                        }
                    }
                }

                if ($file_count == $file_delete_count) {
                    $record->forceDelete();
                } else {
                    return \Response::json(['status' => 'fail', 'record_title' => $title, 'message' => 'There were some files missing when trying to delete this record.']);
                }
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $id, 'record_status' => $title, 'record_title' => $title]);
    }

    /*
    |--------------------------------------------------------------------------
    | Files
    |--------------------------------------------------------------------------
    */

    /**
     * Change the status of a Record.
     *
     * @return View
     */
    public function fileChangeStatus($id)
    {
        try {
            // Save the info to the DB
            $record = ElabelingLibraryFile::withTrashed()->find($id);
            if ($record->active == 1) {
                $record->active = 0;
                $record->save();
            } else {
                $record->active = 1;
                $record->save();
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            //			return \Response::json(['status' => 'fail']);
        }

        return \Response::json(['status' => 'success', 'record_id' => $id, 'record_title' => $record->filename]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroyFile($id)
    {
        try {

            // Save the info to the DB
            $record = ElabelingLibraryFile::findOrFail($id);
            $filename = $record->filename;
            $file = $this->target_dir.'/'.$record->filename;

            if (file_exists($file)) {
                if (unlink($file)) {
                    $record->forceDelete();

                    // Send them to their view -> in this case, the upload file page view
                    return \Response::json(['status' => 'success', 'record_id' => $id, 'record_title' => $filename]);
                }
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function deleteFile($filepath)
    {
        if (file_exists($filepath)) {
            if (unlink($filepath)) {
                return true;
            }
        }
    }

    /**
     * The main ELabeling page -> contains the place where searches are made and Loads Results if shown
     *
     * @return view
     */
    public function partNumber()
    {
        return view('site.elabeling.part-number.index');
    }

    /**
     * The main ELabeling page -> contains the place where searches are made and Loads Results if shown
     *
     * @return view
     */
    public function partNumberSearch(Request $request, $part_number = '')
    {
        $request->validate([
            'part_number' => 'required|string|max:255',
        ]);

        $part_number = isset($request->part_number) ? $request->part_number : $part_number;
        $part_number = str_replace(['PK', 'pk', 'BAW', 'baw', 'AW', 'aw'], ['', '', '', '', '', ''], $part_number);

        $result_items = ElabelingLibraryRecord::join('eifu_library_files', 'eifu_library_files.parent_id', '=', 'eifu_library.id')
            ->join('eifu_resource_types', 'eifu_library.resource_type', '=', 'eifu_resource_types.id')
            ->select('eifu_library.part_number', 'eifu_library.title', 'eifu_library.replacement_part_number', 'eifu_library.replacement_notification_message', 'eifu_library.replacement_rationale_notes', 'eifu_library.replacement_date', 'eifu_library_files.filename', 'eifu_library_files.filesize', 'eifu_library_files.language', 'eifu_resource_types.name', 'eifu_resource_types.order')
            ->where('eifu_library.part_number', $part_number)
            ->where('eifu_library.active', 1)
            ->where('eifu_library_files.active', 1)
            ->orderBy('eifu_library.part_number', 'asc')
            ->get();

        return view('site.elabeling.part-number.index', compact('result_items', 'part_number'));
    }
}

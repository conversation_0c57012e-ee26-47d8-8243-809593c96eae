<?php

namespace App\Http\Controllers\Admin\Elabeling;

use App\Http\Controllers\Controller;
use App\Models\Language;

class LanguageController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Admin Language Management Controller
    |--------------------------------------------------------------------------
    */

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        //$this->middleware('auth');
    }

    /**
     * Show the language management dashboard to the logged in user.
     *
     * @return View
     */
    public function index()
    {
        // Load the part numbers
        $all_languages = Language::get();

        // Load the part numbers
        $enabled_languages = Language::where('active', 1)->get();

        // Load the part numbers
        $disabled_languages = Language::where('active', 0)->get();

        // Send them to their view
        return view('admin.elabeling.languages', compact('enabled_languages', 'disabled_languages', 'all_languages'));
    }

    /**
     * Change the status of a language record.
     *
     * @return View
     */
    public function update($id)
    {
        try {
            // Save the info to the DB
            $record = Language::find($id);
            $language_name = $record->language_name;
            $iso = $record->iso_639_1;
            if ($record->active == 1) {
                $record->active = 0;
                $status = 'inactive';
            } else {
                $record->active = 1;
                $status = 'active';
            }
            $record->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            // return \Response::json(['status' => 'fail']);
            return redirect()->back();
        }

        // Send them to their view -> in this case, the upload file page view
        // return \Response::json(['status' => 'success', 'language_id' => $id, 'active' => $status, 'language_name' => $language_name, 'language_iso_639_1' => $iso]);
        return redirect()->back();
    }
}

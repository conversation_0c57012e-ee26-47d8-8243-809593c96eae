<?php

namespace App\Http\Controllers\Admin\Elabeling;

use App\Http\Controllers\Controller;
use App\Jobs\ImportElabelingData;
use Illuminate\Http\Request;
use Log;

class ImportController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function runImport(ImportElabelingData $importData)
    {
        Log::info('The Elabeling import job has begun to fire.');

        // call the indexing off the job
        $runJob = $importData->handle();
        if ($runJob) {
            Log::info('The Elabeling import job ran well.');
        } else {
            Log::info('The Elabeling import job did not run well.');
        }

        return $runJob;
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}

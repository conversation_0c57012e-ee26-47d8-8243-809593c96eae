<?php

namespace App\Http\Controllers\Admin\GuidanceTechnologies;

use App\Http\Controllers\Controller;
use App\Models\GuidanceTechnologies\Systems;
use App\Models\GuidanceTechnologies\SystemSoftwareFiles;
use App\Models\GuidanceTechnologies\SystemSoftwareVersions;
use App\Services\UploadFileService;
use Auth;
use File;
use Illuminate\Http\Request;

class SystemSoftwareVersionsController extends Controller
{
    public $target_dir;

    public $temp_dir;

    public $allowed_file_types = ['bdu', 'zip', 'txt', 'json'];

    /**
     * Instantiate controller class.
     */
    public function __construct()
    {
        $this->target_dir = storage_path().'/app/uploads/guidance-technologies/system-software-files/';
        $this->temp_dir = storage_path().'/app/uploads/guidance-technologies/temp';
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($uuid, Systems $systems)
    {
        $system = $systems->whereUuid($uuid)->with('versions')->first();

        if ($system) {
            return view('admin.software.system-software-versions.index', compact('system'));
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create($system_uuid, Systems $systems)
    {
        $system = $systems->whereUuid($system_uuid)->first();

        if ($system) {
            return view('admin.software.system-software-versions.create_edit', compact('system'));
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store($system_uuid, Request $request, Systems $systems)
    {
        $request->validate([
            'name' => 'required|string',
            'version' => 'required|string',
            'dev' => 'nullable|boolean',
            'staging' => 'nullable|boolean',
            'prod' => 'nullable|boolean',
            'internal' => 'nullable|boolean',
        ]);

        try {
            $system = $systems->whereUuid($system_uuid)->first();
            $version = $system->versions()->create([
                'name' => $request->name,
                'version' => $request->version,
                'dev' => $request->dev,
                'staging' => $request->staging,
                'internal' => $request->internal,
            ]);

            if (Auth::user()->hasPermissionTo('software upgrade publish')) {
                $version->prod = $request->prod ?? null;
                $version->save();
            }

            if ($version) {
                return redirect()->route('software.system.files.index', ['uuid' => $system_uuid]);
            } else {
                return back();
            }
        } catch (\Exception $e) {
        }
    }

    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function show(SystemSoftwareVersions $systemSoftwareVersions)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit($uuid, SystemSoftwareVersions $systemSoftwareVersions)
    {
        $version = $systemSoftwareVersions->whereUuid($uuid)->first();

        if ($version) {
            return view('admin.software.system-software-versions.create_edit', compact('version'));
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function update($uuid, Request $request, SystemSoftwareVersions $systemSoftwareVersions)
    {
        $request->validate([
            'name' => 'required|string',
            'version' => 'required|string',
            'dev' => 'nullable|boolean',
            'staging' => 'nullable|boolean',
            'prod' => 'nullable|boolean',
            'internal' => 'nullable|boolean',
        ]);

        try {
            $version = $systemSoftwareVersions->whereUuid($uuid)->with('system')->first();

            if ($version) {
                $version->name = $request->name;
                $version->version = $request->version;
                $version->dev = $request->dev ?? null;
                $version->staging = $request->staging ?? null;
                $version->internal = $request->internal ?? null;

                if (Auth::user()->hasPermissionTo('software upgrade publish')) {
                    $version->prod = $request->prod ?? null;
                }

                $version->save();

                return redirect()->route('software.system.files.index', ['uuid' => $version['system']->uuid]);
            } else {
                return back();
            }
        } catch (\Exception $e) {
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function delete($uuid, SystemSoftwareVersions $systemSoftwareVersions)
    {
        try {
            $record = $systemSoftwareVersions->whereUuid($uuid)->first();
            $title = $record->name;

            if ($record) {
                $filepath = $this->target_dir.$record->uuid;

                if ($this->deleteFile($filepath) === true) {
                    $record->forceDelete();

                    return \Response::json(['status' => 'success', 'record_id' => $record->uuid, 'record_title' => $title]);
                } elseif (! File::exists($filepath)) {
                    $record->forceDelete();

                    return \Response::json(['status' => 'success', 'record_id' => $record->uuid, 'record_title' => $title]);
                } else {
                    return \Response::json(['status' => 'fail', 'record_id' => $uuid, 'record_title' => $title, 'message' => 'Sorry, there was a problem deleting that record and its attached files.']);
                }
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }
    }

    /**
     * Change the status of the specified resource.
     *
     * @param  \App\Models\GuidanceTechnologies\Systems  $systems
     * @return \Illuminate\Http\Response
     */
    public function changeStatus($uuid, SystemSoftwareVersions $systemSoftwareVersions)
    {
        try {
            $record = $systemSoftwareVersions->whereUuid($uuid)->first();
            $title = $record->name;
            if ($record->active == 1) {
                $record->active = 0;
                $status = 'inactive';
            } else {
                $record->active = 1;
                $status = 'active';
            }
            $record->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $uuid, 'record_status' => $status, 'record_title' => $title]);
    }

    /**
     * Show upload page.
     *
     * @return View
     */
    public function upload($uuid, SystemSoftwareVersions $systemSoftwareVersions)
    {
        $version = $systemSoftwareVersions->whereUuid($uuid)->with('system')->with('files')->first();
        $allowed_file_types = $this->allowed_file_types;

        if ($version) {
            return view('admin.software.system-software-versions.upload', compact('version', 'allowed_file_types'));
        }
    }

    /**
     * Save the file to your site's storage and database - each file is sent in a separate request!
     *
     * @return View
     */
    public function storeUpload($uuid, Request $request, SystemSoftwareVersions $systemSoftwareVersions)
    {
        // Let's separate out the files into their own array
        $files = $_FILES;

        // Store files in a name spaced folder using the parent version uuid
        $target_dir = $this->target_dir.$uuid;
        // Send the request to a Upload file Service Provider
        $upload = new UploadFileService($request, $files, $target_dir, $this->temp_dir, $this->allowed_file_types);
        $result = $upload->uploadFile();

        //dd(\Log::info($result));

        // Now we'll return the AJAX response in JSON format back to the browser
        if (is_array($result)) {
            $record = $systemSoftwareVersions->whereUuid($uuid)->first();

            if ($record) {
                try {
                    // Save the info to the DB
                    $record->files()->create([
                        'filename' => $result['stored_filename'],
                        'filesize' => $result['filesize'],
                        'checksum' => $result['checksum'],
                    ]);

                    return \Response::json(['output' => 'Your file was uploaded, playa!']);
                } catch (Exception $e) {
                    return $upload->error[] = 'Sorry, but there was an error creating the database record for this file.';
                } // end try/catch
            } // end if record
        }
    }

    /**
     * download update file
     *
     * @return JSON response
     */
    public function download($uuid, Request $request, SystemSoftwareFiles $SystemSoftwareFiles)
    {

        try {
            $record = $SystemSoftwareFiles->whereUuid($uuid)->with('version')->first();

            if (isset($record) && isset($record['version'])) {
                $filename = $record->filename;
                $filepath = $this->target_dir.$record['version']->uuid.DIRECTORY_SEPARATOR.$record->filename;

                if (File::exists($filepath)) {
                    $this->fileDownload($filepath, $filename);
                } else {
                    return \Response::json(['status' => 'fail', 'record_id' => $record->uuid, 'record_title' => $record->filename, 'message' => 'Sorry, there was a problem downloading that file.']);
                }
            } else {
                return \Response::json(['status' => 'fail', 'record_id' => $record->uuid, 'record_title' => $record->filename, 'message' => 'Sorry, we cannot find that record to download.']);
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail', 'record_id' => $record->uuid, 'record_title' => $record->filename]);
        }
    }

    /**
     * function to zip and force download the files using PHP
     *
     * @param  string  $source_filename
     * @param  string  $download_filename
     * @return output file contents
     */
    private function fileDownload($source_filepath, $download_filename)
    {
        if ($source_filepath && File::exists($source_filepath)) {
            $file = $source_filepath;
            $fp = @fopen($file, 'rb');

            $size = filesize($file); // File size
            $length = $size;           // Content length
            $start = 0;               // Start byte
            $end = $size - 1;       // End byte

            header('Content-Disposition: attachment; filename="'.$download_filename.'"');
            header('Content-type: application/force-download');
            header("Accept-Ranges: 0-$length");

            if (isset($_SERVER['HTTP_RANGE'])) {
                $c_start = $start;
                $c_end = $end;

                [, $range] = explode('=', $_SERVER['HTTP_RANGE'], 2);
                if (strpos($range, ',') !== false) {
                    header('HTTP/1.1 416 Requested Range Not Satisfiable');
                    header("Content-Range: bytes $start-$end/$size");
                    exit;
                }
                if ($range == '-') {
                    $c_start = $size - substr($range, 1);
                } else {
                    $range = explode('-', $range);
                    $c_start = $range[0];
                    $c_end = (isset($range[1]) && is_numeric($range[1])) ? $range[1] : $size;
                }
                $c_end = ($c_end > $end) ? $end : $c_end;
                if ($c_start > $c_end || $c_start > $size - 1 || $c_end >= $size) {
                    header('HTTP/1.1 416 Requested Range Not Satisfiable');
                    header("Content-Range: bytes $start-$end/$size");
                    exit;
                }
                $start = $c_start;
                $end = $c_end;
                $length = $end - $start + 1;
                fseek($fp, $start);
                header('HTTP/1.1 206 Partial Content');
            }

            header("Content-Range: bytes $start-$end/$size");
            header('Content-Length: '.$length);

            $buffer = 1024 * 8;
            while (! feof($fp) && ($p = ftell($fp)) <= $end) {
                if ($p + $buffer > $end) {
                    $buffer = $end - $p + 1;
                }
                set_time_limit(0);
                echo fread($fp, $buffer);
                flush();
            }

            fclose($fp);
            exit();
        } else {
            return response()->json(['error' => 'Sorry, but the requested file does not exist.'], $status = 200);
        }
    }

    /**
     * Show the record.
     *
     * @return Redirect
     */
    public function getFiles($uuid, SystemSoftwareVersions $systemSoftwareVersions)
    {
        // Get the record
        $version = $systemSoftwareVersions->whereUuid($uuid)->with('files')->first();

        $header = '';
        $body = '';
        $footer = '';

        $token = csrf_token();

        if ($version && isset($version['files'])) {

            // Let's set some initial variables
            $header = '
            <table id="myTable" class="languages_table display" cellspacing="0" width="100%">
    					<thead>
    						<tr>
                  <th class="col-md-3 alignleft">Filename</th>
                  <th class="col-md-1 alignleft">Filesize</th>
                  <th class="col-md-3 alignleft">UUID</th>
                  <th class="col-md-3 alignleft">Checksum</th>
      						<th class="col-md-2" style="width:50px;">Actions</th>
    						</tr>
    					</thead>
    					<tbody id="tablebody_output">';

            foreach ($version['files'] as $file) {
                $body .= '
                    <tr id="tr'.$file->uuid.'" class="list_item" style="padding:2px 4px 0px 0px;border-top:0px; border-right:0px;border-left:0px;">
                      <td class="col-md-3">'.$file->filename.'</a></td>
                      <td class="col-md-1">'.formatFileSize($file->filesize).'</a></td>
                      <td class="col-md-3">'.$file->uuid.'</a></td>
                      <td class="col-md-3">'.$file->checksum.'</a></td>
                      <td class="col-md-2 col-centered">
                        <span class="download_icon table_icon" style="width: 100%;">
                          <a href="'.route('software.system.files.file.download', [$file->uuid]).'" id="download-record-'.$file->uuid.'" data-record-title="'.$file->uuid.'">
                            <i class="fa fa-fw fa-download download-btn" id="updateDownloadIcon-'.$file->uuid.'"></i></a>
                        <span>
                        <span class="trash_icon table_icon" style="width: 100%;">
                        <a id="delete-record-'.$file->uuid.'" data-record-title="'.$file->uuid.'" onclick="recordStatus(\'delete\', \''.route('software.system.files.file.delete', [$file->uuid]).'\', {_token: \''.csrf_token().'\'})"><i class="fa fa-fw fa-trash trash-btn" id="updateTrashIcon-'.$file->uuid.'"></i></a>
                        <span>
                      </td>
                    </tr>';
            }

            $footer = '
    			     </tbody>
    				</table>';

            $output = $header.$body.$footer;

            // Return it back to them
            return \Response::json(['output' => $this->prepJsonOutput($output)]);
        } else {
            return false;
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroyFile($uuid, SystemSoftwareFiles $SystemSoftwareFiles)
    {
        try {
            $record = $SystemSoftwareFiles->whereUuid($uuid)->with('version')->first();

            if (isset($record) && isset($record['version'])) {
                $filename = $record->filename;
                $filepath = $this->target_dir.$record['version']->uuid.DIRECTORY_SEPARATOR.$record->filename;

                if ($this->deleteFile($filepath)) {
                    $record->forceDelete();

                    // Send them to their view -> in this case, the upload file page view
                    return \Response::json(['status' => 'success', 'record_id' => $record->uuid, 'record_title' => $record->filename]);
                } else {
                    return \Response::json(['status' => 'fail', 'record_id' => $record->uuid, 'record_title' => $record->filename, 'message' => 'Sorry, there was a problem deleting that file.']);
                }
            } else {
                return \Response::json(['status' => 'fail', 'record_id' => $record->uuid, 'record_title' => $record->filename, 'message' => 'Sorry, we cannot find that record to delete.']);
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail', 'record_id' => $record->uuid, 'record_title' => $record->filename]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function deleteFile($filepath)
    {
        if (File::exists($filepath)) {
            if (File::isDirectory($filepath)) {
                if (File::deleteDirectory($filepath)) {
                    return true;
                }
            } elseif (File::isFile($filepath)) {
                if (File::delete($filepath)) {
                    return true;
                }
            }
        }
    }

    public function prepJsonOutput($output)
    {
        //$output = addslashes( $output );
        return str_replace(["\b", "\f", "\n", "\r", "\t", "\'"], ['', '', '', '', '', "'"], $output); // remove control characters
    }

    // end class
}

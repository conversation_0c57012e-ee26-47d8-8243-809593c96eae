<?php

namespace App\Http\Controllers\Admin\GuidanceTechnologies;

use App\Http\Controllers\Controller;
use App\Models\GuidanceTechnologies\OperatingSystemFiles;
use App\Services\UploadFileService;
use Auth;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class OperatingSystemFilesController extends Controller
{
    public $target_dir;

    public $temp_dir;

    public $allowed_file_types = ['bdu', 'zip', 'txt', 'json'];

    /**
     * Instantiate controller class.
     */
    public function __construct()
    {
        $this->target_dir = storage_path().'/app/uploads/guidance-technologies/operating-system-files/';
        $this->temp_dir = storage_path().'/app/uploads/guidance-technologies/temp';
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $os_files = OperatingSystemFiles::orderBy('active', 'asc')->get();

        return view('admin.software.operating-system.index', compact('os_files'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.software.operating-system.create_edit');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function delete($uuid, OperatingSystemFiles $operatingSystemFiles)
    {
        try {
            $record = $operatingSystemFiles->whereUuid($uuid)->first();
            $title = $record->name;

            if ($record) {
                $filepath = $this->target_dir;

                if ($this->deleteFile($filepath) === true) {
                    $record->forceDelete();

                    return \Response::json(['status' => 'success', 'record_id' => $record->uuid, 'record_title' => $title]);
                } elseif (! File::exists($filepath)) {
                    $record->forceDelete();

                    return \Response::json(['status' => 'success', 'record_id' => $record->uuid, 'record_title' => $title]);
                } else {
                    return \Response::json(['status' => 'fail', 'record_id' => $uuid, 'record_title' => $title, 'message' => 'Sorry, there was a problem deleting that record and its attached files.']);
                }
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }
    }

    /**
     * Change the status of the specified resource.
     *
     * @param  \App\Models\GuidanceTechnologies\Systems  $systems
     * @return \Illuminate\Http\Response
     */
    public function fileChangeStatus($uuid, OperatingSystemFiles $operatingSystemFiles)
    {
        try {
            $record = $operatingSystemFiles->whereUuid($uuid)->first();
            $title = $record->filename;
            if ($record->active == 1) {
                $record->active = 0;
                $status = 'inactive';
            } else {
                $record->active = 1;
                $status = 'active';
            }
            $record->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $uuid, 'record_status' => $status, 'record_title' => $title]);
    }

    /**
     * Change the status of the specified resource.
     *
     * @param  \App\Models\GuidanceTechnologies\Systems  $systems
     * @return \Illuminate\Http\Response
     */
    public function setEnvironment($uuid, $environment, Request $request, OperatingSystemFiles $operatingSystemFiles)
    {
        $validator = Validator::make([
            'environment' => $request->environment,
        ], [
            'environment' => [
                'required',
                Rule::in(['dev', 'staging', 'prod']),
            ],
        ], [
            'environment.in' => 'The :attribute field is required and must equal one of these values: :values.',
        ]);

        $output = '';

        try {
            $record = $operatingSystemFiles->whereUuid($uuid)->first();
            $title = $record->filename;
            if ($environment == 'prod' && Auth::user()->hasPermissionTo('software upgrade publish')) {
                if ($record->$environment == 1) {
                    $record->$environment = null;
                    $status = 'inactive';
                } else {
                    $record->$environment = 1;
                    $status = 'active';
                }
            } elseif ($environment == 'dev' || $environment == 'staging') {
                if ($record->$environment == 1) {
                    $record->$environment = null;
                    $status = 'inactive';
                } else {
                    $record->$environment = 1;
                    $status = 'active';
                }
            }

            $record->save();

            if ($record->dev) {
                $output .= '<span class="initial-btn">D</span> ';
            }
            if ($record->staging) {
                $output .= '<span class="initial-btn">S</span> ';
            }
            if ($record->prod) {
                $output .= '<span class="initial-btn">P</span>';
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $uuid, 'record_status' => $status, 'record_title' => $title, 'output' => $output]);
    }

    /**
     * Show upload page.
     *
     * @return View
     */
    public function upload(OperatingSystemFiles $operatingSystemFiles)
    {
        $os_files = $operatingSystemFiles->get();
        $allowed_file_types = $this->allowed_file_types;

        if ($os_files) {
            return view('admin.software.operating-system.upload', compact('os_files', 'allowed_file_types'));
        }
    }

    /**
     * Save the file to your site's storage and database - each file is sent in a separate request!
     *
     * @return View
     */
    public function storeUpload(Request $request, OperatingSystemFiles $operatingSystemFiles)
    {
        // Let's separate out the files into their own array
        $files = $_FILES;

        // Store files in a name spaced folder using the parent version uuid
        $target_dir = $this->target_dir;
        // Send the request to a Upload file Service Provider
        $upload = new UploadFileService($request, $files, $target_dir, $this->temp_dir, $this->allowed_file_types);
        $result = $upload->uploadFile();

        //dd(\Log::info($result));

        // Now we'll return the AJAX response in JSON format back to the browser
        if (is_array($result)) {
            try {
                // Save the info to the DB
                $operatingSystemFiles->create([
                    'filename' => $result['stored_filename'],
                    'filesize' => $result['filesize'],
                    'checksum' => $result['checksum'],
                ]);

                return \Response::json(['output' => 'Your file was uploaded, playa!']);
            } catch (Exception $e) {
                return $upload->error[] = 'Sorry, but there was an error creating the database record for this file.';
            } // end try/catch
        }
    }

    /**
     * Show the record.
     *
     * @return Redirect
     */
    public function getFiles(OperatingSystemFiles $operatingSystemFiles)
    {
        // Get the record
        $os_files = $operatingSystemFiles->get();

        if ($os_files) {
            // Return it back to them
            return \Response::json(['output' => view('admin.software.operating-system.includes.file-list')->with('os_files', $os_files)->render()]);
        } else {
            return false;
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroyFile($uuid, OperatingSystemFiles $operatingSystemFiles)
    {
        try {
            $record = $operatingSystemFiles->whereUuid($uuid)->first();

            if (isset($record)) {
                $filepath = $this->target_dir.$record->filename;

                if ($this->deleteFile($filepath)) {
                    $record->forceDelete();

                    // Send them to their view -> in this case, the upload file page view
                    return \Response::json(['status' => 'success', 'record_id' => $record->uuid, 'record_title' => $record->filename]);
                } else {
                    return \Response::json(['status' => 'fail', 'record_id' => $record->uuid, 'record_title' => $record->filename, 'message' => 'Sorry, there was a problem deleting that file.']);
                }
            } else {
                return \Response::json(['status' => 'fail', 'record_id' => $record->uuid, 'record_title' => $record->filename, 'message' => 'Sorry, we cannot find that record to delete.']);
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail', 'record_id' => $record->uuid, 'record_title' => $record->filename]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function deleteFile($filepath)
    {
        if (File::exists($filepath)) {
            if (File::isDirectory($filepath)) {
                if (File::deleteDirectory($filepath)) {
                    return true;
                }
            } elseif (File::isFile($filepath)) {
                if (File::delete($filepath)) {
                    return true;
                }
            }
        }
    }

    public function prepJsonOutput($output)
    {
        //$output = addslashes( $output );
        return str_replace(["\b", "\f", "\n", "\r", "\t", "\'"], ['', '', '', '', '', "'"], $output); // remove control characters
    }

    // end class
}

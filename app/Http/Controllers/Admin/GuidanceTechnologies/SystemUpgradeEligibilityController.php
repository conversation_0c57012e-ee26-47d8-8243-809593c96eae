<?php

namespace App\Http\Controllers\Admin\GuidanceTechnologies;

use App\Helpers\SerialNumber;
use App\Http\Controllers\Controller;
use App\Models\GuidanceTechnologies\SystemUpgradeEligibility;
use Illuminate\Http\Request;

class SystemUpgradeEligibilityController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $serial_numbers = SystemUpgradeEligibility::orderBy('active', 'asc')->get();

        return view('admin.software.eligibility.index', compact('serial_numbers'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.software.eligibility.create_edit');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, SystemUpgradeEligibility $eligibility, SerialNumber $SN)
    {
        $request->validate([
            'serial_numbers' => 'required|string',
            'internal' => 'nullable|boolean',
            'international' => 'nullable|boolean',
        ]);

        try {
            $serial_numbers = explode(',', $request->serial_numbers);

            foreach ($serial_numbers as $serial_number) {
                $system_model = $SN->split(trim($serial_number))->system_model;

                $record = $eligibility->firstOrNew([
                    'serial_number' => trim($serial_number),
                ]);

                $record->system_model = $system_model;
                $record->internal = $request->internal;
                $record->international = $request->international;
                $record->active = true;

                $record->save();
            }

            return redirect()->route('software.serial.number.eligibility.index');
        } catch (\Exception $e) {
            flash('Sorry, there was an issue saving your serial number(s)')->error();

            return back();
            // return redirect()->back();
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($uuid, SystemUpgradeEligibility $eligibility)
    {
        $serial_number = $eligibility->whereUuid($uuid)->first();

        if ($serial_number) {
            return view('admin.software.eligibility.create_edit', compact('serial_number'));
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $uuid, SystemUpgradeEligibility $eligibility, SerialNumber $SN)
    {
        $request->validate([
            'serial_number' => 'required|string',
            'internal' => 'nullable|boolean',
            'international' => 'nullable|boolean',
        ]);

        try {
            $serial_number = $eligibility->whereUuid($uuid)->first();

            if ($serial_number) {
                $system_model = $SN->split(trim($request->serial_number))->system_model;

                $serial_number->serial_number = trim($request->serial_number);
                $serial_number->system_model = $system_model;
                $serial_number->internal = $request->internal;
                $serial_number->international = $request->international;

                $serial_number->save();

                return redirect()->route('software.serial.number.eligibility.index');
            } else {
                flash('Sorry, there was an issue saving your serial number(s)')->error();

                return back();
            }
        } catch (\Exception $e) {
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function delete($uuid, SystemUpgradeEligibility $eligibility)
    {
        try {
            $record = $eligibility->whereUuid($uuid)->first();
            $title = $record->serial_number;
            $record->forceDelete();
        } catch (\Exception $e) {
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $uuid, 'record_title' => $title]);
    }

    /**
     * Change the status of the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function changeStatus($uuid, SystemUpgradeEligibility $eligibility)
    {
        try {
            $record = $eligibility->whereUuid($uuid)->first();
            $title = $record->system_name;
            if ($record->active == 1) {
                $record->active = 0;
                $status = 'inactive';
            } else {
                $record->active = 1;
                $status = 'active';
            }
            $record->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $uuid, 'record_status' => $status, 'record_title' => $title]);
    }
}

<?php

namespace App\Http\Controllers\Admin\GuidanceTechnologies;

use App\Http\Controllers\Controller;
use App\Models\GuidanceTechnologies\SystemUpdateHistory;
use Carbon\Carbon;
use DB;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Storage;

class SystemUpdateHistoryController extends Controller
{
    private $source_dir;

    /**
     * Constructor
     */
    public function __construct(Request $request)
    {
        // File Storage and Download Paths
        $this->source_dir = storage_path().'/app/uploads/guidance-technologies/error-logs/';
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request, SystemUpdateHistory $SystemUpdateHistory, $environment = 'prod')
    {
        $validator = Validator::make([
            'environment' => $request->environment,
        ], [
            'environment' => [
                'required',
                Rule::in(['dev', 'staging', 'prod']),
            ],
        ], [
            'environment.in' => 'The :attribute field is required and must equal one of these values: :values.',
        ]);

        $update_histories = $SystemUpdateHistory->where($environment, 1)->orderBy('update_datetime', 'desc')->orderBy('serial_number', 'asc')->get();

        return view('admin.software.system-update-history.index', compact('update_histories', 'environment'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function report(Request $request, SystemUpdateHistory $SystemUpdateHistory, $environment = 'prod')
    {
        $validator = Validator::make([
            'environment' => $request->environment,
        ], [
            'environment' => [
                'required',
                Rule::in(['dev', 'staging', 'prod']),
            ],
        ], [
            'environment.in' => 'The :attribute field is required and must equal one of these values: :values.',
        ]);

        // create an array of years with the same keys as values
        $years = array_combine(range(Carbon::now()->year, 2020), range(Carbon::now()->year, 2020));
        $months = [
            '01' => 'January',
            '02' => 'February',
            '03' => 'March',
            '04' => 'April',
            '05' => 'May',
            '06' => 'June',
            '07' => 'July',
            '08' => 'August',
            '09' => 'September',
            '10' => 'October',
            '11' => 'November',
            '12' => 'December',
        ];

        $year = $request->year;
        $month = $request->month;

        if ($request->year && $request->month) {
            $report_title = $months[$request->month].' '.$request->year;
            $update_histories = $SystemUpdateHistory->select('*', DB::raw('count(*) as total'), DB::raw("COUNT(IF(success='1',1, NULL)) 'success'"), DB::raw("COUNT(IF(success='0',1, NULL)) 'failure'"))
                ->where($environment, 1)
                ->whereYear('update_datetime', $request->year)
                ->whereMonth('update_datetime', $request->month)
                ->orderBy('system_name', 'asc')
                ->orderBy('current_version', 'desc')
                ->groupBy('current_version')
                ->get();
        } elseif ($request->year) {
            $report_title = $request->year;
            $update_histories = $SystemUpdateHistory->select('*', DB::raw('count(*) as total'), DB::raw("COUNT(IF(success='1',1, NULL)) 'success'"), DB::raw("COUNT(IF(success='0',1, NULL)) 'failure'"))
                ->where($environment, 1)
                ->whereYear('update_datetime', $request->year)
                ->orderBy('system_name', 'asc')
                ->orderBy('current_version', 'desc')
                ->groupBy('current_version')
                ->get();
        } elseif ($request->month) {
            $report_title = $months[$request->month].' '.Carbon::now()->year;
            $update_histories = $SystemUpdateHistory->select('*', DB::raw('count(*) as total'), DB::raw("COUNT(IF(success='1',1, NULL)) 'success'"), DB::raw("COUNT(IF(success='0',1, NULL)) 'failure'"))
                ->where($environment, 1)
                ->whereMonth('update_datetime', $request->month)
                ->orderBy('system_name', 'asc')
                ->orderBy('current_version', 'desc')
                ->groupBy('current_version')
                ->get();
        } else {
            $report_title = 'Year to Date';
            $update_histories = $SystemUpdateHistory->select('*', DB::raw('count(*) as total'), DB::raw("COUNT(IF(success='1',1, NULL)) 'success'"), DB::raw("COUNT(IF(success='0',1, NULL)) 'failure'"))
                ->where($environment, 1)
                ->whereYear('update_datetime', Carbon::now()->year)
                ->orderBy('system_name', 'asc')
                ->orderBy('current_version', 'desc')
                ->groupBy('current_version')

                ->get();
        }
        // dd($update_histories);

        // prepend select option by combining it with the original array
        $months = ['' => 'Select Month...'] + $months;
        $years = ['' => 'Select Year...'] + $years;

        return view('admin.software.system-update-history.report', compact('update_histories', 'environment', 'year', 'month', 'years', 'months', 'report_title'));
    }

    /**
     * download update file
     *
     * @return JSON response
     */
    public function download($uuid, $filename, Request $request)
    {
        if ($uuid && $filename) {
            $source_filepath = $this->source_dir.$uuid.DIRECTORY_SEPARATOR.$filename;

            $this->fileDownload($source_filepath, $filename);
        } else {
        }
    }

    /**
     * function to zip and force download the files using PHP
     *
     * @param  string  $source_filename
     * @param  string  $download_filename
     * @return output file contents
     */
    private function fileDownload($source_filepath, $download_filename)
    {
        if ($source_filepath && File::exists($source_filepath)) {
            $file = $source_filepath;
            $fp = @fopen($file, 'rb');

            $size = filesize($file); // File size
            $length = $size;           // Content length
            $start = 0;               // Start byte
            $end = $size - 1;       // End byte

            header('Content-Disposition: attachment; filename="'.$download_filename.'"');
            header('Content-type: application/force-download');
            header("Accept-Ranges: 0-$length");

            if (isset($_SERVER['HTTP_RANGE'])) {
                $c_start = $start;
                $c_end = $end;

                [, $range] = explode('=', $_SERVER['HTTP_RANGE'], 2);
                if (strpos($range, ',') !== false) {
                    header('HTTP/1.1 416 Requested Range Not Satisfiable');
                    header("Content-Range: bytes $start-$end/$size");
                    exit;
                }
                if ($range == '-') {
                    $c_start = $size - substr($range, 1);
                } else {
                    $range = explode('-', $range);
                    $c_start = $range[0];
                    $c_end = (isset($range[1]) && is_numeric($range[1])) ? $range[1] : $size;
                }
                $c_end = ($c_end > $end) ? $end : $c_end;
                if ($c_start > $c_end || $c_start > $size - 1 || $c_end >= $size) {
                    header('HTTP/1.1 416 Requested Range Not Satisfiable');
                    header("Content-Range: bytes $start-$end/$size");
                    exit;
                }
                $start = $c_start;
                $end = $c_end;
                $length = $end - $start + 1;
                fseek($fp, $start);
                header('HTTP/1.1 206 Partial Content');
            }

            header("Content-Range: bytes $start-$end/$size");
            header('Content-Length: '.$length);

            $buffer = 1024 * 8;
            while (! feof($fp) && ($p = ftell($fp)) <= $end) {
                if ($p + $buffer > $end) {
                    $buffer = $end - $p + 1;
                }
                set_time_limit(0);
                echo fread($fp, $buffer);
                flush();
            }

            fclose($fp);
            exit();
        } else {
            return response()->json(['error' => 'Sorry, but the requested file does not exist.'], $status = 200);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function delete($uuid, SystemUpdateHistory $SystemUpdateHistory)
    {
        try {
            $record = $SystemUpdateHistory->whereUuid($uuid)->first();
            $title = $record->serial_number;

            if ($record) {
                $filepath = $this->source_dir.$record->uuid;

                if ($this->deleteFile($filepath) === true) {
                    $record->forceDelete();

                    return \Response::json(['status' => 'success', 'record_id' => $record->uuid, 'record_title' => $title]);
                } elseif (! File::exists($filepath)) {
                    $record->forceDelete();

                    return \Response::json(['status' => 'success', 'record_id' => $record->uuid, 'record_title' => $title]);
                } else {
                    return \Response::json(['status' => 'fail', 'record_id' => $uuid, 'record_title' => $title, 'message' => 'Sorry, there was a problem deleting that record and its attached files.']);
                }
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function deleteFile($filepath)
    {
        if (File::exists($filepath)) {
            if (File::isDirectory($filepath)) {
                if (File::deleteDirectory($filepath)) {
                    return true;
                }
            } elseif (File::isFile($filepath)) {
                if (File::delete($filepath)) {
                    return true;
                }
            }
        }
    }
}

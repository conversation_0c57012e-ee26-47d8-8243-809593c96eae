<?php

namespace App\Http\Controllers\Admin\GuidanceTechnologies;

use App\Http\Controllers\Controller;
use App\Models\GuidanceTechnologies\Systems;
use Auth;
use Illuminate\Http\Request;

class SystemsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $systems = Systems::orderBy('active', 'asc')->orderBy('system_name', 'asc')->get();

        return view('admin.software.systems.index', compact('systems'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
        return view('admin.software.systems.create_edit');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'system_name' => 'required|string',
            'system_model' => 'required|string',
            'dev' => 'nullable|boolean',
            'staging' => 'nullable|boolean',
            'prod' => 'nullable|boolean',
            'internal' => 'nullable|boolean',
        ]);

        try {
            $system = Systems::create([
                'system_name' => $request->system_name,
                'system_model' => $request->system_model,
                'dev' => $request->dev,
                'staging' => $request->staging,
                'internal' => $request->internal,
            ]);

            if (Auth::user()->hasPermissionTo('software upgrade publish')) {
                $system->prod = $request->prod ?? null;
                $system->save();
            }

            if ($system) {
                return $this->index();
            } else {
                return back();
            }
        } catch (\Exception $e) {
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit($uuid, Systems $systems)
    {
        $system = $systems->whereUuid($uuid)->first();

        if ($system) {
            return view('admin.software.systems.create_edit', compact('system'));
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function update($uuid, Request $request, Systems $systems)
    {
        $request->validate([
            'system_name' => 'required|string',
            'system_model' => 'required|string',
            'dev' => 'nullable|boolean',
            'staging' => 'nullable|boolean',
            'prod' => 'nullable|boolean',
            'internal' => 'nullable|boolean',
        ]);

        try {
            $system = $systems->whereUuid($uuid)->first();

            if ($system) {
                $system->system_name = $request->system_name;
                $system->system_model = $request->system_model;
                $system->dev = $request->dev;
                $system->staging = $request->staging;
                $system->internal = $request->internal;

                if (Auth::user()->hasPermissionTo('software upgrade publish')) {
                    $system->prod = $request->prod ?? null;
                }

                $system->save();

                return $this->index();
            } else {
                return back();
            }
        } catch (\Exception $e) {
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function delete($uuid, Systems $systems)
    {
        try {
            $record = $systems->whereUuid($uuid)->first();
            $title = $record->system_name;
            $record->delete();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $uuid, 'record_title' => $title]);
    }

    /**
     * Change the status of the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function changeStatus($uuid, Systems $systems)
    {
        try {
            $record = $systems->whereUuid($uuid)->first();
            $title = $record->system_name;
            if ($record->active == 1) {
                $record->active = 0;
                $status = 'inactive';
            } else {
                $record->active = 1;
                $status = 'active';
            }
            $record->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $uuid, 'record_status' => $status, 'record_title' => $title]);
    }
}

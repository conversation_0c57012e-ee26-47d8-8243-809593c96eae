<?php

namespace App\Http\Controllers\Admin\GuidanceTechnologies;

use App\Http\Controllers\Controller;
use App\Models\GuidanceTechnologies\SystemSoftwareFiles;
use Illuminate\Http\Request;

class SystemSoftwareFilesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([

        ]);

        try {
        } catch (\Exception $e) {
        }
    }

    /**
     * Display the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function show(SystemSoftwareFiles $systemSoftwareFiles)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit(SystemSoftwareFiles $systemSoftwareFiles)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, SystemSoftwareFiles $systemSoftwareFiles)
    {
        $request->validate([

        ]);

        try {
        } catch (\Exception $e) {
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function destroy(SystemSoftwareFiles $systemSoftwareFiles)
    {
        $request->validate([

        ]);

        try {
        } catch (\Exception $e) {
        }
    }

    /**
     * Save the file to your site's storage and database - each file is sent in a separate request!
     *
     * @return View
     */
    public function storeupload()
    {

        // Okay - let's grab all the form input and put it in array, which is basically the name, size and token
        $request = \Request::all();

        // Let's separate out the files into their own array
        $files = $_FILES;

        // Send the request to a Upload file Service Provider
        $target_dir = storage_path().'/app/public/literature';
        $temp_dir = storage_path().'/app/uploads/literature/temp';
        $upload = new UploadFileService($request, $files, $target_dir, $temp_dir, ['pdf']);

        // Part of preflight check

        // $this->error[] and $this->message[] available to us through PreFlightCheck() in UploadFileService
        // First, check that the chunk_file_extension is a PDF => $upload->chunk_file_extension; gives "pdf" (as does $upload->source_filename_extension)
        if ($upload->chunk_file_extension !== 'pdf') {
            $upload->error[] = 'Sorry, but '.$upload->source_filename.' must be a pdf.';
        } else {

            // CONTINUE WITH OTHER CHECKS

            // Second, check that the soure_filename is formatted correctly begins with the same numbers as the parent_id => $upload->source_filename; gives "1234_xx.pdf" (as does $upload->source_filename_clean)
            $filename_parts = explode('_', pathinfo($upload->source_filename, PATHINFO_FILENAME));

            // CONTINUE WITH OTHER CHECKS

            // check first part of the file name to make sure it matches tha parent artwork part number
            if ($filename_parts[0] !== $request['tracking_number']) {
                $upload->error[] = 'Sorry, but '.$upload->source_filename.' prefix ('.$filename_parts[0].') does not match the tracking number ('.$request['tracking_number'].'). Please check that it is named correctly and try again.';
            }
        } // END OF FIRST CHECK

        if (! empty($upload->error)) {
            return \Response::json(['error' => $upload->error]);
        } elseif (! empty($upload->message)) {
            return \Response::json(['message' => $upload->message]);
        }

        // End Preflight Check

        // Save the info to the DB
        $record = SiteLiterature::findOrFail($request['parent_id']);

        if ($record) {
            try {
                $record->fill([
                    'filename' => $upload->source_filename_clean,
                    'fileformat' => $upload->source_filename_extension,
                    'filesize' => $upload->source_filesize,
                ])->save();
            } catch (Exception $e) {
                return $upload->error[] = 'Sorry, but there was an error creating the database record for this file.';
            } // end try/catch
        } // end if record

        // Upload checks passed, let the file through
        $result = $upload->uploadFile();

        // Now we'll return the AJAX response in JSON format back to the browser
        if (is_array($result)) {
            return \Response::json(['output' => 'Your file was uploaded, playa!']);
        }
    }
}

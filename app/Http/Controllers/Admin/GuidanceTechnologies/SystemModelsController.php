<?php

namespace App\Http\Controllers\Admin\GuidanceTechnologies;

use App\Http\Controllers\Controller;
use App\Models\GuidanceTechnologies\SystemModels;
use Illuminate\Http\Request;

class SystemModelsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $system_models = SystemModels::orderBy('active', 'asc')->orderBy('system_model', 'asc')->get();

        return view('admin.software.system-models.index', compact('system_models'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
        return view('admin.software.system-models.create_edit');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'system_name' => 'required|string',
            'system_model' => 'required|string',

            'manufacturer_regex' => 'required|string',
            'manufacture_year_regex' => 'required|string',
            'manufacture_month_regex' => 'required|string',
            'system_model_regex' => 'required|string',
            'lot_number_regex' => 'required|string',

            'international' => 'nullable|boolean',
        ]);

        try {
            $system_model = SystemModels::create([
                'system_name' => $request->system_name,
                'system_model' => $request->system_model,

                'manufacturer_regex' => $request->manufacturer_regex,
                'manufacture_year_regex' => $request->manufacture_year_regex,
                'manufacture_month_regex' => $request->manufacture_month_regex,
                'system_model_regex' => $request->system_model_regex,
                'lot_number_regex' => $request->lot_number_regex,

                'international' => $request->international,
            ]);
            if ($system_model) {
                return $this->index();
            } else {
                return back();
            }
        } catch (\Exception $e) {
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit($uuid, SystemModels $SystemModels)
    {
        $system_model = $SystemModels->whereUuid($uuid)->first();

        if ($system_model) {
            return view('admin.software.system-models.create_edit', compact('system_model'));
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function update($uuid, Request $request, SystemModels $SystemModels)
    {
        $request->validate([
            'system_name' => 'required|string',
            'system_model' => 'required|string',

            'manufacturer_regex' => 'required|string',
            'manufacture_year_regex' => 'required|string',
            'manufacture_month_regex' => 'required|string',
            'system_model_regex' => 'required|string',
            'lot_number_regex' => 'required|string',

            'international' => 'nullable|boolean',
        ]);

        // try {
        $system_model = $SystemModels->whereUuid($uuid)->first();

        if ($system_model) {
            $system_model->system_name = $request->system_name;
            $system_model->system_model = $request->system_model;

            $system_model->manufacturer_regex = $request->manufacturer_regex;
            $system_model->manufacture_year_regex = $request->manufacture_year_regex;
            $system_model->manufacture_month_regex = $request->manufacture_month_regex;
            $system_model->system_model_regex = $request->system_model_regex;
            $system_model->lot_number_regex = $request->lot_number_regex;

            $system_model->international = $request->international;

            $system_model->save();
        } else {
            return back();
        }
        // } catch (\Exception $e) {
        // }

        return redirect()->route('software.system.models.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function delete($uuid, SystemModels $SystemModels)
    {
        try {
            $record = $SystemModels->whereUuid($uuid)->first();
            $title = $record->system_name;
            $record->delete();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $uuid, 'record_title' => $title]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @return \Illuminate\Http\Response
     */
    public function clone($uuid, SystemModels $SystemModels)
    {
        try {
            $record = $SystemModels->whereUuid($uuid)->first();
            $title = $record->system_name;
            $clone = $record->replicate();
            $clone->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        return redirect()->route('software.system.models.index');
    }

    /**
     * Change the status of the specified resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function changeStatus($uuid, SystemModels $SystemModels)
    {
        try {
            $record = $SystemModels->whereUuid($uuid)->first();
            $title = $record->system_name;
            if ($record->active == 1) {
                $record->active = 0;
                $status = 'inactive';
            } else {
                $record->active = 1;
                $status = 'active';
            }
            $record->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $uuid, 'record_status' => $status, 'record_title' => $title]);
    }
}

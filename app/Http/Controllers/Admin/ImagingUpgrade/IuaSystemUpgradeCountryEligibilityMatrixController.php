<?php

namespace App\Http\Controllers\ImagingUpgrade;

use App\Http\Controllers\Controller;
use App\Models\ImagingUpgrade\IuaSystemUpgradeCountryEligibilityMatrix;
use Illuminate\Http\Request;

class IuaSystemUpgradeCountryEligibilityMatrixController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(IuaSystemUpgradeCountryEligibilityMatrix $iuaSystemUpgradeCountryEligibilityMatrix)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(IuaSystemUpgradeCountryEligibilityMatrix $iuaSystemUpgradeCountryEligibilityMatrix)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, IuaSystemUpgradeCountryEligibilityMatrix $iuaSystemUpgradeCountryEligibilityMatrix)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(IuaSystemUpgradeCountryEligibilityMatrix $iuaSystemUpgradeCountryEligibilityMatrix)
    {
        //
    }
}

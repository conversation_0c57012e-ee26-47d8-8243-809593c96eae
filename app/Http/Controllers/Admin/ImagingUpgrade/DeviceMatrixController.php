<?php

namespace App\Http\Controllers\Admin\ImagingUpgrade;

use App\Http\Controllers\Controller;
use App\Models\ImagingUpgrade\IuaSystemUpgradeCountryEligibilityMatrix;
use App\Models\ImagingUpgrade\IuaSystemUpgradeCountryEligibilityMatrixUat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class DeviceMatrixController extends Controller
{
    /**
     * Display a listing of the device matrix entries.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $environment = $request->get('environment', 'production');
        $search = $request->get('search', '');
        $perPage = $request->get('per_page', 15);
        
        // Determine which model to use based on environment
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        // Build query with search
        $query = $model::query();
        
        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('system_id', 'like', "%{$search}%")
                  ->orWhere('system_name', 'like', "%{$search}%")
                  ->orWhere('country_code', 'like', "%{$search}%")
                  ->orWhere('country_name', 'like', "%{$search}%");
            });
        }
        
        // Apply filters if provided
        if ($request->has('system_id') && !empty($request->system_id)) {
            $query->where('system_id', $request->system_id);
        }
        
        if ($request->has('country_code') && !empty($request->country_code)) {
            $query->where('country_code', $request->country_code);
        }
        
        // Get unique system IDs and country codes for filters
        $systemIds = $model::select('system_id', 'system_name')
            ->distinct()
            ->orderBy('system_name')
            ->get();
            
        $countryCodes = $model::select('country_code', 'country_name')
            ->distinct()
            ->orderBy('country_name')
            ->get();
        
        // Get paginated results
        $entries = $query->orderBy('system_name')->orderBy('country_name')->paginate($perPage);
        
        return view('admin.device_matrix.index', compact(
            'entries', 
            'environment', 
            'search', 
            'systemIds', 
            'countryCodes',
            'perPage'
        ));
    }

    /**
     * Show the form for creating a new device matrix entry.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        $environment = $request->get('environment', 'production');
        
        // Get unique system IDs and country codes for dropdowns
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
            
        $systemIds = $model::select('system_id', 'system_name')
            ->distinct()
            ->orderBy('system_name')
            ->get();
            
        $countryCodes = $model::select('country_code', 'country_name')
            ->distinct()
            ->orderBy('country_name')
            ->get();
        
        // Check if we're coming from a "Save & Create Another" action
        $lastSystemId = $request->get('last_system_id');
        $lastSystemName = $request->get('last_system_name');
        
        // If we have a last system ID but it's not in our dropdown, add it
        if ($lastSystemId && !$systemIds->contains('system_id', $lastSystemId)) {
            $systemIds->push((object)[
                'system_id' => $lastSystemId,
                'system_name' => $lastSystemName ?: $lastSystemId
            ]);
        }
        
        return view('admin.device_matrix.create', compact(
            'environment', 
            'systemIds', 
            'countryCodes'
        ));
    }

    /**
     * Store a newly created device matrix entry.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $environment = $request->get('environment', 'production');
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        $table = $environment === 'production' 
            ? 'iua_system_upgrade_country_eligibility_matrices' 
            : 'iua_system_upgrade_country_eligibility_matrices_uat';
        
        // Validate the request
        $request->validate([
            'system_id' => [
                'required',
                Rule::unique($table)->where(function ($query) use ($request) {
                    return $query->where('country_code', $request->country_code);
                }),
            ],
            'system_name' => 'required|string|max:255',
            'country_code' => 'required|string|max:10',
            'country_name' => 'required|string|max:255',
            'ultrasound' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\.\-_]+$/',
            'sherlock' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\.\-_]+$/',
            'shell' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\.\-_]+$/',
            'dicom' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\.\-_]+$/',
            'source_filename' => 'nullable|string|max:255',
            'download_filename' => 'nullable|string|max:255',
            'product_image' => 'nullable|string|max:255',
            'installation_instructions' => 'nullable|string|max:255',
        ]);
        
        try {
            DB::beginTransaction();
            
            // Create the new entry
            $entry = $model::create($request->all());
            
            // Log the action
            Log::info("Device matrix entry created", [
                'user_id' => auth()->id(),
                'environment' => $environment,
                'entry_id' => $entry->id,
                'system_id' => $entry->system_id,
                'country_code' => $entry->country_code
            ]);
            
            DB::commit();
            
            // Determine where to redirect based on the submitted action
            $redirectAction = $request->input('submit_action', 'save_and_return');
            
            if ($redirectAction === 'save_and_continue') {
                flash('Device matrix entry created successfully. You can continue editing.')->success();
                return redirect()->route('device.matrix.edit', [
                    'id' => $entry->id, 
                    'environment' => $environment
                ])->with('highlight', true);
            } elseif ($redirectAction === 'save_and_new') {
                flash('Device matrix entry created successfully. You can now create another entry.')->success();
                return redirect()->route('device.matrix.create', [
                    'environment' => $environment,
                    'last_system_id' => $entry->system_id,
                    'last_system_name' => $entry->system_name
                ]);
            } else {
                // Default: save_and_return
                flash('Device matrix entry "<strong>' . e($entry->system_name) . ' (' . e($entry->system_id) . ') - ' . e($entry->country_name) . '</strong>" created successfully.')->success();
                return redirect()->route('device.matrix.index', ['environment' => $environment]);
            }
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create device matrix entry: ' . $e->getMessage());
            
            // Provide more user-friendly error messages
            $errorMessage = 'Failed to create device matrix entry. ';
            
            // Check for common errors and provide helpful messages
            if (strpos($e->getMessage(), 'Duplicate entry') !== false || strpos($e->getMessage(), 'UNIQUE constraint failed') !== false) {
                $errorMessage .= 'A device matrix entry with this System ID and Country Code combination already exists.';
            } elseif (strpos($e->getMessage(), 'Data too long') !== false) {
                $errorMessage .= 'One of the fields exceeds the maximum allowed length.';
            } else {
                $errorMessage .= $e->getMessage();
            }
            
            flash($errorMessage)->error();
            return redirect()->back()->withInput()->with('error_fields', array_keys($request->all()));
        }
    }

    /**
     * Show the form for editing the specified device matrix entry.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function edit(Request $request, $id)
    {
        $environment = $request->get('environment', 'production');
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        $entry = $model::findOrFail($id);
        
        // Get unique system IDs and country codes for dropdowns
        $systemIds = $model::select('system_id', 'system_name')
            ->distinct()
            ->orderBy('system_name')
            ->get();
            
        $countryCodes = $model::select('country_code', 'country_name')
            ->distinct()
            ->orderBy('country_name')
            ->get();
        
        return view('admin.device_matrix.edit', compact(
            'entry', 
            'environment', 
            'systemIds', 
            'countryCodes'
        ));
    }

    /**
     * Update the specified device matrix entry.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $environment = $request->get('environment', 'production');
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        $table = $environment === 'production' 
            ? 'iua_system_upgrade_country_eligibility_matrices' 
            : 'iua_system_upgrade_country_eligibility_matrices_uat';
        
        $entry = $model::findOrFail($id);
        
        // Validate the request
        $request->validate([
            'system_id' => [
                'required',
                Rule::unique($table)->where(function ($query) use ($request) {
                    return $query->where('country_code', $request->country_code);
                })->ignore($id),
            ],
            'system_name' => 'required|string|max:255',
            'country_code' => 'required|string|max:10',
            'country_name' => 'required|string|max:255',
            'ultrasound' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\.\-_]+$/',
            'sherlock' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\.\-_]+$/',
            'shell' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\.\-_]+$/',
            'dicom' => 'nullable|string|max:255|regex:/^[a-zA-Z0-9\.\-_]+$/',
            'source_filename' => 'nullable|string|max:255',
            'download_filename' => 'nullable|string|max:255',
            'product_image' => 'nullable|string|max:255',
            'installation_instructions' => 'nullable|string|max:255',
        ]);
        
        try {
            DB::beginTransaction();
            
            // Store original values for logging
            $originalValues = $entry->getOriginal();
            
            // Update the entry
            $entry->update($request->all());
            
            // Log the action
            Log::info("Device matrix entry updated", [
                'user_id' => auth()->id(),
                'environment' => $environment,
                'entry_id' => $entry->id,
                'system_id' => $entry->system_id,
                'country_code' => $entry->country_code,
                'changes' => array_diff_assoc($entry->getAttributes(), $originalValues)
            ]);
            
            DB::commit();
            
            // Determine where to redirect based on the submitted action
            $redirectAction = $request->input('submit_action', 'save_and_return');
            
            if ($redirectAction === 'save_and_continue') {
                flash('Device matrix entry updated successfully. You can continue editing.')->success();
                return redirect()->route('device.matrix.edit', [
                    'id' => $entry->id, 
                    'environment' => $environment
                ])->with('highlight', true);
            } elseif ($redirectAction === 'save_and_new') {
                flash('Device matrix entry updated successfully. You can now create a new entry.')->success();
                return redirect()->route('device.matrix.create', [
                    'environment' => $environment,
                    'last_system_id' => $entry->system_id,
                    'last_system_name' => $entry->system_name
                ]);
            } else {
                // Default: save_and_return
                flash('Device matrix entry "<strong>' . e($entry->system_name) . ' (' . e($entry->system_id) . ') - ' . e($entry->country_name) . '</strong>" updated successfully.')->success();
                return redirect()->route('device.matrix.index', ['environment' => $environment]);
            }
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update device matrix entry: ' . $e->getMessage());
            
            // Provide more user-friendly error messages
            $errorMessage = 'Failed to update device matrix entry. ';
            
            // Check for common errors and provide helpful messages
            if (strpos($e->getMessage(), 'Duplicate entry') !== false || strpos($e->getMessage(), 'UNIQUE constraint failed') !== false) {
                $errorMessage .= 'A device matrix entry with this System ID and Country Code combination already exists.';
            } elseif (strpos($e->getMessage(), 'Data too long') !== false) {
                $errorMessage .= 'One of the fields exceeds the maximum allowed length.';
            } else {
                $errorMessage .= $e->getMessage();
            }
            
            flash($errorMessage)->error();
            return redirect()->back()->withInput()->with('error_fields', array_keys($request->all()));
        }
    }

    /**
     * Remove the specified device matrix entry.
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request, $id)
    {
        $environment = $request->get('environment', 'production');
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        try {
            DB::beginTransaction();
            
            $entry = $model::findOrFail($id);
            
            // Store entry details for logging
            $entryDetails = [
                'id' => $entry->id,
                'system_id' => $entry->system_id,
                'system_name' => $entry->system_name,
                'country_code' => $entry->country_code,
                'country_name' => $entry->country_name
            ];
            
            $entry->delete();
            
            // Log the action
            Log::info("Device matrix entry deleted", [
                'user_id' => auth()->id(),
                'environment' => $environment,
                'entry_details' => $entryDetails
            ]);
            
            DB::commit();
            
            flash('Device matrix entry deleted successfully.')->success();
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete device matrix entry: ' . $e->getMessage());
            flash('Failed to delete device matrix entry: ' . $e->getMessage())->error();
        }
        
        return redirect()->route('device.matrix.index', ['environment' => $environment]);
    }

    /**
     * Show the form for creating a new system ID.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function createSystem(Request $request)
    {
        $environment = $request->get('environment', 'production');
        return view('admin.device_matrix.create_system', compact('environment'));
    }

    /**
     * Store a newly created system.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeSystem(Request $request)
    {
        $environment = $request->get('environment', 'production');
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        // Validate the request
        $request->validate([
            'system_id' => [
                'required',
                'string',
                'max:50',
                'regex:/^[a-zA-Z0-9\-_]+$/',
            ],
            'system_name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);
        
        try {
            DB::beginTransaction();
            
            // Check if this system already exists in the database
            $existingSystem = $model::where('system_id', $request->system_id)->first();
            
            if (!$existingSystem) {
                // Create a dummy entry to register the system
                // We'll use a placeholder country code that's unlikely to be used
                $model::create([
                    'system_id' => $request->system_id,
                    'system_name' => $request->system_name,
                    'country_code' => 'SYSTEM_PLACEHOLDER',
                    'country_name' => 'System Registration Placeholder',
                ]);
            }
            
            // Store the system information in session for use in the create form
            session()->flash('new_system_id', $request->system_id);
            session()->flash('new_system_name', $request->system_name);
            
            // Log the action
            Log::info("New system created for device matrix", [
                'user_id' => auth()->id(),
                'environment' => $environment,
                'system_id' => $request->system_id,
                'system_name' => $request->system_name
            ]);
            
            DB::commit();
            
            flash('New system created successfully. You can now add entries for this system.')->success();
            return redirect()->route('device.matrix.create', ['environment' => $environment]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create new system: ' . $e->getMessage());
            flash('Failed to create new system: ' . $e->getMessage())->error();
            return redirect()->back()->withInput();
        }
    }

    /**
     * Show the form for creating a new country.
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function createCountry(Request $request)
    {
        $environment = $request->get('environment', 'production');
        return view('admin.device_matrix.create_country', compact('environment'));
    }
    
    /**
     * Store a newly created country.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeCountry(Request $request)
    {
        $environment = $request->get('environment', 'production');
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        // Validate the request
        $request->validate([
            'country_code' => [
                'required',
                'string',
                'max:10',
                'regex:/^[A-Z]{2}$/',
            ],
            'country_name' => 'required|string|max:255',
            'region' => 'nullable|string|max:50',
        ]);
        
        try {
            DB::beginTransaction();
            
            // Check if this country already exists in the database
            $existingCountry = $model::where('country_code', $request->country_code)->first();
            
            if (!$existingCountry) {
                // Create a dummy entry to register the country
                // We'll use a placeholder system ID that's unlikely to be used
                $model::create([
                    'system_id' => 'COUNTRY_PLACEHOLDER',
                    'system_name' => 'Country Registration Placeholder',
                    'country_code' => $request->country_code,
                    'country_name' => $request->country_name,
                ]);
            }
            
            // Store the country information in session for use in the create form
            session()->flash('new_country_code', $request->country_code);
            session()->flash('new_country_name', $request->country_name);
            
            // Log the action
            Log::info("New country created for device matrix", [
                'user_id' => auth()->id(),
                'environment' => $environment,
                'country_code' => $request->country_code,
                'country_name' => $request->country_name,
                'region' => $request->region
            ]);
            
            DB::commit();
            
            flash('New country created successfully. You can now add entries for this country.')->success();
            return redirect()->route('device.matrix.create', ['environment' => $environment]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create new country: ' . $e->getMessage());
            flash('Failed to create new country: ' . $e->getMessage())->error();
            return redirect()->back()->withInput();
        }
    }

    /**
     * Bulk update device matrix entries.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function bulkUpdate(Request $request)
    {
        $environment = $request->get('environment', 'production');
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        $ids = $request->get('ids', []);
        $action = $request->get('bulk_action');
        $field = $request->get('bulk_field');
        $value = $request->get('bulk_value');
        
        if (empty($ids) || empty($action) || ($action === 'update' && (empty($field) || $value === null))) {
            flash('Please select entries and specify the action to perform.')->error();
            return redirect()->back();
        }
        
        try {
            DB::beginTransaction();
            
            if ($action === 'delete') {
                // Get entries for logging before deletion
                $entries = $model::whereIn('id', $ids)->get()->map(function($entry) {
                    return [
                        'id' => $entry->id,
                        'system_id' => $entry->system_id,
                        'country_code' => $entry->country_code
                    ];
                })->toArray();
                
                // Bulk delete
                $model::whereIn('id', $ids)->delete();
                
                // Log the action
                Log::info("Bulk delete performed on device matrix entries", [
                    'user_id' => auth()->id(),
                    'environment' => $environment,
                    'count' => count($ids),
                    'entries' => $entries
                ]);
                
                flash(count($ids) . ' device matrix entries deleted successfully.')->success();
                
            } elseif ($action === 'update') {
                // Validate the field value based on the field
                if (in_array($field, ['ultrasound', 'sherlock', 'shell', 'dicom'])) {
                    if (!preg_match('/^[a-zA-Z0-9\.\-_]+$/', $value)) {
                        throw new \Exception("Invalid format for {$field}. Only alphanumeric characters, dots, hyphens, and underscores are allowed.");
                    }
                }
                
                // Get entries for logging before update
                $entries = $model::whereIn('id', $ids)->get()->map(function($entry) {
                    return [
                        'id' => $entry->id,
                        'system_id' => $entry->system_id,
                        'country_code' => $entry->country_code
                    ];
                })->toArray();
                
                // Bulk update
                $model::whereIn('id', $ids)->update([$field => $value]);
                
                // Log the action
                Log::info("Bulk update performed on device matrix entries", [
                    'user_id' => auth()->id(),
                    'environment' => $environment,
                    'count' => count($ids),
                    'field' => $field,
                    'value' => $value,
                    'entries' => $entries
                ]);
                
                flash(count($ids) . ' device matrix entries updated successfully.')->success();
            }
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to perform bulk action: ' . $e->getMessage());
            flash('Failed to perform bulk action: ' . $e->getMessage())->error();
        }
        
        return redirect()->route('device.matrix.index', ['environment' => $environment]);
    }

    /**
     * Export device matrix entries to CSV.
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Request $request)
    {
        $environment = $request->get('environment', 'production');
        $model = $environment === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        // Check if specific IDs were requested (from selected checkboxes)
        $selectedIds = $request->get('selected_ids', []);
        
        // Apply filters if provided
        $query = $model::query();
        
        if (!empty($selectedIds)) {
            // If specific IDs were requested, use those instead of filters
            $query->whereIn('id', $selectedIds);
        } else {
            // Otherwise apply the filters
            if ($request->has('system_id') && !empty($request->system_id)) {
                $query->where('system_id', $request->system_id);
            }
            
            if ($request->has('country_code') && !empty($request->country_code)) {
                $query->where('country_code', $request->country_code);
            }
            
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('system_id', 'like', "%{$search}%")
                      ->orWhere('system_name', 'like', "%{$search}%")
                      ->orWhere('country_code', 'like', "%{$search}%")
                      ->orWhere('country_name', 'like', "%{$search}%");
                });
            }
        }
        
        $entries = $query->get();
        
        // Log the export
        Log::info("Device matrix entries exported", [
            'user_id' => auth()->id(),
            'environment' => $environment,
            'count' => $entries->count(),
            'export_type' => !empty($selectedIds) ? 'selected' : 'filtered',
            'selected_count' => !empty($selectedIds) ? count($selectedIds) : 0,
            'filters' => $request->only(['system_id', 'country_code', 'search'])
        ]);
        
        $exportType = !empty($selectedIds) ? 'selected' : 'filtered';
        $filename = 'device_matrix_' . $environment . '_' . $exportType . '_' . date('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        
        $callback = function() use ($entries) {
            $file = fopen('php://output', 'w');
            
            // Add headers
            fputcsv($file, [
                'system_id',
                'system_name',
                'country_code',
                'country_name',
                'ultrasound',
                'sherlock',
                'shell',
                'dicom',
                'source_filename',
                'download_filename',
                'product_image',
                'installation_instructions',
            ]);
            
            // Add data
            foreach ($entries as $entry) {
                fputcsv($file, [
                    $entry->system_id,
                    $entry->system_name,
                    $entry->country_code,
                    $entry->country_name,
                    $entry->ultrasound,
                    $entry->sherlock,
                    $entry->shell,
                    $entry->dicom,
                    $entry->source_filename,
                    $entry->download_filename,
                    $entry->product_image,
                    $entry->installation_instructions,
                ]);
            }
            
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }
    
    /**
     * Copy entries from one environment to another.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function copyEnvironment(Request $request)
    {
        $sourceEnv = $request->get('source_environment');
        $targetEnv = $request->get('target_environment');
        $selectedIds = $request->get('selected_ids', []);
        
        if (empty($sourceEnv) || empty($targetEnv) || $sourceEnv === $targetEnv) {
            flash('Please select different source and target environments.')->error();
            return redirect()->back();
        }
        
        $sourceModel = $sourceEnv === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
            
        $targetModel = $targetEnv === 'production' 
            ? IuaSystemUpgradeCountryEligibilityMatrix::class 
            : IuaSystemUpgradeCountryEligibilityMatrixUat::class;
        
        try {
            DB::beginTransaction();
            
            // Get entries to copy
            $query = $sourceModel::query();
            if (!empty($selectedIds)) {
                $query->whereIn('id', $selectedIds);
            }
            $entries = $query->get();
            
            if ($entries->isEmpty()) {
                throw new \Exception('No entries found to copy.');
            }
            
            $copied = 0;
            $updated = 0;
            $skipped = 0;
            
            foreach ($entries as $entry) {
                // Check if entry already exists in target environment
                $existingEntry = $targetModel::where('system_id', $entry->system_id)
                    ->where('country_code', $entry->country_code)
                    ->first();
                
                if ($existingEntry) {
                    // Update existing entry
                    $existingEntry->update([
                        'system_name' => $entry->system_name,
                        'country_name' => $entry->country_name,
                        'ultrasound' => $entry->ultrasound,
                        'sherlock' => $entry->sherlock,
                        'shell' => $entry->shell,
                        'dicom' => $entry->dicom,
                        'source_filename' => $entry->source_filename,
                        'download_filename' => $entry->download_filename,
                        'product_image' => $entry->product_image,
                        'installation_instructions' => $entry->installation_instructions,
                    ]);
                    $updated++;
                } else {
                    // Create new entry
                    $targetModel::create([
                        'system_id' => $entry->system_id,
                        'system_name' => $entry->system_name,
                        'country_code' => $entry->country_code,
                        'country_name' => $entry->country_name,
                        'ultrasound' => $entry->ultrasound,
                        'sherlock' => $entry->sherlock,
                        'shell' => $entry->shell,
                        'dicom' => $entry->dicom,
                        'source_filename' => $entry->source_filename,
                        'download_filename' => $entry->download_filename,
                        'product_image' => $entry->product_image,
                        'installation_instructions' => $entry->installation_instructions,
                    ]);
                    $copied++;
                }
            }
            
            // Log the action
            Log::info("Device matrix entries copied between environments", [
                'user_id' => auth()->id(),
                'source_environment' => $sourceEnv,
                'target_environment' => $targetEnv,
                'total_entries' => $entries->count(),
                'copied' => $copied,
                'updated' => $updated,
                'skipped' => $skipped
            ]);
            
            DB::commit();
            
            flash("Environment copy completed: {$copied} entries copied, {$updated} entries updated.")->success();
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to copy between environments: ' . $e->getMessage());
            flash('Failed to copy between environments: ' . $e->getMessage())->error();
        }
        
        return redirect()->route('device.matrix.index', ['environment' => $targetEnv]);
    }
}
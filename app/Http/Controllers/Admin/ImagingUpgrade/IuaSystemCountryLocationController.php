<?php

namespace App\Http\Controllers\Admin\ImagingUpgrade;

use App\Http\Controllers\Controller;
use App\Models\ImagingUpgrade\IuaSystemCountryLocation;
use Illuminate\Http\Request;

class IuaSystemCountryLocationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(IuaSystemCountryLocation $iuaSystemCountryLocation)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(IuaSystemCountryLocation $iuaSystemCountryLocation)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, IuaSystemCountryLocation $iuaSystemCountryLocation)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(IuaSystemCountryLocation $iuaSystemCountryLocation)
    {
        //
    }
}

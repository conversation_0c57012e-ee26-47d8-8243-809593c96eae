<?php

namespace App\Http\Controllers\Admin\Site;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Site\CreateSiteVideoRequest;
use App\Models\Site\SitePage;
use App\Models\Site\SiteVideo;
use App\Models\Site\SiteVideoFile;
use App\Services\UploadFileService;
use FFmpegMovie;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Image;
use Storage;

class SiteVideosController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        $site_videos = SiteVideo::get();

        return view('admin.site.video.index', compact('site_videos'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $site_pages = SitePage::join('site_categories', 'site_pages.category_id', '=', 'site_categories.id')->select(DB::raw("CONCAT(site_pages.title, ' - ', site_categories.short_name) as title, site_pages.id"))->where('site_pages.active', 1)->pluck('title', 'id')->all();

        return view('admin.site.video.create', compact('site_pages'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return Response
     */
    public function store(CreateSiteVideoRequest $request)
    {
        $video = SiteVideo::create(['tracking_number' => $request->tracking_number,
            'unique_id' => strtolower(str_random(8)),
            'title' => $request->title,
            'site_pages' => $request->site_pages,
        ]);

        //get the selected site pages and save them to the pivot table
        $site_pages = $request->input('site_pages');

        $this->saveSelectedPages($video->id, $site_pages);

        return $this->upload($video->id);
    }

    /**
     * Save the file to your site's storage and database - each file is sent in a separate request!
     *
     * @return View
     */
    public function storeupload()
    {

        // Okay - let's grab all the form input and put it in array, which is basically the name, size and token
        $request = \Request::all();

        // Let's separate out the files into their own array
        $files = $_FILES;

        // Send the request to a Upload file Service Provider
        $target_dir = public_path().'/assets/video';
        $temp_dir = storage_path().'/app/uploads/video/temp';
        $allowed_file_types = ['mp4', 'webm'];
        $upload = new UploadFileService($request, $files, $target_dir, $temp_dir, $allowed_file_types);

        // Part of preflight check

        // $this->error[] and $this->message[] available to us through PreFlightCheck() in UploadFileService
        // First, check that the chunk_file_extension is a mp4 or webm => $upload->chunk_file_extension; gives "pdf" (as does $upload->source_filename_extension)
        if (in_array($upload->chunk_file_extension, $allowed_file_types) !== true) {
            $upload->error[] = 'Sorry, but '.$upload->source_filename.'--'.$upload->chunk_file_extension.' must be a MP4 or WebM video.';
        } else {

            // CONTINUE WITH OTHER CHECKS

            // Second, check that the soure_filename is formatted correctly begins with the same numbers as the parent_id => $upload->source_filename; gives "1234_xx.pdf" (as does $upload->source_filename_clean)
            $filename_parts = explode('_', pathinfo($upload->source_filename, PATHINFO_FILENAME));

            // CONTINUE WITH OTHER CHECKS

            // check first part of the file name to make sure it matches tha parent artwork part number
            if ($filename_parts[0] !== $request['tracking_number']) {
                $upload->error[] = 'Sorry, but '.$upload->source_filename.' prefix ('.$filename_parts[0].') does not match the tracking number ('.$request['tracking_number'].'). Please check that it is named correctly and try again.';
            }
        } // END OF FIRST CHECK

        if (! empty($upload->error)) {
            return \Response::json(['error' => $upload->error]);
        } elseif (! empty($upload->message)) {
            return \Response::json(['message' => $upload->message]);
        }

        // End Preflight Check

        // Upload checks passed, let the file through
        $result = $upload->uploadFile();

        if ($result['success'] == true) {

            // Save the info to the DB
            $record = SiteVideo::findOrFail($request['parent_id']);

            if ($record) {
                try {
                    SiteVideoFile::create([
                        'video_id' => $record->id,
                        'filename' => $upload->source_filename_clean,
                        'fileformat' => $upload->source_filename_extension,
                        'filesize' => $upload->source_filesize,
                    ]);
                } catch (Exception $e) {
                    return $upload->error[] = 'Sorry, but there was an error creating the database record for this file.';
                } // end try/catch
            } // end if record
        }

        // Now we'll return the AJAX response in JSON format back to the browser
        if (is_array($result)) {
            return \Response::json(['output' => 'Your file was uploaded, playa!']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function show($id) {}

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit($id)
    {
        $video = SiteVideo::with('pages')->findOrFail($id);
        $site_pages = SitePage::join('site_categories', 'site_pages.category_id', '=', 'site_categories.id')->select(DB::raw("CONCAT(site_pages.title, ' - ', site_categories.short_name) as title, site_pages.id"))->where('site_pages.active', 1)->pluck('title', 'id')->all();

        return view('admin.site.video.edit', compact('video', 'site_pages'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function update($id, CreateSiteVideoRequest $request)
    {
        $video = SiteVideo::findOrFail($id);
        $video->fill($request->input())->save();

        //get the selected site pages and save them to the pivot table
        $site_pages = $request->input('site_pages');

        $this->saveSelectedPages($video->id, $site_pages);

        return $this->upload($id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroy($id)
    {
        try {

            // Save the info to the DB
            $record = SiteVideo::with('files')->findOrFail($id);

            $title = $record->title;

            if ($record['files']) {

                //if( is_array($record['files']) ){

                $count = 0;

                foreach ($record['files'] as $file) {
                    $filepath = public_path().'\assets\video\\'.$file->filename;
                    if ($this->deleteFile($filepath) == true) {
                        $count++;
                    }
                }
                //}
            }

            $record = SiteVideo::destroy($id);
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $id, 'record_title' => $title]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroyFile($id)
    {
        try {

            // Save the info to the DB
            $record = SiteVideoFile::findOrFail($id);

            // if there is not a filename in the database then don't set a path to delete. could try to delete the whole folder:  public_path(). '\assets\video\
            if ($record->filename) {
                $file = public_path().'\assets\video\\'.$record->filename;
            } else {
                // otherwise still delete the record.
                $record->delete();

                // Send them to their view -> in this case, the upload file page view
                return \Response::json(['status' => 'success', 'record_id' => $id, 'record_title' => 'The record was deleted. But, there was no file to delete.']);
            }

            if (file_exists($file)) {
                if (unlink($file)) {
                    $record->delete();

                    // Send them to their view -> in this case, the upload file page view
                    return \Response::json(['status' => 'success', 'record_id' => $id, 'record_title' => $record->filename]);
                }
            } else {
                // if there is a filename in the record but not an actual file in the system. still delete the record.
                $record->delete();

                // Send them to their view -> in this case, the upload file page view
                return \Response::json(['status' => 'success', 'record_id' => $id, 'record_title' => 'The record was deleted. But, there was no file to delete.']);
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function deleteFile($filepath)
    {
        if (file_exists($filepath)) {
            if (unlink($filepath)) {
                return true;
            }
        }
    }

    /**
     * Add a file to an artcle.
     *
     * @return Response
     */
    public function upload($id)
    {
        // Get the record
        $video = SiteVideo::with('files')->findOrFail($id);

        // Send them to their view
        return view('admin.site.video.upload', compact('video'));
    }

    /**
     * Change the status of a Record.
     *
     * @return View
     */
    public function changeStatus($id)
    {
        try {
            // Save the info to the DB
            $record = SiteVideo::findOrFail($id);
            $title = $record->title;
            if ($record->active == 1) {
                $record->active = 2;
                $status = 'inactive';
            } else {
                $record->active = 1;
                $status = 'active';
            }
            $record->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $id, 'record_status' => $status, 'record_title' => $title]);
    }

    /**
     * update the site pages map.
     *
     * @param  int  $video_id
     * @return Response
     */
    public function saveSelectedPages($video_id, array $site_pages)
    {
        $video = SiteVideo::find($video_id);
        $video->pages()->sync($site_pages); // sync will handle the detach and attach methods automatically.
    }

    /**
     * Show the record.
     *
     * @return Redirect
     */
    public function getFiles($id)
    {
        // Get the record
        $video = SiteVideo::with('files')->findOrFail($id);

        $token = csrf_token();

        $route = route('site.video.destroy.file', [$video->id]);

        if ($video) {

            // Let's set some initial variables
            $output = '<table id="myTable" class="languages_table display" cellspacing="0" width="100%">
					<thead>
						<tr>
							<th class="col-md-9 alignleft">FileName</th>
							<th class="col-md-1" style="width:50px;">Actions</th>
						</tr>
					</thead>
					<tbody id="tablebody_output">';

            foreach ($video['files'] as $file) {
                $output .= '
						<tr id="tr'.$file->id.'" class="list_item" style="padding:2px 4px 0px 0px;border-top:0px; border-right:0px;border-left:0px;">
							<td class="col-md-9"><a href="assets/literature/'.$file->filename.'">'.$file->filename.'</a></td>
							<td class="col-md-1 aligncenter"><span class="trash_icon table_icon" style="width: 100%;"><a id="delete-record-'.$file->id.'" data-record-title="'.$file->title.'" onclick="recordStatus(\'delete\', \''.$route.'\', {id: '.$file->id.', _token: \''.$token.'\'})"><i class="fa fa-trash trash-btn" id="updateTrashIcon-'.$file->id.'"></i></a><span></td>
						</tr>';
            }

            $output .= '</tbody>
				</table>';

            // Return it back to them
            return \Response::json(['output' => $this->prepJsonOutput($output)]);
        } else {
            return false;
        }
    }

    public function prepJsonOutput($output)
    {
        //$output = addslashes( $output );
        return str_replace(["\b", "\f", "\n", "\r", "\t", "\'"], ['', '', '', '', '', "'"], $output); // remove control characters
    }

    public function poster($id)
    {
        // Get the record
        $video = SiteVideo::with('files')->findOrFail($id);

        // Send them to their view
        return view('admin.site.video.poster', compact('video'));
    }

    public function createPoster($id, $time)
    {
        // Get the record
        $video = SiteVideo::with(['files' => function ($query) {
            $query->where('fileformat', 'mp4')->first();
        }])->findOrFail($id);

        $filename = $video->files[0]->filename;
        $unique_id = $video->unique_id;
        $create_images = $this->createVideoImages($unique_id, $filename, $time);

        if ($create_images == true) {
            $output = '
			<div id="preview_items">
			  <hr>
			  <h2 style="margin:0 0 20px 0">Preview Images</h2>
			';
            $output .= '
			  <div">
				<div>Poster Image: 720px wide</div>
				<div><img src="'.url('/assets/video/images/'.$unique_id.'_poster.jpg').'">
				</div>
			  </div>
			  ';
            $output .= '
			  <div">
				<div>Thumbnail Image: 215px wide </div>
				<div><img src="'.url('/assets/video/images/'.$unique_id.'_thumb.jpg').'">
				</div>
			  </div>
			  ';
            $output .= '
			  <div">
				<div>Mobile Thumbnail Image: 95px wide</div>
				<div><img src="'.url('/assets/video/images/'.$unique_id.'_sml_thumb.jpg').'">
				</div>
			  </div>
			</div>
			  ';
        } else {
            $output = '
			  <div">
				<div>Sorry, there was creating the set of images for this video.</div>
			  </div>
			  ';
        }

        // Return it back to them
        return \Response::json(['output' => $this->prepJsonOutput($output)]);
    }

    public function createVideoImages($unique_id, $filename, $time)
    {
        $movie = new FFmpegMovie(public_path().'/assets/video/'.$filename);            // Input fileformates: mov, avi, mpg, wmv, mp4, ogv...
        $poster_width = $movie->getFrameWidth();                            // Return the width of the movie in pixels.
        $poster_height = $movie->getFrameHeight();                        // Return the height of the movie in pixels.
        $playtime = $this->secondsToTimeCode($movie->getDuration());        // Return the height of the movie in pixels.

        //update_video_information ($unique_id, $poster_width, $poster_height, $playtime);

        $framerate = $movie->getFrameRate();

        $frame = ($time > 0) ? $movie->getFrame(round($framerate * $time)) : $movie->getFrame(1);                                    // Returns a frame from the movie as an ffmpeg_frame object. Returns false if the frame was not found.
        $frameimage = $frame->toGDImage();                                // Returns a truecolor GD image of the frame.

        $image_sizes = ['720' => '_poster.jpg', '215' => '_thumb.jpg', '95' => '_sml_thumb.jpg'];

        foreach ($image_sizes as $size => $type) {
            $img = Image::make($frameimage)->resize($size, null, function ($constraint) {
                $constraint->aspectRatio();
            });
            $img->save(public_path().'/assets/video/images/'.$unique_id.$type);
        }

        return (File::exists(public_path().'/assets/video/images/'.$unique_id.'_poster.jpg')) ? true : false;
    }

    /**
     * Convert seconds to video timecode format.
     *
     * @param  string  $time
     * @return string
     */
    public function SecondsToTimeCode($time)
    {
        if (is_numeric($time)) {
            $value = ['years' => '00', 'days' => '00', 'hours' => '00', 'minutes' => '00', 'seconds' => '00'];

            if ($time >= 31556926) {
                $value['years'] = floor($time / 31556926);
                $time = ($time % 31556926);
            }
            if ($time >= 86400) {
                $value['days'] = floor($time / 86400);
                $time = ($time % 86400);
            }
            if ($time >= 3600) {
                $value['hours'] = str_pad(floor($time / 3600), 2, '0', STR_PAD_LEFT);
                $time = ($time % 3600);
            }
            if ($time >= 60) {
                $value['minutes'] = str_pad(floor($time / 60), 2, '0', STR_PAD_LEFT);
                $time = ($time % 60);
            }
            $value['seconds'] = str_pad(floor($time), 2, '0', STR_PAD_LEFT);

            $value = array_unique($value);
            $array_count = count($value);
            if ($array_count > 2) {
                array_shift($value);
            }

            return $timecode = implode(':', $value);
        } else {
            return (bool) false;
        }
    }
}

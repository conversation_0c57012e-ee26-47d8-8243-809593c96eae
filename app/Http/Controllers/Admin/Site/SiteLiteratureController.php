<?php

namespace App\Http\Controllers\Admin\Site;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Site\CreateSiteLiteratureRequest;
use App\Models\Site\SiteLiterature;
use App\Models\Site\SiteLiteratureTypes;
use App\Models\Site\SitePage;
use App\Services\UploadFileService;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Storage;

class SiteLiteratureController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        $site_literature = SiteLiterature::orderBy('tracking_number')->get();

        return view('admin.site.literature.index', compact('site_literature'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $literature_types = SiteLiteratureTypes::orderBy('title', 'ASC')->pluck('title', 'id')->all();
        //$site_pages = SitePage::join('site_categories', 'site_pages.category_id', '=', 'site_categories.id')->select(DB::raw("CONCAT(site_pages.title, ' - ', site_categories.short_name) as title, site_pages.id"))->where('site_pages.active', 1)->pluck('title', 'id')->all();
        $site_pages = SitePage::join('site_categories', 'site_pages.category_id', '=', 'site_categories.id')->select(DB::raw("CONCAT(site_pages.title, ' - ', site_categories.short_name) as title, site_pages.id"))->pluck('title', 'id')->all();

        return view('admin.site.literature.create', compact('literature_types', 'site_pages'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return Response
     */
    public function store(CreateSiteLiteratureRequest $request)
    {
        $literature = SiteLiterature::create($request->all());

        //get the selected site pages and save them to the pivot table
        $site_pages = $request->input('site_pages');

        if (is_array($site_pages)) {
            $this->saveSelectedPages($literature->id, $site_pages);
        }

        return $this->upload($literature->id);
    }

    /**
     * Save the file to your site's storage and database - each file is sent in a separate request!
     *
     * @return View
     */
    public function storeupload()
    {

        // Okay - let's grab all the form input and put it in array, which is basically the name, size and token
        $request = \Request::all();

        // Let's separate out the files into their own array
        $files = $_FILES;

        // Send the request to a Upload file Service Provider
        $target_dir = storage_path().'/app/public/literature';
        $temp_dir = storage_path().'/app/uploads/literature/temp';
        $upload = new UploadFileService($request, $files, $target_dir, $temp_dir, ['pdf']);

        // Part of preflight check

        // $this->error[] and $this->message[] available to us through PreFlightCheck() in UploadFileService
        // First, check that the chunk_file_extension is a PDF => $upload->chunk_file_extension; gives "pdf" (as does $upload->source_filename_extension)
        if ($upload->chunk_file_extension !== 'pdf') {
            $upload->error[] = 'Sorry, but '.$upload->source_filename.' must be a pdf.';
        } else {

            // CONTINUE WITH OTHER CHECKS

            // Second, check that the soure_filename is formatted correctly begins with the same numbers as the parent_id => $upload->source_filename; gives "1234_xx.pdf" (as does $upload->source_filename_clean)
            $filename_parts = explode('_', pathinfo($upload->source_filename, PATHINFO_FILENAME));

            // CONTINUE WITH OTHER CHECKS

            // check first part of the file name to make sure it matches tha parent artwork part number
            if ($filename_parts[0] !== $request['tracking_number']) {
                $upload->error[] = 'Sorry, but '.$upload->source_filename.' prefix ('.$filename_parts[0].') does not match the tracking number ('.$request['tracking_number'].'). Please check that it is named correctly and try again.';
            }
        } // END OF FIRST CHECK

        if (! empty($upload->error)) {
            return \Response::json(['error' => $upload->error]);
        } elseif (! empty($upload->message)) {
            return \Response::json(['message' => $upload->message]);
        }

        // End Preflight Check

        // Save the info to the DB
        $record = SiteLiterature::findOrFail($request['parent_id']);

        if ($record) {
            try {
                $record->fill([
                    'filename' => $upload->source_filename_clean,
                    'fileformat' => $upload->source_filename_extension,
                    'filesize' => $upload->source_filesize,
                ])->save();
            } catch (Exception $e) {
                return $upload->error[] = 'Sorry, but there was an error creating the database record for this file.';
            } // end try/catch
        } // end if record

        // Upload checks passed, let the file through
        $result = $upload->uploadFile();

        // Now we'll return the AJAX response in JSON format back to the browser
        if (is_array($result)) {
            return \Response::json(['output' => 'Your file was uploaded, playa!']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function show($id) {}

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit($id)
    {
        $site_literature = SiteLiterature::with('pages')->findOrFail($id);
        $literature_types = SiteLiteratureTypes::orderBy('title', 'ASC')->pluck('title', 'id')->all();
        //$site_pages = SitePage::join('site_categories', 'site_pages.category_id', '=', 'site_categories.id')->select(DB::raw("CONCAT(site_pages.title, ' - ', site_categories.short_name) as title, site_pages.id"))->where('site_pages.active', 1)->pluck('title', 'id')->all();
        $site_pages = SitePage::join('site_categories', 'site_pages.category_id', '=', 'site_categories.id')->select(DB::raw("CONCAT(site_pages.title, ' - ', site_categories.short_name) as title, site_pages.id"))->pluck('title', 'id')->all();

        return view('admin.site.literature.edit', compact('site_literature', 'literature_types', 'site_pages'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function update($id, CreateSiteLiteratureRequest $request)
    {
        $literature = SiteLiterature::findOrFail($id);
        $literature->fill($request->input())->save();

        //get the selected site pages and save them to the pivot table
        $site_pages = $request->input('site_pages');

        $this->saveSelectedPages($literature->id, $site_pages);

        return $this->upload($id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroy($id)
    {
        try {

            // Save the info to the DB
            $record = SiteLiterature::findOrFail($id);
            $title = $record->title;

            if ($record->filename) {
                $filepath = storage_path().'/app/public/literature/'.$record->filename;

                if ($this->deleteFile($filepath) != true) {
                    return \Response::json(['status' => 'fail']);
                }
            }

            $record = SiteLiterature::destroy($id);
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $id, 'record_title' => $title]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroyFile($id)
    {
        try {

            // Save the info to the DB
            $record = SiteLiterature::findOrFail($id);
            $filename = $record->filename;
            $file = storage_path().'/app/public/literature/'.$record->filename;

            if (file_exists($file)) {
                if (unlink($file)) {
                    $record->filename = '';
                    $record->fileformat = '';
                    $record->filesize = '';
                    $record->save();

                    // Send them to their view -> in this case, the upload file page view
                    return \Response::json(['status' => 'success', 'record_id' => $id, 'record_title' => $filename]);
                }
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function deleteFile($filepath)
    {
        if (file_exists($filepath)) {
            if (unlink($filepath)) {
                return true;
            }
        }
    }

    /**
     * Add a file to an artcle.
     *
     * @return Response
     */
    public function upload($id)
    {
        // Get the record
        $site_literature = SiteLiterature::findOrFail($id);

        // Send them to their view
        return view('admin.site.literature.upload', compact('site_literature'));
    }

    /**
     * Change the status of a Record.
     *
     * @return View
     */
    public function changeStatus($id)
    {
        try {
            // Save the info to the DB
            $record = SiteLiterature::findOrFail($id);
            $title = $record->title;
            if ($record->active == 1) {
                $record->active = 2;
                $status = 'inactive';
            } else {
                $record->active = 1;
                $status = 'active';
            }
            $record->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $id, 'record_status' => $status, 'record_title' => $title]);
    }

    /**
     * update the site pages map.
     *
     * @param  int  $literature_id
     * @return Response
     */
    public function saveSelectedPages($literature_id, array $site_pages)
    {
        $literature = SiteLiterature::find($literature_id);
        $literature->pages()->sync($site_pages); // sync will handle the detach and attach methods automatically.
    }

    /**
     * Show the record.
     *
     * @return Redirect
     */
    public function getfiles($id)
    {
        // Get the record
        $literature = SiteLiterature::findOrFail($id);

        $token = csrf_token();
        // Set the language in plain text
        $route = route('site.literature.destroy.file', [$literature->id]);

        if ($literature) {

            // Let's set some initial variables
            $output = '<table id="myTable" class="languages_table display" cellspacing="0" width="100%">
					<thead>
						<tr>
							<th class="col-md-9 alignleft">FileName</th>
							<th class="col-md-1" style="width:50px;">Actions</th>
						</tr>
					</thead>
					<tbody id="tablebody_output">
						<tr id="tr'.$literature->id.'" class="list_item" style="padding:2px 4px 0px 0px;border-top:0px; border-right:0px;border-left:0px;">
							<td class="col-md-9"><a href="assets/literature/'.$literature->filename.'">'.$literature->filename.'</a></td>
							<td class="col-md-1 aligncenter"><span class="trash_icon table_icon" style="width: 100%;"><a id="delete-record-'.$literature->id.'" data-record-title="'.$literature->title.'" onclick="recordStatus(\'delete\', \''.$route.'\', {id: '.$literature->id.', _token: \''.$token.'\'})"><i class="fa fa-trash trash-btn" id="updateTrashIcon-'.$literature->id.'"></i></a><span></td>
						</tr>
					</tbody>
				</table>';

            // Return it back to them
            return \Response::json(['output' => $this->prepJsonOutput($output)]);
        } else {
            return false;
        }
    }

    public function prepJsonOutput($output)
    {
        //$output = addslashes( $output );
        return str_replace(["\b", "\f", "\n", "\r", "\t", "\'"], ['', '', '', '', '', "'"], $output); // remove control characters
    }
}

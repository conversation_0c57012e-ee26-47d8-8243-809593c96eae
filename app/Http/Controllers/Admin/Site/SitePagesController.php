<?php

namespace App\Http\Controllers\Admin\Site;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Site\CreateSitePageRequest;
use App\Models\Site\SiteCategory;
use App\Models\Site\SitePage;
use App\Models\Site\SiteRegion;
use App\Services\UploadFileService;
use Illuminate\Http\Request;

class SitePagesController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        $site_pages = SitePage::with('category', 'literature', 'videos')->orderBy('active', 'asc')->orderBy('title', 'asc')->get();

        return view('admin.site.page.index', compact('site_pages'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $site_categories = SiteCategory::pluck('title', 'id')->all();
        $site_regions = SiteRegion::pluck('title', 'id')->all();

        return view('admin.site.page.create', compact('site_categories', 'site_regions'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return Response
     */
    public function store(CreateSitePageRequest $request)
    {
        $category = SiteCategory::findOrFail($request->category_id);

        $site_page = SitePage::create($request->all());

        $site_page->category_name = $category->title;
        $site_page->save();

        return $this->index();
    }

    /**
     * Save the file to your site's storage and database - each file is sent in a separate request!
     *
     * @return View
     */
    public function storefile()
    {
        // Okay - let's grab all the form input and put it in array, which is basically the name, size and token
        $request = \Request::all();

        // Let's separate out the files into their own array
        $files = $_FILES;

        // Send the request to a Upload file Service Provider
        $upload = new UploadFileService($request, $files, 'literature');
        $result = $upload->uploadFile();

        // Use a Service Provider to update the DB OR Just create the record here in the controller.... probably easier
        // $this->create_record($file, $record_id)

        // Now we'll return the AJAX response in JSON format back to the browser
        if (is_array($result)) {
            return \Response::json($result);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function show($id) {}

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit($id)
    {
        $site_pages = SitePage::with('regions')->findOrFail($id);
        $site_regions = SiteRegion::pluck('title', 'id')->all();
        // Get Site Categories and subcategories and output the collection with the keys by site_categories.id
        // ->keyBy('id') will return the collection but with keys being the values of id attribute from any model.
        $site_categories = SiteCategory::with('subcategories')->get()->keyBy('id');

        return view('admin.site.page.edit', compact('site_pages', 'site_categories', 'site_regions'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function update($id, CreateSitePageRequest $request)
    {
        $site_page = SitePage::findOrFail($id);
        $category = SiteCategory::findOrFail($request->category_id);
        $site_page->fill($request->input());
        $site_page->category_name = $category->title;
        $site_page->save();

        //get the selected site pages and save them to the pivot table
        $site_regions = is_array($request->input('site_regions')) ? $request->input('site_regions') : [];

        $this->saveSelectedRegions($site_page->id, $site_regions);

        return $this->index();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroy($id)
    {
        try {

            // Save the info to the DB
            $record = SitePage::findOrFail($id);
            $title = $record->title;
            $record = SitePage::destroy($id);
            //$record->delete();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $id, 'record_title' => $title]);
    }

    /**
     * Add a file to an artcle.
     *
     * @return Response
     */
    public function upload($id)
    {
        // Get the record
        $site_pages = SitePage::findOrFail($id);

        // Send them to their view
        return view('admin.site.page.upload', compact('site_pages'));
    }

    /**
     * get page and all literature tied to it.
     *
     * @return Response
     */
    public function literature($id)
    {
        // Get the record
        $site_pages = SitePage::with('literature')->findOrFail($id);

        // Send them to their view
        return view('admin.site.page.literature', compact('site_pages'));
    }

    /**
     * get page and all videos tied to it.
     *
     * @return Response
     */
    public function videos($id)
    {
        // Get the record
        $site_pages = SitePage::with('videos')->findOrFail($id);

        // Send them to their view
        return view('admin.site.page.videos', compact('site_pages'));
    }

    /**
     * Change the status of a Record.
     *
     * @return View
     */
    public function changeStatus($id)
    {
        try {
            // Save the info to the DB
            $record = SitePage::findOrFail($id);
            $title = $record->title;
            if ($record->active == 1) {
                $record->active = 2;
                $status = 'inactive';
            } else {
                $record->active = 1;
                $status = 'active';
            }
            $record->save();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $id, 'record_status' => $status, 'record_title' => $title]);
    }

    /**
     * update the site page regions.
     *
     * @param  int  $page_id
     * @return Response
     */
    public function saveSelectedRegions($page_id, array $site_regions)
    {
        $site_page = SitePage::findOrFail($page_id);
        $site_page->regions()->sync($site_regions); // sync will handle the detach and attach methods automatically.
    }
}

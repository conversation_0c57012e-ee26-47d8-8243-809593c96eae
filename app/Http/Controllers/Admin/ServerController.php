<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ErrorLog;

class ServerController extends Controller
{
    /**
     * Display index page view.
     * GET /sitecategories.
     *
     * @return Response
     */
    public function index()
    {
        if (view()->exists('admin.server.index')) {
            return view('admin.server.index');
        } else {
            return view('errors.404');
        }
    }

    /**
     * Display error log page view.
     * GET /sitecategories.
     *
     * @return Response
     */
    public function errors()
    {
        $errors = ErrorLog::withTrashed()->orderBy('id', 'desc')->get();

        if (view()->exists('admin.server.errors')) {
            return view('admin.server.errors', compact('errors'));
        } else {
            return view('errors.404');
        }
    }

    /**
     * Change the status of a Record.
     *
     * @return View
     */
    public function changeStatus($errorId)
    {
        try {

            // Save the info to the DB
            $record = ErrorLog::withTrashed()->find($errorId);
            if ($record->deleted_at == null) {
                $this->destroy($errorId);
            } else {
                $this->restore($errorId);
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }
    }

    /**
     * Soft deletes the record.
     *
     * @return View
     */
    public function destroy($errorId)
    {
        try {
            // Save the info to the DB
            $record = ErrorLog::withTrashed()->find($errorId);
            $title = $record->title;
            $record->delete();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $errorId, 'record_status' => $title, 'record_title' => $title]);
    }

    /**
     * Restores the users status.
     *
     * @return Response
     */
    public function restore($errorId)
    {
        try {
            // Save the info to the DB
            $record = ErrorLog::withTrashed()->find($errorId);
            $title = $record->title;
            $record->restore();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $errorId, 'record_status' => $title, 'record_title' => $title]);
    }

    /**
     * Delete the record permanately.
     *
     * @return View
     */
    public function forceDelete($errorId)
    {
        try {
            // Save the info to the DB
            $record = ErrorLog::withTrashed()->find($errorId);
            $title = $record->title;
            $record->forceDelete();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $errorId, 'record_status' => $title, 'record_title' => $title]);
    }
}

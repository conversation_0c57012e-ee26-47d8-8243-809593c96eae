<?php

namespace App\Http\Controllers\Admin\ProductCatalog;

use App\Http\Controllers\Controller;
use App\Models\ProductCatalog\CatalogCategory;
use App\Models\ProductCatalog\CategoryField;
use App\Models\ProductCatalog\FieldOption;
use App\Models\ProductCatalog\OptionsMap;
use App\Models\ProductCatalog\ProductCode;

class ProductCatalogController extends Controller
{
    public $product_codes_map = [];

    public $descriptions_map = [];

    public $options_map = [];

    public $images_map = [];

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        return view('admin.catalog.index', ['categories' => $this->catalogCategories()]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function category($category_id)
    {
        $category = CatalogCategory::findOrFail($category_id);
        $fields = $this->getCategoryFields($category->id);
        $products = $this->showProducts($category->id);

        $this->generateJavascript($category->id);

        return view('admin.catalog.category', compact('fields', 'products', 'category'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function editProductCode($id) {}

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function editCategoryField($id) {}

    /**
     * Show all Product Catalog Categories.
     *
     * @return Result
     */
    public function catalogCategories()
    {
        return CatalogCategory::where('active', '=', '1')->get();
    }

    /**
     * Category product specification fields.
     *
     * @param  int  $category_id
     * @return $result
     */
    private function getCategoryFields($category_id)
    {
        return $fields = CategoryField::with(
            ['fieldOptions' => function ($query) {
                $query->orderBy('sort_order', 'asc');
            }]
        )->where('category_id', $category_id)->orderBy('sort_order', 'asc')->get();
    }

    /**
     * get Product Field Options by field ID.
     *
     * @param  int  $field_id
     * @return result object
     */
    private function getProductSpecFieldOptions($field_id)
    {
        return FieldOption::where('field_id', $field_id)->orderBy('sort_order')->get();
    }

    /**
     * get Product Field by field ID.
     *
     * @param  int  $field_id
     * @return array
     */
    private function getProductSpecField($field_id)
    {
        return $product_field = CatalogCategory::find($field_id);
    }

    /**
     * show list of all the category product codes and description.
     *
     * @param  int  $category_id
     * @return $result
     */
    private function showProducts($category_id)
    {
        return ProductCode::where('category_id', $category_id)->orderBy('product_code')->get();
    }

    public function editProductDetail($product_id)
    {
        $product = ProductCode::find($product_id);

        $fields = CategoryField::with(
            ['fieldOptions' => function ($query) {
                $query->orderBy('sort_order', 'asc');
            }]
        )->where('category_id', $product->category_id)->orderBy('sort_order', 'asc')->get();

        $options_map = OptionsMap::where('product_id', $product->id)->pluck('option_id')->all();

        return view('admin.catalog.product', compact('product', 'fields', 'options_map'));
    }

    /**
     * Generate Product Catalog Javascript file by category id.
     *
     * @param  int  $category_id
     * @return string
     */
    private function generateJavascript($category_id)
    {
        $category = CatalogCategory::findOrFail($category_id);

        $products = $this->getDescriptions($category->id);

        $fields = CategoryField::where('category_id', $category->id)->orderBy('sort_order', 'desc')->get();

        $field_names = [];
        $normal_fields = [];
        $add_fields = [];
        $options = [];

        foreach ($fields as $field) {
            $field_names[] = $field->short_name;

            if ($field->additional == 1) {
                $add_fields[] = "'$field->short_name'";
            } else {
                $normal_fields[] = "'$field->short_name'";
            }

            $options[$field->short_name] = $this->getOptions($field->id);
        }

        $filename = storage_path()."/app/public/catalog/catalog-$category->short_name.js";
        $file = @fopen($filename, 'wt');
        if ($file) {
            fwrite($file, 'var normal_fields = new Array('.implode(',', $normal_fields).");\n");
            fwrite($file, 'var add_fields = new Array('.implode(',', $add_fields).");\n\n");

            foreach ($field_names as $var) {
                fwrite($file, "var $var = ".$options[$var]."\n\n");
            }

            fwrite($file, 'var product_codes = new Array('.implode(",\n\t", $this->product_codes_map).");\n\n");
            fwrite($file, 'var desc = new Array('.implode(",\n\t", $this->descriptions_map).");\n\n");
            fwrite($file, 'var images = new Array('.implode(",\n\t", $this->images_map).");\n\n");
            fclose($file);

            return "<span class=\"success\">Wrote catalog Javascript to $filename</span>";
        } else {
            return "<span class=\"error\">Error writing catalog Javascript to $filename</span>";
        }
    }

    /**
     * Regenerate all the PHP and Javascript catalog pages.
     *
     * @return string HTML
     */
    public function regenerateAllCatalogCategories()
    {
        $categories = CatalogCategory::where('active', 1)->get();

        foreach ($categories as $category) {
            echo $this->generateJavascript($category->id);
            echo '<br>';
        }
    }

    private function getDescriptions($category_id)
    {
        $products = ProductCode::where('category_id', $category_id)->orderBy('product_code')->get();

        $count = 0;

        foreach ($products as $product) {
            $this->descriptions_map[$count] = "'".str_replace("'", "\\'", $product->description)."'";
            $this->product_codes_map[$count] = "'$product->product_code'";

            $this->options_map[$product->id] = $count;

            $str = '';
            for ($i = 1; $i < 4; $i++) {
                $image = 'image'.$i;
                if ($product->$image != '') {
                    if ($str != '') {
                        $str .= ',';
                    }
                    $str .= $product->$image;
                }
            }
            $this->images_map[$count] = "'$str'";

            $count++;
        }
    }

    private function getOptions($field_id)
    {
        $fields = FieldOption::with('optionsMap')->where('field_id', $field_id)->orderBy('sort_order', 'asc')->get();

        $add = '';
        $options = 'new Array(';

        foreach ($fields as $field) {
            $array = [];

            foreach ($field->optionsMap as $fieldOption) {
                $array[] = $this->options_map[$fieldOption->product_id];
            }

            if (count($array) == 1) {
                $str = "'$array[0]'";
            } else {
                sort($array); // The javascript code relies on these arrays appearing in sorted order
                $str = implode(',', $array);
            }

            $options .= $add."new Array($str)";
            $add = ",\n\t";
        }
        $options .= ');';

        return $options;
    }

    private function cleanInput($input)
    {
        $input = trim($input);

        return str_replace(["\b", "\f", "\n", "\r", "\t", ' ,'], ['', '', '', '', '', ','], $input); // remove control characters
    }

    private function prepInput($array)
    {
        if (is_array($array)) {
            foreach ($array as &$value) {
                $value = (is_array($value)) ? $this->prepInput($value) : $this->cleanInput($value);
            }
        }

        return $array;
    }

    public function addLine(&$str, $msg, $sep = '<br />')
    {
        if ($str != '') {
            $str .= $sep;
        }
        $str .= $msg;
    }

    // End Class
}

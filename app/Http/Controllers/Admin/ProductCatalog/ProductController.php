<?php

namespace App\Http\Controllers\Admin\ProductCatalog;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProductCatalog\CreateProductRequest;
use App\Models\ProductCatalog\CategoryField;
use App\Models\ProductCatalog\OptionsMap;
use App\Models\ProductCatalog\ProductCode;
use App\Models\ProductCatalog\ProductCodeUpdate;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index() {}

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create($category_id)
    {
        $fields = CategoryField::with('fieldOptions')->where('category_id', $category_id)->orderBy('sort_order', 'asc')->get();
        // get list of selected options by product id

        return view('admin.catalog.product.create', compact('category_id', 'fields'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return Response
     */
    public function store(CreateProductRequest $request)
    {
        $product = ProductCode::create($request->all());

        //get all the  selected options and save them to the options map
        $options = $request->input('options');
        $this->saveOptions($product->id, $options);

        return redirect()->route('catalog.category', [$product->category_id]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit($id)
    {
        // Get product code by id
        $product = ProductCode::find($id);
        // get category fields and option by category id fro the product code query
        $fields = CategoryField::with('fieldOptions')->where('category_id', $product->category_id)->orderBy('sort_order', 'asc')->get();
        // get list of selected options by product id
        $options_map = OptionsMap::where('product_id', $product->id)->pluck('option_id')->all();

        return view('admin.catalog.product.update', compact('product', 'fields', 'options_map'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function update($id, CreateProductRequest $request)
    {
        // get product by Id
        $product = ProductCode::findOrFail($id);

        $product->fill($request->input())->save();

        //delete the current set of stored Options by product id
        $options_map = OptionsMap::where('product_id', $product->id)->delete();

        //get all the  selected options and save them to the options map
        $options = $request->input('options');
        $this->saveOptions($product->id, $options);

        return redirect()->route('catalog.category', [$product->category_id]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroy($id)
    {

        // Get product code by id
        $product = ProductCode::find($id);
        // delete Options by product id
        $options_map = OptionsMap::where('product_id', $product->id)->delete();
        // then delete the parent
        $product->destroy($id);

        return redirect()->route('catalog.category', [$product->category_id]);

        //$product_message = '<p class="success">Selected product(s) deleted</p>';

        //$this->generateJavascript($this->page_id);
    }

    /**
     * update the options map.
     *
     * @param  int  $product_id
     * @return Response
     */
    public function saveOptions($product_id, array $options)
    {
        foreach ($options as $option) {
            OptionsMap::create(['product_id' => $product_id,  'option_id' => $option]);
        }
    }

    /**
     * Batch update product codes by loading the catalog_product_code_updates table with new code details to update the catalog_product_codes table with.
     *
     * @return Response
     */
    public function UpdateProductCodes()
    {
        $updated_product_codes = ProductCodeUpdate::get();

        foreach ($updated_product_codes as $updated_product_code) {

            // get existing product by Id
            $existing_product_code = ProductCode::findOrFail($updated_product_code->id);

            if ($existing_product_code) {
                //				echo $existing_product_code->description.'<br>';

                // set the description to the new code description then save it
                $existing_product_code->description = $updated_product_code->description;
                $existing_product_code->save();

                echo 'id: '.$updated_product_code->id.' description was updated to: '.$updated_product_code->description.'<br>';

                //delete the code from the update table when done. We only need it once.
                $updated_product_code->delete();
            }
        }
    }
}

<?php

namespace App\Http\Controllers\Admin\ProductCatalog;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProductCatalog\CreateFieldRequest;
use App\Models\ProductCatalog\CatalogCategory;
use App\Models\ProductCatalog\CategoryField;
use App\Models\ProductCatalog\FieldOption;
use App\Models\ProductCatalog\OptionsMap;

class FieldController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index() {}

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create($category_id)
    {
        $category = CatalogCategory::findOrFail($category_id);

        return view('admin.catalog.field.create', compact('category'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @return Response
     */
    public function store(CreateFieldRequest $request)
    {
        $field = CategoryField::create($request->all());

        //get the new option and save them to the options map
        $new_option = $request->input('new-option');
        $this->saveOptions($field->id, null, $new_option);

        return redirect()->route('catalog.category', [$field->category_id]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function edit($field_id)
    {
        // get category field
        $field = CategoryField::findOrFail($field_id);
        // get list of selected options by product id
        $options = FieldOption::where('field_id', $field->id)->orderBy('sort_order')->get();

        return view('admin.catalog.field.update', compact('field', 'options'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function update($id, CreateFieldRequest $request)
    {
        // get field by Id
        $field = CategoryField::findOrFail($id);

        $field->fill($request->input())->save();

        //get all the  selected field options and save them
        $options = $request->input('option');
        $new_option = $request->input('new-option');
        $deleted = $request->input('del');

        $this->saveOptions($field->id, $options, $new_option, $deleted);

        return redirect()->route('catalog.category', [$field->category_id]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return Response
     */
    public function destroy($field_id)
    {

        // Get field by id
        $field = CategoryField::find($field_id);
        // delete Options by product id
        //$options_map = OptionsMap::where('product_id', $product->id)->delete();
        // then delete the parent
        $field->delete();

        return redirect()->route('catalog.category', [$field->category_id]);
    }

    /**
     * Create/update the field options.
     *
     * @param  int  $product_id
     * @return Response
     */
    public function saveOptions($field_id, ?array $options = null, ?array $new_option = null, ?array $deleted = null)
    {
        if (is_array($options)) {
            foreach ($options as $id => $option) {
                if (is_array($deleted) && in_array($id, $deleted)) {
                    $field_option = FieldOption::destroy($id);
                } else {
                    FieldOption::updateOrCreate(['id' => $id], ['field_id' => $field_id, 'title' => $option['title'], 'sort_order' => $option['order']]);
                }
            }
        }

        if ($new_option['title'] != '') {
            FieldOption::create(['field_id' => $field_id, 'title' => $new_option['title'], 'sort_order' => $new_option['order']]);
        }
    }
}

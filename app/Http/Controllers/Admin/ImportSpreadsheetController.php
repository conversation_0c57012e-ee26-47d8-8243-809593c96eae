<?php
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Imports\IuaSystemCountryLocationImport;
use App\Imports\IuaSystemUpgradeCountryEligibilityMatrixImport;
use App\Imports\IuaSystemCountryLocationUatImport;
use App\Imports\IuaSystemUpgradeCountryEligibilityMatrixUatImport;
use App\Models\ImagingUpgrade\IuaSystemCountryLocation;
use App\Models\ImagingUpgrade\IuaSystemUpgradeCountryEligibilityMatrix;
use App\Models\ImagingUpgrade\IuaSystemCountryLocationUat;
use App\Models\ImagingUpgrade\IuaSystemUpgradeCountryEligibilityMatrixUat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;

class ImportSpreadsheetController extends Controller
{
    public function index()
    {
        \Flash::info('Welcome to the Import Tool');
        return view('admin.software.import.index');
    }

    public function import(Request $request)
    {
        Log::info('Import process started');

        $request->validate([
            'import_file' => [
                'required',
                'file',
                'mimes:xlsx,xls',
            ],
        ]);

        set_time_limit(600);
        ini_set('memory_limit', '512M');

        try {
            $file = $request->file('import_file')->getClientOriginalName();
            $pathinfo = pathinfo($file);
            $filename = strtolower($pathinfo['filename']);
            
            Log::info('Attempting to import file: ' . $filename);

            switch ($filename) {
                case 'iua_system_country_location':
                    return $this->handleImport(
                        'iua_system_country_location',
                        IuaSystemCountryLocation::class,
                        new IuaSystemCountryLocationImport,
                        $request->file('import_file')
                    );

                case 'iua_system_country_location_uat':
                    return $this->handleImport(
                        'iua_system_country_location_uat',
                        IuaSystemCountryLocationUat::class,
                        new IuaSystemCountryLocationUatImport,
                        $request->file('import_file')
                    );

                case 'iua_system_upgrade_country_eligibility_matrices':
                    return $this->handleImport(
                        'iua_system_upgrade_country_eligibility_matrices',
                        IuaSystemUpgradeCountryEligibilityMatrix::class,
                        new IuaSystemUpgradeCountryEligibilityMatrixImport,
                        $request->file('import_file')
                    );

                case 'iua_system_upgrade_country_eligibility_matrices_uat':
                    return $this->handleImport(
                        'iua_system_upgrade_country_eligibility_matrices_uat',
                        IuaSystemUpgradeCountryEligibilityMatrixUat::class,
                        new IuaSystemUpgradeCountryEligibilityMatrixUatImport,
                        $request->file('import_file'),
                        true
                    );

                default:
                    Log::warning('Invalid filename: ' . $filename);
                    flash('Invalid filename format: "' . $filename . '". Please check the file name and try again.')->error();
                    return redirect()->back();
            }

        } catch (\Exception $e) {
            Log::error('Import failed: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());
            flash('Import failed: ' . $e->getMessage())->error();
            return redirect()->back();
        }
    }

    /**
     * Handle the import process for a specific table
     */
    private function handleImport(string $tableName, string $modelClass, $importClass, $file)
    {
        Log::info("Starting import for table: {$tableName}");
        $backupTable = "{$tableName}_backup";
        
        try {
            // Always create backup
            DB::statement("DROP TABLE IF EXISTS {$backupTable}");
            DB::statement("CREATE TABLE {$backupTable} AS SELECT * FROM {$tableName}");
            Log::info("Backup created for table: {$tableName}");

            // Get initial count
            $initialCount = $modelClass::count();
            
            // Truncate the table
            $modelClass::query()->truncate();
            Log::info("Table {$tableName} truncated successfully");

            // Import the data
            Excel::import($importClass, $file);
            
            // Get final count and calculate difference
            $finalCount = $modelClass::count();
            $recordsImported = $finalCount - $initialCount;
            
            Log::info("Import completed. Records imported: {$recordsImported}");

            if ($finalCount === 0) {
                throw new \Exception("Import completed but no records were imported");
            }

            // Clean up backup table after successful import
            DB::statement("DROP TABLE IF EXISTS {$backupTable}");
            Log::info("Backup table dropped after successful import");

            flash("Data imported successfully into {$tableName}. {$recordsImported} records imported.")->success();
            return redirect()->back();

        } catch (\Exception $e) {
            Log::error("Import failed for table {$tableName}: " . $e->getMessage());
            Log::error("Stack trace: " . $e->getTraceAsString());

            // Restore from backup
            if (DB::select("SHOW TABLES LIKE '{$backupTable}'")) {
                DB::statement("TRUNCATE TABLE {$tableName}");
                DB::statement("INSERT INTO {$tableName} SELECT * FROM {$backupTable}");
                DB::statement("DROP TABLE IF EXISTS {$backupTable}");
                Log::info("Data restored from backup for table: {$tableName}");
            }

            flash("Import failed for {$tableName}: " . $e->getMessage())->error();
            return redirect()->back();
        }
    }
}
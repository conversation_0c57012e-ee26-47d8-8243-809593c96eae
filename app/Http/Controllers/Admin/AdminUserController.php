<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Auth;
use Config;
use DB;
use Hash;
use Illuminate\Http\Request;
use Laracasts\Flash\Flash;
use Spatie\Permission\Models\Role;

class AdminUserController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Admin User Controller
    |--------------------------------------------------------------------------
    */

    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->admin_users = Config::get('settings.url_prefixes.user_prefix');
        $this->middleware('auth');

        // get the count of possible Roles
        $this->roles = Role::all();
    }

    /**
     * Show the list of all users.
     *
     * @return Response
     */
    public function index()
    {
        $users = User::withTrashed()->get();

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the create user form.
     *
     * @return Response
     */
    public function create()
    {
        $roles = $this->roles;

        return view('admin.users.create', compact('roles'));
    }

    /**
     * Process the create user form.
     *
     * @return Response
     */
    public function store(Request $request, User $user, Role $role)
    {

        // generate a random string to use as password
        $password = generate_random_string();

        $role = $role->whereId($request->role)->first();

        // Add the new user
        $user = $user->create([
            'username' => strtolower($request->name_first.$request->name_last),
            'email' => strtolower($request->email),
            'password' => Hash::make($password),
            'department' => $request->department,
            'name_first' => ucfirst($request->name_first),
            'name_last' => ucfirst($request->name_last),
        ])->assignRole($role);

        Flash::success('User Created! They can log in with the email: '.$request->email.' and the password: '.$password);

        return redirect()->route('user.record.show', [$user->id]);
    }

    /**
     * Shows the users profile and edit form.
     *
     * @return Response
     */
    public function show($userId)
    {
        $user = User::withTrashed()->find($userId);
        $roles = $this->roles;

        return view('admin.users.show', compact('user', 'roles'));
    }

    /**
     * Updates a user's record.
     *
     * @return Response
     */
    public function update($userId, Request $request, User $user, Role $role)
    {
        // Get the User
        $user = $user->withTrashed()->find($userId);

        // Update the user
        $user->username = strtolower($request->name_first.$request->name_last);
        $user->email = strtolower($request->email);
        $user->department = $request->department;
        $user->name_first = ucfirst($request->name_first);
        $user->name_last = ucfirst($request->name_last);

        if (Auth::user()->hasPermissionTo('user edit')) {
            $user->syncRoles($request->role);
        }

        $user->save();

        Flash::success('User Saved!');

        return redirect()->back();
    }

    /**
     * Change the status of a Record.
     *
     * @return View
     */
    public function changeStatus($userId)
    {
        try {

            // Save the info to the DB
            $record = User::withTrashed()->find($userId);
            if ($record->deleted_at == null) {
                $this->destroy($userId);
            } else {
                $this->restore($userId);
            }
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }
    }

    /**
     * Soft deletes the record.
     *
     * @return View
     */
    public function destroy($userId)
    {
        try {
            // Save the info to the DB
            $record = User::withTrashed()->find($userId);
            $title = $record->first_name;
            $record->delete();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $userId, 'record_status' => $title, 'record_title' => $title]);
    }

    /**
     * Restores the users status.
     *
     * @return Response
     */
    public function restore($userId)
    {
        try {
            // Save the info to the DB
            $record = User::withTrashed()->find($userId);
            $title = $record->first_name;
            $record->restore();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $userId, 'record_status' => $title, 'record_title' => $title]);
    }

    /**
     * Delete the record permanately.
     *
     * @return View
     */
    public function forceDelete($userId)
    {
        try {
            // Save the info to the DB
            $record = User::withTrashed()->find($userId);
            $title = $record->first_name;
            $record->forceDelete();
        } catch (Exception $e) {

            // Send them to their view -> in this case, the upload file page view
            return \Response::json(['status' => 'fail']);
        }

        // Send them to their view -> in this case, the upload file page view
        return \Response::json(['status' => 'success', 'record_id' => $userId, 'record_status' => $title, 'record_title' => $title]);
    }

    /**
     * Shows the users update password form.
     *
     * @return Response
     */
    public function password($userId)
    {
        $user = User::withTrashed()->find($userId);

        // Check
        if ($user == Auth::user() || Auth::user()->hasPermissionTo('user edit')) {
            return view('admin.users.password', compact('user'));
        } else {
            Flash::error('You do not have permission to edit users.');

            return redirect()->back();
        }
    }

    /**
     * Processes the users update password form.
     *
     * @return Response
     */
    public function updatePassword($userId, Request $request)
    {
        // Get the User
        $user = User::withTrashed()->find($userId);

        if ($user == Auth::user() || Auth::user()->hasPermissionTo('user edit')) {
            // Confirm that the two passwords match
            if ($request->password == $request->confirm_password) {
                // Update the user
                $user->password = Hash::make($request->password);
                $user->save();
            } else {
                Flash::error('Sorry, those passwords didn\'t match, please try again.');

                return redirect()->back();
            }

            Flash::success('This users password has been updated.');

            return redirect()->back();
        } else {
            Flash::error('You do not have permission to update that user\'s password.');

            return redirect()->back();
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function impersonate($id, User $user)
    {
        try {
            $user = User::find($id);

            // if user found and auth user role is greater or equal to the user they are trying to edit
            if ($user && \Auth::user()->hasRole('superadmin')) {
                Auth::login($user);

                return redirect()->route('maestro.dashboard');
            } else {
                flash('Sorry, it doesn\'t look like you have permission to do that.')->error();

                return back();
            }
        } catch (Exception $e) {
            flash('Sorry, there was a problem submitting your request.')->error();
        }
    }
}

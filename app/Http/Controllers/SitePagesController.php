<?php

namespace App\Http\Controllers;

use App\Models\ProductCatalog\CatalogCategory;
use App\Models\Site\Convention;
use App\Models\Site\SiteCategory;
use App\Models\Site\SitePage;
use Illuminate\Http\Request;

class SitePagesController extends Controller
{
    /**
     * Display index page view.
     * GET /sitecategories
     *
     * @return Response
     */
    public function index()
    {
        if (view()->exists('site.products.index')) {
            return view('site.products.index');
        } else {
            return view('errors.404');
        }
    }

    /**
     * Display the specified resource.
     * GET /{category}
     *
     * @param  string  $category
     * @return view
     */
    public function category($category)
    {
        $product_category = SiteCategory::with(['subcategories' => function ($query) {
            $query->with(['pages' => function ($query) {
                $query->where('active', '1')->where('product_page', '1')->orderBy('sort_order')->get();
            }])->where('active', '1')->orderBy('sort_order')->get();
        }])->where('short_name', $category)->where('active', '1')->where('product_category', '1')->orderBy('title')->first();

        $redirect = '';

        switch ($category) {
            case '':
                // code...
                break;

            case 'guidance-technologies':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/catheter-guidance-systems';
                break;

            case 'ultrasound':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/catheter-guidance-systems/ultrasound-guidance-systems';
                break;

            case 'tip-confirmation-location':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/catheter-guidance-systems/tip-confirmation-and-location-systems';
                break;

            case 'needle-guidance-technology':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/catheter-guidance-systems/needle-guidance-systems';
                break;

            case 'access-devices':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/vascular-access-devices';
                break;

            case 'cvcs':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/vascular-access-devices/central-iv-catheters';
                break;

            case 'dialysis':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/urology-and-kidney-health/dialysis-catheters/acute-dialysis-catheters';
                break;

            case 'ir':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/vascular-access-devices/ir-piccs';
                break;

            case 'midline':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/vascular-access-devices/midline-iv-catheters';
                break;

            case 'nursing':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/vascular-access-devices/nursing-piccs-';
                break;

            case 'peripheral':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/vascular-access-devices/peripheral-iv-catheters';
                break;

            case 'port-access-needles':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/vascular-access-devices/port-devices-and-needles/port-access-needles';
                break;

                // case 'ports':
                // $redirect = '';
                // break;

            case 'care-maintenance':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/iv-care-and-maintenance';
                break;

            case 'allpoints-systems':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/vascular-access-devices/port-devices-and-needles/port-access-kits';
                break;

            case 'kits':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/vascular-access-devices/port-devices-and-needles/port-access-kits';
                break;

            case 'procedural':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/iv-care-and-maintenance';
                break;

            case 'stabilization':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/iv-care-and-maintenance/iv-stabilization-devices';
                break;

            case 'dressing-change-kits':
                $redirect = 'https://www.bd.com/en-us/offerings/capabilities/vascular-access/iv-care-and-maintenance/vascular-dressings';
                break;

        }

        if ($redirect) {
            return redirect()->to($redirect)->setStatusCode(301);
        }
    }

    /**
     * Display the specified resource.
     * GET /{category}/{product}
     *
     * @param  string  $category_shortname
     * @param  string  $product_shortname
     * @return view
     */
    public function product(Request $request, $category_shortname, $product_shortname)
    {
        $title = '';
        $category = '';
        $page_details = '';

        // Get page details and product literature
        $page_details = SitePage::with('category')->with(['literature' => function ($query) {
            $query->where('active', '1')->orderBy('sort_order')->get();
        }])->with(['videos' => function ($query) {
            $query->with('files')->where('active', '1')->orderBy('sort_order')->get();
        }])->where('url', $request->path())->first();

        if (! empty($page_details)) {
            $title = $page_details->title;
            $category = $page_details->category->title;
        }

        if ($page_details && $page_details->redirect) {
            return redirect()->to($page_details->redirect)->setStatusCode(301);
        } else {
            return redirect()->to('https://www.bd.com/en-us/offerings/capabilities/vascular-access')->setStatusCode(301);
        }
    }

    /**
     * Display the specified resource.
     * GET /{category}/{product}/{section}
     *
     * @param  string  $category
     * @param  string  $product
     * @param  string  $section
     * @return view
     */
    public function section(Request $request, $category_shortname, $product_shortname, $section = '')
    {
        $title = '';
        $category = '';
        $page_details = '';

        // Get page details and product literature
        $page_details = SitePage::with('category')->with(['literature' => function ($query) {
            $query->where('active', '1')->orderBy('sort_order')->get();
        }])->with(['videos' => function ($query) {
            $query->with('files')->where('active', '1')->orderBy('sort_order')->get();
        }])->where('url', $request->path())->first();

        if (! empty($page_details)) {
            $title = $page_details->title;
            $category = $page_details->category->title;
        }

        if ($page_details && $page_details->redirect) {
            return redirect()->to($page_details->redirect)->setStatusCode(301);
        } else {
            return redirect()->to('https://www.bd.com/en-us/offerings/capabilities/vascular-access')->setStatusCode(301);
        }
    }

    /**
     * Display the specified resource.
     *
     * @return view
     */
    public function sitemap()
    {
        // Get product categories, product page details that have product literature of the given literature_type_id
        $patient_guides = $this->getSiteLiteratureByType('3');

        // Get product categories, product page details and product literature
        $product_categories = $this->getSiteLiterature();

        // Get catalog categories
        $catalog_categories = CatalogCategory::where('active', 1)->get();

        if (view()->exists('site.sitemap')) {
            return view('site.sitemap', compact('product_categories', 'patient_guides', 'catalog_categories'));
        } else {
            return view('errors.404');
        }
    }

    /**
     * Display the specified resource.
     *
     * @return view
     */
    public function patents()
    {

        $redirect = 'https://www.bd.com/en-us/about-bd/patent-coverage';

        if ($redirect) {
            return redirect()->to($redirect)->setStatusCode(301);
        }
    }

    /**
     * Display the specified resource.
     *
     * @return view
     */
    public function conventions()
    {
        // Get all active meeting and convention dates
        $today = \Carbon\Carbon::now()->toDateString();
        $end_of_year = \Carbon\Carbon::now()->endOfYear()->toDateString();
        $conventions_this_year = Convention::whereBetween('end_date', [$today, $end_of_year])->get();

        $start_of_next_year = \Carbon\Carbon::now()->addYear()->startOfYear()->toDateString();
        $end_of_next_year = \Carbon\Carbon::now()->addYear()->endOfYear()->toDateString();
        $conventions_next_year = Convention::whereBetween('end_date', [$start_of_next_year, $end_of_next_year])->get();

        if (view()->exists('site.conventions')) {
            return view('site.conventions', compact('conventions_this_year', 'conventions_next_year'));
        } else {
            return view('errors.404');
        }
    }

    /**
     * Get product categories, product page details that have product literature of the given literature_type_id
     *
     * @param  int  $literature_type_id
     * @return collection
     */
    public function getSiteLiterature()
    {
        $site_literature = SiteCategory::with(
            ['pages' => function ($query) {
                $query->with(['literature' => function ($query) {
                    $query->where('active', '1')->orderBy('sort_order')->get();
                }])->where('active', '1')->where('product_page', '1')->orderBy('title')->get();
            }]
        )->where('active', '1')->where('product_category', '1')->orderBy('title')->get();

        return $site_literature;
    }

    /**
     * Get product categories, product page details that have product literature of the given literature_type_id
     *
     * @param  int  $literature_type_id
     * @return collection
     */
    public function getSiteCategoryLiterature($category)
    {
        $site_literature = SiteCategory::with(
            ['pages' => function ($query) {
                $query->with(['literature' => function ($query) {
                    $query->where('active', '1')->orderBy('sort_order')->get();
                }])->where('active', '1')->where('product_page', '1')->orderBy('title')->get();
            }]
        )->where('short_name', $category)->where('active', '1')->where('product_category', '1')->orderBy('title')->get();

        return $site_literature;
    }

    /**
     * Get product categories, product page details that have product literature of the given literature_type_id
     *
     * @param  int  $literature_type_id
     * @return collection
     */
    public function getSiteCategoryLiteratureByType($category, $literature_type_id)
    {
        $site_literature = SiteCategory::with('pages')->whereHas('pages', function ($query) use ($literature_type_id) {
            $query->with('literature')->whereHas('literature', function ($query) use ($literature_type_id) {
                $query->where('active', '1')->where('literature_type_id', $literature_type_id)->orderBy('sort_order');
            })->where('active', '1')->where('product_page', '1')->orderBy('sort_order');
        })->where('short_name', $category)->where('active', '1')->where('product_category', '1')->orderBy('title')->get();

        return $site_literature;
    }

    /**
     * Get product categories, product page details that have product literature of the given literature_type_id
     *
     * @param  int  $literature_type_id
     * @return collection
     */
    public function getSiteLiteratureByType($literature_type_id)
    {
        $site_literature = SiteCategory::with('pages')->whereHas('pages', function ($query) use ($literature_type_id) {
            $query->with('literature')->whereHas('literature', function ($query) use ($literature_type_id) {
                $query->where('active', '1')->where('literature_type_id', $literature_type_id)->orderBy('sort_order');
            })->where('active', '1')->where('product_page', '1')->orderBy('sort_order');
        })->where('active', '1')->where('product_category', '1')->orderBy('title')->get();

        return $site_literature;
    }
}

<?php

namespace App\Http\Controllers\GuidanceTechnologies;

use App\Helpers\JsonWebToken;
use App\Helpers\SerialNumber;
use App\Http\Controllers\Controller;
use App\Mail\MailableTemplate;
use App\Models\GuidanceTechnologies\OperatingSystemFiles;
use App\Models\GuidanceTechnologies\Systems;
use App\Models\GuidanceTechnologies\SystemSoftwareFiles;
use App\Models\GuidanceTechnologies\SystemSoftwareVersions;
use App\Models\GuidanceTechnologies\SystemUpdateHistory;
use App\Models\ImagingUpgrade\IuaSystemCountryLocation;
use App\Models\ImagingUpgrade\IuaSystemUpgradeCountryEligibilityMatrix;
use App\Services\UploadFileService;
use Carbon\Carbon;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Log;
use Mail;
use Storage;

class GuidanceTechnologiesApiController extends Controller
{
    public $status = 'live';  //Mode: live || test

    private $version = '1.0.3'; // Revision history number

    private $mode;

    // File Storage and Download Paths
    private $temp_dir;

    private $system_download_path;

    private $os_download_path;

    private $error_logs_path;

    private $allowed_file_types = ['bdu', 'zip', 'log', 'json'];

    // DHR contact
    private $to_mail;

    private $to_name;

    private $bcc;

    /**
     * Constructor
     */
    public function __construct(Request $request)
    {

        // File Storage and Download Paths
        $this->temp_dir = storage_path().'/app/uploads/guidance-technologies/temp/';
        $this->system_download_path = storage_path().'/app/uploads/guidance-technologies/system-software-files/';
        $this->os_download_path = storage_path().'/app/uploads/guidance-technologies/operating-system-files/';
        $this->error_logs_path = storage_path().'/app/uploads/guidance-technologies/error-logs/';

        if (($request->environment ?? null) === 'prod') {
            // Prod contacts
            $this->to_mail = '<EMAIL>';
            $this->to_name = 'Dymax';
            $this->bcc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
        } else {
            // Dev & Staging Contacts
            $this->to_mail = '<EMAIL>';
            $this->to_name = 'Staging/Test Mode';
            $this->bcc = ['<EMAIL>', '<EMAIL>'];
        }
    }

    /**
     * validate serial number request
     * string $environment
     * string $serial_number
     *
     * @return JSON response
     */
    public function serialNumberValidation($environment, $serial_number, SerialNumber $SerialNumber)
    {
        $serial_number = strtoupper($serial_number);

        if ($serial_number !== null) {
            return response()->json([
                'api' => 'serial number validation',
                'status' => $this->status,
                'version' => $this->version,
                'is_valid' => $SerialNumber->validate($serial_number),
            ], $status = 200);
        }
    }

    /**
     * check for serial number availability
     * string $environment
     * string $serial_number
     *
     * @return JSON response
     */
    public function checkUpgradeAvailability($environment, $serial_number, Request $request, Systems $Systems, SerialNumber $SerialNumber)
    {
        $serial_number = strtoupper($serial_number);

        // Entry point log with essential request information
        Log::info('Upgrade availability check', [
            'serial_number' => $serial_number,
            'environment' => $environment
        ]);

        $validator = Validator::make([
            'environment' => $request->environment,
            'serial_number' => $request->serial_number,
        ], [
            'environment' => [
                'required',
                Rule::in(['dev', 'staging', 'prod']),
            ],
            'serial_number' => 'required|string',
        ], [
            'environment.in' => 'The :attribute field is required and must equal one of these values: :values.',
            'serial_number.required' => 'The :attribute field is required and must be a string',
        ]);

        if ($validator->fails()) {
            $messages = $validator->messages();

            Log::warning('Validation failed', [
                'errors' => $validator->errors()->toArray()
            ]);

            return response()->json([
                'api' => 'check upgrade availability',
                'status' => $this->status,
                'version' => $this->version,
                'is_eligible' => false,
                'errors' => $validator->errors(),
            ], $status = 200);
        }

        // If environment is DEV, skip country filtering
        if ($environment === 'dev') {
            // Skip country filtering in DEV environment
            return $this->getUpgradeAvailabilityWithoutCountryFiltering($environment, $serial_number, $SerialNumber, $Systems);
        }

        if ($SerialNumber->validate($serial_number)) {
            $system_model = $SerialNumber->system_model;
            $international_model = $SerialNumber->international_model;
            $internal_serial_number = $SerialNumber->internal_serial_number;

            // Serial number validation successful - no need to log details for every request

            // Check if the serial number is in the country location table
            $countryLocation = IuaSystemCountryLocation::where('serial_number', $serial_number)->first();

            if (!$countryLocation) {
                Log::warning('Serial number not found in country location table', [
                    'serial_number' => $serial_number
                ]);

                return response()->json([
                    'api' => 'check upgrade availability',
                    'status' => $this->status,
                    'version' => $this->version,
                    'is_eligible' => false,
                    'message' => 'Device location not found'
                ], $status = 200);
            }

            $countryCode = $countryLocation->country_code;

            // Check if the system model and country are in the eligibility matrix
            $eligibilityMatrix = IuaSystemUpgradeCountryEligibilityMatrix::where('system_id', $system_model)
                ->where('country_code', $countryCode)
                ->first();

            if (!$eligibilityMatrix) {
                Log::warning('System model and country not found in eligibility matrix', [
                    'system_model' => $system_model,
                    'country_code' => $countryCode
                ]);

                return response()->json([
                    'api' => 'check upgrade availability',
                    'status' => $this->status,
                    'version' => $this->version,
                    'is_eligible' => false,
                    'message' => 'Device not eligible for upgrade in this region'
                ], $status = 200);
            }

            // Get the authorized version from the matrix
            $authorizedVersion = $eligibilityMatrix->ultrasound; // Assuming ultrasound is the version field

            // Check if authorized version is null, empty, contains only whitespace, or doesn't contain alphanumeric characters
            if ($authorizedVersion === null) {
                $hasValidContent = false;
            } else {
                $trimmedVersion = trim($authorizedVersion);
                $hasValidContent = !empty($trimmedVersion) && preg_match('/[a-zA-Z0-9]/', $trimmedVersion);
            }

            if (!$hasValidContent) {
                Log::warning('Empty authorized version for serial number', [
                    'serial_number' => $serial_number,
                    'country_code' => $countryCode,
                    'system_model' => $system_model
                ]);

                return response()->json([
                    'api' => 'check upgrade availability',
                    'status' => $this->status,
                    'version' => $this->version,
                    'is_eligible' => false,
                    'message' => 'Unregistered serial number'
                ], $status = 200);
            }

            // check if serial number is internal
            if ($internal_serial_number == true) {
                $system = $Systems->where('system_model', 'like', '%'.$system_model.'%')
                    ->where($environment, 1)
                    ->where('active', 1)
                    ->with(['versions' => function ($query) use ($environment, $authorizedVersion, $internal_serial_number) {
                        $query->where($environment, 1)
                            ->where('active', 1)
                            ->where(function($q) use ($internal_serial_number) {
                                $q->where('internal', 1)->orWhereNull('internal');
                            })
                            ->where(function($q) use ($authorizedVersion) {
                                // Only match if authorized version is not null, not empty, and contains alphanumeric characters
                                if ($authorizedVersion === null) {
                                    // Force no results if authorized version is null
                                    $q->where('id', '=', 0);
                                } else {
                                    $trimmedVersion = trim($authorizedVersion);
                                    $hasValidContent = !empty($trimmedVersion) && preg_match('/[a-zA-Z0-9]/', $trimmedVersion);

                                    if ($hasValidContent) {
                                        $q->where(function($subq) use ($trimmedVersion) {
                                            $subq->where('version', 'like', '%'.$trimmedVersion.'%')
                                                 ->orWhere('name', 'like', '%'.$trimmedVersion.'%');
                                        });
                                    } else {
                                        // Force no results if authorized version is empty
                                        $q->where('id', '=', 0);
                                    }
                                }
                            })
                            ->with('files')
                            ->get();
                    }])->first();
            } else {
                $system = $Systems->where('system_model', 'like', '%'.$system_model.'%')
                    ->where($environment, 1)
                    ->where('active', 1)
                    ->with(['versions' => function ($query) use ($environment, $authorizedVersion) {
                        $query->where($environment, 1)
                            ->where('active', 1)
                            ->whereNull('internal')
                            ->where(function($q) use ($authorizedVersion) {
                                // Only match if authorized version is not null, not empty, and contains alphanumeric characters
                                if ($authorizedVersion === null) {
                                    // Force no results if authorized version is null
                                    $q->where('id', '=', 0);
                                } else {
                                    $trimmedVersion = trim($authorizedVersion);
                                    $hasValidContent = !empty($trimmedVersion) && preg_match('/[a-zA-Z0-9]/', $trimmedVersion);

                                    if ($hasValidContent) {
                                        $q->where(function($subq) use ($trimmedVersion) {
                                            $subq->where('version', 'like', '%'.$trimmedVersion.'%')
                                                 ->orWhere('name', 'like', '%'.$trimmedVersion.'%');
                                        });
                                    } else {
                                        // Force no results if authorized version is empty
                                        $q->where('id', '=', 0);
                                    }
                                }
                            })
                            ->with('files')
                            ->get();
                    }])->first();
            }

            if ($system && count($system->versions) > 0) {
                // Check if any of the versions have empty values
                $hasEmptyVersions = false;
                foreach ($system->versions as $version) {
                    // Check for empty, whitespace-only, or non-printable characters
                    $versionValue = $version->version;
                    $trimmedVersion = trim($versionValue);
                    if (empty($versionValue) || $versionValue === '' || $trimmedVersion === '' || !preg_match('/[a-zA-Z0-9]/', $trimmedVersion)) {
                        $hasEmptyVersions = true;
                        break;
                    }
                }

                // If there are empty versions, return unregistered serial number
                if ($hasEmptyVersions) {
                    Log::warning('Serial number has empty versions', [
                        'serial_number' => $serial_number
                    ]);

                    return response()->json([
                        'api' => 'check upgrade availability',
                        'status' => $this->status,
                        'version' => $this->version,
                        'is_eligible' => false,
                        'message' => 'Unregistered serial number'
                    ], $status = 200);
                }

                // System found with eligible versions - log minimal information
                Log::info('Eligible versions found', [
                    'serial_number' => $serial_number,
                    'version_count' => count($system->versions)
                ]);

                return response()->json([
                    'api' => 'serial number validation',
                    'status' => $this->status,
                    'version' => $this->version,
                    'is_valid' => true,
                    'system' => $system,
                ], $status = 200);
            } else {
                Log::warning('No eligible versions found for system', [
                    'system_model' => $system_model,
                    'authorized_version' => $authorizedVersion
                ]);

                return response()->json([
                    'api' => 'check upgrade availability',
                    'status' => $this->status,
                    'version' => $this->version,
                    'is_eligible' => false,
                    'message' => 'No eligible versions found for this device'
                ], $status = 200);
            }
        } else {
            Log::warning('Serial number validation failed', [
                'serial_number' => $serial_number
            ]);

            return response()->json([
                'api' => 'check upgrade availability',
                'status' => $this->status,
                'version' => $this->version,
                'is_eligible' => false,
                'message' => 'Unregistered serial number'
            ], $status = 200);
        }
    }

    /**
     * Get upgrade availability without country filtering (for DEV environment)
     *
     * @param string $environment
     * @param string $serial_number
     * @param SerialNumber $SerialNumber
     * @param Systems $Systems
     * @return \Illuminate\Http\JsonResponse
     */
    private function getUpgradeAvailabilityWithoutCountryFiltering($environment, $serial_number, SerialNumber $SerialNumber, Systems $Systems)
    {
        $system_model = $SerialNumber->system_model;
        $internal_serial_number = $SerialNumber->internal_serial_number;

        // check if serial number is internal
        if ($internal_serial_number == true) {
            $system = $Systems->where('system_model', 'like', '%'.$system_model.'%')
                ->where($environment, 1)
                ->where('active', 1)
                ->with(['versions' => function ($query) use ($environment) {
                    $query->where($environment, 1)
                        ->where('active', 1)
                        ->with('files')
                        ->get();
                }])->first();
        } else {
            $system = $Systems->where('system_model', 'like', '%'.$system_model.'%')
                ->where($environment, 1)
                ->where('active', 1)
                ->with(['versions' => function ($query) use ($environment) {
                    $query->where($environment, 1)
                        ->where('active', 1)
                        ->whereNull('internal')
                        ->with('files')
                        ->get();
                }])->first();
        }

        if ($system && count($system->versions) > 0) {
            // Check if any of the versions have empty values
            $hasEmptyVersions = false;
            foreach ($system->versions as $version) {
                // Check for empty, whitespace-only, or non-printable characters
                $versionValue = $version->version;
                $trimmedVersion = trim($versionValue);
                if (empty($versionValue) || $versionValue === '' || $trimmedVersion === '' || !preg_match('/[a-zA-Z0-9]/', $trimmedVersion)) {
                    $hasEmptyVersions = true;
                    break;
                }
            }

            // If there are empty versions, return unregistered serial number
            if ($hasEmptyVersions) {
                Log::warning('Serial number has empty versions in DEV environment', [
                    'serial_number' => $serial_number
                ]);

                return response()->json([
                    'api' => 'check upgrade availability',
                    'status' => $this->status,
                    'version' => $this->version,
                    'is_eligible' => false,
                    'message' => 'Unregistered serial number'
                ], $status = 200);
            }

            // System found with eligible versions in DEV environment - minimal logging

            return response()->json([
                'api' => 'serial number validation',
                'status' => $this->status,
                'version' => $this->version,
                'is_valid' => true,
                'system' => $system,
            ], $status = 200);
        } else {
            return response()->json([
                'api' => 'check upgrade availability',
                'status' => $this->status,
                'version' => $this->version,
                'is_eligible' => false,
                'message' => 'No eligible versions found for this device'
            ], $status = 200);
        }
    }

    /**
     * check for OS updates availability
     * string $environment
     * string $serial_number
     *
     * @return JSON response
     */
    public function checkOsUpgradeAvailability($environment, Request $request, OperatingSystemFiles $OperatingSystemFiles)
    {
        $validator = Validator::make([
            'environment' => $request->environment,
        ], [
            'environment' => [
                'required',
                Rule::in(['dev', 'staging', 'prod']),
            ],
        ], [
            'environment.in' => 'The :attribute field is required and must equal one of these values: :values.',
        ]);

        if ($validator->fails()) {
            $messages = $validator->messages();

            return response()->json([
                'api' => 'check operating system upgrade availability',
                'status' => $this->status,
                'version' => $this->version,
                'is_eligible' => false,
                'errors' => $validator->errors(),
            ], $status = 200);
        }

        $files = $OperatingSystemFiles->where($environment, 1)->where('active', 1)->get();

        if ($files) {
            return response()->json([
                'api' => 'check operating system upgrade availability',
                'status' => $this->status,
                'version' => $this->version,
                'operating_system' => $files,
            ], $status = 200);
        } else {
            return response()->json([
                'api' => 'check operating system upgrade availability',
                'status' => $this->status,
                'version' => $this->version,
                'is_eligible' => false,
            ], $status = 200);
        }
    }

    /**
     * download update file
     *
     * @return JSON response
     */
    public function download($environment, $uuid, Request $request, SystemSoftwareFiles $SystemSoftwareFiles, OperatingSystemFiles $OperatingSystemFiles)
    {
        $validator = Validator::make([
            'environment' => $request->environment,
            'uuid' => $request->uuid,
        ], [
            'environment' => [
                'required',
                Rule::in(['dev', 'staging', 'prod']),
            ],
            'uuid' => 'required|uuid',
        ]);

        if ($validator->fails()) {
            $messages = $validator->messages();

            return response()->json([
                'api' => 'check upgrade availability',
                'status' => $this->status,
                'version' => $this->version,
                'is_eligible' => false,
                'errors' => $validator->errors(),
            ], $status = 200);
        }

        if ($uuid) {
            $file = $SystemSoftwareFiles->where('uuid', $uuid)->with(['version' => function ($query) use ($environment) {
                $query->where($environment, 1)->where('active', 1)->first();
            }])->first();

            $version = $file['version'] ?? null;

            if ($version && $file) {
                $source_filepath = $this->system_download_path.$version->uuid.DIRECTORY_SEPARATOR.$file->filename;

                $this->fileDownload($source_filepath, $file->filename);
            } else {
                $file = $OperatingSystemFiles->whereUuid($uuid)->where($environment, 1)->where('active', 1)->first();

                if ($file) {
                    $source_filepath = $this->os_download_path.$file->filename;

                    $this->fileDownload($source_filepath, $file->filename);
                }
            }

            return response()->json([
                'api' => 'file download',
                'status' => $this->status,
                'version' => $this->version,
                'exists' => false,
            ], $status = 200);
        }
    }

    /////--------------------------------------------------------------------------
    ///// 							File FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * function to force download the files using PHP
     *
     * @param  string  $source_filename
     * @param  string  $download_filename
     * @return output file contents
     */
    private function fileDownload($source_filepath, $download_filename)
    {
        if ($source_filepath && File::exists($source_filepath)) {
            set_time_limit(0);

            $file = $source_filepath;
            $fp = @fopen($file, 'rb');

            $size = filesize($file); // File size
            $length = $size;           // Content length
            $start = 0;               // Start byte
            $end = $size - 1;       // End byte

            header('Content-Disposition: attachment; filename="'.$download_filename.'"');
            header('Content-type: application/force-download');
            header("Accept-Ranges: 0-$length");

            if (isset($_SERVER['HTTP_RANGE'])) {
                $c_start = $start;
                $c_end = $end;

                [, $range] = explode('=', $_SERVER['HTTP_RANGE'], 2);
                if (strpos($range, ',') !== false) {
                    header('HTTP/1.1 416 Requested Range Not Satisfiable');
                    header("Content-Range: bytes $start-$end/$size");
                    exit;
                }
                if ($range == '-') {
                    $c_start = $size - substr($range, 1);
                } else {
                    $range = explode('-', $range);
                    $c_start = $range[0];
                    $c_end = (isset($range[1]) && is_numeric($range[1])) ? $range[1] : $size;
                }
                $c_end = ($c_end > $end) ? $end : $c_end;
                if ($c_start > $c_end || $c_start > $size - 1 || $c_end >= $size) {
                    header('HTTP/1.1 416 Requested Range Not Satisfiable');
                    header("Content-Range: bytes $start-$end/$size");
                    exit;
                }
                $start = $c_start;
                $end = $c_end;
                $length = $end - $start + 1;
                fseek($fp, $start);
                header('HTTP/1.1 206 Partial Content');
            }

            header("Content-Range: bytes $start-$end/$size");
            header('Content-Length: '.$length);

            $buffer = 1024 * 8;
            while (! feof($fp) && ($p = ftell($fp)) <= $end) {
                if ($p + $buffer > $end) {
                    $buffer = $end - $p + 1;
                }

                echo fread($fp, $buffer);
                flush();
            }

            fclose($fp);
            exit();
        } else {
            return response()->json(['error' => 'Sorry, but the requested file does not exist.'], $status = 200);
        }
    }

    /////--------------------------------------------------------------------------
    ///// 							STORAGE FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * Process and Store System Upgrade Detail
     *
     * @return array
     */
    public function processSystemUpgradeDetail($environment, Request $request, SystemUpdateHistory $SystemUpdateHistory, SerialNumber $SerialNumber)
    {
        $validator = Validator::make([
            'environment' => $request->environment,
            'serial_number' => $request->serial_number,
            'system_name' => $request->system_name,
            'current_version' => $request->current_version,
            'previous_version' => $request->previous_version,
            'success' => $request->success,
            'update_duration' => $request->update_duration,
            'update_datetime' => $request->update_datetime,
        ], [
            'environment' => [
                'required',
                Rule::in(['dev', 'staging', 'prod']),
            ],
            'serial_number' => 'required|string',
            'system_name' => 'required|string',
            'current_version' => 'required|string',
            'previous_version' => 'required|string',
            'success' => 'required|boolean',
            'update_duration' => 'required|string',
            'update_datetime' => 'required',
        ], [
            'environment.in' => 'The :attribute field is required and must equal one of these values: :values.',
            'serial_number.required' => 'The :attribute field is required and must be a string',
            'serial_number.required' => 'The :attribute field is required and must be a string',
            'system_name.required' => 'The :attribute field is required and must be a string',
            'current_version.required' => 'The :attribute field is required and must be a string',
            'previous_version.required' => 'The :attribute field is required and must be a string',
            'success.required' => 'The :attribute field is required and must be a boolean value',
            'update_duration.required' => 'The :attribute field is required and must be a string',
            'update_datetime.required' => 'The :attribute field is required and must be a string',

        ]);

        if ($validator->fails()) {
            $messages = $validator->messages();

            return response()->json([
                'api' => 'save system upgrade detail',
                'status' => $this->status,
                'version' => $this->version,
                'is_valid' => false,
                'errors' => $validator->errors(),
            ], $status = 200);
        }

        $sn = $SerialNumber->split($request->serial_number);

        $system_detail = $SystemUpdateHistory->create([
            'serial_number' => $request->serial_number,
            'system_name' => $request->system_name,
            'system_model' => $sn->system_model,
            'current_version' => $request->current_version,
            'previous_version' => $request->previous_version,
            'success' => $request->success,
            'update_duration' => $request->update_duration,
            'update_datetime' => $request->update_datetime,
            'dev' => ($request->environment == 'dev') ?? null,
            'staging' => ($request->environment == 'staging') ?? null,
            'prod' => ($request->environment == 'prod') ?? null,

        ]);

        if ($system_detail) {
            // if ($request->environment == 'prod') {
            $this->sendConfirmationEmail($system_detail); //send confirmation emails
            // }

            return response()->json([
                'api' => 'save system upgrade detail',
                'status' => $this->status,
                'version' => $this->version,
                'is_valid' => true,
                'success' => true,
                'uuid' => $system_detail->uuid,
            ], $status = 200);
        } else {
            return response()->json([
                'api' => 'save system upgrade detail',
                'status' => $this->status,
                'version' => $this->version,
                'is_valid' => false,
                'success' => false,
                'errors' => 'there was a problem saving the serial number detail',
            ], $status = 200);
        }
    }

    /**
     * Process and Store System Upgrade Detail
     *
     * @return array
     */
    public function saveLogFiles($environment, $uuid, Request $request, SystemUpdateHistory $SystemUpdateHistory)
    {
        $validator = Validator::make([
            'environment' => $request->environment,
            'uuid' => $request->uuid,
            'file' => $request->file('file'),
        ], [
            'environment' => [
                'required',
                Rule::in(['dev', 'staging', 'prod']),
            ],
            'uuid' => 'required|uuid',
            'file' => 'required|file',
        ]);

        if ($validator->fails()) {
            $messages = $validator->messages();

            return response()->json([
                'api' => 'save error log files',
                'status' => $this->status,
                'version' => $this->version,
                'is_valid' => false,
                'success' => false,
                'errors' => $validator->errors(),
            ], $status = 200);
        }

        $update_history = $SystemUpdateHistory->where('uuid', $uuid)->first();

        if ($uuid && $update_history) {
            $file = $_FILES;

            $request['name'] = $file['file']['name'];
            $request['src_filename'] = $file['file']['name'];
            $request['filesize'] = $file['file']['size'];

            // Store files in a name spaced folder using the parent version uuid
            $target_dir = $this->error_logs_path.$uuid.DIRECTORY_SEPARATOR;
            // Send the request to a Upload file Service Provider
            $upload = new UploadFileService($request, $file, $target_dir, $this->temp_dir, $this->allowed_file_types);
            $result = $upload->uploadFile();

            if (is_array($result)) {
                $update_history->log_file = $result['stored_filename'];
                $update_history->save();

                // if ($request->environment == 'prod') {
                // $this->sendConfirmationEmail($system_detail); //send confirmation emails
                // }

                return response()->json([
                    'api' => 'save error log files',
                    'status' => $this->status,
                    'version' => $this->version,
                    'is_valid' => true,
                    'success' => true,
                ], $status = 200);
            } else {
                return response()->json([
                    'api' => 'save error log files',
                    'status' => $this->status,
                    'version' => $this->version,
                    'is_valid' => false,
                    'success' => false,
                    'errors' => 'There was a problem saving the system log file.',
                ], $status = 200);
            }
        } else {
            return response()->json([
                'api' => 'save error log files',
                'status' => $this->status,
                'version' => $this->version,
                'is_valid' => false,
                'success' => false,
                'errors' => 'There was a problem saving the system log file. Either the UUID is missing or does not match a system update history record.',
            ], $status = 200);
        }
    }

    /////--------------------------------------------------------------------------
    ///// 							Mail FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * Send email confirmation
     *
     * @return send email
     */
    private function sendConfirmationEmail($system_detail)
    {
        $data = [
            'to_name' => $this->to_name,
            'upgrade_matrix_table' => $this->upgradeMatrixTable($system_detail),
        ];

        try {
            $mail = Mail::to($this->to_mail, $this->to_name);

            $mail->bcc($this->bcc);

            $subject = 'Guidance Technologies Software Upgrade Confirmation';
            $template = 'emails.guidance-technology-upgrade';

            $mail->send(new MailableTemplate($template, $data, $subject));
        } catch (Exception $e) {
            // Send do download page
            Log::info('Mail failed to send: '.$email['recipient_email']);

            return false;
        }

        return true;
    }

    /**
     * generate serial number table matrix
     *
     * @param  int  $border
     * @return string HTML
     */
    private function upgradeMatrixTable($system_detail, $border = 0)
    {
        $system_info_fields = [
            'serial_number',
            'system_name',
            'current_version',
            'previous_version',
            'success',
            'update_duration',
            'update_datetime',
        ];

        if ($system_detail) {
            $table_headers = [
                'Serial Number',
                'System Name',
                'Current Version',
                'Previous Version',
                'Success',
                'Update Duration',
                'Update DateTime',
            ];

            $table_open = '<table width="100%" border="'.$border.'" cellspacing="0" cellpadding="0" style="text-align:left; border-bottom:3px solid #CEE2E9;" id="table1" class="spec_table">
                              <tr class="color_one">';
            foreach ($table_headers as $column_header) {
                $table_open .= '<th style="border-bottom:2px solid #CEE2E9; padding:5px 10px; text-align:left; vertical-align:bottom;">'.$column_header.'</th>';
            }
            $table_open .= '</tr>';

            $table_rows = '';

            $table_rows .= '<tr>';

            foreach ($system_info_fields as $k) {
                $table_rows .= '<td style="border-bottom:1px solid #CEE2E9; color:#444444; padding:3px 10px; text-align:left;">'.$system_detail->$k.'</td>';
            }

            $table_rows .= '</tr>';

            $table_close = '</table>';

            return $table_open.$table_rows.$table_close;
        }
    }

    /////--------------------------------------------------------------------------
    ///// 							HELPER FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * generate JWT token
     *
     * @return string JSON
     */
    public function encode(Request $request, JsonWebToken $jwt)
    {
        if ($request->ip() == '127.0.0.1' || $request->ip() == '***********' || $request->ip() == '************') {
            $token = (string) $jwt->encode($payload = 'Do it now!');

            return response()->json([
                'api' => 'jwt token encode',
                'status' => $this->status,
                'version' => $this->version,
                'access_token' => $token,
            ], $status = 200);
        } else {
            return response()->json([
                'api' => 'jwt token encode',
                'status' => $this->status,
                'version' => $this->version,
                'ipv4' => $request->ip(),
                'access_token' => 'unauthorized',
            ], $status = 200);
        }
    }

    /**
     * get UTC time
     *
     * @return string JSON
     */
    public function getTimestamp()
    {
        $time = Carbon::now('UTC')->timestamp;

        return response()->json([
            'api' => 'UTC timestamp',
            'status' => $this->status,
            'version' => $this->version,
            'utc_time' => $time,
        ], $status = 200);
    }

    // /**
    // * Validate that email is valid format
    // *
    // * @param string $str
    // * @return bool
    // */
    // private function validEmail($str)
    // {
    //     return (preg_match("/^[a-z0-9]+([_+\\.-][a-z0-9]+)*@([a-z0-9]+([\.-][a-z0-9]+)*)+\\.[a-z]{2,}$/ix", $str)) ? true : false;
    // }
    //
    //
    // /**
    // * sorts array by order and only returns keys that match the order array
    // *
    // * @param array $array_to_sort
    // * @param array $order
    // * @return array $sorted_array
    // */
    // private function sortArrayKeysByArray($array_to_sort, $order)
    // {
    //     if (is_array($order)) {
    //         foreach ($order as $k) {
    //             if (property_exists($k, $array_to_sort)) {
    //                 $sorted_array[$k] = $array_to_sort[$k];
    //             }
    //         }
    //     }
    //
    //     return $sorted_array;
    // }
}

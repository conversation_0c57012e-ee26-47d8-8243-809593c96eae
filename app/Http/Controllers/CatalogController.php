<?php

namespace App\Http\Controllers;

use App\Models\ProductCatalog\CatalogCategory;
use App\Models\ProductCatalog\CategoryField;
use Log;

class CatalogController extends Controller
{
    /**
     * Display index page view.
     * GET /sitecategories.
     *
     * @return Response
     */
    public function index()
    {
        // Get product code by id
        $catalog_categories = CatalogCategory::where('active', '1')->where('published', '1')->orderBy('title')->get();

        if (view()->exists('site.catalog.index')) {
            return view('site.catalog.index', compact('catalog_categories'));
        } else {
            return view('errors.404');
        }
    }

    /**
     * Display the specified resource.
     * GET /{category}.
     *
     * @param  string  $category
     * @return view
     */
    public function category($category, $type = '')
    {
        $catalog_category = CatalogCategory::where('short_name', $category)->first();
        if ($catalog_category) {
            $fields = CategoryField::with(
                ['fieldOptions' => function ($query) {
                    $query->orderBy('sort_order', 'asc');
                }]
            )->where('category_id', $catalog_category->id)->orderBy('sort_order', 'asc')->get();
        } else {
            Log::error($category);

            return view('errors.404');
        }
        if (view()->exists('site.catalog.selection')) {
            return view('site.catalog.selection', compact('fields', 'catalog_category', 'type'));
        } else {
            return view('errors.404');
        }
    }

    /**
     * Display the specified resource.
     * GET /{category}/{product}.
     *
     * @param  string  $category
     * @param  string  $product
     * @return view
     */
    public function product($category, $product)
    {
        if (view()->exists("site.catalog.$category.$product")) {
            return view("site.catalog.$category.$product");
        } else {
            return view('errors.404');
        }
    }
}

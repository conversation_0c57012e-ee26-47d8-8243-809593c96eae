<?php

namespace App\Http\Controllers\ImagingUpgrade;

//if($_SERVER['REMOTE_ADDR'] == '**************' || $_SERVER['REMOTE_ADDR'] == '************' || $_SERVER['HTTP_HOST'] == 'slcpc06813'){
//	ini_set('display_errors', 1); // display errors
//	error_reporting (-1); // Report all PHP errors
//}

use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Mail\MailableTemplate;
use App\Models\ImagingUpgrade\IuaSystemCountryLocation;
use App\Models\ImagingUpgrade\IuaSystemCountryLocationUat;
use App\Models\ImagingUpgrade\IuaSystemUpgradeCountryEligibilityMatrix;
use App\Models\ImagingUpgrade\IuaSystemUpgradeCountryEligibilityMatrixUat;
use App\Models\ImagingUpgrade\IuaUserDetail;
use App\Models\ImagingUpgrade\IuaUserDetailUat;
use App\Models\Site\SitePage;
use App\Services\DownloadFileService;
use Illuminate\Http\Request;
use Log;
use Mail;
use Storage;

class ImagingUpgradeApiController extends Controller
{
    public $api_status = 'live';  //Mode: live || test

    private $version = '4.0.2'; // Revision history number

    private $mode;

    //////	Download Source Path
    private $download_source_path = 'imaging_upgrade/prod_source/';

    private $download_file_route = 'update.file.download';

    //////	Dymax Email Address
    private $to_mail = '<EMAIL>';

    private $to_name = 'Dymax';

    private $bcc = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

    private $domain_name = 'https://www.bardaccess.com/';

    private $literature_path = '/storage/literature/';

    private $upgrade_matrix_array = [];

    private $system_upgrade_detail;

    private $system_info_fields;

    private $upgrade_info;

    private $user_info;

    private $system_detail;

    private $system_name;

    private $sn_indentifiers;

    /**
     * Constructor
     */
    public function __construct(Request $request)
    {
        $route_segment = $request->segment(4);

        if (isset($route_segment) && $route_segment == 'uat') {
            $this->mode = 'uat';

            //////  UAT User Acceptance Testing
            //////  IUA - Set UAT table in the Imaging Upgrade API Database Models
            // $this->system_upgrade_matrix_table            = 'iua_system_upgrade_matrices_uat';
            // $this->user_detail_table                      = 'iua_user_details_uat';
            // $this->user_serial_numbers_detail_table       = 'iua_user_serial_numbers_details_uat';

            //////	Download Source Path
            $this->download_source_path = 'imaging_upgrade/test_source/';
            $this->download_file_route = 'update.file.download.uat';

            //////	UAT Email Address
            //$to_mail									= "<EMAIL>";
            //$to_name									= "Bill Roeca";
            $this->to_mail = '<EMAIL>';
            $this->to_name = 'Staging/Test Mode';
            $this->bcc = ['<EMAIL>', '<EMAIL>'];
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function index()
    {
        if (view()->exists('site.products.imaging.upgrade.device-manager-download')) {
            return view('site.products.imaging.upgrade.device-manager-download');
        } else {
            return view('errors.404');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function form()
    {
        if (view()->exists('site.products.imaging.upgrade.test-form')) {
            return view('site.products.imaging.upgrade.test-form');
        } else {
            return view('errors.404');
        }
    }

    /**
     * system upgrade detail request
     * array $request
     *
     * @return JSON response
     */
    public function systemUpgradeDetail(Request $request)
    {
        if ($request->input('system_upgrade_detail') !== null) {
            return response()->json(['system_upgrade_detail_confirmation' => $this->processSystemUpgradeDetail($request->input('system_upgrade_detail'))], $status = 200, $headers = ['X-CSRF-TOKEN' => csrf_token()]);
        }

        /*		$system_upgrade_detail = '{"contact":{"department":"Media Services","organization":"Bard Access","name":"Kristoph Herron","phone":"************","email":"<EMAIL>","optin":true,"ismanager":true},"serial_numbers":[{"serial_number":"DYUDA101","location":"Facility","department":"Department","contact":"Contact Person","email":"Contact Email","installed_version":{"dicom":"1.1.3.3","ultrasound":"1.0.2","sherlock":"1.02","shell":"2.1.4"}},{"serial_number":"DYUDA111","location":"North Campus","department":"Training Lab","contact":"Teacher","email":"************","installed_version":{"dicom":"1.1.3.3","ultrasound":"1.0.2","sherlock":"1.02","shell":"2.1.4"}}]}';

                if ( $system_upgrade_detail !== NULL ){

                    return response()->json(["system_upgrade_detail_confirmation" => $this->processSystemUpgradeDetail( $system_upgrade_detail ) ]);

                }*/
    }

    /**
     * validate serial number request
     * array $serial_number
     *
     * @return JSON response
     */
    public function serialNumberValidation($serial_number)
    {
        if ($serial_number !== null) {
            return response()->json(['serial_number_detail' => $this->processSerialNumberInput([$serial_number])], $status = 200, $headers = ['X-CSRF-TOKEN' => csrf_token()]);
        }
    }

    /**
     * validate serial number request
     * object $request
     *
     * @return JSON response
     */
    public function serialNumberValidationForm(Request $request)
    {
        if ($request->input('serial_number') !== null) {
            return $this->serialNumberValidation($request->input('serial_number'));
        }
    }

    /**
     * validate system country location
     * string $system_id
     *
     * @return object
     */
    public function systemUpgradeCountryLocation($serial_number)
    {
        if ($this->mode == 'uat') {
            $country_code = IuaSystemCountryLocationUat::where('serial_number', $serial_number)->first();
        } else {
            $country_code = IuaSystemCountryLocation::where('serial_number', $serial_number)->first();
        }

        return $country_code;
    }

    /**
     * validate system upgrade eligibility
     * string $system_id
     *
     * @return bool
     */
    public function upgradeMatrix($system_id, $country_code)
    {
        if ($this->mode == 'uat') {
            $systems = IuaSystemUpgradeCountryEligibilityMatrixUat::where('system_id', $system_id)->where('country_code', $country_code)->get();
        } else {
            $systems = IuaSystemUpgradeCountryEligibilityMatrix::where('system_id', $system_id)->where('country_code', $country_code)->get();
        }

        if ($systems->count() > 0) {
            foreach ($systems as $system) {
                $this->upgrade_matrix_array[$system->system_id]['software_detail'] = ['system_name' => $system->system_name, 'ultrasound' => $system->ultrasound, 'sherlock' => $system->sherlock, 'shell' => $system->shell, 'dicom' => $system->dicom];
                $this->upgrade_matrix_array[$system->system_id]['resources'] = ['source_filename' => $system->source_filename, 'download_filename' => $system->download_filename, 'product_image' => $system->product_image, 'installation_instructions' => $system->installation_instructions];
            }
        }
    }

    /**
     * download update file
     *
     * @return JSON response
     */
    public function download($serial_number)
    {
        if ($this->validateSerialNumber($serial_number) == true) {

            $system_id = $this->identifySystemModel($serial_number);
            $location = $this->systemUpgradeCountryLocation($serial_number);

            if ($location) {
                $this->upgradeMatrix($system_id, $location->country_code);

                $source_file = $this->upgrade_matrix_array[$system_id]['resources']['source_filename'];

                $this->fileDownload($source_file, $source_file); //initiate the file download
            } else {
                return false;
            }
        }
    }

    /**
     * validate serial number request
     * array $request
     *
     * @return JSON response
     */
    public function getFileChecksum($serial_number, Request $request)
    {
        if ($serial_number !== null) {
            $serial_number = strtoupper($serial_number);

            if ($this->validateSerialNumber($serial_number) == true) {

                $system_id = $this->identifySystemModel($serial_number);
                $location = $this->systemUpgradeCountryLocation($serial_number);

                if ($location) {
                    $this->upgradeMatrix($system_id, $location->country_code);

                    $system_id = $this->identifySystemModel($serial_number);
                    $source_file = $this->upgrade_matrix_array[$system_id]['resources']['source_filename'];

                    // set storage path, path subset and filename
                    $filepath = Storage::disk('local')->path($this->download_source_path.$source_file);

                    $md5_checksum = hash_file('md5', $filepath);

                    return response()->json(['serial_number' => $serial_number, 'file' => $source_file, 'md5_checksum' => $md5_checksum], $status = 200, $headers = ['X-CSRF-TOKEN' => csrf_token()]);
                } else {
                    return false;
                }
            }
        }
    }

    /////--------------------------------------------------------------------------
    ///// 							SERIAL NUMBER FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * Validate serial numbers
     *
     * @param  string  $serial_number
     * @return bool
     */
    private function validateSerialNumber($serial_number)
    {
        if (strlen($serial_number) == 8) {
            $this->sn_indentifiers = $this->splitSerialNumber($serial_number, [2, 1, 1, 1, 3]);

            // Allowed Character Sets:
            $manufacturer = (preg_match('/^([d][y])+$/i', $this->sn_indentifiers[0]));
            $manufacture_year = (preg_match('/^([a-z])+$/i', $this->sn_indentifiers[1]));
            $manufacture_month = (preg_match('/^([a-lnp-z])+$/i', $this->sn_indentifiers[2]));
            $system_id = (preg_match('/^([DQ])+$/i', $this->sn_indentifiers[3])); // SR5 & SR6 & Vision, Vision II & Prevue & Prevue+ & Sherlock 3CG
            $lot_number = (preg_match('/^([0-9])+$/i', $this->sn_indentifiers[4]));
        } elseif (strlen($serial_number) == 9) {
            $this->sn_indentifiers = $this->splitSerialNumber($serial_number, [2, 1, 1, 2, 3]);

            // AB C D EF GHI
            // AB indicate the plant
            // C indicates the year
            // D indicates the month
            // EF will either be AC for the United States system, AD will be for the Europe system
            // GHI indicate 000-999 for the unit number built

            // Allowed Character Sets:
            $manufacturer = (preg_match('/^(dy|as)+$/i', $this->sn_indentifiers[0]));  // match only "dy" or "as". Ex: DYTTAC123 or ASTTAC123
            $manufacture_year = (preg_match('/^([a-z])+$/i', $this->sn_indentifiers[1]));
            $manufacture_month = (preg_match('/^([a-np-z])+$/i', $this->sn_indentifiers[2]));
            $system_id = (preg_match('/^([ABCDEFY])+$/i', $this->sn_indentifiers[3])); // SR8 - AC for the United States system, AD will be for the Europe system
            $lot_number = (preg_match('/^([0-9])+$/i', $this->sn_indentifiers[4]));
        } else {
            return false;
        }

        if ($system_id == true) {
            $location = $this->systemUpgradeCountryLocation($serial_number);
            $system_model = $this->identifySystemModel($serial_number);

            if ($location) {

                if ($this->mode == 'uat') {
                    $system = IuaSystemUpgradeCountryEligibilityMatrixUat::where('system_id', $system_model)->where('country_code', $location->country_code)->first();
                } else {
                    $system = IuaSystemUpgradeCountryEligibilityMatrix::where('system_id', $system_model)->where('country_code', $location->country_code)->first();
                }

                if ($system && $system->ultrasound && $system->sherlock && $system->shell && $system->dicom) {
                    $eligible = true;
                } else {
                    $eligible = false;
                }
            } else {
                $eligible = false;
            }
        }

        return ($manufacturer == true && $manufacture_month == true && $manufacture_year == true && $system_id == true && $lot_number == true && $eligible == true) ? true : false; // True == Valid Serial number
    }

    /**
     * Split serial number into its schema
     *
     * @param  string  $serial_number
     * @param  array  $position_length
     * @return array $split_serial_number_array
     */
    private function splitSerialNumber($serial_number, $position_length)
    {
        $pos = 0;

        foreach ($position_length as &$value) {
            $split_serial_number_array[] = substr($serial_number, $pos, $value);
            $pos += $value;
        }

        return $split_serial_number_array;
    }

    /**
     * Identify system model: Linux vs Windows
     *
     * @param  string  $serial_number
     * @return string
     */
    private function identifySystemModel($serial_number)
    {
        return $system_id = $this->sn_indentifiers[3];
    }

    /**
     * Identify bardaccess product page ID
     *
     * @param  string  $system_id
     * @return string $product_page_id
     */
    private function identifyProductPage($system_id)
    {
        $product_page_id = '';

        switch ($system_id) {

            //Windows - SR 8
            case 'AC':
            case 'AD':
                $product_page_id = 174;
                break;

                //Linux - Prevue+
            case 'Q':
                $product_page_id = null;
                break;

                //Linux - Prevue II AKA: Prevue NXT
            case 'AY':
            case 'BA':
                $product_page_id = 175;
                break;

                // Sherlock 3CG
            case 'D':
                $product_page_id = null;
                break;

                // Sherlock 3CG+
            case 'AE':
                $product_page_id = 176;
                break;

                // Sherlock 3CG+
            case 'AF': // international
                $product_page_id = 178;
                break;

                // SR9
            case 'CA':
                $product_page_id = 179;
                break;
        }

        return $product_page_id;
    }

    /**
     * Process and validate the serial number
     *
     * @param  array  $array
     * @return array $serial_number_validation
     */
    private function processSerialNumberInput($array = '')
    {
        $i = 0;
        foreach ($array as $serial_number) {

            // Check serial validity first, which sets system identifiers array.
            $serial_number_validity = $this->validateSerialNumber($serial_number);
            $serial_number = strtoupper($serial_number);

            $location = $this->systemUpgradeCountryLocation($serial_number);

            if ($serial_number_validity == true && isset($location)) {
                $system_id = $this->identifySystemModel($serial_number);

                $this->upgradeMatrix($system_id, $location->country_code);
                $product_info = $this->getProductInfo();
                $product_page_id = $this->identifyProductPage($system_id);

                $serial_number_validation[$i]['serial_number'] = $serial_number;
                $serial_number_validation[$i]['valid'] = $serial_number_validity;
                $serial_number_validation[$i]['system_model'] = $system_id;
                $serial_number_validation[$i]['software_version'] = $this->upgrade_matrix_array[$system_id]['software_detail'];
                $serial_number_validation[$i]['download_url'] = route($this->download_file_route, [$serial_number]);
                $serial_number_validation[$i]['title'] = ($product_page_id != null) ? $product_info[$product_page_id]['title'] : $this->upgrade_matrix_array[$system_id]['software_detail']['system_name'];    // if the title isn't in the search_page_index table then fallback to the title from the upgrade matrix table.
                $serial_number_validation[$i]['webpage'] = ($product_page_id != null) ? $product_info[$product_page_id]['webpage'] : null;
                // use the image URL set in the Matrix as the default otherwise use the Product Hero Image.
                $serial_number_validation[$i]['product_image'] = $this->upgrade_matrix_array[$system_id]['resources']['product_image'] ? $this->upgrade_matrix_array[$system_id]['resources']['product_image'] : null;
                $serial_number_validation[$i]['installation_instructions'] = $this->upgrade_matrix_array[$system_id]['resources']['installation_instructions'];
                $serial_number_validation[$i]['resources'] = ($product_page_id != null) ? $this->processResources($product_info[$product_page_id]['resources']) : null;
                $serial_number_validation[$i]['message'] = null;
            } else {
                $serial_number_validation[$i]['serial_number'] = $serial_number;
                $serial_number_validation[$i]['valid'] = $serial_number_validity;
                $serial_number_validation[$i]['message'] = null;
            }
            $i++;
        }

        // return output array
        return $serial_number_validation;
    }

    /**
     * Get product info from product page info from the search page index
     *
     * @return string
     */
    private function getProductInfo()
    {
        $row = [];

        $site_pages = SitePage::with(['literature' => function ($query) {
            $query->where('active', '1')->orderBy('sort_order')->get();
        }])->where('category_id', '8')->where('product_page', '1')->get();

        if ($site_pages->count() > 0) {
            foreach ($site_pages as $site_page) {
                $row[$site_page->id] = ['webpage' => url($site_page->url), 'title' => strip_tags($site_page->title), 'product_image' => $site_page->product_image, 'resources' => $site_page->literature];
            }
        }

        return $row;
    }

    /**
     * Process product resources and convert from HTML to an array.
     *
     * @param  object  $product_resources
     * @return array $resources
     */
    private function processResources($product_resources = '')
    {
        if ($product_resources->count() > 0) {
            $i = 0;
            foreach ($product_resources as $literature) {
                $resources[$i]['key'] = $literature->title;
                $resources[$i]['value'] = asset($this->literature_path.$literature->filename);
                $i++;
            }

            return $resources;
        }
    }

    /////--------------------------------------------------------------------------
    ///// 							File FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * function to zip and force download the files using PHP
     *
     * @param  string  $source_filename
     * @param  string  $download_file_name
     * @return output file contents
     */
    private function fileDownload($source_filename, $download_file_name)
    {
        $source_filepath = $this->download_source_path.$source_filename;

        if ($source_filename && Storage::exists($source_filepath)) {
            $filepath = Storage::disk('local')->path($source_filepath);

            // mod_xsendfile is a small Apache 2 module that processes X-Sendfile headers registered by the original output handler.
            // If it encounters the presence of such header it will discard all output and send the file specified by that header
            // instead using Apache internal including all optimizations like caching-headers and sendfile or mmap if configured.
            // http://www.brighterlamp.com/2010/10/send-files-faster-better-with-php-mod_xsendfile
            /*
                        $response = \Response::download($filepath);
                        $response->trustXSendfileTypeHeader();
                        $response->prepare(Request::createFromGlobals());
                        $response->send();
            */
            // alternative File download service if the byte-range requests don't work correctly from the default laravel methods.
            $download = new DownloadFileService;
            $download->setByFile($filepath);                    //Download from a file
            $download->use_resume = true;                        //Enable Resume Mode
            $download->output_filename = $download_file_name;    //Set output file name
            $download->mime = 'application/force-download';        //Set MIME type
            $download->download();                                //Download File
        } else {
            return response()->json(['error' => 'Sorry, but the requested file does not exist.'], $status = 200, $headers = ['X-CSRF-TOKEN' => csrf_token()]);
        }
    }

    /////--------------------------------------------------------------------------
    ///// 							Mail FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * Send email confirmation
     *
     * @return send email
     */
    private function sendConfirmationEmail()
    {
        $data = ['to_name' => $this->to_name, 'upgrade_matrix_table' => $this->upgradeMatrixTable(), 'confirmation_user_info' => $this->confirmationUserInfo()];

        try {
            $mail = Mail::to($this->to_mail, $this->to_name);

            $mail->bcc($this->bcc);

            $subject = 'Imaging Software Upgrade Confirmation';
            $template = 'emails.imaging-upgrade';

            $mail->send(new MailableTemplate($template, $data, $subject));

        } catch (Exception $e) {
            // Send do download page
            Log::info('Mail failed to send: '.$this->to_mail);

            return false;
        }

        return true;
    }

    /**
     * Format user detail for Email confirmation.
     *
     * @return string $user_fields
     */
    private function confirmationUserInfo()
    {
        $map_user_fields = ['name', 'title', 'organization', 'department', 'email', 'phone'];

        if (is_array($this->user_info)) {
            $user_fields = '';
            foreach ($this->sortArrayKeysByArray($this->user_info, $map_user_fields) as $k => $v) {
                $user_fields .= '<p><b>'.$k.'</b>: '.$v.'</p>';
            }

            return $user_fields;
        }
    }

    /**
     * generate serial number table matrix
     *
     * @param  int  $border
     * @return string HTML
     */
    private function upgradeMatrixTable($border = 0)
    {
        if ($this->mode == 'uat') {
            $systems = IuaSystemUpgradeCountryEligibilityMatrixUat::all();
        } else {
            $systems = IuaSystemUpgradeCountryEligibilityMatrix::all();
        }

        if ($systems->count() > 0) {
            foreach ($systems as $system) {
                $this->upgrade_matrix_array[$system->system_id]['software_detail'] = ['system_name' => $system->system_name, 'ultrasound' => $system->ultrasound, 'sherlock' => $system->sherlock, 'shell' => $system->shell, 'dicom' => $system->dicom];
                $this->upgrade_matrix_array[$system->system_id]['resources'] = ['source_filename' => $system->source_filename, 'download_filename' => $system->download_filename, 'product_image' => $system->product_image, 'installation_instructions' => $system->installation_instructions];
            }
        }

        $this->system_info_fields = ['serial_number', 'system_name', 'ultrasound', 'sherlock', 'shell', 'dicom', 'contact', 'department', 'email', 'location'];

        if (is_array($this->system_upgrade_detail)) {
            $table_headers = ['Serial Number', 'Ultrasound', 'Sherlock', 'Shell', 'Dicom', 'Contact', 'Department', 'Email', 'Location'];

            $table_open = '
    <table width="100%" border="'.$border.'" cellspacing="0" cellpadding="0" style="text-align:left; border-bottom:3px solid #CEE2E9;" id="table1" class="spec_table">
      <tr class="color_one">';
            foreach ($table_headers as $column_header) {
                $table_open .= '
		<th style="border-bottom:2px solid #CEE2E9; padding:5px 10px; text-align:left; vertical-align:bottom;">'.$column_header.'</th>';
            }
            $table_open .= '
	  </tr>
';
            $table_rows = '';
            foreach ($this->system_detail as $system_info) {
                $installed_version = $system_info['installed_version'];

                $this->system_upgrade_detail = array_merge($system_info, $installed_version);
                $table_rows .=
                    '<tr>';
                $system_upgrade_detail = $this->sortArrayKeysByArray($this->system_upgrade_detail, $this->system_info_fields);
                foreach ($system_upgrade_detail as $k => $v) {
                    if (in_array($k, $this->system_info_fields)) {
                        $table_rows .=
                            '<td style="border-bottom:1px solid #CEE2E9; color:#444444; padding:3px 10px; text-align:left;">'.$v.'</td>';
                    }
                }
                $table_rows .=
                    '</tr>';
            }

            $table_close =
                '</table>';

            return $table_open.$table_rows.$table_close;
        }
    }

    /////--------------------------------------------------------------------------
    ///// 							STORAGE FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * Process and Store System Upgrade Detail
     *
     * @return array
     */
    private function processSystemUpgradeDetail($request)
    {
        $this->upgrade_info = json_decode(stripslashes($request), true);

        //dd($this->upgrade_info);

        $this->user_info = $this->upgrade_info['contact'];
        $this->system_detail = $this->upgrade_info['serial_numbers'];

        if (is_array($this->user_info)) {
            $user_detail = ($this->mode == 'uat') ? IuaUserDetailUat::create($this->user_info) : IuaUserDetail::create($this->user_info);
        }

        if ($user_detail->exists == true) {

            //dd($user_detail);

            if (is_array($this->system_detail)) {
                $insert = 0;

                foreach ($this->system_detail as $system_info) {
                    $installed_version = $system_info['installed_version'];

                    $this->system_upgrade_detail = array_merge($system_info, $installed_version);
                    unset($this->system_upgrade_detail['installed_version']);

                    //dd( $this->system_upgrade_detail);

                    if (is_array($this->system_upgrade_detail)) {
                        $system_detail = $user_detail->serialNumbersDetail()->create($this->system_upgrade_detail);

                        $insert++;
                    }
                }
            }

            if ($insert >= 1 && $insert == count($this->upgrade_info['serial_numbers'])) {
                $success = $this->sendConfirmationEmail(); //send confirmation emails

                if ($success == true) {
                    return ['success' => true, 'message' => 'Your information was saved successfully.'];
                } else {
                    return ['success' => false, 'message' => 'Your information was saved successfully. But, there was a problem sending out your confirmation email.'];
                }
            } else {
                return ['success' => false, 'message' => 'Your information was saved successfully. But, there was a problem saving your serial number information.'];
            }
        } else {
            return ['success' => false, 'message' => 'There was a problem saving your user profile.'];
        }
    }

    /**
     * Get System name from serial number
     *
     * @return array
     */
    private function getSystemName($serial_number)
    {
        $system_name = $this->processSerialNumberInput([$serial_number]);

        return $system_name[0]['title'] ?? '';
    }

    /**
     * Get System name from serial number
     *
     * @return array
     */
    public function addSystemNameToSerialNumbersDetails()
    {
        $details = \App\Models\ImagingUpgrade\IuaUserSerialNumbersDetail::all();

        foreach ($details as $detail) {
            $x = $this->validateSerialNumber($detail->serial_number);
            if ($x == true) {
                $this->getSystemName($detail->serial_number);
            }
        }

    }

    /////--------------------------------------------------------------------------
    ///// 							HELPER FUNCTIONS
    /////--------------------------------------------------------------------------

    /**
     * Validate that email is valid format
     *
     * @param  string  $str
     * @return bool
     */
    private function validEmail($str)
    {
        return (preg_match("/^[a-z0-9]+([_+\\.-][a-z0-9]+)*@([a-z0-9]+([\.-][a-z0-9]+)*)+\\.[a-z]{2,}$/ix", $str)) ? true : false;
    }

    /**
     * sorts array by order and only returns  keys that match the order array
     *
     * @param  array  $array_to_sort
     * @param  array  $order
     * @return array $sorted_array
     */
    private function sortArrayKeysByArray($array_to_sort, $order)
    {
        if (is_array($order)) {
            foreach ($order as $k) {
                if (array_key_exists($k, $array_to_sort)) {
                    $sorted_array[$k] = $array_to_sort[$k];
                }
            }
        }

        return $sorted_array;
    }
}
<?php

namespace App\Http\Requests;

class ElabelingRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'ifu_search' => 'required|alpha_num|max:10',
        ];
    }
}

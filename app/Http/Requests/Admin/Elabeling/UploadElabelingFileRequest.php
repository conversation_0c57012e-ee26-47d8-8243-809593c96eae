<?php

namespace App\Http\Requests\Admin\Elabeling;

use App\Http\Requests\Request;
use App\Services\CustomValidators\ElabelingFileUploadValidator;

class UploadElabelingFileRequest extends Request
{
    protected $extra_file_validation;

    /**
     * Constructor for request from file upload form
     *
     * @return bool
     */
    public function __construct(ElabelingFileUploadValidator $extra_file_validation)
    {
        $this->extra_file_validation = $extra_file_validation;
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file' => 'required|mimes:pdf',
        ];
    }

    /**
     * After Standard validation methods of authorize() and rules() get run, run this.
     *
     * @return bool
     */
    public function __destruct()
    {
        // check if valid
        if ($this->extra_file_validation->validateFile($request)) {

            return true;

        }

        // if not, throw exception
        throw new Exception('Extra Validation failed', $this->extra_file_validation->getErrors());
    }
}

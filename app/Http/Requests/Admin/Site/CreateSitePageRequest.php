<?php

namespace App\Http\Requests\Admin\Site;

use App\Http\Requests\Request;

class CreateSitePageRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'product_page' => 'required',
            'featured' => 'required',
            'new_product' => 'required',
            'active' => 'required',
            'url' => 'required',
        ];
    }
}

<?php

/****************************
* Comments: ELabeling Helpers which can be loaded into scope from any view
*****************************/

// GET ALL OF THE RELATED DOCUMENTS
function getResourceItem($part_number, $language, $trashed)
{
    if ($trashed) {
        $relatedItem = \DB::table('eifu_library')
            ->join('eifu_library_files', 'eifu_library_files.parent_id', '=', 'eifu_library.id')
            ->join('eifu_resource_types', 'eifu_library.resource_type', '=', 'eifu_resource_types.id')
            ->select('eifu_library.part_number', 'eifu_library.title', 'eifu_library.replacement_part_number', 'eifu_library.replacement_notification_message', 'eifu_library.replacement_rationale_notes', 'eifu_library.replacement_date', 'eifu_library_files.filename', 'eifu_library_files.filesize', 'eifu_resource_types.name', 'eifu_resource_types.order')
            ->where('eifu_library.part_number', $part_number)
            ->whereNotNull('eifu_library.deleted_at')
            ->where('eifu_library_files.language', $language)
                    //->orWhere('eifu_library_files.language', 'en')
            ->orderBy('eifu_library.part_number', 'asc')
            ->first();
    } else {
        $relatedItem = \DB::table('eifu_library')
            ->join('eifu_library_files', 'eifu_library_files.parent_id', '=', 'eifu_library.id')
            ->join('eifu_resource_types', 'eifu_library.resource_type', '=', 'eifu_resource_types.id')
            ->select('eifu_library.part_number', 'eifu_library.title', 'eifu_library.replacement_part_number', 'eifu_library.replacement_notification_message', 'eifu_library.replacement_rationale_notes', 'eifu_library.replacement_date', 'eifu_library_files.filename', 'eifu_library_files.filesize', 'eifu_resource_types.name', 'eifu_resource_types.order')
            ->where('eifu_library.part_number', $part_number)
            ->where('eifu_library_files.language', $language)
                    //->orWhere('eifu_library_files.language', 'en')
            ->orderBy('eifu_library.part_number', 'asc')
            ->first();
    }

    if (isset($relatedItem) > 0) {

        // If a part number/approval number has been replaced by a new part then get the replacement part number info instead.
        if (isset($relatedItem->replacement_part_number) && ($relatedItem->replacement_part_number != '')) {

            // Loop through self to get and return the associative result set
            return $this->getResourceItem($relatedItem->replacement_part_number, $language);
        } else {

            // return associative result set
            return $relatedItem;
        }
    } else {
        return false;
    }
}

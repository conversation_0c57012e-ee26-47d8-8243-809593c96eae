<?php

namespace App\Jobs;

use App\Mail\MailableTemplate;
use DB;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;
use Mail;

class ImagingUpgradeReportingJob implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('Starting Imaging Upgrades Monthly Report job');

        // Query using the new country-based matrix
        $devices = DB::select(
            'SELECT
            m.system_name,
            m.country_code,
            c.name as country_name,
            m.ultrasound,
            m.sherlock,
            m.shell,
            m.dicom,
            COUNT(DISTINCT d.serial_number) AS device_count
        FROM
            iua_system_upgrade_country_eligibility_matrices m
            INNER JOIN
            iua_user_serial_numbers_details d
            ON
                m.ultrasound = d.ultrasound AND
                m.sherlock = d.sherlock AND
                m.shell = d.shell AND
                m.dicom = d.dicom
            LEFT JOIN
            iua_system_country_location l
            ON
                d.serial_number = l.serial_number
            LEFT JOIN
            gt_countries c
            ON
                m.country_code = c.alpha_2_code
        WHERE
            YEAR(d.created_at) = YEAR(CURRENT_DATE() - INTERVAL 1 MONTH)
        AND
            MONTH(d.created_at) = MONTH(CURRENT_DATE() - INTERVAL 1 MONTH)
        GROUP BY
            m.system_name,
            m.country_code,
            c.name,
            m.ultrasound,
            m.sherlock,
            m.shell,
            m.dicom'
        );

        Log::info('Found ' . count($devices) . ' device records for the report');

        $date = \Carbon\Carbon::now('America/Denver')->subMonth()->format('F Y');
        // dd($date);

        // try {
        $mail = Mail::to(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']);

        $subject = 'Bard Access Systems | Imaging Upgrades Monthly Report';
        $data = ['devices' => $devices, 'date' => $date];
        $template = 'emails.imaging-upgrade-reporting';

        $mail->send(new MailableTemplate($template, $data, $subject));
        // } catch (Exception $e) {
        //     // Send do download page
        //     Log::info('Imaging Upgrades Report Mail failed to send.'.$e);
        // }
    }
}

<?php

namespace App\Jobs;

use App\Mail\MailableTemplate;
use DB;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Log;
use Mail;

class ImagingUpgradeWeeklyReporting<PERSON><PERSON> implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $devices = DB::select('SELECT iua_system_upgrade_matrices.system_name, iua_user_serial_numbers_details.ultrasound, iua_user_serial_numbers_details.sherlock, iua_user_serial_numbers_details.shell, iua_user_serial_numbers_details.dicom, COUNT(DISTINCT iua_user_serial_numbers_details.serial_number) AS device_count FROM iua_system_upgrade_matrices JOIN iua_user_serial_numbers_details ON iua_system_upgrade_matrices.ultrasound = iua_user_serial_numbers_details.ultrasound WHERE YEAR(iua_user_serial_numbers_details.created_at) = YEAR(CURRENT_DATE() - INTERVAL 1 WEEK) AND  WEEK(iua_user_serial_numbers_details.created_at) = WEEK(CURRENT_DATE() - INTERVAL 1 WEEK) GROUP BY iua_user_serial_numbers_details.ultrasound');

        $date = \Carbon\Carbon::now('America/Denver')->subWeek()->format('F Y');
        // dd($date);

        try {
            $mail = Mail::to(['<EMAIL>']);

            $subject = 'Bard Access Systems | Imaging Upgrades Weekly Report';
            $data = ['devices' => $devices, 'date' => $date];
            $template = 'emails.imaging-upgrade-reporting';

            $mail->send(new MailableTemplate($template, $data, $subject));
        } catch (Exception $e) {
            // Send do download page
            Log::info('Imaging Upgrades Report Mail failed to send.'.$e);
        }
    }
}

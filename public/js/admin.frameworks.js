if(function(t,e){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(t,e){"use strict";var i=[],n=t.document,s=Object.getPrototypeOf,o=i.slice,a=i.concat,r=i.push,l=i.indexOf,h={},c=h.toString,u=h.hasOwnProperty,d=u.toString,p=d.call(Object),f={},g=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType},m=function(t){return null!=t&&t===t.window},v={type:!0,src:!0,noModule:!0};function _(t,e,i){var s,o=(e=e||n).createElement("script");if(o.text=t,i)for(s in v)i[s]&&(o[s]=i[s]);e.head.appendChild(o).parentNode.removeChild(o)}function b(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?h[c.call(t)]||"object":typeof t}var y=function(t,e){return new y.fn.init(t,e)},w=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;function x(t){var e=!!t&&"length"in t&&t.length,i=b(t);return!g(t)&&!m(t)&&("array"===i||0===e||"number"==typeof e&&e>0&&e-1 in t)}y.fn=y.prototype={jquery:"3.3.1",constructor:y,length:0,toArray:function(){return o.call(this)},get:function(t){return null==t?o.call(this):t<0?this[t+this.length]:this[t]},pushStack:function(t){var e=y.merge(this.constructor(),t);return e.prevObject=this,e},each:function(t){return y.each(this,t)},map:function(t){return this.pushStack(y.map(this,function(e,i){return t.call(e,i,e)}))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(t){var e=this.length,i=+t+(t<0?e:0);return this.pushStack(i>=0&&i<e?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:r,sort:i.sort,splice:i.splice},y.extend=y.fn.extend=function(){var t,e,i,n,s,o,a=arguments[0]||{},r=1,l=arguments.length,h=!1;for("boolean"==typeof a&&(h=a,a=arguments[r]||{},r++),"object"==typeof a||g(a)||(a={}),r===l&&(a=this,r--);r<l;r++)if(null!=(t=arguments[r]))for(e in t)i=a[e],a!==(n=t[e])&&(h&&n&&(y.isPlainObject(n)||(s=Array.isArray(n)))?(s?(s=!1,o=i&&Array.isArray(i)?i:[]):o=i&&y.isPlainObject(i)?i:{},a[e]=y.extend(h,o,n)):void 0!==n&&(a[e]=n));return a},y.extend({expando:"jQuery"+("3.3.1"+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isPlainObject:function(t){var e,i;return!(!t||"[object Object]"!==c.call(t)||(e=s(t))&&("function"!=typeof(i=u.call(e,"constructor")&&e.constructor)||d.call(i)!==p))},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},globalEval:function(t){_(t)},each:function(t,e){var i,n=0;if(x(t))for(i=t.length;n<i&&!1!==e.call(t[n],n,t[n]);n++);else for(n in t)if(!1===e.call(t[n],n,t[n]))break;return t},trim:function(t){return null==t?"":(t+"").replace(w,"")},makeArray:function(t,e){var i=e||[];return null!=t&&(x(Object(t))?y.merge(i,"string"==typeof t?[t]:t):r.call(i,t)),i},inArray:function(t,e,i){return null==e?-1:l.call(e,t,i)},merge:function(t,e){for(var i=+e.length,n=0,s=t.length;n<i;n++)t[s++]=e[n];return t.length=s,t},grep:function(t,e,i){for(var n=[],s=0,o=t.length,a=!i;s<o;s++)!e(t[s],s)!==a&&n.push(t[s]);return n},map:function(t,e,i){var n,s,o=0,r=[];if(x(t))for(n=t.length;o<n;o++)null!=(s=e(t[o],o,i))&&r.push(s);else for(o in t)null!=(s=e(t[o],o,i))&&r.push(s);return a.apply([],r)},guid:1,support:f}),"function"==typeof Symbol&&(y.fn[Symbol.iterator]=i[Symbol.iterator]),y.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(t,e){h["[object "+e+"]"]=e.toLowerCase()});var C=function(t){var e,i,n,s,o,a,r,l,h,c,u,d,p,f,g,m,v,_,b,y="sizzle"+1*new Date,w=t.document,x=0,C=0,D=at(),T=at(),S=at(),k=function(t,e){return t===e&&(u=!0),0},I={}.hasOwnProperty,E=[],A=E.pop,P=E.push,M=E.push,O=E.slice,N=function(t,e){for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i;return-1},H="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",R="[\\x20\\t\\r\\n\\f]",L="(?:\\\\.|[\\w-]|[^\0-\\xa0])+",F="\\["+R+"*("+L+")(?:"+R+"*([*^$|!~]?=)"+R+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+L+"))|)"+R+"*\\]",j=":("+L+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+F+")*)|.*)\\)|)",W=new RegExp(R+"+","g"),z=new RegExp("^"+R+"+|((?:^|[^\\\\])(?:\\\\.)*)"+R+"+$","g"),B=new RegExp("^"+R+"*,"+R+"*"),$=new RegExp("^"+R+"*([>+~]|"+R+")"+R+"*"),q=new RegExp("="+R+"*([^\\]'\"]*?)"+R+"*\\]","g"),U=new RegExp(j),Y=new RegExp("^"+L+"$"),V={ID:new RegExp("^#("+L+")"),CLASS:new RegExp("^\\.("+L+")"),TAG:new RegExp("^("+L+"|[*])"),ATTR:new RegExp("^"+F),PSEUDO:new RegExp("^"+j),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+R+"*(even|odd|(([+-]|)(\\d*)n|)"+R+"*(?:([+-]|)"+R+"*(\\d+)|))"+R+"*\\)|)","i"),bool:new RegExp("^(?:"+H+")$","i"),needsContext:new RegExp("^"+R+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+R+"*((?:-\\d)?\\d*)"+R+"*\\)|)(?=[^-]|$)","i")},K=/^(?:input|select|textarea|button)$/i,X=/^h\d$/i,G=/^[^{]+\{\s*\[native \w/,J=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Z=/[+~]/,Q=new RegExp("\\\\([\\da-f]{1,6}"+R+"?|("+R+")|.)","ig"),tt=function(t,e,i){var n="0x"+e-65536;return n!=n||i?e:n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320)},et=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,it=function(t,e){return e?"\0"===t?"�":t.slice(0,-1)+"\\"+t.charCodeAt(t.length-1).toString(16)+" ":"\\"+t},nt=function(){d()},st=_t(function(t){return!0===t.disabled&&("form"in t||"label"in t)},{dir:"parentNode",next:"legend"});try{M.apply(E=O.call(w.childNodes),w.childNodes),E[w.childNodes.length].nodeType}catch(t){M={apply:E.length?function(t,e){P.apply(t,O.call(e))}:function(t,e){for(var i=t.length,n=0;t[i++]=e[n++];);t.length=i-1}}}function ot(t,e,n,s){var o,r,h,c,u,f,v,_=e&&e.ownerDocument,x=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==x&&9!==x&&11!==x)return n;if(!s&&((e?e.ownerDocument||e:w)!==p&&d(e),e=e||p,g)){if(11!==x&&(u=J.exec(t)))if(o=u[1]){if(9===x){if(!(h=e.getElementById(o)))return n;if(h.id===o)return n.push(h),n}else if(_&&(h=_.getElementById(o))&&b(e,h)&&h.id===o)return n.push(h),n}else{if(u[2])return M.apply(n,e.getElementsByTagName(t)),n;if((o=u[3])&&i.getElementsByClassName&&e.getElementsByClassName)return M.apply(n,e.getElementsByClassName(o)),n}if(i.qsa&&!S[t+" "]&&(!m||!m.test(t))){if(1!==x)_=e,v=t;else if("object"!==e.nodeName.toLowerCase()){for((c=e.getAttribute("id"))?c=c.replace(et,it):e.setAttribute("id",c=y),r=(f=a(t)).length;r--;)f[r]="#"+c+" "+vt(f[r]);v=f.join(","),_=Z.test(t)&&gt(e.parentNode)||e}if(v)try{return M.apply(n,_.querySelectorAll(v)),n}catch(t){}finally{c===y&&e.removeAttribute("id")}}}return l(t.replace(z,"$1"),e,n,s)}function at(){var t=[];return function e(i,s){return t.push(i+" ")>n.cacheLength&&delete e[t.shift()],e[i+" "]=s}}function rt(t){return t[y]=!0,t}function lt(t){var e=p.createElement("fieldset");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function ht(t,e){for(var i=t.split("|"),s=i.length;s--;)n.attrHandle[i[s]]=e}function ct(t,e){var i=e&&t,n=i&&1===t.nodeType&&1===e.nodeType&&t.sourceIndex-e.sourceIndex;if(n)return n;if(i)for(;i=i.nextSibling;)if(i===e)return-1;return t?1:-1}function ut(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function dt(t){return function(e){var i=e.nodeName.toLowerCase();return("input"===i||"button"===i)&&e.type===t}}function pt(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&st(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function ft(t){return rt(function(e){return e=+e,rt(function(i,n){for(var s,o=t([],i.length,e),a=o.length;a--;)i[s=o[a]]&&(i[s]=!(n[s]=i[s]))})})}function gt(t){return t&&void 0!==t.getElementsByTagName&&t}for(e in i=ot.support={},o=ot.isXML=function(t){var e=t&&(t.ownerDocument||t).documentElement;return!!e&&"HTML"!==e.nodeName},d=ot.setDocument=function(t){var e,s,a=t?t.ownerDocument||t:w;return a!==p&&9===a.nodeType&&a.documentElement?(f=(p=a).documentElement,g=!o(p),w!==p&&(s=p.defaultView)&&s.top!==s&&(s.addEventListener?s.addEventListener("unload",nt,!1):s.attachEvent&&s.attachEvent("onunload",nt)),i.attributes=lt(function(t){return t.className="i",!t.getAttribute("className")}),i.getElementsByTagName=lt(function(t){return t.appendChild(p.createComment("")),!t.getElementsByTagName("*").length}),i.getElementsByClassName=G.test(p.getElementsByClassName),i.getById=lt(function(t){return f.appendChild(t).id=y,!p.getElementsByName||!p.getElementsByName(y).length}),i.getById?(n.filter.ID=function(t){var e=t.replace(Q,tt);return function(t){return t.getAttribute("id")===e}},n.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var i=e.getElementById(t);return i?[i]:[]}}):(n.filter.ID=function(t){var e=t.replace(Q,tt);return function(t){var i=void 0!==t.getAttributeNode&&t.getAttributeNode("id");return i&&i.value===e}},n.find.ID=function(t,e){if(void 0!==e.getElementById&&g){var i,n,s,o=e.getElementById(t);if(o){if((i=o.getAttributeNode("id"))&&i.value===t)return[o];for(s=e.getElementsByName(t),n=0;o=s[n++];)if((i=o.getAttributeNode("id"))&&i.value===t)return[o]}return[]}}),n.find.TAG=i.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):i.qsa?e.querySelectorAll(t):void 0}:function(t,e){var i,n=[],s=0,o=e.getElementsByTagName(t);if("*"===t){for(;i=o[s++];)1===i.nodeType&&n.push(i);return n}return o},n.find.CLASS=i.getElementsByClassName&&function(t,e){if(void 0!==e.getElementsByClassName&&g)return e.getElementsByClassName(t)},v=[],m=[],(i.qsa=G.test(p.querySelectorAll))&&(lt(function(t){f.appendChild(t).innerHTML="<a id='"+y+"'></a><select id='"+y+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+R+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||m.push("\\["+R+"*(?:value|"+H+")"),t.querySelectorAll("[id~="+y+"-]").length||m.push("~="),t.querySelectorAll(":checked").length||m.push(":checked"),t.querySelectorAll("a#"+y+"+*").length||m.push(".#.+[+~]")}),lt(function(t){t.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var e=p.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&m.push("name"+R+"*[*^$|!~]?="),2!==t.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),f.appendChild(t).disabled=!0,2!==t.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),m.push(",.*:")})),(i.matchesSelector=G.test(_=f.matches||f.webkitMatchesSelector||f.mozMatchesSelector||f.oMatchesSelector||f.msMatchesSelector))&&lt(function(t){i.disconnectedMatch=_.call(t,"*"),_.call(t,"[s!='']:x"),v.push("!=",j)}),m=m.length&&new RegExp(m.join("|")),v=v.length&&new RegExp(v.join("|")),e=G.test(f.compareDocumentPosition),b=e||G.test(f.contains)?function(t,e){var i=9===t.nodeType?t.documentElement:t,n=e&&e.parentNode;return t===n||!(!n||1!==n.nodeType||!(i.contains?i.contains(n):t.compareDocumentPosition&&16&t.compareDocumentPosition(n)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},k=e?function(t,e){if(t===e)return u=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n||(1&(n=(t.ownerDocument||t)===(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!i.sortDetached&&e.compareDocumentPosition(t)===n?t===p||t.ownerDocument===w&&b(w,t)?-1:e===p||e.ownerDocument===w&&b(w,e)?1:c?N(c,t)-N(c,e):0:4&n?-1:1)}:function(t,e){if(t===e)return u=!0,0;var i,n=0,s=t.parentNode,o=e.parentNode,a=[t],r=[e];if(!s||!o)return t===p?-1:e===p?1:s?-1:o?1:c?N(c,t)-N(c,e):0;if(s===o)return ct(t,e);for(i=t;i=i.parentNode;)a.unshift(i);for(i=e;i=i.parentNode;)r.unshift(i);for(;a[n]===r[n];)n++;return n?ct(a[n],r[n]):a[n]===w?-1:r[n]===w?1:0},p):p},ot.matches=function(t,e){return ot(t,null,null,e)},ot.matchesSelector=function(t,e){if((t.ownerDocument||t)!==p&&d(t),e=e.replace(q,"='$1']"),i.matchesSelector&&g&&!S[e+" "]&&(!v||!v.test(e))&&(!m||!m.test(e)))try{var n=_.call(t,e);if(n||i.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){}return ot(e,p,null,[t]).length>0},ot.contains=function(t,e){return(t.ownerDocument||t)!==p&&d(t),b(t,e)},ot.attr=function(t,e){(t.ownerDocument||t)!==p&&d(t);var s=n.attrHandle[e.toLowerCase()],o=s&&I.call(n.attrHandle,e.toLowerCase())?s(t,e,!g):void 0;return void 0!==o?o:i.attributes||!g?t.getAttribute(e):(o=t.getAttributeNode(e))&&o.specified?o.value:null},ot.escape=function(t){return(t+"").replace(et,it)},ot.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},ot.uniqueSort=function(t){var e,n=[],s=0,o=0;if(u=!i.detectDuplicates,c=!i.sortStable&&t.slice(0),t.sort(k),u){for(;e=t[o++];)e===t[o]&&(s=n.push(o));for(;s--;)t.splice(n[s],1)}return c=null,t},s=ot.getText=function(t){var e,i="",n=0,o=t.nodeType;if(o){if(1===o||9===o||11===o){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)i+=s(t)}else if(3===o||4===o)return t.nodeValue}else for(;e=t[n++];)i+=s(e);return i},(n=ot.selectors={cacheLength:50,createPseudo:rt,match:V,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(Q,tt),t[3]=(t[3]||t[4]||t[5]||"").replace(Q,tt),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||ot.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&ot.error(t[0]),t},PSEUDO:function(t){var e,i=!t[6]&&t[2];return V.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":i&&U.test(i)&&(e=a(i,!0))&&(e=i.indexOf(")",i.length-e)-i.length)&&(t[0]=t[0].slice(0,e),t[2]=i.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(Q,tt).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=D[t+" "];return e||(e=new RegExp("(^|"+R+")"+t+"("+R+"|$)"))&&D(t,function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,e,i){return function(n){var s=ot.attr(n,t);return null==s?"!="===e:!e||(s+="","="===e?s===i:"!="===e?s!==i:"^="===e?i&&0===s.indexOf(i):"*="===e?i&&s.indexOf(i)>-1:"$="===e?i&&s.slice(-i.length)===i:"~="===e?(" "+s.replace(W," ")+" ").indexOf(i)>-1:"|="===e&&(s===i||s.slice(0,i.length+1)===i+"-"))}},CHILD:function(t,e,i,n,s){var o="nth"!==t.slice(0,3),a="last"!==t.slice(-4),r="of-type"===e;return 1===n&&0===s?function(t){return!!t.parentNode}:function(e,i,l){var h,c,u,d,p,f,g=o!==a?"nextSibling":"previousSibling",m=e.parentNode,v=r&&e.nodeName.toLowerCase(),_=!l&&!r,b=!1;if(m){if(o){for(;g;){for(d=e;d=d[g];)if(r?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;f=g="only"===t&&!f&&"nextSibling"}return!0}if(f=[a?m.firstChild:m.lastChild],a&&_){for(b=(p=(h=(c=(u=(d=m)[y]||(d[y]={}))[d.uniqueID]||(u[d.uniqueID]={}))[t]||[])[0]===x&&h[1])&&h[2],d=p&&m.childNodes[p];d=++p&&d&&d[g]||(b=p=0)||f.pop();)if(1===d.nodeType&&++b&&d===e){c[t]=[x,p,b];break}}else if(_&&(b=p=(h=(c=(u=(d=e)[y]||(d[y]={}))[d.uniqueID]||(u[d.uniqueID]={}))[t]||[])[0]===x&&h[1]),!1===b)for(;(d=++p&&d&&d[g]||(b=p=0)||f.pop())&&((r?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++b||(_&&((c=(u=d[y]||(d[y]={}))[d.uniqueID]||(u[d.uniqueID]={}))[t]=[x,b]),d!==e)););return(b-=s)===n||b%n==0&&b/n>=0}}},PSEUDO:function(t,e){var i,s=n.pseudos[t]||n.setFilters[t.toLowerCase()]||ot.error("unsupported pseudo: "+t);return s[y]?s(e):s.length>1?(i=[t,t,"",e],n.setFilters.hasOwnProperty(t.toLowerCase())?rt(function(t,i){for(var n,o=s(t,e),a=o.length;a--;)t[n=N(t,o[a])]=!(i[n]=o[a])}):function(t){return s(t,0,i)}):s}},pseudos:{not:rt(function(t){var e=[],i=[],n=r(t.replace(z,"$1"));return n[y]?rt(function(t,e,i,s){for(var o,a=n(t,null,s,[]),r=t.length;r--;)(o=a[r])&&(t[r]=!(e[r]=o))}):function(t,s,o){return e[0]=t,n(e,null,o,i),e[0]=null,!i.pop()}}),has:rt(function(t){return function(e){return ot(t,e).length>0}}),contains:rt(function(t){return t=t.replace(Q,tt),function(e){return(e.textContent||e.innerText||s(e)).indexOf(t)>-1}}),lang:rt(function(t){return Y.test(t||"")||ot.error("unsupported lang: "+t),t=t.replace(Q,tt).toLowerCase(),function(e){var i;do{if(i=g?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(i=i.toLowerCase())===t||0===i.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var i=t.location&&t.location.hash;return i&&i.slice(1)===e.id},root:function(t){return t===f},focus:function(t){return t===p.activeElement&&(!p.hasFocus||p.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:pt(!1),disabled:pt(!0),checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!n.pseudos.empty(t)},header:function(t){return X.test(t.nodeName)},input:function(t){return K.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:ft(function(){return[0]}),last:ft(function(t,e){return[e-1]}),eq:ft(function(t,e,i){return[i<0?i+e:i]}),even:ft(function(t,e){for(var i=0;i<e;i+=2)t.push(i);return t}),odd:ft(function(t,e){for(var i=1;i<e;i+=2)t.push(i);return t}),lt:ft(function(t,e,i){for(var n=i<0?i+e:i;--n>=0;)t.push(n);return t}),gt:ft(function(t,e,i){for(var n=i<0?i+e:i;++n<e;)t.push(n);return t})}}).pseudos.nth=n.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})n.pseudos[e]=ut(e);for(e in{submit:!0,reset:!0})n.pseudos[e]=dt(e);function mt(){}function vt(t){for(var e=0,i=t.length,n="";e<i;e++)n+=t[e].value;return n}function _t(t,e,i){var n=e.dir,s=e.next,o=s||n,a=i&&"parentNode"===o,r=C++;return e.first?function(e,i,s){for(;e=e[n];)if(1===e.nodeType||a)return t(e,i,s);return!1}:function(e,i,l){var h,c,u,d=[x,r];if(l){for(;e=e[n];)if((1===e.nodeType||a)&&t(e,i,l))return!0}else for(;e=e[n];)if(1===e.nodeType||a)if(c=(u=e[y]||(e[y]={}))[e.uniqueID]||(u[e.uniqueID]={}),s&&s===e.nodeName.toLowerCase())e=e[n]||e;else{if((h=c[o])&&h[0]===x&&h[1]===r)return d[2]=h[2];if(c[o]=d,d[2]=t(e,i,l))return!0}return!1}}function bt(t){return t.length>1?function(e,i,n){for(var s=t.length;s--;)if(!t[s](e,i,n))return!1;return!0}:t[0]}function yt(t,e,i,n,s){for(var o,a=[],r=0,l=t.length,h=null!=e;r<l;r++)(o=t[r])&&(i&&!i(o,n,s)||(a.push(o),h&&e.push(r)));return a}function wt(t,e,i,n,s,o){return n&&!n[y]&&(n=wt(n)),s&&!s[y]&&(s=wt(s,o)),rt(function(o,a,r,l){var h,c,u,d=[],p=[],f=a.length,g=o||function(t,e,i){for(var n=0,s=e.length;n<s;n++)ot(t,e[n],i);return i}(e||"*",r.nodeType?[r]:r,[]),m=!t||!o&&e?g:yt(g,d,t,r,l),v=i?s||(o?t:f||n)?[]:a:m;if(i&&i(m,v,r,l),n)for(h=yt(v,p),n(h,[],r,l),c=h.length;c--;)(u=h[c])&&(v[p[c]]=!(m[p[c]]=u));if(o){if(s||t){if(s){for(h=[],c=v.length;c--;)(u=v[c])&&h.push(m[c]=u);s(null,v=[],h,l)}for(c=v.length;c--;)(u=v[c])&&(h=s?N(o,u):d[c])>-1&&(o[h]=!(a[h]=u))}}else v=yt(v===a?v.splice(f,v.length):v),s?s(null,a,v,l):M.apply(a,v)})}function xt(t){for(var e,i,s,o=t.length,a=n.relative[t[0].type],r=a||n.relative[" "],l=a?1:0,c=_t(function(t){return t===e},r,!0),u=_t(function(t){return N(e,t)>-1},r,!0),d=[function(t,i,n){var s=!a&&(n||i!==h)||((e=i).nodeType?c(t,i,n):u(t,i,n));return e=null,s}];l<o;l++)if(i=n.relative[t[l].type])d=[_t(bt(d),i)];else{if((i=n.filter[t[l].type].apply(null,t[l].matches))[y]){for(s=++l;s<o&&!n.relative[t[s].type];s++);return wt(l>1&&bt(d),l>1&&vt(t.slice(0,l-1).concat({value:" "===t[l-2].type?"*":""})).replace(z,"$1"),i,l<s&&xt(t.slice(l,s)),s<o&&xt(t=t.slice(s)),s<o&&vt(t))}d.push(i)}return bt(d)}function Ct(t,e){var i=e.length>0,s=t.length>0,o=function(o,a,r,l,c){var u,f,m,v=0,_="0",b=o&&[],y=[],w=h,C=o||s&&n.find.TAG("*",c),D=x+=null==w?1:Math.random()||.1,T=C.length;for(c&&(h=a===p||a||c);_!==T&&null!=(u=C[_]);_++){if(s&&u){for(f=0,a||u.ownerDocument===p||(d(u),r=!g);m=t[f++];)if(m(u,a||p,r)){l.push(u);break}c&&(x=D)}i&&((u=!m&&u)&&v--,o&&b.push(u))}if(v+=_,i&&_!==v){for(f=0;m=e[f++];)m(b,y,a,r);if(o){if(v>0)for(;_--;)b[_]||y[_]||(y[_]=A.call(l));y=yt(y)}M.apply(l,y),c&&!o&&y.length>0&&v+e.length>1&&ot.uniqueSort(l)}return c&&(x=D,h=w),b};return i?rt(o):o}return mt.prototype=n.filters=n.pseudos,n.setFilters=new mt,a=ot.tokenize=function(t,e){var i,s,o,a,r,l,h,c=T[t+" "];if(c)return e?0:c.slice(0);for(r=t,l=[],h=n.preFilter;r;){for(a in i&&!(s=B.exec(r))||(s&&(r=r.slice(s[0].length)||r),l.push(o=[])),i=!1,(s=$.exec(r))&&(i=s.shift(),o.push({value:i,type:s[0].replace(z," ")}),r=r.slice(i.length)),n.filter)!(s=V[a].exec(r))||h[a]&&!(s=h[a](s))||(i=s.shift(),o.push({value:i,type:a,matches:s}),r=r.slice(i.length));if(!i)break}return e?r.length:r?ot.error(t):T(t,l).slice(0)},r=ot.compile=function(t,e){var i,n=[],s=[],o=S[t+" "];if(!o){for(e||(e=a(t)),i=e.length;i--;)(o=xt(e[i]))[y]?n.push(o):s.push(o);(o=S(t,Ct(s,n))).selector=t}return o},l=ot.select=function(t,e,i,s){var o,l,h,c,u,d="function"==typeof t&&t,p=!s&&a(t=d.selector||t);if(i=i||[],1===p.length){if((l=p[0]=p[0].slice(0)).length>2&&"ID"===(h=l[0]).type&&9===e.nodeType&&g&&n.relative[l[1].type]){if(!(e=(n.find.ID(h.matches[0].replace(Q,tt),e)||[])[0]))return i;d&&(e=e.parentNode),t=t.slice(l.shift().value.length)}for(o=V.needsContext.test(t)?0:l.length;o--&&(h=l[o],!n.relative[c=h.type]);)if((u=n.find[c])&&(s=u(h.matches[0].replace(Q,tt),Z.test(l[0].type)&&gt(e.parentNode)||e))){if(l.splice(o,1),!(t=s.length&&vt(l)))return M.apply(i,s),i;break}}return(d||r(t,p))(s,e,!g,i,!e||Z.test(t)&&gt(e.parentNode)||e),i},i.sortStable=y.split("").sort(k).join("")===y,i.detectDuplicates=!!u,d(),i.sortDetached=lt(function(t){return 1&t.compareDocumentPosition(p.createElement("fieldset"))}),lt(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||ht("type|href|height|width",function(t,e,i){if(!i)return t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),i.attributes&&lt(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||ht("value",function(t,e,i){if(!i&&"input"===t.nodeName.toLowerCase())return t.defaultValue}),lt(function(t){return null==t.getAttribute("disabled")})||ht(H,function(t,e,i){var n;if(!i)return!0===t[e]?e.toLowerCase():(n=t.getAttributeNode(e))&&n.specified?n.value:null}),ot}(t);y.find=C,y.expr=C.selectors,y.expr[":"]=y.expr.pseudos,y.uniqueSort=y.unique=C.uniqueSort,y.text=C.getText,y.isXMLDoc=C.isXML,y.contains=C.contains,y.escapeSelector=C.escape;var D=function(t,e,i){for(var n=[],s=void 0!==i;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(s&&y(t).is(i))break;n.push(t)}return n},T=function(t,e){for(var i=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&i.push(t);return i},S=y.expr.match.needsContext;function k(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()}var I=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function E(t,e,i){return g(e)?y.grep(t,function(t,n){return!!e.call(t,n,t)!==i}):e.nodeType?y.grep(t,function(t){return t===e!==i}):"string"!=typeof e?y.grep(t,function(t){return l.call(e,t)>-1!==i}):y.filter(e,t,i)}y.filter=function(t,e,i){var n=e[0];return i&&(t=":not("+t+")"),1===e.length&&1===n.nodeType?y.find.matchesSelector(n,t)?[n]:[]:y.find.matches(t,y.grep(e,function(t){return 1===t.nodeType}))},y.fn.extend({find:function(t){var e,i,n=this.length,s=this;if("string"!=typeof t)return this.pushStack(y(t).filter(function(){for(e=0;e<n;e++)if(y.contains(s[e],this))return!0}));for(i=this.pushStack([]),e=0;e<n;e++)y.find(t,s[e],i);return n>1?y.uniqueSort(i):i},filter:function(t){return this.pushStack(E(this,t||[],!1))},not:function(t){return this.pushStack(E(this,t||[],!0))},is:function(t){return!!E(this,"string"==typeof t&&S.test(t)?y(t):t||[],!1).length}});var A,P=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(y.fn.init=function(t,e,i){var s,o;if(!t)return this;if(i=i||A,"string"==typeof t){if(!(s="<"===t[0]&&">"===t[t.length-1]&&t.length>=3?[null,t,null]:P.exec(t))||!s[1]&&e)return!e||e.jquery?(e||i).find(t):this.constructor(e).find(t);if(s[1]){if(e=e instanceof y?e[0]:e,y.merge(this,y.parseHTML(s[1],e&&e.nodeType?e.ownerDocument||e:n,!0)),I.test(s[1])&&y.isPlainObject(e))for(s in e)g(this[s])?this[s](e[s]):this.attr(s,e[s]);return this}return(o=n.getElementById(s[2]))&&(this[0]=o,this.length=1),this}return t.nodeType?(this[0]=t,this.length=1,this):g(t)?void 0!==i.ready?i.ready(t):t(y):y.makeArray(t,this)}).prototype=y.fn,A=y(n);var M=/^(?:parents|prev(?:Until|All))/,O={children:!0,contents:!0,next:!0,prev:!0};function N(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}y.fn.extend({has:function(t){var e=y(t,this),i=e.length;return this.filter(function(){for(var t=0;t<i;t++)if(y.contains(this,e[t]))return!0})},closest:function(t,e){var i,n=0,s=this.length,o=[],a="string"!=typeof t&&y(t);if(!S.test(t))for(;n<s;n++)for(i=this[n];i&&i!==e;i=i.parentNode)if(i.nodeType<11&&(a?a.index(i)>-1:1===i.nodeType&&y.find.matchesSelector(i,t))){o.push(i);break}return this.pushStack(o.length>1?y.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?l.call(y(t),this[0]):l.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(y.uniqueSort(y.merge(this.get(),y(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),y.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return D(t,"parentNode")},parentsUntil:function(t,e,i){return D(t,"parentNode",i)},next:function(t){return N(t,"nextSibling")},prev:function(t){return N(t,"previousSibling")},nextAll:function(t){return D(t,"nextSibling")},prevAll:function(t){return D(t,"previousSibling")},nextUntil:function(t,e,i){return D(t,"nextSibling",i)},prevUntil:function(t,e,i){return D(t,"previousSibling",i)},siblings:function(t){return T((t.parentNode||{}).firstChild,t)},children:function(t){return T(t.firstChild)},contents:function(t){return k(t,"iframe")?t.contentDocument:(k(t,"template")&&(t=t.content||t),y.merge([],t.childNodes))}},function(t,e){y.fn[t]=function(i,n){var s=y.map(this,e,i);return"Until"!==t.slice(-5)&&(n=i),n&&"string"==typeof n&&(s=y.filter(n,s)),this.length>1&&(O[t]||y.uniqueSort(s),M.test(t)&&s.reverse()),this.pushStack(s)}});var H=/[^\x20\t\r\n\f]+/g;function R(t){return t}function L(t){throw t}function F(t,e,i,n){var s;try{t&&g(s=t.promise)?s.call(t).done(e).fail(i):t&&g(s=t.then)?s.call(t,e,i):e.apply(void 0,[t].slice(n))}catch(t){i.apply(void 0,[t])}}y.Callbacks=function(t){t="string"==typeof t?function(t){var e={};return y.each(t.match(H)||[],function(t,i){e[i]=!0}),e}(t):y.extend({},t);var e,i,n,s,o=[],a=[],r=-1,l=function(){for(s=s||t.once,n=e=!0;a.length;r=-1)for(i=a.shift();++r<o.length;)!1===o[r].apply(i[0],i[1])&&t.stopOnFalse&&(r=o.length,i=!1);t.memory||(i=!1),e=!1,s&&(o=i?[]:"")},h={add:function(){return o&&(i&&!e&&(r=o.length-1,a.push(i)),function e(i){y.each(i,function(i,n){g(n)?t.unique&&h.has(n)||o.push(n):n&&n.length&&"string"!==b(n)&&e(n)})}(arguments),i&&!e&&l()),this},remove:function(){return y.each(arguments,function(t,e){for(var i;(i=y.inArray(e,o,i))>-1;)o.splice(i,1),i<=r&&r--}),this},has:function(t){return t?y.inArray(t,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return s=a=[],o=i="",this},disabled:function(){return!o},lock:function(){return s=a=[],i||e||(o=i=""),this},locked:function(){return!!s},fireWith:function(t,i){return s||(i=[t,(i=i||[]).slice?i.slice():i],a.push(i),e||l()),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!n}};return h},y.extend({Deferred:function(e){var i=[["notify","progress",y.Callbacks("memory"),y.Callbacks("memory"),2],["resolve","done",y.Callbacks("once memory"),y.Callbacks("once memory"),0,"resolved"],["reject","fail",y.Callbacks("once memory"),y.Callbacks("once memory"),1,"rejected"]],n="pending",s={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(t){return s.then(null,t)},pipe:function(){var t=arguments;return y.Deferred(function(e){y.each(i,function(i,n){var s=g(t[n[4]])&&t[n[4]];o[n[1]](function(){var t=s&&s.apply(this,arguments);t&&g(t.promise)?t.promise().progress(e.notify).done(e.resolve).fail(e.reject):e[n[0]+"With"](this,s?[t]:arguments)})}),t=null}).promise()},then:function(e,n,s){var o=0;function a(e,i,n,s){return function(){var r=this,l=arguments,h=function(){var t,h;if(!(e<o)){if((t=n.apply(r,l))===i.promise())throw new TypeError("Thenable self-resolution");h=t&&("object"==typeof t||"function"==typeof t)&&t.then,g(h)?s?h.call(t,a(o,i,R,s),a(o,i,L,s)):(o++,h.call(t,a(o,i,R,s),a(o,i,L,s),a(o,i,R,i.notifyWith))):(n!==R&&(r=void 0,l=[t]),(s||i.resolveWith)(r,l))}},c=s?h:function(){try{h()}catch(t){y.Deferred.exceptionHook&&y.Deferred.exceptionHook(t,c.stackTrace),e+1>=o&&(n!==L&&(r=void 0,l=[t]),i.rejectWith(r,l))}};e?c():(y.Deferred.getStackHook&&(c.stackTrace=y.Deferred.getStackHook()),t.setTimeout(c))}}return y.Deferred(function(t){i[0][3].add(a(0,t,g(s)?s:R,t.notifyWith)),i[1][3].add(a(0,t,g(e)?e:R)),i[2][3].add(a(0,t,g(n)?n:L))}).promise()},promise:function(t){return null!=t?y.extend(t,s):s}},o={};return y.each(i,function(t,e){var a=e[2],r=e[5];s[e[1]]=a.add,r&&a.add(function(){n=r},i[3-t][2].disable,i[3-t][3].disable,i[0][2].lock,i[0][3].lock),a.add(e[3].fire),o[e[0]]=function(){return o[e[0]+"With"](this===o?void 0:this,arguments),this},o[e[0]+"With"]=a.fireWith}),s.promise(o),e&&e.call(o,o),o},when:function(t){var e=arguments.length,i=e,n=Array(i),s=o.call(arguments),a=y.Deferred(),r=function(t){return function(i){n[t]=this,s[t]=arguments.length>1?o.call(arguments):i,--e||a.resolveWith(n,s)}};if(e<=1&&(F(t,a.done(r(i)).resolve,a.reject,!e),"pending"===a.state()||g(s[i]&&s[i].then)))return a.then();for(;i--;)F(s[i],r(i),a.reject);return a.promise()}});var j=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;y.Deferred.exceptionHook=function(e,i){t.console&&t.console.warn&&e&&j.test(e.name)&&t.console.warn("jQuery.Deferred exception: "+e.message,e.stack,i)},y.readyException=function(e){t.setTimeout(function(){throw e})};var W=y.Deferred();function z(){n.removeEventListener("DOMContentLoaded",z),t.removeEventListener("load",z),y.ready()}y.fn.ready=function(t){return W.then(t).catch(function(t){y.readyException(t)}),this},y.extend({isReady:!1,readyWait:1,ready:function(t){(!0===t?--y.readyWait:y.isReady)||(y.isReady=!0,!0!==t&&--y.readyWait>0||W.resolveWith(n,[y]))}}),y.ready.then=W.then,"complete"===n.readyState||"loading"!==n.readyState&&!n.documentElement.doScroll?t.setTimeout(y.ready):(n.addEventListener("DOMContentLoaded",z),t.addEventListener("load",z));var B=function(t,e,i,n,s,o,a){var r=0,l=t.length,h=null==i;if("object"===b(i))for(r in s=!0,i)B(t,e,r,i[r],!0,o,a);else if(void 0!==n&&(s=!0,g(n)||(a=!0),h&&(a?(e.call(t,n),e=null):(h=e,e=function(t,e,i){return h.call(y(t),i)})),e))for(;r<l;r++)e(t[r],i,a?n:n.call(t[r],r,e(t[r],i)));return s?t:h?e.call(t):l?e(t[0],i):o},$=/^-ms-/,q=/-([a-z])/g;function U(t,e){return e.toUpperCase()}function Y(t){return t.replace($,"ms-").replace(q,U)}var V=function(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType};function K(){this.expando=y.expando+K.uid++}K.uid=1,K.prototype={cache:function(t){var e=t[this.expando];return e||(e={},V(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e},set:function(t,e,i){var n,s=this.cache(t);if("string"==typeof e)s[Y(e)]=i;else for(n in e)s[Y(n)]=e[n];return s},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][Y(e)]},access:function(t,e,i){return void 0===e||e&&"string"==typeof e&&void 0===i?this.get(t,e):(this.set(t,e,i),void 0!==i?i:e)},remove:function(t,e){var i,n=t[this.expando];if(void 0!==n){if(void 0!==e){i=(e=Array.isArray(e)?e.map(Y):(e=Y(e))in n?[e]:e.match(H)||[]).length;for(;i--;)delete n[e[i]]}(void 0===e||y.isEmptyObject(n))&&(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){var e=t[this.expando];return void 0!==e&&!y.isEmptyObject(e)}};var X=new K,G=new K,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Z=/[A-Z]/g;function Q(t,e,i){var n;if(void 0===i&&1===t.nodeType)if(n="data-"+e.replace(Z,"-$&").toLowerCase(),"string"==typeof(i=t.getAttribute(n))){try{i=function(t){return"true"===t||"false"!==t&&("null"===t?null:t===+t+""?+t:J.test(t)?JSON.parse(t):t)}(i)}catch(t){}G.set(t,e,i)}else i=void 0;return i}y.extend({hasData:function(t){return G.hasData(t)||X.hasData(t)},data:function(t,e,i){return G.access(t,e,i)},removeData:function(t,e){G.remove(t,e)},_data:function(t,e,i){return X.access(t,e,i)},_removeData:function(t,e){X.remove(t,e)}}),y.fn.extend({data:function(t,e){var i,n,s,o=this[0],a=o&&o.attributes;if(void 0===t){if(this.length&&(s=G.get(o),1===o.nodeType&&!X.get(o,"hasDataAttrs"))){for(i=a.length;i--;)a[i]&&0===(n=a[i].name).indexOf("data-")&&(n=Y(n.slice(5)),Q(o,n,s[n]));X.set(o,"hasDataAttrs",!0)}return s}return"object"==typeof t?this.each(function(){G.set(this,t)}):B(this,function(e){var i;if(o&&void 0===e){if(void 0!==(i=G.get(o,t)))return i;if(void 0!==(i=Q(o,t)))return i}else this.each(function(){G.set(this,t,e)})},null,e,arguments.length>1,null,!0)},removeData:function(t){return this.each(function(){G.remove(this,t)})}}),y.extend({queue:function(t,e,i){var n;if(t)return e=(e||"fx")+"queue",n=X.get(t,e),i&&(!n||Array.isArray(i)?n=X.access(t,e,y.makeArray(i)):n.push(i)),n||[]},dequeue:function(t,e){e=e||"fx";var i=y.queue(t,e),n=i.length,s=i.shift(),o=y._queueHooks(t,e);"inprogress"===s&&(s=i.shift(),n--),s&&("fx"===e&&i.unshift("inprogress"),delete o.stop,s.call(t,function(){y.dequeue(t,e)},o)),!n&&o&&o.empty.fire()},_queueHooks:function(t,e){var i=e+"queueHooks";return X.get(t,i)||X.access(t,i,{empty:y.Callbacks("once memory").add(function(){X.remove(t,[e+"queue",i])})})}}),y.fn.extend({queue:function(t,e){var i=2;return"string"!=typeof t&&(e=t,t="fx",i--),arguments.length<i?y.queue(this[0],t):void 0===e?this:this.each(function(){var i=y.queue(this,t,e);y._queueHooks(this,t),"fx"===t&&"inprogress"!==i[0]&&y.dequeue(this,t)})},dequeue:function(t){return this.each(function(){y.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var i,n=1,s=y.Deferred(),o=this,a=this.length,r=function(){--n||s.resolveWith(o,[o])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(i=X.get(o[a],t+"queueHooks"))&&i.empty&&(n++,i.empty.add(r));return r(),s.promise(e)}});var tt=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,et=new RegExp("^(?:([+-])=|)("+tt+")([a-z%]*)$","i"),it=["Top","Right","Bottom","Left"],nt=function(t,e){return"none"===(t=e||t).style.display||""===t.style.display&&y.contains(t.ownerDocument,t)&&"none"===y.css(t,"display")},st=function(t,e,i,n){var s,o,a={};for(o in e)a[o]=t.style[o],t.style[o]=e[o];for(o in s=i.apply(t,n||[]),e)t.style[o]=a[o];return s};function ot(t,e,i,n){var s,o,a=20,r=n?function(){return n.cur()}:function(){return y.css(t,e,"")},l=r(),h=i&&i[3]||(y.cssNumber[e]?"":"px"),c=(y.cssNumber[e]||"px"!==h&&+l)&&et.exec(y.css(t,e));if(c&&c[3]!==h){for(l/=2,h=h||c[3],c=+l||1;a--;)y.style(t,e,c+h),(1-o)*(1-(o=r()/l||.5))<=0&&(a=0),c/=o;c*=2,y.style(t,e,c+h),i=i||[]}return i&&(c=+c||+l||0,s=i[1]?c+(i[1]+1)*i[2]:+i[2],n&&(n.unit=h,n.start=c,n.end=s)),s}var at={};function rt(t){var e,i=t.ownerDocument,n=t.nodeName,s=at[n];return s||(e=i.body.appendChild(i.createElement(n)),s=y.css(e,"display"),e.parentNode.removeChild(e),"none"===s&&(s="block"),at[n]=s,s)}function lt(t,e){for(var i,n,s=[],o=0,a=t.length;o<a;o++)(n=t[o]).style&&(i=n.style.display,e?("none"===i&&(s[o]=X.get(n,"display")||null,s[o]||(n.style.display="")),""===n.style.display&&nt(n)&&(s[o]=rt(n))):"none"!==i&&(s[o]="none",X.set(n,"display",i)));for(o=0;o<a;o++)null!=s[o]&&(t[o].style.display=s[o]);return t}y.fn.extend({show:function(){return lt(this,!0)},hide:function(){return lt(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){nt(this)?y(this).show():y(this).hide()})}});var ht=/^(?:checkbox|radio)$/i,ct=/<([a-z][^\/\0>\x20\t\r\n\f]+)/i,ut=/^$|^module$|\/(?:java|ecma)script/i,dt={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function pt(t,e){var i;return i=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[],void 0===e||e&&k(t,e)?y.merge([t],i):i}function ft(t,e){for(var i=0,n=t.length;i<n;i++)X.set(t[i],"globalEval",!e||X.get(e[i],"globalEval"))}dt.optgroup=dt.option,dt.tbody=dt.tfoot=dt.colgroup=dt.caption=dt.thead,dt.th=dt.td;var gt=/<|&#?\w+;/;function mt(t,e,i,n,s){for(var o,a,r,l,h,c,u=e.createDocumentFragment(),d=[],p=0,f=t.length;p<f;p++)if((o=t[p])||0===o)if("object"===b(o))y.merge(d,o.nodeType?[o]:o);else if(gt.test(o)){for(a=a||u.appendChild(e.createElement("div")),r=(ct.exec(o)||["",""])[1].toLowerCase(),l=dt[r]||dt._default,a.innerHTML=l[1]+y.htmlPrefilter(o)+l[2],c=l[0];c--;)a=a.lastChild;y.merge(d,a.childNodes),(a=u.firstChild).textContent=""}else d.push(e.createTextNode(o));for(u.textContent="",p=0;o=d[p++];)if(n&&y.inArray(o,n)>-1)s&&s.push(o);else if(h=y.contains(o.ownerDocument,o),a=pt(u.appendChild(o),"script"),h&&ft(a),i)for(c=0;o=a[c++];)ut.test(o.type||"")&&i.push(o);return u}!function(){var t=n.createDocumentFragment().appendChild(n.createElement("div")),e=n.createElement("input");e.setAttribute("type","radio"),e.setAttribute("checked","checked"),e.setAttribute("name","t"),t.appendChild(e),f.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",f.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue}();var vt=n.documentElement,_t=/^key/,bt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,yt=/^([^.]*)(?:\.(.+)|)/;function wt(){return!0}function xt(){return!1}function Ct(){try{return n.activeElement}catch(t){}}function Dt(t,e,i,n,s,o){var a,r;if("object"==typeof e){for(r in"string"!=typeof i&&(n=n||i,i=void 0),e)Dt(t,r,i,n,e[r],o);return t}if(null==n&&null==s?(s=i,n=i=void 0):null==s&&("string"==typeof i?(s=n,n=void 0):(s=n,n=i,i=void 0)),!1===s)s=xt;else if(!s)return t;return 1===o&&(a=s,(s=function(t){return y().off(t),a.apply(this,arguments)}).guid=a.guid||(a.guid=y.guid++)),t.each(function(){y.event.add(this,e,s,n,i)})}y.event={global:{},add:function(t,e,i,n,s){var o,a,r,l,h,c,u,d,p,f,g,m=X.get(t);if(m)for(i.handler&&(i=(o=i).handler,s=o.selector),s&&y.find.matchesSelector(vt,s),i.guid||(i.guid=y.guid++),(l=m.events)||(l=m.events={}),(a=m.handle)||(a=m.handle=function(e){return void 0!==y&&y.event.triggered!==e.type?y.event.dispatch.apply(t,arguments):void 0}),h=(e=(e||"").match(H)||[""]).length;h--;)p=g=(r=yt.exec(e[h])||[])[1],f=(r[2]||"").split(".").sort(),p&&(u=y.event.special[p]||{},p=(s?u.delegateType:u.bindType)||p,u=y.event.special[p]||{},c=y.extend({type:p,origType:g,data:n,handler:i,guid:i.guid,selector:s,needsContext:s&&y.expr.match.needsContext.test(s),namespace:f.join(".")},o),(d=l[p])||((d=l[p]=[]).delegateCount=0,u.setup&&!1!==u.setup.call(t,n,f,a)||t.addEventListener&&t.addEventListener(p,a)),u.add&&(u.add.call(t,c),c.handler.guid||(c.handler.guid=i.guid)),s?d.splice(d.delegateCount++,0,c):d.push(c),y.event.global[p]=!0)},remove:function(t,e,i,n,s){var o,a,r,l,h,c,u,d,p,f,g,m=X.hasData(t)&&X.get(t);if(m&&(l=m.events)){for(h=(e=(e||"").match(H)||[""]).length;h--;)if(p=g=(r=yt.exec(e[h])||[])[1],f=(r[2]||"").split(".").sort(),p){for(u=y.event.special[p]||{},d=l[p=(n?u.delegateType:u.bindType)||p]||[],r=r[2]&&new RegExp("(^|\\.)"+f.join("\\.(?:.*\\.|)")+"(\\.|$)"),a=o=d.length;o--;)c=d[o],!s&&g!==c.origType||i&&i.guid!==c.guid||r&&!r.test(c.namespace)||n&&n!==c.selector&&("**"!==n||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,u.remove&&u.remove.call(t,c));a&&!d.length&&(u.teardown&&!1!==u.teardown.call(t,f,m.handle)||y.removeEvent(t,p,m.handle),delete l[p])}else for(p in l)y.event.remove(t,p+e[h],i,n,!0);y.isEmptyObject(l)&&X.remove(t,"handle events")}},dispatch:function(t){var e,i,n,s,o,a,r=y.event.fix(t),l=new Array(arguments.length),h=(X.get(this,"events")||{})[r.type]||[],c=y.event.special[r.type]||{};for(l[0]=r,e=1;e<arguments.length;e++)l[e]=arguments[e];if(r.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,r)){for(a=y.event.handlers.call(this,r,h),e=0;(s=a[e++])&&!r.isPropagationStopped();)for(r.currentTarget=s.elem,i=0;(o=s.handlers[i++])&&!r.isImmediatePropagationStopped();)r.rnamespace&&!r.rnamespace.test(o.namespace)||(r.handleObj=o,r.data=o.data,void 0!==(n=((y.event.special[o.origType]||{}).handle||o.handler).apply(s.elem,l))&&!1===(r.result=n)&&(r.preventDefault(),r.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,r),r.result}},handlers:function(t,e){var i,n,s,o,a,r=[],l=e.delegateCount,h=t.target;if(l&&h.nodeType&&!("click"===t.type&&t.button>=1))for(;h!==this;h=h.parentNode||this)if(1===h.nodeType&&("click"!==t.type||!0!==h.disabled)){for(o=[],a={},i=0;i<l;i++)void 0===a[s=(n=e[i]).selector+" "]&&(a[s]=n.needsContext?y(s,this).index(h)>-1:y.find(s,this,null,[h]).length),a[s]&&o.push(n);o.length&&r.push({elem:h,handlers:o})}return h=this,l<e.length&&r.push({elem:h,handlers:e.slice(l)}),r},addProp:function(t,e){Object.defineProperty(y.Event.prototype,t,{enumerable:!0,configurable:!0,get:g(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(t){return t[y.expando]?t:new y.Event(t)},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==Ct()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===Ct()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if("checkbox"===this.type&&this.click&&k(this,"input"))return this.click(),!1},_default:function(t){return k(t.target,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},y.removeEvent=function(t,e,i){t.removeEventListener&&t.removeEventListener(e,i)},y.Event=function(t,e){if(!(this instanceof y.Event))return new y.Event(t,e);t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?wt:xt,this.target=t.target&&3===t.target.nodeType?t.target.parentNode:t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget):this.type=t,e&&y.extend(this,e),this.timeStamp=t&&t.timeStamp||Date.now(),this[y.expando]=!0},y.Event.prototype={constructor:y.Event,isDefaultPrevented:xt,isPropagationStopped:xt,isImmediatePropagationStopped:xt,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=wt,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=wt,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=wt,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},y.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(t){var e=t.button;return null==t.which&&_t.test(t.type)?null!=t.charCode?t.charCode:t.keyCode:!t.which&&void 0!==e&&bt.test(t.type)?1&e?1:2&e?3:4&e?2:0:t.which}},y.event.addProp),y.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){y.event.special[t]={delegateType:e,bindType:e,handle:function(t){var i,n=t.relatedTarget,s=t.handleObj;return n&&(n===this||y.contains(this,n))||(t.type=s.origType,i=s.handler.apply(this,arguments),t.type=e),i}}}),y.fn.extend({on:function(t,e,i,n){return Dt(this,t,e,i,n)},one:function(t,e,i,n){return Dt(this,t,e,i,n,1)},off:function(t,e,i){var n,s;if(t&&t.preventDefault&&t.handleObj)return n=t.handleObj,y(t.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler),this;if("object"==typeof t){for(s in t)this.off(s,e,t[s]);return this}return!1!==e&&"function"!=typeof e||(i=e,e=void 0),!1===i&&(i=xt),this.each(function(){y.event.remove(this,t,i,e)})}});var Tt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,St=/<script|<style|<link/i,kt=/checked\s*(?:[^=]|=\s*.checked.)/i,It=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Et(t,e){return k(t,"table")&&k(11!==e.nodeType?e:e.firstChild,"tr")&&y(t).children("tbody")[0]||t}function At(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function Pt(t){return"true/"===(t.type||"").slice(0,5)?t.type=t.type.slice(5):t.removeAttribute("type"),t}function Mt(t,e){var i,n,s,o,a,r,l,h;if(1===e.nodeType){if(X.hasData(t)&&(o=X.access(t),a=X.set(e,o),h=o.events))for(s in delete a.handle,a.events={},h)for(i=0,n=h[s].length;i<n;i++)y.event.add(e,s,h[s][i]);G.hasData(t)&&(r=G.access(t),l=y.extend({},r),G.set(e,l))}}function Ot(t,e){var i=e.nodeName.toLowerCase();"input"===i&&ht.test(t.type)?e.checked=t.checked:"input"!==i&&"textarea"!==i||(e.defaultValue=t.defaultValue)}function Nt(t,e,i,n){e=a.apply([],e);var s,o,r,l,h,c,u=0,d=t.length,p=d-1,m=e[0],v=g(m);if(v||d>1&&"string"==typeof m&&!f.checkClone&&kt.test(m))return t.each(function(s){var o=t.eq(s);v&&(e[0]=m.call(this,s,o.html())),Nt(o,e,i,n)});if(d&&(o=(s=mt(e,t[0].ownerDocument,!1,t,n)).firstChild,1===s.childNodes.length&&(s=o),o||n)){for(l=(r=y.map(pt(s,"script"),At)).length;u<d;u++)h=s,u!==p&&(h=y.clone(h,!0,!0),l&&y.merge(r,pt(h,"script"))),i.call(t[u],h,u);if(l)for(c=r[r.length-1].ownerDocument,y.map(r,Pt),u=0;u<l;u++)h=r[u],ut.test(h.type||"")&&!X.access(h,"globalEval")&&y.contains(c,h)&&(h.src&&"module"!==(h.type||"").toLowerCase()?y._evalUrl&&y._evalUrl(h.src):_(h.textContent.replace(It,""),c,h))}return t}function Ht(t,e,i){for(var n,s=e?y.filter(e,t):t,o=0;null!=(n=s[o]);o++)i||1!==n.nodeType||y.cleanData(pt(n)),n.parentNode&&(i&&y.contains(n.ownerDocument,n)&&ft(pt(n,"script")),n.parentNode.removeChild(n));return t}y.extend({htmlPrefilter:function(t){return t.replace(Tt,"<$1></$2>")},clone:function(t,e,i){var n,s,o,a,r=t.cloneNode(!0),l=y.contains(t.ownerDocument,t);if(!(f.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||y.isXMLDoc(t)))for(a=pt(r),n=0,s=(o=pt(t)).length;n<s;n++)Ot(o[n],a[n]);if(e)if(i)for(o=o||pt(t),a=a||pt(r),n=0,s=o.length;n<s;n++)Mt(o[n],a[n]);else Mt(t,r);return(a=pt(r,"script")).length>0&&ft(a,!l&&pt(t,"script")),r},cleanData:function(t){for(var e,i,n,s=y.event.special,o=0;void 0!==(i=t[o]);o++)if(V(i)){if(e=i[X.expando]){if(e.events)for(n in e.events)s[n]?y.event.remove(i,n):y.removeEvent(i,n,e.handle);i[X.expando]=void 0}i[G.expando]&&(i[G.expando]=void 0)}}}),y.fn.extend({detach:function(t){return Ht(this,t,!0)},remove:function(t){return Ht(this,t)},text:function(t){return B(this,function(t){return void 0===t?y.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)})},null,t,arguments.length)},append:function(){return Nt(this,arguments,function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Et(this,t).appendChild(t)})},prepend:function(){return Nt(this,arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=Et(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return Nt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return Nt(this,arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(y.cleanData(pt(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map(function(){return y.clone(this,t,e)})},html:function(t){return B(this,function(t){var e=this[0]||{},i=0,n=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!St.test(t)&&!dt[(ct.exec(t)||["",""])[1].toLowerCase()]){t=y.htmlPrefilter(t);try{for(;i<n;i++)1===(e=this[i]||{}).nodeType&&(y.cleanData(pt(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=[];return Nt(this,arguments,function(e){var i=this.parentNode;y.inArray(this,t)<0&&(y.cleanData(pt(this)),i&&i.replaceChild(e,this))},t)}}),y.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){y.fn[t]=function(t){for(var i,n=[],s=y(t),o=s.length-1,a=0;a<=o;a++)i=a===o?this:this.clone(!0),y(s[a])[e](i),r.apply(n,i.get());return this.pushStack(n)}});var Rt=new RegExp("^("+tt+")(?!px)[a-z%]+$","i"),Lt=function(e){var i=e.ownerDocument.defaultView;return i&&i.opener||(i=t),i.getComputedStyle(e)},Ft=new RegExp(it.join("|"),"i");function jt(t,e,i){var n,s,o,a,r=t.style;return(i=i||Lt(t))&&(""!==(a=i.getPropertyValue(e)||i[e])||y.contains(t.ownerDocument,t)||(a=y.style(t,e)),!f.pixelBoxStyles()&&Rt.test(a)&&Ft.test(e)&&(n=r.width,s=r.minWidth,o=r.maxWidth,r.minWidth=r.maxWidth=r.width=a,a=i.width,r.width=n,r.minWidth=s,r.maxWidth=o)),void 0!==a?a+"":a}function Wt(t,e){return{get:function(){if(!t())return(this.get=e).apply(this,arguments);delete this.get}}}!function(){function e(){if(c){h.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",c.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",vt.appendChild(h).appendChild(c);var e=t.getComputedStyle(c);s="1%"!==e.top,l=12===i(e.marginLeft),c.style.right="60%",r=36===i(e.right),o=36===i(e.width),c.style.position="absolute",a=36===c.offsetWidth||"absolute",vt.removeChild(h),c=null}}function i(t){return Math.round(parseFloat(t))}var s,o,a,r,l,h=n.createElement("div"),c=n.createElement("div");c.style&&(c.style.backgroundClip="content-box",c.cloneNode(!0).style.backgroundClip="",f.clearCloneStyle="content-box"===c.style.backgroundClip,y.extend(f,{boxSizingReliable:function(){return e(),o},pixelBoxStyles:function(){return e(),r},pixelPosition:function(){return e(),s},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),a}}))}();var zt=/^(none|table(?!-c[ea]).+)/,Bt=/^--/,$t={position:"absolute",visibility:"hidden",display:"block"},qt={letterSpacing:"0",fontWeight:"400"},Ut=["Webkit","Moz","ms"],Yt=n.createElement("div").style;function Vt(t){var e=y.cssProps[t];return e||(e=y.cssProps[t]=function(t){if(t in Yt)return t;for(var e=t[0].toUpperCase()+t.slice(1),i=Ut.length;i--;)if((t=Ut[i]+e)in Yt)return t}(t)||t),e}function Kt(t,e,i){var n=et.exec(e);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):e}function Xt(t,e,i,n,s,o){var a="width"===e?1:0,r=0,l=0;if(i===(n?"border":"content"))return 0;for(;a<4;a+=2)"margin"===i&&(l+=y.css(t,i+it[a],!0,s)),n?("content"===i&&(l-=y.css(t,"padding"+it[a],!0,s)),"margin"!==i&&(l-=y.css(t,"border"+it[a]+"Width",!0,s))):(l+=y.css(t,"padding"+it[a],!0,s),"padding"!==i?l+=y.css(t,"border"+it[a]+"Width",!0,s):r+=y.css(t,"border"+it[a]+"Width",!0,s));return!n&&o>=0&&(l+=Math.max(0,Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-o-l-r-.5))),l}function Gt(t,e,i){var n=Lt(t),s=jt(t,e,n),o="border-box"===y.css(t,"boxSizing",!1,n),a=o;if(Rt.test(s)){if(!i)return s;s="auto"}return a=a&&(f.boxSizingReliable()||s===t.style[e]),("auto"===s||!parseFloat(s)&&"inline"===y.css(t,"display",!1,n))&&(s=t["offset"+e[0].toUpperCase()+e.slice(1)],a=!0),(s=parseFloat(s)||0)+Xt(t,e,i||(o?"border":"content"),a,n,s)+"px"}function Jt(t,e,i,n,s){return new Jt.prototype.init(t,e,i,n,s)}y.extend({cssHooks:{opacity:{get:function(t,e){if(e){var i=jt(t,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(t,e,i,n){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var s,o,a,r=Y(e),l=Bt.test(e),h=t.style;if(l||(e=Vt(r)),a=y.cssHooks[e]||y.cssHooks[r],void 0===i)return a&&"get"in a&&void 0!==(s=a.get(t,!1,n))?s:h[e];"string"==(o=typeof i)&&(s=et.exec(i))&&s[1]&&(i=ot(t,e,s),o="number"),null!=i&&i==i&&("number"===o&&(i+=s&&s[3]||(y.cssNumber[r]?"":"px")),f.clearCloneStyle||""!==i||0!==e.indexOf("background")||(h[e]="inherit"),a&&"set"in a&&void 0===(i=a.set(t,i,n))||(l?h.setProperty(e,i):h[e]=i))}},css:function(t,e,i,n){var s,o,a,r=Y(e);return Bt.test(e)||(e=Vt(r)),(a=y.cssHooks[e]||y.cssHooks[r])&&"get"in a&&(s=a.get(t,!0,i)),void 0===s&&(s=jt(t,e,n)),"normal"===s&&e in qt&&(s=qt[e]),""===i||i?(o=parseFloat(s),!0===i||isFinite(o)?o||0:s):s}}),y.each(["height","width"],function(t,e){y.cssHooks[e]={get:function(t,i,n){if(i)return!zt.test(y.css(t,"display"))||t.getClientRects().length&&t.getBoundingClientRect().width?Gt(t,e,n):st(t,$t,function(){return Gt(t,e,n)})},set:function(t,i,n){var s,o=Lt(t),a="border-box"===y.css(t,"boxSizing",!1,o),r=n&&Xt(t,e,n,a,o);return a&&f.scrollboxSize()===o.position&&(r-=Math.ceil(t["offset"+e[0].toUpperCase()+e.slice(1)]-parseFloat(o[e])-Xt(t,e,"border",!1,o)-.5)),r&&(s=et.exec(i))&&"px"!==(s[3]||"px")&&(t.style[e]=i,i=y.css(t,e)),Kt(0,i,r)}}}),y.cssHooks.marginLeft=Wt(f.reliableMarginLeft,function(t,e){if(e)return(parseFloat(jt(t,"marginLeft"))||t.getBoundingClientRect().left-st(t,{marginLeft:0},function(){return t.getBoundingClientRect().left}))+"px"}),y.each({margin:"",padding:"",border:"Width"},function(t,e){y.cssHooks[t+e]={expand:function(i){for(var n=0,s={},o="string"==typeof i?i.split(" "):[i];n<4;n++)s[t+it[n]+e]=o[n]||o[n-2]||o[0];return s}},"margin"!==t&&(y.cssHooks[t+e].set=Kt)}),y.fn.extend({css:function(t,e){return B(this,function(t,e,i){var n,s,o={},a=0;if(Array.isArray(e)){for(n=Lt(t),s=e.length;a<s;a++)o[e[a]]=y.css(t,e[a],!1,n);return o}return void 0!==i?y.style(t,e,i):y.css(t,e)},t,e,arguments.length>1)}}),y.Tween=Jt,Jt.prototype={constructor:Jt,init:function(t,e,i,n,s,o){this.elem=t,this.prop=i,this.easing=s||y.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=n,this.unit=o||(y.cssNumber[i]?"":"px")},cur:function(){var t=Jt.propHooks[this.prop];return t&&t.get?t.get(this):Jt.propHooks._default.get(this)},run:function(t){var e,i=Jt.propHooks[this.prop];return this.options.duration?this.pos=e=y.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),i&&i.set?i.set(this):Jt.propHooks._default.set(this),this}},Jt.prototype.init.prototype=Jt.prototype,Jt.propHooks={_default:{get:function(t){var e;return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(e=y.css(t.elem,t.prop,""))&&"auto"!==e?e:0},set:function(t){y.fx.step[t.prop]?y.fx.step[t.prop](t):1!==t.elem.nodeType||null==t.elem.style[y.cssProps[t.prop]]&&!y.cssHooks[t.prop]?t.elem[t.prop]=t.now:y.style(t.elem,t.prop,t.now+t.unit)}}},Jt.propHooks.scrollTop=Jt.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},y.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},y.fx=Jt.prototype.init,y.fx.step={};var Zt,Qt,te=/^(?:toggle|show|hide)$/,ee=/queueHooks$/;function ie(){Qt&&(!1===n.hidden&&t.requestAnimationFrame?t.requestAnimationFrame(ie):t.setTimeout(ie,y.fx.interval),y.fx.tick())}function ne(){return t.setTimeout(function(){Zt=void 0}),Zt=Date.now()}function se(t,e){var i,n=0,s={height:t};for(e=e?1:0;n<4;n+=2-e)s["margin"+(i=it[n])]=s["padding"+i]=t;return e&&(s.opacity=s.width=t),s}function oe(t,e,i){for(var n,s=(ae.tweeners[e]||[]).concat(ae.tweeners["*"]),o=0,a=s.length;o<a;o++)if(n=s[o].call(i,e,t))return n}function ae(t,e,i){var n,s,o=0,a=ae.prefilters.length,r=y.Deferred().always(function(){delete l.elem}),l=function(){if(s)return!1;for(var e=Zt||ne(),i=Math.max(0,h.startTime+h.duration-e),n=1-(i/h.duration||0),o=0,a=h.tweens.length;o<a;o++)h.tweens[o].run(n);return r.notifyWith(t,[h,n,i]),n<1&&a?i:(a||r.notifyWith(t,[h,1,0]),r.resolveWith(t,[h]),!1)},h=r.promise({elem:t,props:y.extend({},e),opts:y.extend(!0,{specialEasing:{},easing:y.easing._default},i),originalProperties:e,originalOptions:i,startTime:Zt||ne(),duration:i.duration,tweens:[],createTween:function(e,i){var n=y.Tween(t,h.opts,e,i,h.opts.specialEasing[e]||h.opts.easing);return h.tweens.push(n),n},stop:function(e){var i=0,n=e?h.tweens.length:0;if(s)return this;for(s=!0;i<n;i++)h.tweens[i].run(1);return e?(r.notifyWith(t,[h,1,0]),r.resolveWith(t,[h,e])):r.rejectWith(t,[h,e]),this}}),c=h.props;for(function(t,e){var i,n,s,o,a;for(i in t)if(s=e[n=Y(i)],o=t[i],Array.isArray(o)&&(s=o[1],o=t[i]=o[0]),i!==n&&(t[n]=o,delete t[i]),(a=y.cssHooks[n])&&"expand"in a)for(i in o=a.expand(o),delete t[n],o)i in t||(t[i]=o[i],e[i]=s);else e[n]=s}(c,h.opts.specialEasing);o<a;o++)if(n=ae.prefilters[o].call(h,t,c,h.opts))return g(n.stop)&&(y._queueHooks(h.elem,h.opts.queue).stop=n.stop.bind(n)),n;return y.map(c,oe,h),g(h.opts.start)&&h.opts.start.call(t,h),h.progress(h.opts.progress).done(h.opts.done,h.opts.complete).fail(h.opts.fail).always(h.opts.always),y.fx.timer(y.extend(l,{elem:t,anim:h,queue:h.opts.queue})),h}y.Animation=y.extend(ae,{tweeners:{"*":[function(t,e){var i=this.createTween(t,e);return ot(i.elem,t,et.exec(e),i),i}]},tweener:function(t,e){g(t)?(e=t,t=["*"]):t=t.match(H);for(var i,n=0,s=t.length;n<s;n++)i=t[n],ae.tweeners[i]=ae.tweeners[i]||[],ae.tweeners[i].unshift(e)},prefilters:[function(t,e,i){var n,s,o,a,r,l,h,c,u="width"in e||"height"in e,d=this,p={},f=t.style,g=t.nodeType&&nt(t),m=X.get(t,"fxshow");for(n in i.queue||(null==(a=y._queueHooks(t,"fx")).unqueued&&(a.unqueued=0,r=a.empty.fire,a.empty.fire=function(){a.unqueued||r()}),a.unqueued++,d.always(function(){d.always(function(){a.unqueued--,y.queue(t,"fx").length||a.empty.fire()})})),e)if(s=e[n],te.test(s)){if(delete e[n],o=o||"toggle"===s,s===(g?"hide":"show")){if("show"!==s||!m||void 0===m[n])continue;g=!0}p[n]=m&&m[n]||y.style(t,n)}if((l=!y.isEmptyObject(e))||!y.isEmptyObject(p))for(n in u&&1===t.nodeType&&(i.overflow=[f.overflow,f.overflowX,f.overflowY],null==(h=m&&m.display)&&(h=X.get(t,"display")),"none"===(c=y.css(t,"display"))&&(h?c=h:(lt([t],!0),h=t.style.display||h,c=y.css(t,"display"),lt([t]))),("inline"===c||"inline-block"===c&&null!=h)&&"none"===y.css(t,"float")&&(l||(d.done(function(){f.display=h}),null==h&&(c=f.display,h="none"===c?"":c)),f.display="inline-block")),i.overflow&&(f.overflow="hidden",d.always(function(){f.overflow=i.overflow[0],f.overflowX=i.overflow[1],f.overflowY=i.overflow[2]})),l=!1,p)l||(m?"hidden"in m&&(g=m.hidden):m=X.access(t,"fxshow",{display:h}),o&&(m.hidden=!g),g&&lt([t],!0),d.done(function(){for(n in g||lt([t]),X.remove(t,"fxshow"),p)y.style(t,n,p[n])})),l=oe(g?m[n]:0,n,d),n in m||(m[n]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(t,e){e?ae.prefilters.unshift(t):ae.prefilters.push(t)}}),y.speed=function(t,e,i){var n=t&&"object"==typeof t?y.extend({},t):{complete:i||!i&&e||g(t)&&t,duration:t,easing:i&&e||e&&!g(e)&&e};return y.fx.off?n.duration=0:"number"!=typeof n.duration&&(n.duration in y.fx.speeds?n.duration=y.fx.speeds[n.duration]:n.duration=y.fx.speeds._default),null!=n.queue&&!0!==n.queue||(n.queue="fx"),n.old=n.complete,n.complete=function(){g(n.old)&&n.old.call(this),n.queue&&y.dequeue(this,n.queue)},n},y.fn.extend({fadeTo:function(t,e,i,n){return this.filter(nt).css("opacity",0).show().end().animate({opacity:e},t,i,n)},animate:function(t,e,i,n){var s=y.isEmptyObject(t),o=y.speed(e,i,n),a=function(){var e=ae(this,y.extend({},t),o);(s||X.get(this,"finish"))&&e.stop(!0)};return a.finish=a,s||!1===o.queue?this.each(a):this.queue(o.queue,a)},stop:function(t,e,i){var n=function(t){var e=t.stop;delete t.stop,e(i)};return"string"!=typeof t&&(i=e,e=t,t=void 0),e&&!1!==t&&this.queue(t||"fx",[]),this.each(function(){var e=!0,s=null!=t&&t+"queueHooks",o=y.timers,a=X.get(this);if(s)a[s]&&a[s].stop&&n(a[s]);else for(s in a)a[s]&&a[s].stop&&ee.test(s)&&n(a[s]);for(s=o.length;s--;)o[s].elem!==this||null!=t&&o[s].queue!==t||(o[s].anim.stop(i),e=!1,o.splice(s,1));!e&&i||y.dequeue(this,t)})},finish:function(t){return!1!==t&&(t=t||"fx"),this.each(function(){var e,i=X.get(this),n=i[t+"queue"],s=i[t+"queueHooks"],o=y.timers,a=n?n.length:0;for(i.finish=!0,y.queue(this,t,[]),s&&s.stop&&s.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<a;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete i.finish})}}),y.each(["toggle","show","hide"],function(t,e){var i=y.fn[e];y.fn[e]=function(t,n,s){return null==t||"boolean"==typeof t?i.apply(this,arguments):this.animate(se(e,!0),t,n,s)}}),y.each({slideDown:se("show"),slideUp:se("hide"),slideToggle:se("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){y.fn[t]=function(t,i,n){return this.animate(e,t,i,n)}}),y.timers=[],y.fx.tick=function(){var t,e=0,i=y.timers;for(Zt=Date.now();e<i.length;e++)(t=i[e])()||i[e]!==t||i.splice(e--,1);i.length||y.fx.stop(),Zt=void 0},y.fx.timer=function(t){y.timers.push(t),y.fx.start()},y.fx.interval=13,y.fx.start=function(){Qt||(Qt=!0,ie())},y.fx.stop=function(){Qt=null},y.fx.speeds={slow:600,fast:200,_default:400},y.fn.delay=function(e,i){return e=y.fx&&y.fx.speeds[e]||e,i=i||"fx",this.queue(i,function(i,n){var s=t.setTimeout(i,e);n.stop=function(){t.clearTimeout(s)}})},function(){var t=n.createElement("input"),e=n.createElement("select").appendChild(n.createElement("option"));t.type="checkbox",f.checkOn=""!==t.value,f.optSelected=e.selected,(t=n.createElement("input")).value="t",t.type="radio",f.radioValue="t"===t.value}();var re,le=y.expr.attrHandle;y.fn.extend({attr:function(t,e){return B(this,y.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each(function(){y.removeAttr(this,t)})}}),y.extend({attr:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?y.prop(t,e,i):(1===o&&y.isXMLDoc(t)||(s=y.attrHooks[e.toLowerCase()]||(y.expr.match.bool.test(e)?re:void 0)),void 0!==i?null===i?void y.removeAttr(t,e):s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:(t.setAttribute(e,i+""),i):s&&"get"in s&&null!==(n=s.get(t,e))?n:null==(n=y.find.attr(t,e))?void 0:n)},attrHooks:{type:{set:function(t,e){if(!f.radioValue&&"radio"===e&&k(t,"input")){var i=t.value;return t.setAttribute("type",e),i&&(t.value=i),e}}}},removeAttr:function(t,e){var i,n=0,s=e&&e.match(H);if(s&&1===t.nodeType)for(;i=s[n++];)t.removeAttribute(i)}}),re={set:function(t,e,i){return!1===e?y.removeAttr(t,i):t.setAttribute(i,i),i}},y.each(y.expr.match.bool.source.match(/\w+/g),function(t,e){var i=le[e]||y.find.attr;le[e]=function(t,e,n){var s,o,a=e.toLowerCase();return n||(o=le[a],le[a]=s,s=null!=i(t,e,n)?a:null,le[a]=o),s}});var he=/^(?:input|select|textarea|button)$/i,ce=/^(?:a|area)$/i;function ue(t){return(t.match(H)||[]).join(" ")}function de(t){return t.getAttribute&&t.getAttribute("class")||""}function pe(t){return Array.isArray(t)?t:"string"==typeof t&&t.match(H)||[]}y.fn.extend({prop:function(t,e){return B(this,y.prop,t,e,arguments.length>1)},removeProp:function(t){return this.each(function(){delete this[y.propFix[t]||t]})}}),y.extend({prop:function(t,e,i){var n,s,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&y.isXMLDoc(t)||(e=y.propFix[e]||e,s=y.propHooks[e]),void 0!==i?s&&"set"in s&&void 0!==(n=s.set(t,i,e))?n:t[e]=i:s&&"get"in s&&null!==(n=s.get(t,e))?n:t[e]},propHooks:{tabIndex:{get:function(t){var e=y.find.attr(t,"tabindex");return e?parseInt(e,10):he.test(t.nodeName)||ce.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),f.optSelected||(y.propHooks.selected={get:function(t){var e=t.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null},set:function(t){var e=t.parentNode;e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex)}}),y.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){y.propFix[this.toLowerCase()]=this}),y.fn.extend({addClass:function(t){var e,i,n,s,o,a,r,l=0;if(g(t))return this.each(function(e){y(this).addClass(t.call(this,e,de(this)))});if((e=pe(t)).length)for(;i=this[l++];)if(s=de(i),n=1===i.nodeType&&" "+ue(s)+" "){for(a=0;o=e[a++];)n.indexOf(" "+o+" ")<0&&(n+=o+" ");s!==(r=ue(n))&&i.setAttribute("class",r)}return this},removeClass:function(t){var e,i,n,s,o,a,r,l=0;if(g(t))return this.each(function(e){y(this).removeClass(t.call(this,e,de(this)))});if(!arguments.length)return this.attr("class","");if((e=pe(t)).length)for(;i=this[l++];)if(s=de(i),n=1===i.nodeType&&" "+ue(s)+" "){for(a=0;o=e[a++];)for(;n.indexOf(" "+o+" ")>-1;)n=n.replace(" "+o+" "," ");s!==(r=ue(n))&&i.setAttribute("class",r)}return this},toggleClass:function(t,e){var i=typeof t,n="string"===i||Array.isArray(t);return"boolean"==typeof e&&n?e?this.addClass(t):this.removeClass(t):g(t)?this.each(function(i){y(this).toggleClass(t.call(this,i,de(this),e),e)}):this.each(function(){var e,s,o,a;if(n)for(s=0,o=y(this),a=pe(t);e=a[s++];)o.hasClass(e)?o.removeClass(e):o.addClass(e);else void 0!==t&&"boolean"!==i||((e=de(this))&&X.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",e||!1===t?"":X.get(this,"__className__")||""))})},hasClass:function(t){var e,i,n=0;for(e=" "+t+" ";i=this[n++];)if(1===i.nodeType&&(" "+ue(de(i))+" ").indexOf(e)>-1)return!0;return!1}});var fe=/\r/g;y.fn.extend({val:function(t){var e,i,n,s=this[0];return arguments.length?(n=g(t),this.each(function(i){var s;1===this.nodeType&&(null==(s=n?t.call(this,i,y(this).val()):t)?s="":"number"==typeof s?s+="":Array.isArray(s)&&(s=y.map(s,function(t){return null==t?"":t+""})),(e=y.valHooks[this.type]||y.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,s,"value")||(this.value=s))})):s?(e=y.valHooks[s.type]||y.valHooks[s.nodeName.toLowerCase()])&&"get"in e&&void 0!==(i=e.get(s,"value"))?i:"string"==typeof(i=s.value)?i.replace(fe,""):null==i?"":i:void 0}}),y.extend({valHooks:{option:{get:function(t){var e=y.find.attr(t,"value");return null!=e?e:ue(y.text(t))}},select:{get:function(t){var e,i,n,s=t.options,o=t.selectedIndex,a="select-one"===t.type,r=a?null:[],l=a?o+1:s.length;for(n=o<0?l:a?o:0;n<l;n++)if(((i=s[n]).selected||n===o)&&!i.disabled&&(!i.parentNode.disabled||!k(i.parentNode,"optgroup"))){if(e=y(i).val(),a)return e;r.push(e)}return r},set:function(t,e){for(var i,n,s=t.options,o=y.makeArray(e),a=s.length;a--;)((n=s[a]).selected=y.inArray(y.valHooks.option.get(n),o)>-1)&&(i=!0);return i||(t.selectedIndex=-1),o}}}}),y.each(["radio","checkbox"],function(){y.valHooks[this]={set:function(t,e){if(Array.isArray(e))return t.checked=y.inArray(y(t).val(),e)>-1}},f.checkOn||(y.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})}),f.focusin="onfocusin"in t;var ge=/^(?:focusinfocus|focusoutblur)$/,me=function(t){t.stopPropagation()};y.extend(y.event,{trigger:function(e,i,s,o){var a,r,l,h,c,d,p,f,v=[s||n],_=u.call(e,"type")?e.type:e,b=u.call(e,"namespace")?e.namespace.split("."):[];if(r=f=l=s=s||n,3!==s.nodeType&&8!==s.nodeType&&!ge.test(_+y.event.triggered)&&(_.indexOf(".")>-1&&(_=(b=_.split(".")).shift(),b.sort()),c=_.indexOf(":")<0&&"on"+_,(e=e[y.expando]?e:new y.Event(_,"object"==typeof e&&e)).isTrigger=o?2:3,e.namespace=b.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=s),i=null==i?[e]:y.makeArray(i,[e]),p=y.event.special[_]||{},o||!p.trigger||!1!==p.trigger.apply(s,i))){if(!o&&!p.noBubble&&!m(s)){for(h=p.delegateType||_,ge.test(h+_)||(r=r.parentNode);r;r=r.parentNode)v.push(r),l=r;l===(s.ownerDocument||n)&&v.push(l.defaultView||l.parentWindow||t)}for(a=0;(r=v[a++])&&!e.isPropagationStopped();)f=r,e.type=a>1?h:p.bindType||_,(d=(X.get(r,"events")||{})[e.type]&&X.get(r,"handle"))&&d.apply(r,i),(d=c&&r[c])&&d.apply&&V(r)&&(e.result=d.apply(r,i),!1===e.result&&e.preventDefault());return e.type=_,o||e.isDefaultPrevented()||p._default&&!1!==p._default.apply(v.pop(),i)||!V(s)||c&&g(s[_])&&!m(s)&&((l=s[c])&&(s[c]=null),y.event.triggered=_,e.isPropagationStopped()&&f.addEventListener(_,me),s[_](),e.isPropagationStopped()&&f.removeEventListener(_,me),y.event.triggered=void 0,l&&(s[c]=l)),e.result}},simulate:function(t,e,i){var n=y.extend(new y.Event,i,{type:t,isSimulated:!0});y.event.trigger(n,null,e)}}),y.fn.extend({trigger:function(t,e){return this.each(function(){y.event.trigger(t,e,this)})},triggerHandler:function(t,e){var i=this[0];if(i)return y.event.trigger(t,e,i,!0)}}),f.focusin||y.each({focus:"focusin",blur:"focusout"},function(t,e){var i=function(t){y.event.simulate(e,t.target,y.event.fix(t))};y.event.special[e]={setup:function(){var n=this.ownerDocument||this,s=X.access(n,e);s||n.addEventListener(t,i,!0),X.access(n,e,(s||0)+1)},teardown:function(){var n=this.ownerDocument||this,s=X.access(n,e)-1;s?X.access(n,e,s):(n.removeEventListener(t,i,!0),X.remove(n,e))}}});var ve=t.location,_e=Date.now(),be=/\?/;y.parseXML=function(e){var i;if(!e||"string"!=typeof e)return null;try{i=(new t.DOMParser).parseFromString(e,"text/xml")}catch(t){i=void 0}return i&&!i.getElementsByTagName("parsererror").length||y.error("Invalid XML: "+e),i};var ye=/\[\]$/,we=/\r?\n/g,xe=/^(?:submit|button|image|reset|file)$/i,Ce=/^(?:input|select|textarea|keygen)/i;function De(t,e,i,n){var s;if(Array.isArray(e))y.each(e,function(e,s){i||ye.test(t)?n(t,s):De(t+"["+("object"==typeof s&&null!=s?e:"")+"]",s,i,n)});else if(i||"object"!==b(e))n(t,e);else for(s in e)De(t+"["+s+"]",e[s],i,n)}y.param=function(t,e){var i,n=[],s=function(t,e){var i=g(e)?e():e;n[n.length]=encodeURIComponent(t)+"="+encodeURIComponent(null==i?"":i)};if(Array.isArray(t)||t.jquery&&!y.isPlainObject(t))y.each(t,function(){s(this.name,this.value)});else for(i in t)De(i,t[i],e,s);return n.join("&")},y.fn.extend({serialize:function(){return y.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=y.prop(this,"elements");return t?y.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!y(this).is(":disabled")&&Ce.test(this.nodeName)&&!xe.test(t)&&(this.checked||!ht.test(t))}).map(function(t,e){var i=y(this).val();return null==i?null:Array.isArray(i)?y.map(i,function(t){return{name:e.name,value:t.replace(we,"\r\n")}}):{name:e.name,value:i.replace(we,"\r\n")}}).get()}});var Te=/%20/g,Se=/#.*$/,ke=/([?&])_=[^&]*/,Ie=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ee=/^(?:GET|HEAD)$/,Ae=/^\/\//,Pe={},Me={},Oe="*/".concat("*"),Ne=n.createElement("a");function He(t){return function(e,i){"string"!=typeof e&&(i=e,e="*");var n,s=0,o=e.toLowerCase().match(H)||[];if(g(i))for(;n=o[s++];)"+"===n[0]?(n=n.slice(1)||"*",(t[n]=t[n]||[]).unshift(i)):(t[n]=t[n]||[]).push(i)}}function Re(t,e,i,n){var s={},o=t===Me;function a(r){var l;return s[r]=!0,y.each(t[r]||[],function(t,r){var h=r(e,i,n);return"string"!=typeof h||o||s[h]?o?!(l=h):void 0:(e.dataTypes.unshift(h),a(h),!1)}),l}return a(e.dataTypes[0])||!s["*"]&&a("*")}function Le(t,e){var i,n,s=y.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((s[i]?t:n||(n={}))[i]=e[i]);return n&&y.extend(!0,t,n),t}Ne.href=ve.href,y.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ve.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(ve.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Oe,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":y.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?Le(Le(t,y.ajaxSettings),e):Le(y.ajaxSettings,t)},ajaxPrefilter:He(Pe),ajaxTransport:He(Me),ajax:function(e,i){"object"==typeof e&&(i=e,e=void 0),i=i||{};var s,o,a,r,l,h,c,u,d,p,f=y.ajaxSetup({},i),g=f.context||f,m=f.context&&(g.nodeType||g.jquery)?y(g):y.event,v=y.Deferred(),_=y.Callbacks("once memory"),b=f.statusCode||{},w={},x={},C="canceled",D={readyState:0,getResponseHeader:function(t){var e;if(c){if(!r)for(r={};e=Ie.exec(a);)r[e[1].toLowerCase()]=e[2];e=r[t.toLowerCase()]}return null==e?null:e},getAllResponseHeaders:function(){return c?a:null},setRequestHeader:function(t,e){return null==c&&(t=x[t.toLowerCase()]=x[t.toLowerCase()]||t,w[t]=e),this},overrideMimeType:function(t){return null==c&&(f.mimeType=t),this},statusCode:function(t){var e;if(t)if(c)D.always(t[D.status]);else for(e in t)b[e]=[b[e],t[e]];return this},abort:function(t){var e=t||C;return s&&s.abort(e),T(0,e),this}};if(v.promise(D),f.url=((e||f.url||ve.href)+"").replace(Ae,ve.protocol+"//"),f.type=i.method||i.type||f.method||f.type,f.dataTypes=(f.dataType||"*").toLowerCase().match(H)||[""],null==f.crossDomain){h=n.createElement("a");try{h.href=f.url,h.href=h.href,f.crossDomain=Ne.protocol+"//"+Ne.host!=h.protocol+"//"+h.host}catch(t){f.crossDomain=!0}}if(f.data&&f.processData&&"string"!=typeof f.data&&(f.data=y.param(f.data,f.traditional)),Re(Pe,f,i,D),c)return D;for(d in(u=y.event&&f.global)&&0==y.active++&&y.event.trigger("ajaxStart"),f.type=f.type.toUpperCase(),f.hasContent=!Ee.test(f.type),o=f.url.replace(Se,""),f.hasContent?f.data&&f.processData&&0===(f.contentType||"").indexOf("application/x-www-form-urlencoded")&&(f.data=f.data.replace(Te,"+")):(p=f.url.slice(o.length),f.data&&(f.processData||"string"==typeof f.data)&&(o+=(be.test(o)?"&":"?")+f.data,delete f.data),!1===f.cache&&(o=o.replace(ke,"$1"),p=(be.test(o)?"&":"?")+"_="+_e+++p),f.url=o+p),f.ifModified&&(y.lastModified[o]&&D.setRequestHeader("If-Modified-Since",y.lastModified[o]),y.etag[o]&&D.setRequestHeader("If-None-Match",y.etag[o])),(f.data&&f.hasContent&&!1!==f.contentType||i.contentType)&&D.setRequestHeader("Content-Type",f.contentType),D.setRequestHeader("Accept",f.dataTypes[0]&&f.accepts[f.dataTypes[0]]?f.accepts[f.dataTypes[0]]+("*"!==f.dataTypes[0]?", "+Oe+"; q=0.01":""):f.accepts["*"]),f.headers)D.setRequestHeader(d,f.headers[d]);if(f.beforeSend&&(!1===f.beforeSend.call(g,D,f)||c))return D.abort();if(C="abort",_.add(f.complete),D.done(f.success),D.fail(f.error),s=Re(Me,f,i,D)){if(D.readyState=1,u&&m.trigger("ajaxSend",[D,f]),c)return D;f.async&&f.timeout>0&&(l=t.setTimeout(function(){D.abort("timeout")},f.timeout));try{c=!1,s.send(w,T)}catch(t){if(c)throw t;T(-1,t)}}else T(-1,"No Transport");function T(e,i,n,r){var h,d,p,w,x,C=i;c||(c=!0,l&&t.clearTimeout(l),s=void 0,a=r||"",D.readyState=e>0?4:0,h=e>=200&&e<300||304===e,n&&(w=function(t,e,i){for(var n,s,o,a,r=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===n&&(n=t.mimeType||e.getResponseHeader("Content-Type"));if(n)for(s in r)if(r[s]&&r[s].test(n)){l.unshift(s);break}if(l[0]in i)o=l[0];else{for(s in i){if(!l[0]||t.converters[s+" "+l[0]]){o=s;break}a||(a=s)}o=o||a}if(o)return o!==l[0]&&l.unshift(o),i[o]}(f,D,n)),w=function(t,e,i,n){var s,o,a,r,l,h={},c=t.dataTypes.slice();if(c[1])for(a in t.converters)h[a.toLowerCase()]=t.converters[a];for(o=c.shift();o;)if(t.responseFields[o]&&(i[t.responseFields[o]]=e),!l&&n&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(a=h[l+" "+o]||h["* "+o]))for(s in h)if((r=s.split(" "))[1]===o&&(a=h[l+" "+r[0]]||h["* "+r[0]])){!0===a?a=h[s]:!0!==h[s]&&(o=r[0],c.unshift(r[1]));break}if(!0!==a)if(a&&t.throws)e=a(e);else try{e=a(e)}catch(t){return{state:"parsererror",error:a?t:"No conversion from "+l+" to "+o}}}return{state:"success",data:e}}(f,w,D,h),h?(f.ifModified&&((x=D.getResponseHeader("Last-Modified"))&&(y.lastModified[o]=x),(x=D.getResponseHeader("etag"))&&(y.etag[o]=x)),204===e||"HEAD"===f.type?C="nocontent":304===e?C="notmodified":(C=w.state,d=w.data,h=!(p=w.error))):(p=C,!e&&C||(C="error",e<0&&(e=0))),D.status=e,D.statusText=(i||C)+"",h?v.resolveWith(g,[d,C,D]):v.rejectWith(g,[D,C,p]),D.statusCode(b),b=void 0,u&&m.trigger(h?"ajaxSuccess":"ajaxError",[D,f,h?d:p]),_.fireWith(g,[D,C]),u&&(m.trigger("ajaxComplete",[D,f]),--y.active||y.event.trigger("ajaxStop")))}return D},getJSON:function(t,e,i){return y.get(t,e,i,"json")},getScript:function(t,e){return y.get(t,void 0,e,"script")}}),y.each(["get","post"],function(t,e){y[e]=function(t,i,n,s){return g(i)&&(s=s||n,n=i,i=void 0),y.ajax(y.extend({url:t,type:e,dataType:s,data:i,success:n},y.isPlainObject(t)&&t))}}),y._evalUrl=function(t){return y.ajax({url:t,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,throws:!0})},y.fn.extend({wrapAll:function(t){var e;return this[0]&&(g(t)&&(t=t.call(this[0])),e=y(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t}).append(this)),this},wrapInner:function(t){return g(t)?this.each(function(e){y(this).wrapInner(t.call(this,e))}):this.each(function(){var e=y(this),i=e.contents();i.length?i.wrapAll(t):e.append(t)})},wrap:function(t){var e=g(t);return this.each(function(i){y(this).wrapAll(e?t.call(this,i):t)})},unwrap:function(t){return this.parent(t).not("body").each(function(){y(this).replaceWith(this.childNodes)}),this}}),y.expr.pseudos.hidden=function(t){return!y.expr.pseudos.visible(t)},y.expr.pseudos.visible=function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)},y.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(t){}};var Fe={0:200,1223:204},je=y.ajaxSettings.xhr();f.cors=!!je&&"withCredentials"in je,f.ajax=je=!!je,y.ajaxTransport(function(e){var i,n;if(f.cors||je&&!e.crossDomain)return{send:function(s,o){var a,r=e.xhr();if(r.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(a in e.xhrFields)r[a]=e.xhrFields[a];for(a in e.mimeType&&r.overrideMimeType&&r.overrideMimeType(e.mimeType),e.crossDomain||s["X-Requested-With"]||(s["X-Requested-With"]="XMLHttpRequest"),s)r.setRequestHeader(a,s[a]);i=function(t){return function(){i&&(i=n=r.onload=r.onerror=r.onabort=r.ontimeout=r.onreadystatechange=null,"abort"===t?r.abort():"error"===t?"number"!=typeof r.status?o(0,"error"):o(r.status,r.statusText):o(Fe[r.status]||r.status,r.statusText,"text"!==(r.responseType||"text")||"string"!=typeof r.responseText?{binary:r.response}:{text:r.responseText},r.getAllResponseHeaders()))}},r.onload=i(),n=r.onerror=r.ontimeout=i("error"),void 0!==r.onabort?r.onabort=n:r.onreadystatechange=function(){4===r.readyState&&t.setTimeout(function(){i&&n()})},i=i("abort");try{r.send(e.hasContent&&e.data||null)}catch(t){if(i)throw t}},abort:function(){i&&i()}}}),y.ajaxPrefilter(function(t){t.crossDomain&&(t.contents.script=!1)}),y.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return y.globalEval(t),t}}}),y.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")}),y.ajaxTransport("script",function(t){var e,i;if(t.crossDomain)return{send:function(s,o){e=y("<script>").prop({charset:t.scriptCharset,src:t.url}).on("load error",i=function(t){e.remove(),i=null,t&&o("error"===t.type?404:200,t.type)}),n.head.appendChild(e[0])},abort:function(){i&&i()}}});var We=[],ze=/(=)\?(?=&|$)|\?\?/;y.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=We.pop()||y.expando+"_"+_e++;return this[t]=!0,t}}),y.ajaxPrefilter("json jsonp",function(e,i,n){var s,o,a,r=!1!==e.jsonp&&(ze.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&ze.test(e.data)&&"data");if(r||"jsonp"===e.dataTypes[0])return s=e.jsonpCallback=g(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,r?e[r]=e[r].replace(ze,"$1"+s):!1!==e.jsonp&&(e.url+=(be.test(e.url)?"&":"?")+e.jsonp+"="+s),e.converters["script json"]=function(){return a||y.error(s+" was not called"),a[0]},e.dataTypes[0]="json",o=t[s],t[s]=function(){a=arguments},n.always(function(){void 0===o?y(t).removeProp(s):t[s]=o,e[s]&&(e.jsonpCallback=i.jsonpCallback,We.push(s)),a&&g(o)&&o(a[0]),a=o=void 0}),"script"}),f.createHTMLDocument=function(){var t=n.implementation.createHTMLDocument("").body;return t.innerHTML="<form></form><form></form>",2===t.childNodes.length}(),y.parseHTML=function(t,e,i){return"string"!=typeof t?[]:("boolean"==typeof e&&(i=e,e=!1),e||(f.createHTMLDocument?((s=(e=n.implementation.createHTMLDocument("")).createElement("base")).href=n.location.href,e.head.appendChild(s)):e=n),a=!i&&[],(o=I.exec(t))?[e.createElement(o[1])]:(o=mt([t],e,a),a&&a.length&&y(a).remove(),y.merge([],o.childNodes)));var s,o,a},y.fn.load=function(t,e,i){var n,s,o,a=this,r=t.indexOf(" ");return r>-1&&(n=ue(t.slice(r)),t=t.slice(0,r)),g(e)?(i=e,e=void 0):e&&"object"==typeof e&&(s="POST"),a.length>0&&y.ajax({url:t,type:s||"GET",dataType:"html",data:e}).done(function(t){o=arguments,a.html(n?y("<div>").append(y.parseHTML(t)).find(n):t)}).always(i&&function(t,e){a.each(function(){i.apply(this,o||[t.responseText,e,t])})}),this},y.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){y.fn[e]=function(t){return this.on(e,t)}}),y.expr.pseudos.animated=function(t){return y.grep(y.timers,function(e){return t===e.elem}).length},y.offset={setOffset:function(t,e,i){var n,s,o,a,r,l,h=y.css(t,"position"),c=y(t),u={};"static"===h&&(t.style.position="relative"),r=c.offset(),o=y.css(t,"top"),l=y.css(t,"left"),("absolute"===h||"fixed"===h)&&(o+l).indexOf("auto")>-1?(a=(n=c.position()).top,s=n.left):(a=parseFloat(o)||0,s=parseFloat(l)||0),g(e)&&(e=e.call(t,i,y.extend({},r))),null!=e.top&&(u.top=e.top-r.top+a),null!=e.left&&(u.left=e.left-r.left+s),"using"in e?e.using.call(t,u):c.css(u)}},y.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){y.offset.setOffset(this,t,e)});var e,i,n=this[0];return n?n.getClientRects().length?(e=n.getBoundingClientRect(),i=n.ownerDocument.defaultView,{top:e.top+i.pageYOffset,left:e.left+i.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var t,e,i,n=this[0],s={top:0,left:0};if("fixed"===y.css(n,"position"))e=n.getBoundingClientRect();else{for(e=this.offset(),i=n.ownerDocument,t=n.offsetParent||i.documentElement;t&&(t===i.body||t===i.documentElement)&&"static"===y.css(t,"position");)t=t.parentNode;t&&t!==n&&1===t.nodeType&&((s=y(t).offset()).top+=y.css(t,"borderTopWidth",!0),s.left+=y.css(t,"borderLeftWidth",!0))}return{top:e.top-s.top-y.css(n,"marginTop",!0),left:e.left-s.left-y.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent;t&&"static"===y.css(t,"position");)t=t.offsetParent;return t||vt})}}),y.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var i="pageYOffset"===e;y.fn[t]=function(n){return B(this,function(t,n,s){var o;if(m(t)?o=t:9===t.nodeType&&(o=t.defaultView),void 0===s)return o?o[e]:t[n];o?o.scrollTo(i?o.pageXOffset:s,i?s:o.pageYOffset):t[n]=s},t,n,arguments.length)}}),y.each(["top","left"],function(t,e){y.cssHooks[e]=Wt(f.pixelPosition,function(t,i){if(i)return i=jt(t,e),Rt.test(i)?y(t).position()[e]+"px":i})}),y.each({Height:"height",Width:"width"},function(t,e){y.each({padding:"inner"+t,content:e,"":"outer"+t},function(i,n){y.fn[n]=function(s,o){var a=arguments.length&&(i||"boolean"!=typeof s),r=i||(!0===s||!0===o?"margin":"border");return B(this,function(e,i,s){var o;return m(e)?0===n.indexOf("outer")?e["inner"+t]:e.document.documentElement["client"+t]:9===e.nodeType?(o=e.documentElement,Math.max(e.body["scroll"+t],o["scroll"+t],e.body["offset"+t],o["offset"+t],o["client"+t])):void 0===s?y.css(e,i,r):y.style(e,i,s,r)},e,a?s:void 0,a)}})}),y.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(t,e){y.fn[e]=function(t,i){return arguments.length>0?this.on(e,null,t,i):this.trigger(e)}}),y.fn.extend({hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),y.fn.extend({bind:function(t,e,i){return this.on(t,null,e,i)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,i,n){return this.on(e,t,i,n)},undelegate:function(t,e,i){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",i)}}),y.proxy=function(t,e){var i,n,s;if("string"==typeof e&&(i=t[e],e=t,t=i),g(t))return n=o.call(arguments,2),(s=function(){return t.apply(e||this,n.concat(o.call(arguments)))}).guid=t.guid=t.guid||y.guid++,s},y.holdReady=function(t){t?y.readyWait++:y.ready(!0)},y.isArray=Array.isArray,y.parseJSON=JSON.parse,y.nodeName=k,y.isFunction=g,y.isWindow=m,y.camelCase=Y,y.type=b,y.now=Date.now,y.isNumeric=function(t){var e=y.type(t);return("number"===e||"string"===e)&&!isNaN(t-parseFloat(t))},"function"==typeof define&&define.amd&&define("jquery",[],function(){return y});var Be=t.jQuery,$e=t.$;return y.noConflict=function(e){return t.$===y&&(t.$=$e),e&&t.jQuery===y&&(t.jQuery=Be),y},e||(t.jQuery=t.$=y),y}),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(function(t){function e(){this._curInst=null,this._keyEvent=!1,this._disabledInputs=[],this._datepickerShowing=!1,this._inDialog=!1,this._mainDivId="ui-datepicker-div",this._inlineClass="ui-datepicker-inline",this._appendClass="ui-datepicker-append",this._triggerClass="ui-datepicker-trigger",this._dialogClass="ui-datepicker-dialog",this._disableClass="ui-datepicker-disabled",this._unselectableClass="ui-datepicker-unselectable",this._currentClass="ui-datepicker-current-day",this._dayOverClass="ui-datepicker-days-cell-over",this.regional=[],this.regional[""]={closeText:"Done",prevText:"Prev",nextText:"Next",currentText:"Today",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],weekHeader:"Wk",dateFormat:"mm/dd/yy",firstDay:0,isRTL:!1,showMonthAfterYear:!1,yearSuffix:""},this._defaults={showOn:"focus",showAnim:"fadeIn",showOptions:{},defaultDate:null,appendText:"",buttonText:"...",buttonImage:"",buttonImageOnly:!1,hideIfNoPrevNext:!1,navigationAsDateFormat:!1,gotoCurrent:!1,changeMonth:!1,changeYear:!1,yearRange:"c-10:c+10",showOtherMonths:!1,selectOtherMonths:!1,showWeek:!1,calculateWeek:this.iso8601Week,shortYearCutoff:"+10",minDate:null,maxDate:null,duration:"fast",beforeShowDay:null,beforeShow:null,onSelect:null,onChangeMonthYear:null,onClose:null,numberOfMonths:1,showCurrentAtPos:0,stepMonths:1,stepBigMonths:12,altField:"",altFormat:"",constrainInput:!0,showButtonPanel:!1,autoSize:!1,disabled:!1},t.extend(this._defaults,this.regional[""]),this.regional.en=t.extend(!0,{},this.regional[""]),this.regional["en-US"]=t.extend(!0,{},this.regional.en),this.dpDiv=i(t("<div id='"+this._mainDivId+"' class='ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>"))}function i(e){var i="button, .ui-datepicker-prev, .ui-datepicker-next, .ui-datepicker-calendar td a";return e.on("mouseout",i,function(){t(this).removeClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&t(this).removeClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&t(this).removeClass("ui-datepicker-next-hover")}).on("mouseover",i,n)}function n(){t.datepicker._isDisabledDatepicker(d.inline?d.dpDiv.parent()[0]:d.input[0])||(t(this).parents(".ui-datepicker-calendar").find("a").removeClass("ui-state-hover"),t(this).addClass("ui-state-hover"),-1!==this.className.indexOf("ui-datepicker-prev")&&t(this).addClass("ui-datepicker-prev-hover"),-1!==this.className.indexOf("ui-datepicker-next")&&t(this).addClass("ui-datepicker-next-hover"))}function s(e,i){for(var n in t.extend(e,i),i)null==i[n]&&(e[n]=i[n]);return e}function o(t){return function(){var e=this.element.val();t.apply(this,arguments),this._refresh(),e!==this.element.val()&&this._trigger("change")}}t.ui=t.ui||{},t.ui.version="1.12.1";var a=0,r=Array.prototype.slice;t.cleanData=function(e){return function(i){var n,s,o;for(o=0;null!=(s=i[o]);o++)try{(n=t._data(s,"events"))&&n.remove&&t(s).triggerHandler("remove")}catch(t){}e(i)}}(t.cleanData),t.widget=function(e,i,n){var s,o,a,r={},l=e.split(".")[0],h=l+"-"+(e=e.split(".")[1]);return n||(n=i,i=t.Widget),t.isArray(n)&&(n=t.extend.apply(null,[{}].concat(n))),t.expr[":"][h.toLowerCase()]=function(e){return!!t.data(e,h)},t[l]=t[l]||{},s=t[l][e],o=t[l][e]=function(t,e){return this._createWidget?void(arguments.length&&this._createWidget(t,e)):new o(t,e)},t.extend(o,s,{version:n.version,_proto:t.extend({},n),_childConstructors:[]}),(a=new i).options=t.widget.extend({},a.options),t.each(n,function(e,n){return t.isFunction(n)?void(r[e]=function(){function t(){return i.prototype[e].apply(this,arguments)}function s(t){return i.prototype[e].apply(this,t)}return function(){var e,i=this._super,o=this._superApply;return this._super=t,this._superApply=s,e=n.apply(this,arguments),this._super=i,this._superApply=o,e}}()):void(r[e]=n)}),o.prototype=t.widget.extend(a,{widgetEventPrefix:s&&a.widgetEventPrefix||e},r,{constructor:o,namespace:l,widgetName:e,widgetFullName:h}),s?(t.each(s._childConstructors,function(e,i){var n=i.prototype;t.widget(n.namespace+"."+n.widgetName,o,i._proto)}),delete s._childConstructors):i._childConstructors.push(o),t.widget.bridge(e,o),o},t.widget.extend=function(e){for(var i,n,s=r.call(arguments,1),o=0,a=s.length;a>o;o++)for(i in s[o])n=s[o][i],s[o].hasOwnProperty(i)&&void 0!==n&&(e[i]=t.isPlainObject(n)?t.isPlainObject(e[i])?t.widget.extend({},e[i],n):t.widget.extend({},n):n);return e},t.widget.bridge=function(e,i){var n=i.prototype.widgetFullName||e;t.fn[e]=function(s){var o="string"==typeof s,a=r.call(arguments,1),l=this;return o?this.length||"instance"!==s?this.each(function(){var i,o=t.data(this,n);return"instance"===s?(l=o,!1):o?t.isFunction(o[s])&&"_"!==s.charAt(0)?(i=o[s].apply(o,a))!==o&&void 0!==i?(l=i&&i.jquery?l.pushStack(i.get()):i,!1):void 0:t.error("no such method '"+s+"' for "+e+" widget instance"):t.error("cannot call methods on "+e+" prior to initialization; attempted to call method '"+s+"'")}):l=void 0:(a.length&&(s=t.widget.extend.apply(null,[s].concat(a))),this.each(function(){var e=t.data(this,n);e?(e.option(s||{}),e._init&&e._init()):t.data(this,n,new i(s,this))})),l}},t.Widget=function(){},t.Widget._childConstructors=[],t.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{classes:{},disabled:!1,create:null},_createWidget:function(e,i){i=t(i||this.defaultElement||this)[0],this.element=t(i),this.uuid=a++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=t(),this.hoverable=t(),this.focusable=t(),this.classesElementLookup={},i!==this&&(t.data(i,this.widgetFullName,this),this._on(!0,this.element,{remove:function(t){t.target===i&&this.destroy()}}),this.document=t(i.style?i.ownerDocument:i.document||i),this.window=t(this.document[0].defaultView||this.document[0].parentWindow)),this.options=t.widget.extend({},this.options,this._getCreateOptions(),e),this._create(),this.options.disabled&&this._setOptionDisabled(this.options.disabled),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:function(){return{}},_getCreateEventData:t.noop,_create:t.noop,_init:t.noop,destroy:function(){var e=this;this._destroy(),t.each(this.classesElementLookup,function(t,i){e._removeClass(i,t)}),this.element.off(this.eventNamespace).removeData(this.widgetFullName),this.widget().off(this.eventNamespace).removeAttr("aria-disabled"),this.bindings.off(this.eventNamespace)},_destroy:t.noop,widget:function(){return this.element},option:function(e,i){var n,s,o,a=e;if(0===arguments.length)return t.widget.extend({},this.options);if("string"==typeof e)if(a={},n=e.split("."),e=n.shift(),n.length){for(s=a[e]=t.widget.extend({},this.options[e]),o=0;n.length-1>o;o++)s[n[o]]=s[n[o]]||{},s=s[n[o]];if(e=n.pop(),1===arguments.length)return void 0===s[e]?null:s[e];s[e]=i}else{if(1===arguments.length)return void 0===this.options[e]?null:this.options[e];a[e]=i}return this._setOptions(a),this},_setOptions:function(t){var e;for(e in t)this._setOption(e,t[e]);return this},_setOption:function(t,e){return"classes"===t&&this._setOptionClasses(e),this.options[t]=e,"disabled"===t&&this._setOptionDisabled(e),this},_setOptionClasses:function(e){var i,n,s;for(i in e)s=this.classesElementLookup[i],e[i]!==this.options.classes[i]&&s&&s.length&&(n=t(s.get()),this._removeClass(s,i),n.addClass(this._classes({element:n,keys:i,classes:e,add:!0})))},_setOptionDisabled:function(t){this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!!t),t&&(this._removeClass(this.hoverable,null,"ui-state-hover"),this._removeClass(this.focusable,null,"ui-state-focus"))},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_classes:function(e){function i(i,o){var a,r;for(r=0;i.length>r;r++)a=s.classesElementLookup[i[r]]||t(),a=e.add?t(t.unique(a.get().concat(e.element.get()))):t(a.not(e.element).get()),s.classesElementLookup[i[r]]=a,n.push(i[r]),o&&e.classes[i[r]]&&n.push(e.classes[i[r]])}var n=[],s=this;return e=t.extend({element:this.element,classes:this.options.classes||{}},e),this._on(e.element,{remove:"_untrackClassesElement"}),e.keys&&i(e.keys.match(/\S+/g)||[],!0),e.extra&&i(e.extra.match(/\S+/g)||[]),n.join(" ")},_untrackClassesElement:function(e){var i=this;t.each(i.classesElementLookup,function(n,s){-1!==t.inArray(e.target,s)&&(i.classesElementLookup[n]=t(s.not(e.target).get()))})},_removeClass:function(t,e,i){return this._toggleClass(t,e,i,!1)},_addClass:function(t,e,i){return this._toggleClass(t,e,i,!0)},_toggleClass:function(t,e,i,n){n="boolean"==typeof n?n:i;var s="string"==typeof t||null===t,o={extra:s?e:i,keys:s?t:e,element:s?this.element:t,add:n};return o.element.toggleClass(this._classes(o),n),this},_on:function(e,i,n){var s,o=this;"boolean"!=typeof e&&(n=i,i=e,e=!1),n?(i=s=t(i),this.bindings=this.bindings.add(i)):(n=i,i=this.element,s=this.widget()),t.each(n,function(n,a){function r(){return e||!0!==o.options.disabled&&!t(this).hasClass("ui-state-disabled")?("string"==typeof a?o[a]:a).apply(o,arguments):void 0}"string"!=typeof a&&(r.guid=a.guid=a.guid||r.guid||t.guid++);var l=n.match(/^([\w:-]*)\s*(.*)$/),h=l[1]+o.eventNamespace,c=l[2];c?s.on(h,c,r):i.on(h,r)})},_off:function(e,i){i=(i||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.off(i).off(i),this.bindings=t(this.bindings.not(e).get()),this.focusable=t(this.focusable.not(e).get()),this.hoverable=t(this.hoverable.not(e).get())},_delay:function(t,e){var i=this;return setTimeout(function(){return("string"==typeof t?i[t]:t).apply(i,arguments)},e||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){this._addClass(t(e.currentTarget),null,"ui-state-hover")},mouseleave:function(e){this._removeClass(t(e.currentTarget),null,"ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){this._addClass(t(e.currentTarget),null,"ui-state-focus")},focusout:function(e){this._removeClass(t(e.currentTarget),null,"ui-state-focus")}})},_trigger:function(e,i,n){var s,o,a=this.options[e];if(n=n||{},(i=t.Event(i)).type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),i.target=this.element[0],o=i.originalEvent)for(s in o)s in i||(i[s]=o[s]);return this.element.trigger(i,n),!(t.isFunction(a)&&!1===a.apply(this.element[0],[i].concat(n))||i.isDefaultPrevented())}},t.each({show:"fadeIn",hide:"fadeOut"},function(e,i){t.Widget.prototype["_"+e]=function(n,s,o){"string"==typeof s&&(s={effect:s});var a,r=s?!0===s||"number"==typeof s?i:s.effect||i:e;"number"==typeof(s=s||{})&&(s={duration:s}),a=!t.isEmptyObject(s),s.complete=o,s.delay&&n.delay(s.delay),a&&t.effects&&t.effects.effect[r]?n[e](s):r!==e&&n[r]?n[r](s.duration,s.easing,o):n.queue(function(i){t(this)[e](),o&&o.call(n[0]),i()})}}),t.widget,function(){function e(t,e,i){return[parseFloat(t[0])*(c.test(t[0])?e/100:1),parseFloat(t[1])*(c.test(t[1])?i/100:1)]}function i(e,i){return parseInt(t.css(e,i),10)||0}var n,s=Math.max,o=Math.abs,a=/left|center|right/,r=/top|center|bottom/,l=/[\+\-]\d+(\.[\d]+)?%?/,h=/^\w+/,c=/%$/,u=t.fn.position;t.position={scrollbarWidth:function(){if(void 0!==n)return n;var e,i,s=t("<div style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'><div style='height:100px;width:auto;'></div></div>"),o=s.children()[0];return t("body").append(s),e=o.offsetWidth,s.css("overflow","scroll"),e===(i=o.offsetWidth)&&(i=s[0].clientWidth),s.remove(),n=e-i},getScrollInfo:function(e){var i=e.isWindow||e.isDocument?"":e.element.css("overflow-x"),n=e.isWindow||e.isDocument?"":e.element.css("overflow-y"),s="scroll"===i||"auto"===i&&e.width<e.element[0].scrollWidth;return{width:"scroll"===n||"auto"===n&&e.height<e.element[0].scrollHeight?t.position.scrollbarWidth():0,height:s?t.position.scrollbarWidth():0}},getWithinInfo:function(e){var i=t(e||window),n=t.isWindow(i[0]),s=!!i[0]&&9===i[0].nodeType;return{element:i,isWindow:n,isDocument:s,offset:!n&&!s?t(e).offset():{left:0,top:0},scrollLeft:i.scrollLeft(),scrollTop:i.scrollTop(),width:i.outerWidth(),height:i.outerHeight()}}},t.fn.position=function(n){if(!n||!n.of)return u.apply(this,arguments);n=t.extend({},n);var c,d,p,f,g,m,v=t(n.of),_=t.position.getWithinInfo(n.within),b=t.position.getScrollInfo(_),y=(n.collision||"flip").split(" "),w={};return m=function(e){var i=e[0];return 9===i.nodeType?{width:e.width(),height:e.height(),offset:{top:0,left:0}}:t.isWindow(i)?{width:e.width(),height:e.height(),offset:{top:e.scrollTop(),left:e.scrollLeft()}}:i.preventDefault?{width:0,height:0,offset:{top:i.pageY,left:i.pageX}}:{width:e.outerWidth(),height:e.outerHeight(),offset:e.offset()}}(v),v[0].preventDefault&&(n.at="left top"),d=m.width,p=m.height,f=m.offset,g=t.extend({},f),t.each(["my","at"],function(){var t,e,i=(n[this]||"").split(" ");1===i.length&&(i=a.test(i[0])?i.concat(["center"]):r.test(i[0])?["center"].concat(i):["center","center"]),i[0]=a.test(i[0])?i[0]:"center",i[1]=r.test(i[1])?i[1]:"center",t=l.exec(i[0]),e=l.exec(i[1]),w[this]=[t?t[0]:0,e?e[0]:0],n[this]=[h.exec(i[0])[0],h.exec(i[1])[0]]}),1===y.length&&(y[1]=y[0]),"right"===n.at[0]?g.left+=d:"center"===n.at[0]&&(g.left+=d/2),"bottom"===n.at[1]?g.top+=p:"center"===n.at[1]&&(g.top+=p/2),c=e(w.at,d,p),g.left+=c[0],g.top+=c[1],this.each(function(){var a,r,l=t(this),h=l.outerWidth(),u=l.outerHeight(),m=i(this,"marginLeft"),x=i(this,"marginTop"),C=h+m+i(this,"marginRight")+b.width,D=u+x+i(this,"marginBottom")+b.height,T=t.extend({},g),S=e(w.my,l.outerWidth(),l.outerHeight());"right"===n.my[0]?T.left-=h:"center"===n.my[0]&&(T.left-=h/2),"bottom"===n.my[1]?T.top-=u:"center"===n.my[1]&&(T.top-=u/2),T.left+=S[0],T.top+=S[1],a={marginLeft:m,marginTop:x},t.each(["left","top"],function(e,i){t.ui.position[y[e]]&&t.ui.position[y[e]][i](T,{targetWidth:d,targetHeight:p,elemWidth:h,elemHeight:u,collisionPosition:a,collisionWidth:C,collisionHeight:D,offset:[c[0]+S[0],c[1]+S[1]],my:n.my,at:n.at,within:_,elem:l})}),n.using&&(r=function(t){var e=f.left-T.left,i=e+d-h,a=f.top-T.top,r=a+p-u,c={target:{element:v,left:f.left,top:f.top,width:d,height:p},element:{element:l,left:T.left,top:T.top,width:h,height:u},horizontal:0>i?"left":e>0?"right":"center",vertical:0>r?"top":a>0?"bottom":"middle"};h>d&&d>o(e+i)&&(c.horizontal="center"),u>p&&p>o(a+r)&&(c.vertical="middle"),c.important=s(o(e),o(i))>s(o(a),o(r))?"horizontal":"vertical",n.using.call(this,t,c)}),l.offset(t.extend(T,{using:r}))})},t.ui.position={fit:{left:function(t,e){var i,n=e.within,o=n.isWindow?n.scrollLeft:n.offset.left,a=n.width,r=t.left-e.collisionPosition.marginLeft,l=o-r,h=r+e.collisionWidth-a-o;e.collisionWidth>a?l>0&&0>=h?(i=t.left+l+e.collisionWidth-a-o,t.left+=l-i):t.left=h>0&&0>=l?o:l>h?o+a-e.collisionWidth:o:l>0?t.left+=l:h>0?t.left-=h:t.left=s(t.left-r,t.left)},top:function(t,e){var i,n=e.within,o=n.isWindow?n.scrollTop:n.offset.top,a=e.within.height,r=t.top-e.collisionPosition.marginTop,l=o-r,h=r+e.collisionHeight-a-o;e.collisionHeight>a?l>0&&0>=h?(i=t.top+l+e.collisionHeight-a-o,t.top+=l-i):t.top=h>0&&0>=l?o:l>h?o+a-e.collisionHeight:o:l>0?t.top+=l:h>0?t.top-=h:t.top=s(t.top-r,t.top)}},flip:{left:function(t,e){var i,n,s=e.within,a=s.offset.left+s.scrollLeft,r=s.width,l=s.isWindow?s.scrollLeft:s.offset.left,h=t.left-e.collisionPosition.marginLeft,c=h-l,u=h+e.collisionWidth-r-l,d="left"===e.my[0]?-e.elemWidth:"right"===e.my[0]?e.elemWidth:0,p="left"===e.at[0]?e.targetWidth:"right"===e.at[0]?-e.targetWidth:0,f=-2*e.offset[0];0>c?(0>(i=t.left+d+p+f+e.collisionWidth-r-a)||o(c)>i)&&(t.left+=d+p+f):u>0&&(((n=t.left-e.collisionPosition.marginLeft+d+p+f-l)>0||u>o(n))&&(t.left+=d+p+f))},top:function(t,e){var i,n,s=e.within,a=s.offset.top+s.scrollTop,r=s.height,l=s.isWindow?s.scrollTop:s.offset.top,h=t.top-e.collisionPosition.marginTop,c=h-l,u=h+e.collisionHeight-r-l,d="top"===e.my[1]?-e.elemHeight:"bottom"===e.my[1]?e.elemHeight:0,p="top"===e.at[1]?e.targetHeight:"bottom"===e.at[1]?-e.targetHeight:0,f=-2*e.offset[1];0>c?(0>(n=t.top+d+p+f+e.collisionHeight-r-a)||o(c)>n)&&(t.top+=d+p+f):u>0&&(((i=t.top-e.collisionPosition.marginTop+d+p+f-l)>0||u>o(i))&&(t.top+=d+p+f))}},flipfit:{left:function(){t.ui.position.flip.left.apply(this,arguments),t.ui.position.fit.left.apply(this,arguments)},top:function(){t.ui.position.flip.top.apply(this,arguments),t.ui.position.fit.top.apply(this,arguments)}}}}(),t.ui.position,t.extend(t.expr[":"],{data:t.expr.createPseudo?t.expr.createPseudo(function(e){return function(i){return!!t.data(i,e)}}):function(e,i,n){return!!t.data(e,n[3])}}),t.fn.extend({disableSelection:function(){var t="onselectstart"in document.createElement("div")?"selectstart":"mousedown";return function(){return this.on(t+".ui-disableSelection",function(t){t.preventDefault()})}}(),enableSelection:function(){return this.off(".ui-disableSelection")}});var l="ui-effects-",h="ui-effects-style",c="ui-effects-animated",u=t;t.effects={effect:{}},function(t,e){function i(t,e,i){var n=c[e.type]||{};return null==t?i||!e.def?null:e.def:(t=n.floor?~~t:parseFloat(t),isNaN(t)?e.def:n.mod?(t+n.mod)%n.mod:0>t?0:t>n.max?n.max:t)}function n(i){var n=l(),s=n._rgba=[];return i=i.toLowerCase(),p(r,function(t,o){var a,r=o.re.exec(i),l=r&&o.parse(r),c=o.space||"rgba";return l?(a=n[c](l),n[h[c].cache]=a[h[c].cache],s=n._rgba=a._rgba,!1):e}),s.length?("0,0,0,0"===s.join()&&t.extend(s,o.transparent),n):o[i]}function s(t,e,i){return 1>6*(i=(i+1)%1)?t+6*(e-t)*i:1>2*i?e:2>3*i?t+6*(e-t)*(2/3-i):t}var o,a=/^([\-+])=\s*(\d+\.?\d*)/,r=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[t[1],t[2],t[3],t[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[2.55*t[1],2.55*t[2],2.55*t[3],t[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})/,parse:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])/,parse:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(t){return[t[1],t[2]/100,t[3]/100,t[4]]}}],l=t.Color=function(e,i,n,s){return new t.Color.fn.parse(e,i,n,s)},h={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},c={byte:{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},u=l.support={},d=t("<p>")[0],p=t.each;d.style.cssText="background-color:rgba(1,1,1,.5)",u.rgba=d.style.backgroundColor.indexOf("rgba")>-1,p(h,function(t,e){e.cache="_"+t,e.props.alpha={idx:3,type:"percent",def:1}}),l.fn=t.extend(l.prototype,{parse:function(s,a,r,c){if(s===e)return this._rgba=[null,null,null,null],this;(s.jquery||s.nodeType)&&(s=t(s).css(a),a=e);var u=this,d=t.type(s),f=this._rgba=[];return a!==e&&(s=[s,a,r,c],d="array"),"string"===d?this.parse(n(s)||o._default):"array"===d?(p(h.rgba.props,function(t,e){f[e.idx]=i(s[e.idx],e)}),this):"object"===d?(p(h,s instanceof l?function(t,e){s[e.cache]&&(u[e.cache]=s[e.cache].slice())}:function(e,n){var o=n.cache;p(n.props,function(t,e){if(!u[o]&&n.to){if("alpha"===t||null==s[t])return;u[o]=n.to(u._rgba)}u[o][e.idx]=i(s[t],e,!0)}),u[o]&&0>t.inArray(null,u[o].slice(0,3))&&(u[o][3]=1,n.from&&(u._rgba=n.from(u[o])))}),this):e},is:function(t){var i=l(t),n=!0,s=this;return p(h,function(t,o){var a,r=i[o.cache];return r&&(a=s[o.cache]||o.to&&o.to(s._rgba)||[],p(o.props,function(t,i){return null!=r[i.idx]?n=r[i.idx]===a[i.idx]:e})),n}),n},_space:function(){var t=[],e=this;return p(h,function(i,n){e[n.cache]&&t.push(i)}),t.pop()},transition:function(t,e){var n=l(t),s=n._space(),o=h[s],a=0===this.alpha()?l("transparent"):this,r=a[o.cache]||o.to(a._rgba),u=r.slice();return n=n[o.cache],p(o.props,function(t,s){var o=s.idx,a=r[o],l=n[o],h=c[s.type]||{};null!==l&&(null===a?u[o]=l:(h.mod&&(l-a>h.mod/2?a+=h.mod:a-l>h.mod/2&&(a-=h.mod)),u[o]=i((l-a)*e+a,s)))}),this[s](u)},blend:function(e){if(1===this._rgba[3])return this;var i=this._rgba.slice(),n=i.pop(),s=l(e)._rgba;return l(t.map(i,function(t,e){return(1-n)*s[e]+n*t}))},toRgbaString:function(){var e="rgba(",i=t.map(this._rgba,function(t,e){return null==t?e>2?1:0:t});return 1===i[3]&&(i.pop(),e="rgb("),e+i.join()+")"},toHslaString:function(){var e="hsla(",i=t.map(this.hsla(),function(t,e){return null==t&&(t=e>2?1:0),e&&3>e&&(t=Math.round(100*t)+"%"),t});return 1===i[3]&&(i.pop(),e="hsl("),e+i.join()+")"},toHexString:function(e){var i=this._rgba.slice(),n=i.pop();return e&&i.push(~~(255*n)),"#"+t.map(i,function(t){return 1===(t=(t||0).toString(16)).length?"0"+t:t}).join("")},toString:function(){return 0===this._rgba[3]?"transparent":this.toRgbaString()}}),l.fn.parse.prototype=l.fn,h.hsla.to=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e,i,n=t[0]/255,s=t[1]/255,o=t[2]/255,a=t[3],r=Math.max(n,s,o),l=Math.min(n,s,o),h=r-l,c=r+l,u=.5*c;return e=l===r?0:n===r?60*(s-o)/h+360:s===r?60*(o-n)/h+120:60*(n-s)/h+240,i=0===h?0:.5>=u?h/c:h/(2-c),[Math.round(e)%360,i,u,null==a?1:a]},h.hsla.from=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e=t[0]/360,i=t[1],n=t[2],o=t[3],a=.5>=n?n*(1+i):n+i-n*i,r=2*n-a;return[Math.round(255*s(r,a,e+1/3)),Math.round(255*s(r,a,e)),Math.round(255*s(r,a,e-1/3)),o]},p(h,function(n,s){var o=s.props,r=s.cache,h=s.to,c=s.from;l.fn[n]=function(n){if(h&&!this[r]&&(this[r]=h(this._rgba)),n===e)return this[r].slice();var s,a=t.type(n),u="array"===a||"object"===a?n:arguments,d=this[r].slice();return p(o,function(t,e){var n=u["object"===a?t:e.idx];null==n&&(n=d[e.idx]),d[e.idx]=i(n,e)}),c?((s=l(c(d)))[r]=d,s):l(d)},p(o,function(e,i){l.fn[e]||(l.fn[e]=function(s){var o,r=t.type(s),l="alpha"===e?this._hsla?"hsla":"rgba":n,h=this[l](),c=h[i.idx];return"undefined"===r?c:("function"===r&&(s=s.call(this,c),r=t.type(s)),null==s&&i.empty?this:("string"===r&&((o=a.exec(s))&&(s=c+parseFloat(o[2])*("+"===o[1]?1:-1))),h[i.idx]=s,this[l](h)))})})}),l.hook=function(e){var i=e.split(" ");p(i,function(e,i){t.cssHooks[i]={set:function(e,s){var o,a,r="";if("transparent"!==s&&("string"!==t.type(s)||(o=n(s)))){if(s=l(o||s),!u.rgba&&1!==s._rgba[3]){for(a="backgroundColor"===i?e.parentNode:e;(""===r||"transparent"===r)&&a&&a.style;)try{r=t.css(a,"backgroundColor"),a=a.parentNode}catch(t){}s=s.blend(r&&"transparent"!==r?r:"_default")}s=s.toRgbaString()}try{e.style[i]=s}catch(t){}}},t.fx.step[i]=function(e){e.colorInit||(e.start=l(e.elem,i),e.end=l(e.end),e.colorInit=!0),t.cssHooks[i].set(e.elem,e.start.transition(e.end,e.pos))}})},l.hook("backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor"),t.cssHooks.borderColor={expand:function(t){var e={};return p(["Top","Right","Bottom","Left"],function(i,n){e["border"+n+"Color"]=t}),e}},o=t.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"}}(u),function(){function e(e){var i,n,s=e.ownerDocument.defaultView?e.ownerDocument.defaultView.getComputedStyle(e,null):e.currentStyle,o={};if(s&&s.length&&s[0]&&s[s[0]])for(n=s.length;n--;)"string"==typeof s[i=s[n]]&&(o[t.camelCase(i)]=s[i]);else for(i in s)"string"==typeof s[i]&&(o[i]=s[i]);return o}function i(e,i){var n,o,a={};for(n in i)o=i[n],e[n]!==o&&(s[n]||(t.fx.step[n]||!isNaN(parseFloat(o)))&&(a[n]=o));return a}var n=["add","remove","toggle"],s={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1};t.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],function(e,i){t.fx.step[i]=function(t){("none"!==t.end&&!t.setAttr||1===t.pos&&!t.setAttr)&&(u.style(t.elem,i,t.end),t.setAttr=!0)}}),t.fn.addBack||(t.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),t.effects.animateClass=function(s,o,a,r){var l=t.speed(o,a,r);return this.queue(function(){var o,a=t(this),r=a.attr("class")||"",h=l.children?a.find("*").addBack():a;h=h.map(function(){return{el:t(this),start:e(this)}}),(o=function(){t.each(n,function(t,e){s[e]&&a[e+"Class"](s[e])})})(),h=h.map(function(){return this.end=e(this.el[0]),this.diff=i(this.start,this.end),this}),a.attr("class",r),h=h.map(function(){var e=this,i=t.Deferred(),n=t.extend({},l,{queue:!1,complete:function(){i.resolve(e)}});return this.el.animate(this.diff,n),i.promise()}),t.when.apply(t,h.get()).done(function(){o(),t.each(arguments,function(){var e=this.el;t.each(this.diff,function(t){e.css(t,"")})}),l.complete.call(a[0])})})},t.fn.extend({addClass:function(e){return function(i,n,s,o){return n?t.effects.animateClass.call(this,{add:i},n,s,o):e.apply(this,arguments)}}(t.fn.addClass),removeClass:function(e){return function(i,n,s,o){return arguments.length>1?t.effects.animateClass.call(this,{remove:i},n,s,o):e.apply(this,arguments)}}(t.fn.removeClass),toggleClass:function(e){return function(i,n,s,o,a){return"boolean"==typeof n||void 0===n?s?t.effects.animateClass.call(this,n?{add:i}:{remove:i},s,o,a):e.apply(this,arguments):t.effects.animateClass.call(this,{toggle:i},n,s,o)}}(t.fn.toggleClass),switchClass:function(e,i,n,s,o){return t.effects.animateClass.call(this,{add:i,remove:e},n,s,o)}})}(),function(){function e(e,i,n,s){return t.isPlainObject(e)&&(i=e,e=e.effect),e={effect:e},null==i&&(i={}),t.isFunction(i)&&(s=i,n=null,i={}),("number"==typeof i||t.fx.speeds[i])&&(s=n,n=i,i={}),t.isFunction(n)&&(s=n,n=null),i&&t.extend(e,i),n=n||i.duration,e.duration=t.fx.off?0:"number"==typeof n?n:n in t.fx.speeds?t.fx.speeds[n]:t.fx.speeds._default,e.complete=s||i.complete,e}function i(e){return!(e&&"number"!=typeof e&&!t.fx.speeds[e])||("string"==typeof e&&!t.effects.effect[e]||(!!t.isFunction(e)||"object"==typeof e&&!e.effect))}function n(t,e){var i=e.outerWidth(),n=e.outerHeight(),s=/^rect\((-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto)\)$/.exec(t)||["",0,i,n,0];return{top:parseFloat(s[1])||0,right:"auto"===s[2]?i:parseFloat(s[2]),bottom:"auto"===s[3]?n:parseFloat(s[3]),left:parseFloat(s[4])||0}}t.expr&&t.expr.filters&&t.expr.filters.animated&&(t.expr.filters.animated=function(e){return function(i){return!!t(i).data(c)||e(i)}}(t.expr.filters.animated)),!1!==t.uiBackCompat&&t.extend(t.effects,{save:function(t,e){for(var i=0,n=e.length;n>i;i++)null!==e[i]&&t.data(l+e[i],t[0].style[e[i]])},restore:function(t,e){for(var i,n=0,s=e.length;s>n;n++)null!==e[n]&&(i=t.data(l+e[n]),t.css(e[n],i))},setMode:function(t,e){return"toggle"===e&&(e=t.is(":hidden")?"show":"hide"),e},createWrapper:function(e){if(e.parent().is(".ui-effects-wrapper"))return e.parent();var i={width:e.outerWidth(!0),height:e.outerHeight(!0),float:e.css("float")},n=t("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),s={width:e.width(),height:e.height()},o=document.activeElement;try{o.id}catch(t){o=document.body}return e.wrap(n),(e[0]===o||t.contains(e[0],o))&&t(o).trigger("focus"),n=e.parent(),"static"===e.css("position")?(n.css({position:"relative"}),e.css({position:"relative"})):(t.extend(i,{position:e.css("position"),zIndex:e.css("z-index")}),t.each(["top","left","bottom","right"],function(t,n){i[n]=e.css(n),isNaN(parseInt(i[n],10))&&(i[n]="auto")}),e.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),e.css(s),n.css(i).show()},removeWrapper:function(e){var i=document.activeElement;return e.parent().is(".ui-effects-wrapper")&&(e.parent().replaceWith(e),(e[0]===i||t.contains(e[0],i))&&t(i).trigger("focus")),e}}),t.extend(t.effects,{version:"1.12.1",define:function(e,i,n){return n||(n=i,i="effect"),t.effects.effect[e]=n,t.effects.effect[e].mode=i,n},scaledDimensions:function(t,e,i){if(0===e)return{height:0,width:0,outerHeight:0,outerWidth:0};var n="horizontal"!==i?(e||100)/100:1,s="vertical"!==i?(e||100)/100:1;return{height:t.height()*s,width:t.width()*n,outerHeight:t.outerHeight()*s,outerWidth:t.outerWidth()*n}},clipToBox:function(t){return{width:t.clip.right-t.clip.left,height:t.clip.bottom-t.clip.top,left:t.clip.left,top:t.clip.top}},unshift:function(t,e,i){var n=t.queue();e>1&&n.splice.apply(n,[1,0].concat(n.splice(e,i))),t.dequeue()},saveStyle:function(t){t.data(h,t[0].style.cssText)},restoreStyle:function(t){t[0].style.cssText=t.data(h)||"",t.removeData(h)},mode:function(t,e){var i=t.is(":hidden");return"toggle"===e&&(e=i?"show":"hide"),(i?"hide"===e:"show"===e)&&(e="none"),e},getBaseline:function(t,e){var i,n;switch(t[0]){case"top":i=0;break;case"middle":i=.5;break;case"bottom":i=1;break;default:i=t[0]/e.height}switch(t[1]){case"left":n=0;break;case"center":n=.5;break;case"right":n=1;break;default:n=t[1]/e.width}return{x:n,y:i}},createPlaceholder:function(e){var i,n=e.css("position"),s=e.position();return e.css({marginTop:e.css("marginTop"),marginBottom:e.css("marginBottom"),marginLeft:e.css("marginLeft"),marginRight:e.css("marginRight")}).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()),/^(static|relative)/.test(n)&&(n="absolute",i=t("<"+e[0].nodeName+">").insertAfter(e).css({display:/^(inline|ruby)/.test(e.css("display"))?"inline-block":"block",visibility:"hidden",marginTop:e.css("marginTop"),marginBottom:e.css("marginBottom"),marginLeft:e.css("marginLeft"),marginRight:e.css("marginRight"),float:e.css("float")}).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()).addClass("ui-effects-placeholder"),e.data(l+"placeholder",i)),e.css({position:n,left:s.left,top:s.top}),i},removePlaceholder:function(t){var e=l+"placeholder",i=t.data(e);i&&(i.remove(),t.removeData(e))},cleanUp:function(e){t.effects.restoreStyle(e),t.effects.removePlaceholder(e)},setTransition:function(e,i,n,s){return s=s||{},t.each(i,function(t,i){var o=e.cssUnit(i);o[0]>0&&(s[i]=o[0]*n+o[1])}),s}}),t.fn.extend({effect:function(){function i(e){function i(){t.isFunction(l)&&l.call(a[0]),t.isFunction(e)&&e()}var a=t(this);n.mode=u.shift(),!1===t.uiBackCompat||o?"none"===n.mode?(a[h](),i()):s.call(a[0],n,function(){a.removeData(c),t.effects.cleanUp(a),"hide"===n.mode&&a.hide(),i()}):(a.is(":hidden")?"hide"===h:"show"===h)?(a[h](),i()):s.call(a[0],n,i)}var n=e.apply(this,arguments),s=t.effects.effect[n.effect],o=s.mode,a=n.queue,r=a||"fx",l=n.complete,h=n.mode,u=[],d=function(e){var i=t(this),n=t.effects.mode(i,h)||o;i.data(c,!0),u.push(n),o&&("show"===n||n===o&&"hide"===n)&&i.show(),o&&"none"===n||t.effects.saveStyle(i),t.isFunction(e)&&e()};return t.fx.off||!s?h?this[h](n.duration,l):this.each(function(){l&&l.call(this)}):!1===a?this.each(d).each(i):this.queue(r,d).queue(r,i)},show:function(t){return function(n){if(i(n))return t.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="show",this.effect.call(this,s)}}(t.fn.show),hide:function(t){return function(n){if(i(n))return t.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="hide",this.effect.call(this,s)}}(t.fn.hide),toggle:function(t){return function(n){if(i(n)||"boolean"==typeof n)return t.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="toggle",this.effect.call(this,s)}}(t.fn.toggle),cssUnit:function(e){var i=this.css(e),n=[];return t.each(["em","px","%","pt"],function(t,e){i.indexOf(e)>0&&(n=[parseFloat(i),e])}),n},cssClip:function(t){return t?this.css("clip","rect("+t.top+"px "+t.right+"px "+t.bottom+"px "+t.left+"px)"):n(this.css("clip"),this)},transfer:function(e,i){var n=t(this),s=t(e.to),o="fixed"===s.css("position"),a=t("body"),r=o?a.scrollTop():0,l=o?a.scrollLeft():0,h=s.offset(),c={top:h.top-r,left:h.left-l,height:s.innerHeight(),width:s.innerWidth()},u=n.offset(),d=t("<div class='ui-effects-transfer'></div>").appendTo("body").addClass(e.className).css({top:u.top-r,left:u.left-l,height:n.innerHeight(),width:n.innerWidth(),position:o?"fixed":"absolute"}).animate(c,e.duration,e.easing,function(){d.remove(),t.isFunction(i)&&i()})}}),t.fx.step.clip=function(e){e.clipInit||(e.start=t(e.elem).cssClip(),"string"==typeof e.end&&(e.end=n(e.end,e.elem)),e.clipInit=!0),t(e.elem).cssClip({top:e.pos*(e.end.top-e.start.top)+e.start.top,right:e.pos*(e.end.right-e.start.right)+e.start.right,bottom:e.pos*(e.end.bottom-e.start.bottom)+e.start.bottom,left:e.pos*(e.end.left-e.start.left)+e.start.left})}}(),function(){var e={};t.each(["Quad","Cubic","Quart","Quint","Expo"],function(t,i){e[i]=function(e){return Math.pow(e,t+2)}}),t.extend(e,{Sine:function(t){return 1-Math.cos(t*Math.PI/2)},Circ:function(t){return 1-Math.sqrt(1-t*t)},Elastic:function(t){return 0===t||1===t?t:-Math.pow(2,8*(t-1))*Math.sin((80*(t-1)-7.5)*Math.PI/15)},Back:function(t){return t*t*(3*t-2)},Bounce:function(t){for(var e,i=4;((e=Math.pow(2,--i))-1)/11>t;);return 1/Math.pow(4,3-i)-7.5625*Math.pow((3*e-2)/22-t,2)}}),t.each(e,function(e,i){t.easing["easeIn"+e]=i,t.easing["easeOut"+e]=function(t){return 1-i(1-t)},t.easing["easeInOut"+e]=function(t){return.5>t?i(2*t)/2:1-i(-2*t+2)/2}})}();t.effects;t.effects.define("blind","hide",function(e,i){var n={up:["bottom","top"],vertical:["bottom","top"],down:["top","bottom"],left:["right","left"],horizontal:["right","left"],right:["left","right"]},s=t(this),o=e.direction||"up",a=s.cssClip(),r={clip:t.extend({},a)},l=t.effects.createPlaceholder(s);r.clip[n[o][0]]=r.clip[n[o][1]],"show"===e.mode&&(s.cssClip(r.clip),l&&l.css(t.effects.clipToBox(r)),r.clip=a),l&&l.animate(t.effects.clipToBox(r),e.duration,e.easing),s.animate(r,{queue:!1,duration:e.duration,easing:e.easing,complete:i})}),t.effects.define("bounce",function(e,i){var n,s,o,a=t(this),r=e.mode,l="hide"===r,h="show"===r,c=e.direction||"up",u=e.distance,d=e.times||5,p=2*d+(h||l?1:0),f=e.duration/p,g=e.easing,m="up"===c||"down"===c?"top":"left",v="up"===c||"left"===c,_=0,b=a.queue().length;for(t.effects.createPlaceholder(a),o=a.css(m),u||(u=a["top"===m?"outerHeight":"outerWidth"]()/3),h&&((s={opacity:1})[m]=o,a.css("opacity",0).css(m,v?2*-u:2*u).animate(s,f,g)),l&&(u/=Math.pow(2,d-1)),(s={})[m]=o;d>_;_++)(n={})[m]=(v?"-=":"+=")+u,a.animate(n,f,g).animate(s,f,g),u=l?2*u:u/2;l&&((n={opacity:0})[m]=(v?"-=":"+=")+u,a.animate(n,f,g)),a.queue(i),t.effects.unshift(a,b,p+1)}),t.effects.define("clip","hide",function(e,i){var n,s={},o=t(this),a=e.direction||"vertical",r="both"===a,l=r||"horizontal"===a,h=r||"vertical"===a;n=o.cssClip(),s.clip={top:h?(n.bottom-n.top)/2:n.top,right:l?(n.right-n.left)/2:n.right,bottom:h?(n.bottom-n.top)/2:n.bottom,left:l?(n.right-n.left)/2:n.left},t.effects.createPlaceholder(o),"show"===e.mode&&(o.cssClip(s.clip),s.clip=n),o.animate(s,{queue:!1,duration:e.duration,easing:e.easing,complete:i})}),t.effects.define("drop","hide",function(e,i){var n,s=t(this),o="show"===e.mode,a=e.direction||"left",r="up"===a||"down"===a?"top":"left",l="up"===a||"left"===a?"-=":"+=",h="+="===l?"-=":"+=",c={opacity:0};t.effects.createPlaceholder(s),n=e.distance||s["top"===r?"outerHeight":"outerWidth"](!0)/2,c[r]=l+n,o&&(s.css(c),c[r]=h+n,c.opacity=1),s.animate(c,{queue:!1,duration:e.duration,easing:e.easing,complete:i})}),t.effects.define("explode","hide",function(e,i){function n(){v.push(this),v.length===c*u&&(d.css({visibility:"visible"}),t(v).remove(),i())}var s,o,a,r,l,h,c=e.pieces?Math.round(Math.sqrt(e.pieces)):3,u=c,d=t(this),p="show"===e.mode,f=d.show().css("visibility","hidden").offset(),g=Math.ceil(d.outerWidth()/u),m=Math.ceil(d.outerHeight()/c),v=[];for(s=0;c>s;s++)for(r=f.top+s*m,h=s-(c-1)/2,o=0;u>o;o++)a=f.left+o*g,l=o-(u-1)/2,d.clone().appendTo("body").wrap("<div></div>").css({position:"absolute",visibility:"visible",left:-o*g,top:-s*m}).parent().addClass("ui-effects-explode").css({position:"absolute",overflow:"hidden",width:g,height:m,left:a+(p?l*g:0),top:r+(p?h*m:0),opacity:p?0:1}).animate({left:a+(p?0:l*g),top:r+(p?0:h*m),opacity:p?1:0},e.duration||500,e.easing,n)}),t.effects.define("fade","toggle",function(e,i){var n="show"===e.mode;t(this).css("opacity",n?0:1).animate({opacity:n?1:0},{queue:!1,duration:e.duration,easing:e.easing,complete:i})}),t.effects.define("fold","hide",function(e,i){var n=t(this),s=e.mode,o="show"===s,a="hide"===s,r=e.size||15,l=/([0-9]+)%/.exec(r),h=!!e.horizFirst?["right","bottom"]:["bottom","right"],c=e.duration/2,u=t.effects.createPlaceholder(n),d=n.cssClip(),p={clip:t.extend({},d)},f={clip:t.extend({},d)},g=[d[h[0]],d[h[1]]],m=n.queue().length;l&&(r=parseInt(l[1],10)/100*g[a?0:1]),p.clip[h[0]]=r,f.clip[h[0]]=r,f.clip[h[1]]=0,o&&(n.cssClip(f.clip),u&&u.css(t.effects.clipToBox(f)),f.clip=d),n.queue(function(i){u&&u.animate(t.effects.clipToBox(p),c,e.easing).animate(t.effects.clipToBox(f),c,e.easing),i()}).animate(p,c,e.easing).animate(f,c,e.easing).queue(i),t.effects.unshift(n,m,4)}),t.effects.define("highlight","show",function(e,i){var n=t(this),s={backgroundColor:n.css("backgroundColor")};"hide"===e.mode&&(s.opacity=0),t.effects.saveStyle(n),n.css({backgroundImage:"none",backgroundColor:e.color||"#ffff99"}).animate(s,{queue:!1,duration:e.duration,easing:e.easing,complete:i})}),t.effects.define("size",function(e,i){var n,s,o,a=t(this),r=["fontSize"],l=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],h=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],c=e.mode,u="effect"!==c,d=e.scale||"both",p=e.origin||["middle","center"],f=a.css("position"),g=a.position(),m=t.effects.scaledDimensions(a),v=e.from||m,_=e.to||t.effects.scaledDimensions(a,0);t.effects.createPlaceholder(a),"show"===c&&(o=v,v=_,_=o),s={from:{y:v.height/m.height,x:v.width/m.width},to:{y:_.height/m.height,x:_.width/m.width}},("box"===d||"both"===d)&&(s.from.y!==s.to.y&&(v=t.effects.setTransition(a,l,s.from.y,v),_=t.effects.setTransition(a,l,s.to.y,_)),s.from.x!==s.to.x&&(v=t.effects.setTransition(a,h,s.from.x,v),_=t.effects.setTransition(a,h,s.to.x,_))),("content"===d||"both"===d)&&s.from.y!==s.to.y&&(v=t.effects.setTransition(a,r,s.from.y,v),_=t.effects.setTransition(a,r,s.to.y,_)),p&&(n=t.effects.getBaseline(p,m),v.top=(m.outerHeight-v.outerHeight)*n.y+g.top,v.left=(m.outerWidth-v.outerWidth)*n.x+g.left,_.top=(m.outerHeight-_.outerHeight)*n.y+g.top,_.left=(m.outerWidth-_.outerWidth)*n.x+g.left),a.css(v),("content"===d||"both"===d)&&(l=l.concat(["marginTop","marginBottom"]).concat(r),h=h.concat(["marginLeft","marginRight"]),a.find("*[width]").each(function(){var i=t(this),n=t.effects.scaledDimensions(i),o={height:n.height*s.from.y,width:n.width*s.from.x,outerHeight:n.outerHeight*s.from.y,outerWidth:n.outerWidth*s.from.x},a={height:n.height*s.to.y,width:n.width*s.to.x,outerHeight:n.height*s.to.y,outerWidth:n.width*s.to.x};s.from.y!==s.to.y&&(o=t.effects.setTransition(i,l,s.from.y,o),a=t.effects.setTransition(i,l,s.to.y,a)),s.from.x!==s.to.x&&(o=t.effects.setTransition(i,h,s.from.x,o),a=t.effects.setTransition(i,h,s.to.x,a)),u&&t.effects.saveStyle(i),i.css(o),i.animate(a,e.duration,e.easing,function(){u&&t.effects.restoreStyle(i)})})),a.animate(_,{queue:!1,duration:e.duration,easing:e.easing,complete:function(){var e=a.offset();0===_.opacity&&a.css("opacity",v.opacity),u||(a.css("position","static"===f?"relative":f).offset(e),t.effects.saveStyle(a)),i()}})}),t.effects.define("scale",function(e,i){var n=t(this),s=e.mode,o=parseInt(e.percent,10)||(0===parseInt(e.percent,10)?0:"effect"!==s?0:100),a=t.extend(!0,{from:t.effects.scaledDimensions(n),to:t.effects.scaledDimensions(n,o,e.direction||"both"),origin:e.origin||["middle","center"]},e);e.fade&&(a.from.opacity=1,a.to.opacity=0),t.effects.effect.size.call(this,a,i)}),t.effects.define("puff","hide",function(e,i){var n=t.extend(!0,{},e,{fade:!0,percent:parseInt(e.percent,10)||150});t.effects.effect.scale.call(this,n,i)}),t.effects.define("pulsate","show",function(e,i){var n=t(this),s=e.mode,o="show"===s,a=o||"hide"===s,r=2*(e.times||5)+(a?1:0),l=e.duration/r,h=0,c=1,u=n.queue().length;for((o||!n.is(":visible"))&&(n.css("opacity",0).show(),h=1);r>c;c++)n.animate({opacity:h},l,e.easing),h=1-h;n.animate({opacity:h},l,e.easing),n.queue(i),t.effects.unshift(n,u,r+1)}),t.effects.define("shake",function(e,i){var n=1,s=t(this),o=e.direction||"left",a=e.distance||20,r=e.times||3,l=2*r+1,h=Math.round(e.duration/l),c="up"===o||"down"===o?"top":"left",u="up"===o||"left"===o,d={},p={},f={},g=s.queue().length;for(t.effects.createPlaceholder(s),d[c]=(u?"-=":"+=")+a,p[c]=(u?"+=":"-=")+2*a,f[c]=(u?"-=":"+=")+2*a,s.animate(d,h,e.easing);r>n;n++)s.animate(p,h,e.easing).animate(f,h,e.easing);s.animate(p,h,e.easing).animate(d,h/2,e.easing).queue(i),t.effects.unshift(s,g,l+1)}),t.effects.define("slide","show",function(e,i){var n,s,o=t(this),a={up:["bottom","top"],down:["top","bottom"],left:["right","left"],right:["left","right"]},r=e.mode,l=e.direction||"left",h="up"===l||"down"===l?"top":"left",c="up"===l||"left"===l,u=e.distance||o["top"===h?"outerHeight":"outerWidth"](!0),d={};t.effects.createPlaceholder(o),n=o.cssClip(),s=o.position()[h],d[h]=(c?-1:1)*u+s,d.clip=o.cssClip(),d.clip[a[l][1]]=d.clip[a[l][0]],"show"===r&&(o.cssClip(d.clip),o.css(h,d[h]),d.clip=n,d[h]=s),o.animate(d,{queue:!1,duration:e.duration,easing:e.easing,complete:i})}),!1!==t.uiBackCompat&&t.effects.define("transfer",function(e,i){t(this).transfer(e,i)}),t.ui.focusable=function(e,i){var n,s,o,a,r,l=e.nodeName.toLowerCase();return"area"===l?(s=(n=e.parentNode).name,!(!e.href||!s||"map"!==n.nodeName.toLowerCase())&&((o=t("img[usemap='#"+s+"']")).length>0&&o.is(":visible"))):(/^(input|select|textarea|button|object)$/.test(l)?(a=!e.disabled)&&((r=t(e).closest("fieldset")[0])&&(a=!r.disabled)):a="a"===l&&e.href||i,a&&t(e).is(":visible")&&function(t){for(var e=t.css("visibility");"inherit"===e;)e=(t=t.parent()).css("visibility");return"hidden"!==e}(t(e)))},t.extend(t.expr[":"],{focusable:function(e){return t.ui.focusable(e,null!=t.attr(e,"tabindex"))}}),t.ui.focusable,t.fn.form=function(){return"string"==typeof this[0].form?this.closest("form"):t(this[0].form)},t.ui.formResetMixin={_formResetHandler:function(){var e=t(this);setTimeout(function(){var i=e.data("ui-form-reset-instances");t.each(i,function(){this.refresh()})})},_bindFormResetHandler:function(){if(this.form=this.element.form(),this.form.length){var t=this.form.data("ui-form-reset-instances")||[];t.length||this.form.on("reset.ui-form-reset",this._formResetHandler),t.push(this),this.form.data("ui-form-reset-instances",t)}},_unbindFormResetHandler:function(){if(this.form.length){var e=this.form.data("ui-form-reset-instances");e.splice(t.inArray(this,e),1),e.length?this.form.data("ui-form-reset-instances",e):this.form.removeData("ui-form-reset-instances").off("reset.ui-form-reset")}}},"1.7"===t.fn.jquery.substring(0,3)&&(t.each(["Width","Height"],function(e,i){function n(e,i,n,o){return t.each(s,function(){i-=parseFloat(t.css(e,"padding"+this))||0,n&&(i-=parseFloat(t.css(e,"border"+this+"Width"))||0),o&&(i-=parseFloat(t.css(e,"margin"+this))||0)}),i}var s="Width"===i?["Left","Right"]:["Top","Bottom"],o=i.toLowerCase(),a={innerWidth:t.fn.innerWidth,innerHeight:t.fn.innerHeight,outerWidth:t.fn.outerWidth,outerHeight:t.fn.outerHeight};t.fn["inner"+i]=function(e){return void 0===e?a["inner"+i].call(this):this.each(function(){t(this).css(o,n(this,e)+"px")})},t.fn["outer"+i]=function(e,s){return"number"!=typeof e?a["outer"+i].call(this,e):this.each(function(){t(this).css(o,n(this,e,!0,s)+"px")})}}),t.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),t.ui.keyCode={BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38},t.ui.escapeSelector=function(){var t=/([!"#$%&'()*+,.\/:;<=>?@[\]^`{|}~])/g;return function(e){return e.replace(t,"\\$1")}}(),t.fn.labels=function(){var e,i,n,s,o;return this[0].labels&&this[0].labels.length?this.pushStack(this[0].labels):(s=this.eq(0).parents("label"),(n=this.attr("id"))&&(o=(e=this.eq(0).parents().last()).add(e.length?e.siblings():this.siblings()),i="label[for='"+t.ui.escapeSelector(n)+"']",s=s.add(o.find(i).addBack(i))),this.pushStack(s))},t.fn.scrollParent=function(e){var i=this.css("position"),n="absolute"===i,s=e?/(auto|scroll|hidden)/:/(auto|scroll)/,o=this.parents().filter(function(){var e=t(this);return(!n||"static"!==e.css("position"))&&s.test(e.css("overflow")+e.css("overflow-y")+e.css("overflow-x"))}).eq(0);return"fixed"!==i&&o.length?o:t(this[0].ownerDocument||document)},t.extend(t.expr[":"],{tabbable:function(e){var i=t.attr(e,"tabindex"),n=null!=i;return(!n||i>=0)&&t.ui.focusable(e,n)}}),t.fn.extend({uniqueId:function(){var t=0;return function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++t)})}}(),removeUniqueId:function(){return this.each(function(){/^ui-id-\d+$/.test(this.id)&&t(this).removeAttr("id")})}}),t.widget("ui.accordion",{version:"1.12.1",options:{active:0,animate:{},classes:{"ui-accordion-header":"ui-corner-top","ui-accordion-header-collapsed":"ui-corner-all","ui-accordion-content":"ui-corner-bottom"},collapsible:!1,event:"click",header:"> li > :first-child, > :not(li):even",heightStyle:"auto",icons:{activeHeader:"ui-icon-triangle-1-s",header:"ui-icon-triangle-1-e"},activate:null,beforeActivate:null},hideProps:{borderTopWidth:"hide",borderBottomWidth:"hide",paddingTop:"hide",paddingBottom:"hide",height:"hide"},showProps:{borderTopWidth:"show",borderBottomWidth:"show",paddingTop:"show",paddingBottom:"show",height:"show"},_create:function(){var e=this.options;this.prevShow=this.prevHide=t(),this._addClass("ui-accordion","ui-widget ui-helper-reset"),this.element.attr("role","tablist"),e.collapsible||!1!==e.active&&null!=e.active||(e.active=0),this._processPanels(),0>e.active&&(e.active+=this.headers.length),this._refresh()},_getCreateEventData:function(){return{header:this.active,panel:this.active.length?this.active.next():t()}},_createIcons:function(){var e,i,n=this.options.icons;n&&(e=t("<span>"),this._addClass(e,"ui-accordion-header-icon","ui-icon "+n.header),e.prependTo(this.headers),i=this.active.children(".ui-accordion-header-icon"),this._removeClass(i,n.header)._addClass(i,null,n.activeHeader)._addClass(this.headers,"ui-accordion-icons"))},_destroyIcons:function(){this._removeClass(this.headers,"ui-accordion-icons"),this.headers.children(".ui-accordion-header-icon").remove()},_destroy:function(){var t;this.element.removeAttr("role"),this.headers.removeAttr("role aria-expanded aria-selected aria-controls tabIndex").removeUniqueId(),this._destroyIcons(),t=this.headers.next().css("display","").removeAttr("role aria-hidden aria-labelledby").removeUniqueId(),"content"!==this.options.heightStyle&&t.css("height","")},_setOption:function(t,e){return"active"===t?void this._activate(e):("event"===t&&(this.options.event&&this._off(this.headers,this.options.event),this._setupEvents(e)),this._super(t,e),"collapsible"!==t||e||!1!==this.options.active||this._activate(0),void("icons"===t&&(this._destroyIcons(),e&&this._createIcons())))},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",t),this._toggleClass(null,"ui-state-disabled",!!t),this._toggleClass(this.headers.add(this.headers.next()),null,"ui-state-disabled",!!t)},_keydown:function(e){if(!e.altKey&&!e.ctrlKey){var i=t.ui.keyCode,n=this.headers.length,s=this.headers.index(e.target),o=!1;switch(e.keyCode){case i.RIGHT:case i.DOWN:o=this.headers[(s+1)%n];break;case i.LEFT:case i.UP:o=this.headers[(s-1+n)%n];break;case i.SPACE:case i.ENTER:this._eventHandler(e);break;case i.HOME:o=this.headers[0];break;case i.END:o=this.headers[n-1]}o&&(t(e.target).attr("tabIndex",-1),t(o).attr("tabIndex",0),t(o).trigger("focus"),e.preventDefault())}},_panelKeyDown:function(e){e.keyCode===t.ui.keyCode.UP&&e.ctrlKey&&t(e.currentTarget).prev().trigger("focus")},refresh:function(){var e=this.options;this._processPanels(),!1===e.active&&!0===e.collapsible||!this.headers.length?(e.active=!1,this.active=t()):!1===e.active?this._activate(0):this.active.length&&!t.contains(this.element[0],this.active[0])?this.headers.length===this.headers.find(".ui-state-disabled").length?(e.active=!1,this.active=t()):this._activate(Math.max(0,e.active-1)):e.active=this.headers.index(this.active),this._destroyIcons(),this._refresh()},_processPanels:function(){var t=this.headers,e=this.panels;this.headers=this.element.find(this.options.header),this._addClass(this.headers,"ui-accordion-header ui-accordion-header-collapsed","ui-state-default"),this.panels=this.headers.next().filter(":not(.ui-accordion-content-active)").hide(),this._addClass(this.panels,"ui-accordion-content","ui-helper-reset ui-widget-content"),e&&(this._off(t.not(this.headers)),this._off(e.not(this.panels)))},_refresh:function(){var e,i=this.options,n=i.heightStyle,s=this.element.parent();this.active=this._findActive(i.active),this._addClass(this.active,"ui-accordion-header-active","ui-state-active")._removeClass(this.active,"ui-accordion-header-collapsed"),this._addClass(this.active.next(),"ui-accordion-content-active"),this.active.next().show(),this.headers.attr("role","tab").each(function(){var e=t(this),i=e.uniqueId().attr("id"),n=e.next(),s=n.uniqueId().attr("id");e.attr("aria-controls",s),n.attr("aria-labelledby",i)}).next().attr("role","tabpanel"),this.headers.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}).next().attr({"aria-hidden":"true"}).hide(),this.active.length?this.active.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}).next().attr({"aria-hidden":"false"}):this.headers.eq(0).attr("tabIndex",0),this._createIcons(),this._setupEvents(i.event),"fill"===n?(e=s.height(),this.element.siblings(":visible").each(function(){var i=t(this),n=i.css("position");"absolute"!==n&&"fixed"!==n&&(e-=i.outerHeight(!0))}),this.headers.each(function(){e-=t(this).outerHeight(!0)}),this.headers.next().each(function(){t(this).height(Math.max(0,e-t(this).innerHeight()+t(this).height()))}).css("overflow","auto")):"auto"===n&&(e=0,this.headers.next().each(function(){var i=t(this).is(":visible");i||t(this).show(),e=Math.max(e,t(this).css("height","").height()),i||t(this).hide()}).height(e))},_activate:function(e){var i=this._findActive(e)[0];i!==this.active[0]&&(i=i||this.active[0],this._eventHandler({target:i,currentTarget:i,preventDefault:t.noop}))},_findActive:function(e){return"number"==typeof e?this.headers.eq(e):t()},_setupEvents:function(e){var i={keydown:"_keydown"};e&&t.each(e.split(" "),function(t,e){i[e]="_eventHandler"}),this._off(this.headers.add(this.headers.next())),this._on(this.headers,i),this._on(this.headers.next(),{keydown:"_panelKeyDown"}),this._hoverable(this.headers),this._focusable(this.headers)},_eventHandler:function(e){var i,n,s=this.options,o=this.active,a=t(e.currentTarget),r=a[0]===o[0],l=r&&s.collapsible,h=l?t():a.next(),c=o.next(),u={oldHeader:o,oldPanel:c,newHeader:l?t():a,newPanel:h};e.preventDefault(),r&&!s.collapsible||!1===this._trigger("beforeActivate",e,u)||(s.active=!l&&this.headers.index(a),this.active=r?t():a,this._toggle(u),this._removeClass(o,"ui-accordion-header-active","ui-state-active"),s.icons&&(i=o.children(".ui-accordion-header-icon"),this._removeClass(i,null,s.icons.activeHeader)._addClass(i,null,s.icons.header)),r||(this._removeClass(a,"ui-accordion-header-collapsed")._addClass(a,"ui-accordion-header-active","ui-state-active"),s.icons&&(n=a.children(".ui-accordion-header-icon"),this._removeClass(n,null,s.icons.header)._addClass(n,null,s.icons.activeHeader)),this._addClass(a.next(),"ui-accordion-content-active")))},_toggle:function(e){var i=e.newPanel,n=this.prevShow.length?this.prevShow:e.oldPanel;this.prevShow.add(this.prevHide).stop(!0,!0),this.prevShow=i,this.prevHide=n,this.options.animate?this._animate(i,n,e):(n.hide(),i.show(),this._toggleComplete(e)),n.attr({"aria-hidden":"true"}),n.prev().attr({"aria-selected":"false","aria-expanded":"false"}),i.length&&n.length?n.prev().attr({tabIndex:-1,"aria-expanded":"false"}):i.length&&this.headers.filter(function(){return 0===parseInt(t(this).attr("tabIndex"),10)}).attr("tabIndex",-1),i.attr("aria-hidden","false").prev().attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_animate:function(t,e,i){var n,s,o,a=this,r=0,l=t.css("box-sizing"),h=t.length&&(!e.length||t.index()<e.index()),c=this.options.animate||{},u=h&&c.down||c,d=function(){a._toggleComplete(i)};return"number"==typeof u&&(o=u),"string"==typeof u&&(s=u),s=s||u.easing||c.easing,o=o||u.duration||c.duration,e.length?t.length?(n=t.show().outerHeight(),e.animate(this.hideProps,{duration:o,easing:s,step:function(t,e){e.now=Math.round(t)}}),void t.hide().animate(this.showProps,{duration:o,easing:s,complete:d,step:function(t,i){i.now=Math.round(t),"height"!==i.prop?"content-box"===l&&(r+=i.now):"content"!==a.options.heightStyle&&(i.now=Math.round(n-e.outerHeight()-r),r=0)}})):e.animate(this.hideProps,o,s,d):t.animate(this.showProps,o,s,d)},_toggleComplete:function(t){var e=t.oldPanel,i=e.prev();this._removeClass(e,"ui-accordion-content-active"),this._removeClass(i,"ui-accordion-header-active")._addClass(i,"ui-accordion-header-collapsed"),e.length&&(e.parent()[0].className=e.parent()[0].className),this._trigger("activate",null,t)}}),t.ui.safeActiveElement=function(t){var e;try{e=t.activeElement}catch(i){e=t.body}return e||(e=t.body),e.nodeName||(e=t.body),e},t.widget("ui.menu",{version:"1.12.1",defaultElement:"<ul>",delay:300,options:{icons:{submenu:"ui-icon-caret-1-e"},items:"> *",menus:"ul",position:{my:"left top",at:"right top"},role:"menu",blur:null,focus:null,select:null},_create:function(){this.activeMenu=this.element,this.mouseHandled=!1,this.element.uniqueId().attr({role:this.options.role,tabIndex:0}),this._addClass("ui-menu","ui-widget ui-widget-content"),this._on({"mousedown .ui-menu-item":function(t){t.preventDefault()},"click .ui-menu-item":function(e){var i=t(e.target),n=t(t.ui.safeActiveElement(this.document[0]));!this.mouseHandled&&i.not(".ui-state-disabled").length&&(this.select(e),e.isPropagationStopped()||(this.mouseHandled=!0),i.has(".ui-menu").length?this.expand(e):!this.element.is(":focus")&&n.closest(".ui-menu").length&&(this.element.trigger("focus",[!0]),this.active&&1===this.active.parents(".ui-menu").length&&clearTimeout(this.timer)))},"mouseenter .ui-menu-item":function(e){if(!this.previousFilter){var i=t(e.target).closest(".ui-menu-item"),n=t(e.currentTarget);i[0]===n[0]&&(this._removeClass(n.siblings().children(".ui-state-active"),null,"ui-state-active"),this.focus(e,n))}},mouseleave:"collapseAll","mouseleave .ui-menu":"collapseAll",focus:function(t,e){var i=this.active||this.element.find(this.options.items).eq(0);e||this.focus(t,i)},blur:function(e){this._delay(function(){!t.contains(this.element[0],t.ui.safeActiveElement(this.document[0]))&&this.collapseAll(e)})},keydown:"_keydown"}),this.refresh(),this._on(this.document,{click:function(t){this._closeOnDocumentClick(t)&&this.collapseAll(t),this.mouseHandled=!1}})},_destroy:function(){var e=this.element.find(".ui-menu-item").removeAttr("role aria-disabled").children(".ui-menu-item-wrapper").removeUniqueId().removeAttr("tabIndex role aria-haspopup");this.element.removeAttr("aria-activedescendant").find(".ui-menu").addBack().removeAttr("role aria-labelledby aria-expanded aria-hidden aria-disabled tabIndex").removeUniqueId().show(),e.children().each(function(){var e=t(this);e.data("ui-menu-submenu-caret")&&e.remove()})},_keydown:function(e){var i,n,s,o,a=!0;switch(e.keyCode){case t.ui.keyCode.PAGE_UP:this.previousPage(e);break;case t.ui.keyCode.PAGE_DOWN:this.nextPage(e);break;case t.ui.keyCode.HOME:this._move("first","first",e);break;case t.ui.keyCode.END:this._move("last","last",e);break;case t.ui.keyCode.UP:this.previous(e);break;case t.ui.keyCode.DOWN:this.next(e);break;case t.ui.keyCode.LEFT:this.collapse(e);break;case t.ui.keyCode.RIGHT:this.active&&!this.active.is(".ui-state-disabled")&&this.expand(e);break;case t.ui.keyCode.ENTER:case t.ui.keyCode.SPACE:this._activate(e);break;case t.ui.keyCode.ESCAPE:this.collapse(e);break;default:a=!1,n=this.previousFilter||"",o=!1,s=e.keyCode>=96&&105>=e.keyCode?""+(e.keyCode-96):String.fromCharCode(e.keyCode),clearTimeout(this.filterTimer),s===n?o=!0:s=n+s,i=this._filterMenuItems(s),(i=o&&-1!==i.index(this.active.next())?this.active.nextAll(".ui-menu-item"):i).length||(s=String.fromCharCode(e.keyCode),i=this._filterMenuItems(s)),i.length?(this.focus(e,i),this.previousFilter=s,this.filterTimer=this._delay(function(){delete this.previousFilter},1e3)):delete this.previousFilter}a&&e.preventDefault()},_activate:function(t){this.active&&!this.active.is(".ui-state-disabled")&&(this.active.children("[aria-haspopup='true']").length?this.expand(t):this.select(t))},refresh:function(){var e,i,n,s,o=this,a=this.options.icons.submenu,r=this.element.find(this.options.menus);this._toggleClass("ui-menu-icons",null,!!this.element.find(".ui-icon").length),i=r.filter(":not(.ui-menu)").hide().attr({role:this.options.role,"aria-hidden":"true","aria-expanded":"false"}).each(function(){var e=t(this),i=e.prev(),n=t("<span>").data("ui-menu-submenu-caret",!0);o._addClass(n,"ui-menu-icon","ui-icon "+a),i.attr("aria-haspopup","true").prepend(n),e.attr("aria-labelledby",i.attr("id"))}),this._addClass(i,"ui-menu","ui-widget ui-widget-content ui-front"),(e=r.add(this.element).find(this.options.items)).not(".ui-menu-item").each(function(){var e=t(this);o._isDivider(e)&&o._addClass(e,"ui-menu-divider","ui-widget-content")}),s=(n=e.not(".ui-menu-item, .ui-menu-divider")).children().not(".ui-menu").uniqueId().attr({tabIndex:-1,role:this._itemRole()}),this._addClass(n,"ui-menu-item")._addClass(s,"ui-menu-item-wrapper"),e.filter(".ui-state-disabled").attr("aria-disabled","true"),this.active&&!t.contains(this.element[0],this.active[0])&&this.blur()},_itemRole:function(){return{menu:"menuitem",listbox:"option"}[this.options.role]},_setOption:function(t,e){if("icons"===t){var i=this.element.find(".ui-menu-icon");this._removeClass(i,null,this.options.icons.submenu)._addClass(i,null,e.submenu)}this._super(t,e)},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",t+""),this._toggleClass(null,"ui-state-disabled",!!t)},focus:function(t,e){var i,n,s;this.blur(t,t&&"focus"===t.type),this._scrollIntoView(e),this.active=e.first(),n=this.active.children(".ui-menu-item-wrapper"),this._addClass(n,null,"ui-state-active"),this.options.role&&this.element.attr("aria-activedescendant",n.attr("id")),s=this.active.parent().closest(".ui-menu-item").children(".ui-menu-item-wrapper"),this._addClass(s,null,"ui-state-active"),t&&"keydown"===t.type?this._close():this.timer=this._delay(function(){this._close()},this.delay),(i=e.children(".ui-menu")).length&&t&&/^mouse/.test(t.type)&&this._startOpening(i),this.activeMenu=e.parent(),this._trigger("focus",t,{item:e})},_scrollIntoView:function(e){var i,n,s,o,a,r;this._hasScroll()&&(i=parseFloat(t.css(this.activeMenu[0],"borderTopWidth"))||0,n=parseFloat(t.css(this.activeMenu[0],"paddingTop"))||0,s=e.offset().top-this.activeMenu.offset().top-i-n,o=this.activeMenu.scrollTop(),a=this.activeMenu.height(),r=e.outerHeight(),0>s?this.activeMenu.scrollTop(o+s):s+r>a&&this.activeMenu.scrollTop(o+s-a+r))},blur:function(t,e){e||clearTimeout(this.timer),this.active&&(this._removeClass(this.active.children(".ui-menu-item-wrapper"),null,"ui-state-active"),this._trigger("blur",t,{item:this.active}),this.active=null)},_startOpening:function(t){clearTimeout(this.timer),"true"===t.attr("aria-hidden")&&(this.timer=this._delay(function(){this._close(),this._open(t)},this.delay))},_open:function(e){var i=t.extend({of:this.active},this.options.position);clearTimeout(this.timer),this.element.find(".ui-menu").not(e.parents(".ui-menu")).hide().attr("aria-hidden","true"),e.show().removeAttr("aria-hidden").attr("aria-expanded","true").position(i)},collapseAll:function(e,i){clearTimeout(this.timer),this.timer=this._delay(function(){var n=i?this.element:t(e&&e.target).closest(this.element.find(".ui-menu"));n.length||(n=this.element),this._close(n),this.blur(e),this._removeClass(n.find(".ui-state-active"),null,"ui-state-active"),this.activeMenu=n},this.delay)},_close:function(t){t||(t=this.active?this.active.parent():this.element),t.find(".ui-menu").hide().attr("aria-hidden","true").attr("aria-expanded","false")},_closeOnDocumentClick:function(e){return!t(e.target).closest(".ui-menu").length},_isDivider:function(t){return!/[^\-\u2014\u2013\s]/.test(t.text())},collapse:function(t){var e=this.active&&this.active.parent().closest(".ui-menu-item",this.element);e&&e.length&&(this._close(),this.focus(t,e))},expand:function(t){var e=this.active&&this.active.children(".ui-menu ").find(this.options.items).first();e&&e.length&&(this._open(e.parent()),this._delay(function(){this.focus(t,e)}))},next:function(t){this._move("next","first",t)},previous:function(t){this._move("prev","last",t)},isFirstItem:function(){return this.active&&!this.active.prevAll(".ui-menu-item").length},isLastItem:function(){return this.active&&!this.active.nextAll(".ui-menu-item").length},_move:function(t,e,i){var n;this.active&&(n="first"===t||"last"===t?this.active["first"===t?"prevAll":"nextAll"](".ui-menu-item").eq(-1):this.active[t+"All"](".ui-menu-item").eq(0)),n&&n.length&&this.active||(n=this.activeMenu.find(this.options.items)[e]()),this.focus(i,n)},nextPage:function(e){var i,n,s;return this.active?void(this.isLastItem()||(this._hasScroll()?(n=this.active.offset().top,s=this.element.height(),this.active.nextAll(".ui-menu-item").each(function(){return 0>(i=t(this)).offset().top-n-s}),this.focus(e,i)):this.focus(e,this.activeMenu.find(this.options.items)[this.active?"last":"first"]()))):void this.next(e)},previousPage:function(e){var i,n,s;return this.active?void(this.isFirstItem()||(this._hasScroll()?(n=this.active.offset().top,s=this.element.height(),this.active.prevAll(".ui-menu-item").each(function(){return(i=t(this)).offset().top-n+s>0}),this.focus(e,i)):this.focus(e,this.activeMenu.find(this.options.items).first()))):void this.next(e)},_hasScroll:function(){return this.element.outerHeight()<this.element.prop("scrollHeight")},select:function(e){this.active=this.active||t(e.target).closest(".ui-menu-item");var i={item:this.active};this.active.has(".ui-menu").length||this.collapseAll(e,!0),this._trigger("select",e,i)},_filterMenuItems:function(e){var i=e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"),n=RegExp("^"+i,"i");return this.activeMenu.find(this.options.items).filter(".ui-menu-item").filter(function(){return n.test(t.trim(t(this).children(".ui-menu-item-wrapper").text()))})}}),t.widget("ui.autocomplete",{version:"1.12.1",defaultElement:"<input>",options:{appendTo:null,autoFocus:!1,delay:300,minLength:1,position:{my:"left top",at:"left bottom",collision:"none"},source:null,change:null,close:null,focus:null,open:null,response:null,search:null,select:null},requestIndex:0,pending:0,_create:function(){var e,i,n,s=this.element[0].nodeName.toLowerCase(),o="textarea"===s,a="input"===s;this.isMultiLine=o||!a&&this._isContentEditable(this.element),this.valueMethod=this.element[o||a?"val":"text"],this.isNewMenu=!0,this._addClass("ui-autocomplete-input"),this.element.attr("autocomplete","off"),this._on(this.element,{keydown:function(s){if(this.element.prop("readOnly"))return e=!0,n=!0,void(i=!0);e=!1,n=!1,i=!1;var o=t.ui.keyCode;switch(s.keyCode){case o.PAGE_UP:e=!0,this._move("previousPage",s);break;case o.PAGE_DOWN:e=!0,this._move("nextPage",s);break;case o.UP:e=!0,this._keyEvent("previous",s);break;case o.DOWN:e=!0,this._keyEvent("next",s);break;case o.ENTER:this.menu.active&&(e=!0,s.preventDefault(),this.menu.select(s));break;case o.TAB:this.menu.active&&this.menu.select(s);break;case o.ESCAPE:this.menu.element.is(":visible")&&(this.isMultiLine||this._value(this.term),this.close(s),s.preventDefault());break;default:i=!0,this._searchTimeout(s)}},keypress:function(n){if(e)return e=!1,void((!this.isMultiLine||this.menu.element.is(":visible"))&&n.preventDefault());if(!i){var s=t.ui.keyCode;switch(n.keyCode){case s.PAGE_UP:this._move("previousPage",n);break;case s.PAGE_DOWN:this._move("nextPage",n);break;case s.UP:this._keyEvent("previous",n);break;case s.DOWN:this._keyEvent("next",n)}}},input:function(t){return n?(n=!1,void t.preventDefault()):void this._searchTimeout(t)},focus:function(){this.selectedItem=null,this.previous=this._value()},blur:function(t){return this.cancelBlur?void delete this.cancelBlur:(clearTimeout(this.searching),this.close(t),void this._change(t))}}),this._initSource(),this.menu=t("<ul>").appendTo(this._appendTo()).menu({role:null}).hide().menu("instance"),this._addClass(this.menu.element,"ui-autocomplete","ui-front"),this._on(this.menu.element,{mousedown:function(e){e.preventDefault(),this.cancelBlur=!0,this._delay(function(){delete this.cancelBlur,this.element[0]!==t.ui.safeActiveElement(this.document[0])&&this.element.trigger("focus")})},menufocus:function(e,i){var n,s;return this.isNewMenu&&(this.isNewMenu=!1,e.originalEvent&&/^mouse/.test(e.originalEvent.type))?(this.menu.blur(),void this.document.one("mousemove",function(){t(e.target).trigger(e.originalEvent)})):(s=i.item.data("ui-autocomplete-item"),!1!==this._trigger("focus",e,{item:s})&&e.originalEvent&&/^key/.test(e.originalEvent.type)&&this._value(s.value),void((n=i.item.attr("aria-label")||s.value)&&t.trim(n).length&&(this.liveRegion.children().hide(),t("<div>").text(n).appendTo(this.liveRegion))))},menuselect:function(e,i){var n=i.item.data("ui-autocomplete-item"),s=this.previous;this.element[0]!==t.ui.safeActiveElement(this.document[0])&&(this.element.trigger("focus"),this.previous=s,this._delay(function(){this.previous=s,this.selectedItem=n})),!1!==this._trigger("select",e,{item:n})&&this._value(n.value),this.term=this._value(),this.close(e),this.selectedItem=n}}),this.liveRegion=t("<div>",{role:"status","aria-live":"assertive","aria-relevant":"additions"}).appendTo(this.document[0].body),this._addClass(this.liveRegion,null,"ui-helper-hidden-accessible"),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_destroy:function(){clearTimeout(this.searching),this.element.removeAttr("autocomplete"),this.menu.element.remove(),this.liveRegion.remove()},_setOption:function(t,e){this._super(t,e),"source"===t&&this._initSource(),"appendTo"===t&&this.menu.element.appendTo(this._appendTo()),"disabled"===t&&e&&this.xhr&&this.xhr.abort()},_isEventTargetInWidget:function(e){var i=this.menu.element[0];return e.target===this.element[0]||e.target===i||t.contains(i,e.target)},_closeOnClickOutside:function(t){this._isEventTargetInWidget(t)||this.close()},_appendTo:function(){var e=this.options.appendTo;return e&&(e=e.jquery||e.nodeType?t(e):this.document.find(e).eq(0)),e&&e[0]||(e=this.element.closest(".ui-front, dialog")),e.length||(e=this.document[0].body),e},_initSource:function(){var e,i,n=this;t.isArray(this.options.source)?(e=this.options.source,this.source=function(i,n){n(t.ui.autocomplete.filter(e,i.term))}):"string"==typeof this.options.source?(i=this.options.source,this.source=function(e,s){n.xhr&&n.xhr.abort(),n.xhr=t.ajax({url:i,data:e,dataType:"json",success:function(t){s(t)},error:function(){s([])}})}):this.source=this.options.source},_searchTimeout:function(t){clearTimeout(this.searching),this.searching=this._delay(function(){var e=this.term===this._value(),i=this.menu.element.is(":visible"),n=t.altKey||t.ctrlKey||t.metaKey||t.shiftKey;(!e||e&&!i&&!n)&&(this.selectedItem=null,this.search(null,t))},this.options.delay)},search:function(t,e){return t=null!=t?t:this._value(),this.term=this._value(),t.length<this.options.minLength?this.close(e):!1!==this._trigger("search",e)?this._search(t):void 0},_search:function(t){this.pending++,this._addClass("ui-autocomplete-loading"),this.cancelSearch=!1,this.source({term:t},this._response())},_response:function(){var e=++this.requestIndex;return t.proxy(function(t){e===this.requestIndex&&this.__response(t),this.pending--,this.pending||this._removeClass("ui-autocomplete-loading")},this)},__response:function(t){t&&(t=this._normalize(t)),this._trigger("response",null,{content:t}),!this.options.disabled&&t&&t.length&&!this.cancelSearch?(this._suggest(t),this._trigger("open")):this._close()},close:function(t){this.cancelSearch=!0,this._close(t)},_close:function(t){this._off(this.document,"mousedown"),this.menu.element.is(":visible")&&(this.menu.element.hide(),this.menu.blur(),this.isNewMenu=!0,this._trigger("close",t))},_change:function(t){this.previous!==this._value()&&this._trigger("change",t,{item:this.selectedItem})},_normalize:function(e){return e.length&&e[0].label&&e[0].value?e:t.map(e,function(e){return"string"==typeof e?{label:e,value:e}:t.extend({},e,{label:e.label||e.value,value:e.value||e.label})})},_suggest:function(e){var i=this.menu.element.empty();this._renderMenu(i,e),this.isNewMenu=!0,this.menu.refresh(),i.show(),this._resizeMenu(),i.position(t.extend({of:this.element},this.options.position)),this.options.autoFocus&&this.menu.next(),this._on(this.document,{mousedown:"_closeOnClickOutside"})},_resizeMenu:function(){var t=this.menu.element;t.outerWidth(Math.max(t.width("").outerWidth()+1,this.element.outerWidth()))},_renderMenu:function(e,i){var n=this;t.each(i,function(t,i){n._renderItemData(e,i)})},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-autocomplete-item",e)},_renderItem:function(e,i){return t("<li>").append(t("<div>").text(i.label)).appendTo(e)},_move:function(t,e){return this.menu.element.is(":visible")?this.menu.isFirstItem()&&/^previous/.test(t)||this.menu.isLastItem()&&/^next/.test(t)?(this.isMultiLine||this._value(this.term),void this.menu.blur()):void this.menu[t](e):void this.search(null,e)},widget:function(){return this.menu.element},_value:function(){return this.valueMethod.apply(this.element,arguments)},_keyEvent:function(t,e){(!this.isMultiLine||this.menu.element.is(":visible"))&&(this._move(t,e),e.preventDefault())},_isContentEditable:function(t){if(!t.length)return!1;var e=t.prop("contentEditable");return"inherit"===e?this._isContentEditable(t.parent()):"true"===e}}),t.extend(t.ui.autocomplete,{escapeRegex:function(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")},filter:function(e,i){var n=RegExp(t.ui.autocomplete.escapeRegex(i),"i");return t.grep(e,function(t){return n.test(t.label||t.value||t)})}}),t.widget("ui.autocomplete",t.ui.autocomplete,{options:{messages:{noResults:"No search results.",results:function(t){return t+(t>1?" results are":" result is")+" available, use up and down arrow keys to navigate."}}},__response:function(e){var i;this._superApply(arguments),this.options.disabled||this.cancelSearch||(i=e&&e.length?this.options.messages.results(e.length):this.options.messages.noResults,this.liveRegion.children().hide(),t("<div>").text(i).appendTo(this.liveRegion))}}),t.ui.autocomplete;var d,p=/ui-corner-([a-z]){2,6}/g;t.widget("ui.controlgroup",{version:"1.12.1",defaultElement:"<div>",options:{direction:"horizontal",disabled:null,onlyVisible:!0,items:{button:"input[type=button], input[type=submit], input[type=reset], button, a",controlgroupLabel:".ui-controlgroup-label",checkboxradio:"input[type='checkbox'], input[type='radio']",selectmenu:"select",spinner:".ui-spinner-input"}},_create:function(){this._enhance()},_enhance:function(){this.element.attr("role","toolbar"),this.refresh()},_destroy:function(){this._callChildMethod("destroy"),this.childWidgets.removeData("ui-controlgroup-data"),this.element.removeAttr("role"),this.options.items.controlgroupLabel&&this.element.find(this.options.items.controlgroupLabel).find(".ui-controlgroup-label-contents").contents().unwrap()},_initWidgets:function(){var e=this,i=[];t.each(this.options.items,function(n,s){var o,a={};return s?"controlgroupLabel"===n?((o=e.element.find(s)).each(function(){var e=t(this);e.children(".ui-controlgroup-label-contents").length||e.contents().wrapAll("<span class='ui-controlgroup-label-contents'></span>")}),e._addClass(o,null,"ui-widget ui-widget-content ui-state-default"),void(i=i.concat(o.get()))):void(t.fn[n]&&(a=e["_"+n+"Options"]?e["_"+n+"Options"]("middle"):{classes:{}},e.element.find(s).each(function(){var s=t(this),o=s[n]("instance"),r=t.widget.extend({},a);if("button"!==n||!s.parent(".ui-spinner").length){o||(o=s[n]()[n]("instance")),o&&(r.classes=e._resolveClassesValues(r.classes,o)),s[n](r);var l=s[n]("widget");t.data(l[0],"ui-controlgroup-data",o||s[n]("instance")),i.push(l[0])}}))):void 0}),this.childWidgets=t(t.unique(i)),this._addClass(this.childWidgets,"ui-controlgroup-item")},_callChildMethod:function(e){this.childWidgets.each(function(){var i=t(this).data("ui-controlgroup-data");i&&i[e]&&i[e]()})},_updateCornerClass:function(t,e){var i=this._buildSimpleOptions(e,"label").classes.label;this._removeClass(t,null,"ui-corner-top ui-corner-bottom ui-corner-left ui-corner-right ui-corner-all"),this._addClass(t,null,i)},_buildSimpleOptions:function(t,e){var i="vertical"===this.options.direction,n={classes:{}};return n.classes[e]={middle:"",first:"ui-corner-"+(i?"top":"left"),last:"ui-corner-"+(i?"bottom":"right"),only:"ui-corner-all"}[t],n},_spinnerOptions:function(t){var e=this._buildSimpleOptions(t,"ui-spinner");return e.classes["ui-spinner-up"]="",e.classes["ui-spinner-down"]="",e},_buttonOptions:function(t){return this._buildSimpleOptions(t,"ui-button")},_checkboxradioOptions:function(t){return this._buildSimpleOptions(t,"ui-checkboxradio-label")},_selectmenuOptions:function(t){var e="vertical"===this.options.direction;return{width:!!e&&"auto",classes:{middle:{"ui-selectmenu-button-open":"","ui-selectmenu-button-closed":""},first:{"ui-selectmenu-button-open":"ui-corner-"+(e?"top":"tl"),"ui-selectmenu-button-closed":"ui-corner-"+(e?"top":"left")},last:{"ui-selectmenu-button-open":e?"":"ui-corner-tr","ui-selectmenu-button-closed":"ui-corner-"+(e?"bottom":"right")},only:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"}}[t]}},_resolveClassesValues:function(e,i){var n={};return t.each(e,function(s){var o=i.options.classes[s]||"";o=t.trim(o.replace(p,"")),n[s]=(o+" "+e[s]).replace(/\s+/g," ")}),n},_setOption:function(t,e){return"direction"===t&&this._removeClass("ui-controlgroup-"+this.options.direction),this._super(t,e),"disabled"===t?void this._callChildMethod(e?"disable":"enable"):void this.refresh()},refresh:function(){var e,i=this;this._addClass("ui-controlgroup ui-controlgroup-"+this.options.direction),"horizontal"===this.options.direction&&this._addClass(null,"ui-helper-clearfix"),this._initWidgets(),e=this.childWidgets,this.options.onlyVisible&&(e=e.filter(":visible")),e.length&&(t.each(["first","last"],function(t,n){var s=e[n]().data("ui-controlgroup-data");if(s&&i["_"+s.widgetName+"Options"]){var o=i["_"+s.widgetName+"Options"](1===e.length?"only":n);o.classes=i._resolveClassesValues(o.classes,s),s.element[s.widgetName](o)}else i._updateCornerClass(e[n](),n)}),this._callChildMethod("refresh"))}}),t.widget("ui.checkboxradio",[t.ui.formResetMixin,{version:"1.12.1",options:{disabled:null,label:null,icon:!0,classes:{"ui-checkboxradio-label":"ui-corner-all","ui-checkboxradio-icon":"ui-corner-all"}},_getCreateOptions:function(){var e,i,n=this,s=this._super()||{};return this._readType(),i=this.element.labels(),this.label=t(i[i.length-1]),this.label.length||t.error("No label found for checkboxradio widget"),this.originalLabel="",this.label.contents().not(this.element[0]).each(function(){n.originalLabel+=3===this.nodeType?t(this).text():this.outerHTML}),this.originalLabel&&(s.label=this.originalLabel),null!=(e=this.element[0].disabled)&&(s.disabled=e),s},_create:function(){var t=this.element[0].checked;this._bindFormResetHandler(),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled),this._setOption("disabled",this.options.disabled),this._addClass("ui-checkboxradio","ui-helper-hidden-accessible"),this._addClass(this.label,"ui-checkboxradio-label","ui-button ui-widget"),"radio"===this.type&&this._addClass(this.label,"ui-checkboxradio-radio-label"),this.options.label&&this.options.label!==this.originalLabel?this._updateLabel():this.originalLabel&&(this.options.label=this.originalLabel),this._enhance(),t&&(this._addClass(this.label,"ui-checkboxradio-checked","ui-state-active"),this.icon&&this._addClass(this.icon,null,"ui-state-hover")),this._on({change:"_toggleClasses",focus:function(){this._addClass(this.label,null,"ui-state-focus ui-visual-focus")},blur:function(){this._removeClass(this.label,null,"ui-state-focus ui-visual-focus")}})},_readType:function(){var e=this.element[0].nodeName.toLowerCase();this.type=this.element[0].type,"input"===e&&/radio|checkbox/.test(this.type)||t.error("Can't create checkboxradio on element.nodeName="+e+" and element.type="+this.type)},_enhance:function(){this._updateIcon(this.element[0].checked)},widget:function(){return this.label},_getRadioGroup:function(){var e=this.element[0].name,i="input[name='"+t.ui.escapeSelector(e)+"']";return e?(this.form.length?t(this.form[0].elements).filter(i):t(i).filter(function(){return 0===t(this).form().length})).not(this.element):t([])},_toggleClasses:function(){var e=this.element[0].checked;this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",e),this.options.icon&&"checkbox"===this.type&&this._toggleClass(this.icon,null,"ui-icon-check ui-state-checked",e)._toggleClass(this.icon,null,"ui-icon-blank",!e),"radio"===this.type&&this._getRadioGroup().each(function(){var e=t(this).checkboxradio("instance");e&&e._removeClass(e.label,"ui-checkboxradio-checked","ui-state-active")})},_destroy:function(){this._unbindFormResetHandler(),this.icon&&(this.icon.remove(),this.iconSpace.remove())},_setOption:function(t,e){return"label"!==t||e?(this._super(t,e),"disabled"===t?(this._toggleClass(this.label,null,"ui-state-disabled",e),void(this.element[0].disabled=e)):void this.refresh()):void 0},_updateIcon:function(e){var i="ui-icon ui-icon-background ";this.options.icon?(this.icon||(this.icon=t("<span>"),this.iconSpace=t("<span> </span>"),this._addClass(this.iconSpace,"ui-checkboxradio-icon-space")),"checkbox"===this.type?(i+=e?"ui-icon-check ui-state-checked":"ui-icon-blank",this._removeClass(this.icon,null,e?"ui-icon-blank":"ui-icon-check")):i+="ui-icon-blank",this._addClass(this.icon,"ui-checkboxradio-icon",i),e||this._removeClass(this.icon,null,"ui-icon-check ui-state-checked"),this.icon.prependTo(this.label).after(this.iconSpace)):void 0!==this.icon&&(this.icon.remove(),this.iconSpace.remove(),delete this.icon)},_updateLabel:function(){var t=this.label.contents().not(this.element[0]);this.icon&&(t=t.not(this.icon[0])),this.iconSpace&&(t=t.not(this.iconSpace[0])),t.remove(),this.label.append(this.options.label)},refresh:function(){var t=this.element[0].checked,e=this.element[0].disabled;this._updateIcon(t),this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",t),null!==this.options.label&&this._updateLabel(),e!==this.options.disabled&&this._setOptions({disabled:e})}}]),t.ui.checkboxradio,t.widget("ui.button",{version:"1.12.1",defaultElement:"<button>",options:{classes:{"ui-button":"ui-corner-all"},disabled:null,icon:null,iconPosition:"beginning",label:null,showLabel:!0},_getCreateOptions:function(){var t,e=this._super()||{};return this.isInput=this.element.is("input"),null!=(t=this.element[0].disabled)&&(e.disabled=t),this.originalLabel=this.isInput?this.element.val():this.element.html(),this.originalLabel&&(e.label=this.originalLabel),e},_create:function(){!this.option.showLabel&!this.options.icon&&(this.options.showLabel=!0),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled||!1),this.hasTitle=!!this.element.attr("title"),this.options.label&&this.options.label!==this.originalLabel&&(this.isInput?this.element.val(this.options.label):this.element.html(this.options.label)),this._addClass("ui-button","ui-widget"),this._setOption("disabled",this.options.disabled),this._enhance(),this.element.is("a")&&this._on({keyup:function(e){e.keyCode===t.ui.keyCode.SPACE&&(e.preventDefault(),this.element[0].click?this.element[0].click():this.element.trigger("click"))}})},_enhance:function(){this.element.is("button")||this.element.attr("role","button"),this.options.icon&&(this._updateIcon("icon",this.options.icon),this._updateTooltip())},_updateTooltip:function(){this.title=this.element.attr("title"),this.options.showLabel||this.title||this.element.attr("title",this.options.label)},_updateIcon:function(e,i){var n="iconPosition"!==e,s=n?this.options.iconPosition:i,o="top"===s||"bottom"===s;this.icon?n&&this._removeClass(this.icon,null,this.options.icon):(this.icon=t("<span>"),this._addClass(this.icon,"ui-button-icon","ui-icon"),this.options.showLabel||this._addClass("ui-button-icon-only")),n&&this._addClass(this.icon,null,i),this._attachIcon(s),o?(this._addClass(this.icon,null,"ui-widget-icon-block"),this.iconSpace&&this.iconSpace.remove()):(this.iconSpace||(this.iconSpace=t("<span> </span>"),this._addClass(this.iconSpace,"ui-button-icon-space")),this._removeClass(this.icon,null,"ui-wiget-icon-block"),this._attachIconSpace(s))},_destroy:function(){this.element.removeAttr("role"),this.icon&&this.icon.remove(),this.iconSpace&&this.iconSpace.remove(),this.hasTitle||this.element.removeAttr("title")},_attachIconSpace:function(t){this.icon[/^(?:end|bottom)/.test(t)?"before":"after"](this.iconSpace)},_attachIcon:function(t){this.element[/^(?:end|bottom)/.test(t)?"append":"prepend"](this.icon)},_setOptions:function(t){var e=void 0===t.showLabel?this.options.showLabel:t.showLabel,i=void 0===t.icon?this.options.icon:t.icon;e||i||(t.showLabel=!0),this._super(t)},_setOption:function(t,e){"icon"===t&&(e?this._updateIcon(t,e):this.icon&&(this.icon.remove(),this.iconSpace&&this.iconSpace.remove())),"iconPosition"===t&&this._updateIcon(t,e),"showLabel"===t&&(this._toggleClass("ui-button-icon-only",null,!e),this._updateTooltip()),"label"===t&&(this.isInput?this.element.val(e):(this.element.html(e),this.icon&&(this._attachIcon(this.options.iconPosition),this._attachIconSpace(this.options.iconPosition)))),this._super(t,e),"disabled"===t&&(this._toggleClass(null,"ui-state-disabled",e),this.element[0].disabled=e,e&&this.element.blur())},refresh:function(){var t=this.element.is("input, button")?this.element[0].disabled:this.element.hasClass("ui-button-disabled");t!==this.options.disabled&&this._setOptions({disabled:t}),this._updateTooltip()}}),!1!==t.uiBackCompat&&(t.widget("ui.button",t.ui.button,{options:{text:!0,icons:{primary:null,secondary:null}},_create:function(){this.options.showLabel&&!this.options.text&&(this.options.showLabel=this.options.text),!this.options.showLabel&&this.options.text&&(this.options.text=this.options.showLabel),this.options.icon||!this.options.icons.primary&&!this.options.icons.secondary?this.options.icon&&(this.options.icons.primary=this.options.icon):this.options.icons.primary?this.options.icon=this.options.icons.primary:(this.options.icon=this.options.icons.secondary,this.options.iconPosition="end"),this._super()},_setOption:function(t,e){return"text"===t?void this._super("showLabel",e):("showLabel"===t&&(this.options.text=e),"icon"===t&&(this.options.icons.primary=e),"icons"===t&&(e.primary?(this._super("icon",e.primary),this._super("iconPosition","beginning")):e.secondary&&(this._super("icon",e.secondary),this._super("iconPosition","end"))),void this._superApply(arguments))}}),t.fn.button=function(e){return function(){return!this.length||this.length&&"INPUT"!==this[0].tagName||this.length&&"INPUT"===this[0].tagName&&"checkbox"!==this.attr("type")&&"radio"!==this.attr("type")?e.apply(this,arguments):(t.ui.checkboxradio||t.error("Checkboxradio widget missing"),0===arguments.length?this.checkboxradio({icon:!1}):this.checkboxradio.apply(this,arguments))}}(t.fn.button),t.fn.buttonset=function(){return t.ui.controlgroup||t.error("Controlgroup widget missing"),"option"===arguments[0]&&"items"===arguments[1]&&arguments[2]?this.controlgroup.apply(this,[arguments[0],"items.button",arguments[2]]):"option"===arguments[0]&&"items"===arguments[1]?this.controlgroup.apply(this,[arguments[0],"items.button"]):("object"==typeof arguments[0]&&arguments[0].items&&(arguments[0].items={button:arguments[0].items}),this.controlgroup.apply(this,arguments))}),t.ui.button,t.extend(t.ui,{datepicker:{version:"1.12.1"}}),t.extend(e.prototype,{markerClassName:"hasDatepicker",maxRows:4,_widgetDatepicker:function(){return this.dpDiv},setDefaults:function(t){return s(this._defaults,t||{}),this},_attachDatepicker:function(e,i){var n,s,o;s="div"===(n=e.nodeName.toLowerCase())||"span"===n,e.id||(this.uuid+=1,e.id="dp"+this.uuid),(o=this._newInst(t(e),s)).settings=t.extend({},i||{}),"input"===n?this._connectDatepicker(e,o):s&&this._inlineDatepicker(e,o)},_newInst:function(e,n){return{id:e[0].id.replace(/([^A-Za-z0-9_\-])/g,"\\\\$1"),input:e,selectedDay:0,selectedMonth:0,selectedYear:0,drawMonth:0,drawYear:0,inline:n,dpDiv:n?i(t("<div class='"+this._inlineClass+" ui-datepicker ui-widget ui-widget-content ui-helper-clearfix ui-corner-all'></div>")):this.dpDiv}},_connectDatepicker:function(e,i){var n=t(e);i.append=t([]),i.trigger=t([]),n.hasClass(this.markerClassName)||(this._attachments(n,i),n.addClass(this.markerClassName).on("keydown",this._doKeyDown).on("keypress",this._doKeyPress).on("keyup",this._doKeyUp),this._autoSize(i),t.data(e,"datepicker",i),i.settings.disabled&&this._disableDatepicker(e))},_attachments:function(e,i){var n,s,o,a=this._get(i,"appendText"),r=this._get(i,"isRTL");i.append&&i.append.remove(),a&&(i.append=t("<span class='"+this._appendClass+"'>"+a+"</span>"),e[r?"before":"after"](i.append)),e.off("focus",this._showDatepicker),i.trigger&&i.trigger.remove(),("focus"===(n=this._get(i,"showOn"))||"both"===n)&&e.on("focus",this._showDatepicker),("button"===n||"both"===n)&&(s=this._get(i,"buttonText"),o=this._get(i,"buttonImage"),i.trigger=t(this._get(i,"buttonImageOnly")?t("<img/>").addClass(this._triggerClass).attr({src:o,alt:s,title:s}):t("<button type='button'></button>").addClass(this._triggerClass).html(o?t("<img/>").attr({src:o,alt:s,title:s}):s)),e[r?"before":"after"](i.trigger),i.trigger.on("click",function(){return t.datepicker._datepickerShowing&&t.datepicker._lastInput===e[0]?t.datepicker._hideDatepicker():t.datepicker._datepickerShowing&&t.datepicker._lastInput!==e[0]?(t.datepicker._hideDatepicker(),t.datepicker._showDatepicker(e[0])):t.datepicker._showDatepicker(e[0]),!1}))},_autoSize:function(t){if(this._get(t,"autoSize")&&!t.inline){var e,i,n,s,o=new Date(2009,11,20),a=this._get(t,"dateFormat");a.match(/[DM]/)&&(e=function(t){for(i=0,n=0,s=0;t.length>s;s++)t[s].length>i&&(i=t[s].length,n=s);return n},o.setMonth(e(this._get(t,a.match(/MM/)?"monthNames":"monthNamesShort"))),o.setDate(e(this._get(t,a.match(/DD/)?"dayNames":"dayNamesShort"))+20-o.getDay())),t.input.attr("size",this._formatDate(t,o).length)}},_inlineDatepicker:function(e,i){var n=t(e);n.hasClass(this.markerClassName)||(n.addClass(this.markerClassName).append(i.dpDiv),t.data(e,"datepicker",i),this._setDate(i,this._getDefaultDate(i),!0),this._updateDatepicker(i),this._updateAlternate(i),i.settings.disabled&&this._disableDatepicker(e),i.dpDiv.css("display","block"))},_dialogDatepicker:function(e,i,n,o,a){var r,l,h,c,u,d=this._dialogInst;return d||(this.uuid+=1,r="dp"+this.uuid,this._dialogInput=t("<input type='text' id='"+r+"' style='position: absolute; top: -100px; width: 0px;'/>"),this._dialogInput.on("keydown",this._doKeyDown),t("body").append(this._dialogInput),(d=this._dialogInst=this._newInst(this._dialogInput,!1)).settings={},t.data(this._dialogInput[0],"datepicker",d)),s(d.settings,o||{}),i=i&&i.constructor===Date?this._formatDate(d,i):i,this._dialogInput.val(i),this._pos=a?a.length?a:[a.pageX,a.pageY]:null,this._pos||(l=document.documentElement.clientWidth,h=document.documentElement.clientHeight,c=document.documentElement.scrollLeft||document.body.scrollLeft,u=document.documentElement.scrollTop||document.body.scrollTop,this._pos=[l/2-100+c,h/2-150+u]),this._dialogInput.css("left",this._pos[0]+20+"px").css("top",this._pos[1]+"px"),d.settings.onSelect=n,this._inDialog=!0,this.dpDiv.addClass(this._dialogClass),this._showDatepicker(this._dialogInput[0]),t.blockUI&&t.blockUI(this.dpDiv),t.data(this._dialogInput[0],"datepicker",d),this},_destroyDatepicker:function(e){var i,n=t(e),s=t.data(e,"datepicker");n.hasClass(this.markerClassName)&&(i=e.nodeName.toLowerCase(),t.removeData(e,"datepicker"),"input"===i?(s.append.remove(),s.trigger.remove(),n.removeClass(this.markerClassName).off("focus",this._showDatepicker).off("keydown",this._doKeyDown).off("keypress",this._doKeyPress).off("keyup",this._doKeyUp)):("div"===i||"span"===i)&&n.removeClass(this.markerClassName).empty(),d===s&&(d=null))},_enableDatepicker:function(e){var i,n,s=t(e),o=t.data(e,"datepicker");s.hasClass(this.markerClassName)&&("input"===(i=e.nodeName.toLowerCase())?(e.disabled=!1,o.trigger.filter("button").each(function(){this.disabled=!1}).end().filter("img").css({opacity:"1.0",cursor:""})):("div"===i||"span"===i)&&((n=s.children("."+this._inlineClass)).children().removeClass("ui-state-disabled"),n.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!1)),this._disabledInputs=t.map(this._disabledInputs,function(t){return t===e?null:t}))},_disableDatepicker:function(e){var i,n,s=t(e),o=t.data(e,"datepicker");s.hasClass(this.markerClassName)&&("input"===(i=e.nodeName.toLowerCase())?(e.disabled=!0,o.trigger.filter("button").each(function(){this.disabled=!0}).end().filter("img").css({opacity:"0.5",cursor:"default"})):("div"===i||"span"===i)&&((n=s.children("."+this._inlineClass)).children().addClass("ui-state-disabled"),n.find("select.ui-datepicker-month, select.ui-datepicker-year").prop("disabled",!0)),this._disabledInputs=t.map(this._disabledInputs,function(t){return t===e?null:t}),this._disabledInputs[this._disabledInputs.length]=e)},_isDisabledDatepicker:function(t){if(!t)return!1;for(var e=0;this._disabledInputs.length>e;e++)if(this._disabledInputs[e]===t)return!0;return!1},_getInst:function(e){try{return t.data(e,"datepicker")}catch(t){throw"Missing instance data for this datepicker"}},_optionDatepicker:function(e,i,n){var o,a,r,l,h=this._getInst(e);return 2===arguments.length&&"string"==typeof i?"defaults"===i?t.extend({},t.datepicker._defaults):h?"all"===i?t.extend({},h.settings):this._get(h,i):null:(o=i||{},"string"==typeof i&&((o={})[i]=n),void(h&&(this._curInst===h&&this._hideDatepicker(),a=this._getDateDatepicker(e,!0),r=this._getMinMaxDate(h,"min"),l=this._getMinMaxDate(h,"max"),s(h.settings,o),null!==r&&void 0!==o.dateFormat&&void 0===o.minDate&&(h.settings.minDate=this._formatDate(h,r)),null!==l&&void 0!==o.dateFormat&&void 0===o.maxDate&&(h.settings.maxDate=this._formatDate(h,l)),"disabled"in o&&(o.disabled?this._disableDatepicker(e):this._enableDatepicker(e)),this._attachments(t(e),h),this._autoSize(h),this._setDate(h,a),this._updateAlternate(h),this._updateDatepicker(h))))},_changeDatepicker:function(t,e,i){this._optionDatepicker(t,e,i)},_refreshDatepicker:function(t){var e=this._getInst(t);e&&this._updateDatepicker(e)},_setDateDatepicker:function(t,e){var i=this._getInst(t);i&&(this._setDate(i,e),this._updateDatepicker(i),this._updateAlternate(i))},_getDateDatepicker:function(t,e){var i=this._getInst(t);return i&&!i.inline&&this._setDateFromField(i,e),i?this._getDate(i):null},_doKeyDown:function(e){var i,n,s,o=t.datepicker._getInst(e.target),a=!0,r=o.dpDiv.is(".ui-datepicker-rtl");if(o._keyEvent=!0,t.datepicker._datepickerShowing)switch(e.keyCode){case 9:t.datepicker._hideDatepicker(),a=!1;break;case 13:return(s=t("td."+t.datepicker._dayOverClass+":not(."+t.datepicker._currentClass+")",o.dpDiv))[0]&&t.datepicker._selectDay(e.target,o.selectedMonth,o.selectedYear,s[0]),(i=t.datepicker._get(o,"onSelect"))?(n=t.datepicker._formatDate(o),i.apply(o.input?o.input[0]:null,[n,o])):t.datepicker._hideDatepicker(),!1;case 27:t.datepicker._hideDatepicker();break;case 33:t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(o,"stepBigMonths"):-t.datepicker._get(o,"stepMonths"),"M");break;case 34:t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(o,"stepBigMonths"):+t.datepicker._get(o,"stepMonths"),"M");break;case 35:(e.ctrlKey||e.metaKey)&&t.datepicker._clearDate(e.target),a=e.ctrlKey||e.metaKey;break;case 36:(e.ctrlKey||e.metaKey)&&t.datepicker._gotoToday(e.target),a=e.ctrlKey||e.metaKey;break;case 37:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,r?1:-1,"D"),a=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?-t.datepicker._get(o,"stepBigMonths"):-t.datepicker._get(o,"stepMonths"),"M");break;case 38:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,-7,"D"),a=e.ctrlKey||e.metaKey;break;case 39:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,r?-1:1,"D"),a=e.ctrlKey||e.metaKey,e.originalEvent.altKey&&t.datepicker._adjustDate(e.target,e.ctrlKey?+t.datepicker._get(o,"stepBigMonths"):+t.datepicker._get(o,"stepMonths"),"M");break;case 40:(e.ctrlKey||e.metaKey)&&t.datepicker._adjustDate(e.target,7,"D"),a=e.ctrlKey||e.metaKey;break;default:a=!1}else 36===e.keyCode&&e.ctrlKey?t.datepicker._showDatepicker(this):a=!1;a&&(e.preventDefault(),e.stopPropagation())},_doKeyPress:function(e){var i,n,s=t.datepicker._getInst(e.target);return t.datepicker._get(s,"constrainInput")?(i=t.datepicker._possibleChars(t.datepicker._get(s,"dateFormat")),n=String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),e.ctrlKey||e.metaKey||" ">n||!i||i.indexOf(n)>-1):void 0},_doKeyUp:function(e){var i=t.datepicker._getInst(e.target);if(i.input.val()!==i.lastVal)try{t.datepicker.parseDate(t.datepicker._get(i,"dateFormat"),i.input?i.input.val():null,t.datepicker._getFormatConfig(i))&&(t.datepicker._setDateFromField(i),t.datepicker._updateAlternate(i),t.datepicker._updateDatepicker(i))}catch(t){}return!0},_showDatepicker:function(e){var i,n,o,a,r,l,h;("input"!==(e=e.target||e).nodeName.toLowerCase()&&(e=t("input",e.parentNode)[0]),t.datepicker._isDisabledDatepicker(e)||t.datepicker._lastInput===e)||(i=t.datepicker._getInst(e),t.datepicker._curInst&&t.datepicker._curInst!==i&&(t.datepicker._curInst.dpDiv.stop(!0,!0),i&&t.datepicker._datepickerShowing&&t.datepicker._hideDatepicker(t.datepicker._curInst.input[0])),!1!==(o=(n=t.datepicker._get(i,"beforeShow"))?n.apply(e,[e,i]):{})&&(s(i.settings,o),i.lastVal=null,t.datepicker._lastInput=e,t.datepicker._setDateFromField(i),t.datepicker._inDialog&&(e.value=""),t.datepicker._pos||(t.datepicker._pos=t.datepicker._findPos(e),t.datepicker._pos[1]+=e.offsetHeight),a=!1,t(e).parents().each(function(){return!(a|="fixed"===t(this).css("position"))}),r={left:t.datepicker._pos[0],top:t.datepicker._pos[1]},t.datepicker._pos=null,i.dpDiv.empty(),i.dpDiv.css({position:"absolute",display:"block",top:"-1000px"}),t.datepicker._updateDatepicker(i),r=t.datepicker._checkOffset(i,r,a),i.dpDiv.css({position:t.datepicker._inDialog&&t.blockUI?"static":a?"fixed":"absolute",display:"none",left:r.left+"px",top:r.top+"px"}),i.inline||(l=t.datepicker._get(i,"showAnim"),h=t.datepicker._get(i,"duration"),i.dpDiv.css("z-index",function(t){for(var e,i;t.length&&t[0]!==document;){if(("absolute"===(e=t.css("position"))||"relative"===e||"fixed"===e)&&(i=parseInt(t.css("zIndex"),10),!isNaN(i)&&0!==i))return i;t=t.parent()}return 0}(t(e))+1),t.datepicker._datepickerShowing=!0,t.effects&&t.effects.effect[l]?i.dpDiv.show(l,t.datepicker._get(i,"showOptions"),h):i.dpDiv[l||"show"](l?h:null),t.datepicker._shouldFocusInput(i)&&i.input.trigger("focus"),t.datepicker._curInst=i)))},_updateDatepicker:function(e){this.maxRows=4,d=e,e.dpDiv.empty().append(this._generateHTML(e)),this._attachHandlers(e);var i,s=this._getNumberOfMonths(e),o=s[1],a=e.dpDiv.find("."+this._dayOverClass+" a");a.length>0&&n.apply(a.get(0)),e.dpDiv.removeClass("ui-datepicker-multi-2 ui-datepicker-multi-3 ui-datepicker-multi-4").width(""),o>1&&e.dpDiv.addClass("ui-datepicker-multi-"+o).css("width",17*o+"em"),e.dpDiv[(1!==s[0]||1!==s[1]?"add":"remove")+"Class"]("ui-datepicker-multi"),e.dpDiv[(this._get(e,"isRTL")?"add":"remove")+"Class"]("ui-datepicker-rtl"),e===t.datepicker._curInst&&t.datepicker._datepickerShowing&&t.datepicker._shouldFocusInput(e)&&e.input.trigger("focus"),e.yearshtml&&(i=e.yearshtml,setTimeout(function(){i===e.yearshtml&&e.yearshtml&&e.dpDiv.find("select.ui-datepicker-year:first").replaceWith(e.yearshtml),i=e.yearshtml=null},0))},_shouldFocusInput:function(t){return t.input&&t.input.is(":visible")&&!t.input.is(":disabled")&&!t.input.is(":focus")},_checkOffset:function(e,i,n){var s=e.dpDiv.outerWidth(),o=e.dpDiv.outerHeight(),a=e.input?e.input.outerWidth():0,r=e.input?e.input.outerHeight():0,l=document.documentElement.clientWidth+(n?0:t(document).scrollLeft()),h=document.documentElement.clientHeight+(n?0:t(document).scrollTop());return i.left-=this._get(e,"isRTL")?s-a:0,i.left-=n&&i.left===e.input.offset().left?t(document).scrollLeft():0,i.top-=n&&i.top===e.input.offset().top+r?t(document).scrollTop():0,i.left-=Math.min(i.left,i.left+s>l&&l>s?Math.abs(i.left+s-l):0),i.top-=Math.min(i.top,i.top+o>h&&h>o?Math.abs(o+r):0),i},_findPos:function(e){for(var i,n=this._getInst(e),s=this._get(n,"isRTL");e&&("hidden"===e.type||1!==e.nodeType||t.expr.filters.hidden(e));)e=e[s?"previousSibling":"nextSibling"];return[(i=t(e).offset()).left,i.top]},_hideDatepicker:function(e){var i,n,s,o,a=this._curInst;!a||e&&a!==t.data(e,"datepicker")||this._datepickerShowing&&(i=this._get(a,"showAnim"),n=this._get(a,"duration"),s=function(){t.datepicker._tidyDialog(a)},t.effects&&(t.effects.effect[i]||t.effects[i])?a.dpDiv.hide(i,t.datepicker._get(a,"showOptions"),n,s):a.dpDiv["slideDown"===i?"slideUp":"fadeIn"===i?"fadeOut":"hide"](i?n:null,s),i||s(),this._datepickerShowing=!1,(o=this._get(a,"onClose"))&&o.apply(a.input?a.input[0]:null,[a.input?a.input.val():"",a]),this._lastInput=null,this._inDialog&&(this._dialogInput.css({position:"absolute",left:"0",top:"-100px"}),t.blockUI&&(t.unblockUI(),t("body").append(this.dpDiv))),this._inDialog=!1)},_tidyDialog:function(t){t.dpDiv.removeClass(this._dialogClass).off(".ui-datepicker-calendar")},_checkExternalClick:function(e){if(t.datepicker._curInst){var i=t(e.target),n=t.datepicker._getInst(i[0]);(i[0].id!==t.datepicker._mainDivId&&0===i.parents("#"+t.datepicker._mainDivId).length&&!i.hasClass(t.datepicker.markerClassName)&&!i.closest("."+t.datepicker._triggerClass).length&&t.datepicker._datepickerShowing&&(!t.datepicker._inDialog||!t.blockUI)||i.hasClass(t.datepicker.markerClassName)&&t.datepicker._curInst!==n)&&t.datepicker._hideDatepicker()}},_adjustDate:function(e,i,n){var s=t(e),o=this._getInst(s[0]);this._isDisabledDatepicker(s[0])||(this._adjustInstDate(o,i+("M"===n?this._get(o,"showCurrentAtPos"):0),n),this._updateDatepicker(o))},_gotoToday:function(e){var i,n=t(e),s=this._getInst(n[0]);this._get(s,"gotoCurrent")&&s.currentDay?(s.selectedDay=s.currentDay,s.drawMonth=s.selectedMonth=s.currentMonth,s.drawYear=s.selectedYear=s.currentYear):(i=new Date,s.selectedDay=i.getDate(),s.drawMonth=s.selectedMonth=i.getMonth(),s.drawYear=s.selectedYear=i.getFullYear()),this._notifyChange(s),this._adjustDate(n)},_selectMonthYear:function(e,i,n){var s=t(e),o=this._getInst(s[0]);o["selected"+("M"===n?"Month":"Year")]=o["draw"+("M"===n?"Month":"Year")]=parseInt(i.options[i.selectedIndex].value,10),this._notifyChange(o),this._adjustDate(s)},_selectDay:function(e,i,n,s){var o,a=t(e);t(s).hasClass(this._unselectableClass)||this._isDisabledDatepicker(a[0])||((o=this._getInst(a[0])).selectedDay=o.currentDay=t("a",s).html(),o.selectedMonth=o.currentMonth=i,o.selectedYear=o.currentYear=n,this._selectDate(e,this._formatDate(o,o.currentDay,o.currentMonth,o.currentYear)))},_clearDate:function(e){var i=t(e);this._selectDate(i,"")},_selectDate:function(e,i){var n,s=t(e),o=this._getInst(s[0]);i=null!=i?i:this._formatDate(o),o.input&&o.input.val(i),this._updateAlternate(o),(n=this._get(o,"onSelect"))?n.apply(o.input?o.input[0]:null,[i,o]):o.input&&o.input.trigger("change"),o.inline?this._updateDatepicker(o):(this._hideDatepicker(),this._lastInput=o.input[0],"object"!=typeof o.input[0]&&o.input.trigger("focus"),this._lastInput=null)},_updateAlternate:function(e){var i,n,s,o=this._get(e,"altField");o&&(i=this._get(e,"altFormat")||this._get(e,"dateFormat"),n=this._getDate(e),s=this.formatDate(i,n,this._getFormatConfig(e)),t(o).val(s))},noWeekends:function(t){var e=t.getDay();return[e>0&&6>e,""]},iso8601Week:function(t){var e,i=new Date(t.getTime());return i.setDate(i.getDate()+4-(i.getDay()||7)),e=i.getTime(),i.setMonth(0),i.setDate(1),Math.floor(Math.round((e-i)/864e5)/7)+1},parseDate:function(e,i,n){if(null==e||null==i)throw"Invalid arguments";if(""===(i="object"==typeof i?""+i:i+""))return null;var s,o,a,r,l=0,h=(n?n.shortYearCutoff:null)||this._defaults.shortYearCutoff,c="string"!=typeof h?h:(new Date).getFullYear()%100+parseInt(h,10),u=(n?n.dayNamesShort:null)||this._defaults.dayNamesShort,d=(n?n.dayNames:null)||this._defaults.dayNames,p=(n?n.monthNamesShort:null)||this._defaults.monthNamesShort,f=(n?n.monthNames:null)||this._defaults.monthNames,g=-1,m=-1,v=-1,_=-1,b=!1,y=function(t){var i=e.length>s+1&&e.charAt(s+1)===t;return i&&s++,i},w=function(t){var e=y(t),n="@"===t?14:"!"===t?20:"y"===t&&e?4:"o"===t?3:2,s=RegExp("^\\d{"+("y"===t?n:1)+","+n+"}"),o=i.substring(l).match(s);if(!o)throw"Missing number at position "+l;return l+=o[0].length,parseInt(o[0],10)},x=function(e,n,s){var o=-1,a=t.map(y(e)?s:n,function(t,e){return[[e,t]]}).sort(function(t,e){return-(t[1].length-e[1].length)});if(t.each(a,function(t,e){var n=e[1];return i.substr(l,n.length).toLowerCase()===n.toLowerCase()?(o=e[0],l+=n.length,!1):void 0}),-1!==o)return o+1;throw"Unknown name at position "+l},C=function(){if(i.charAt(l)!==e.charAt(s))throw"Unexpected literal at position "+l;l++};for(s=0;e.length>s;s++)if(b)"'"!==e.charAt(s)||y("'")?C():b=!1;else switch(e.charAt(s)){case"d":v=w("d");break;case"D":x("D",u,d);break;case"o":_=w("o");break;case"m":m=w("m");break;case"M":m=x("M",p,f);break;case"y":g=w("y");break;case"@":g=(r=new Date(w("@"))).getFullYear(),m=r.getMonth()+1,v=r.getDate();break;case"!":g=(r=new Date((w("!")-this._ticksTo1970)/1e4)).getFullYear(),m=r.getMonth()+1,v=r.getDate();break;case"'":y("'")?C():b=!0;break;default:C()}if(i.length>l&&(a=i.substr(l),!/^\s+/.test(a)))throw"Extra/unparsed characters found in date: "+a;if(-1===g?g=(new Date).getFullYear():100>g&&(g+=(new Date).getFullYear()-(new Date).getFullYear()%100+(c>=g?0:-100)),_>-1)for(m=1,v=_;!((o=this._getDaysInMonth(g,m-1))>=v);)m++,v-=o;if((r=this._daylightSavingAdjust(new Date(g,m-1,v))).getFullYear()!==g||r.getMonth()+1!==m||r.getDate()!==v)throw"Invalid date";return r},ATOM:"yy-mm-dd",COOKIE:"D, dd M yy",ISO_8601:"yy-mm-dd",RFC_822:"D, d M y",RFC_850:"DD, dd-M-y",RFC_1036:"D, d M y",RFC_1123:"D, d M yy",RFC_2822:"D, d M yy",RSS:"D, d M y",TICKS:"!",TIMESTAMP:"@",W3C:"yy-mm-dd",_ticksTo1970:864e9*(718685+Math.floor(492.5)-Math.floor(19.7)+Math.floor(4.925)),formatDate:function(t,e,i){if(!e)return"";var n,s=(i?i.dayNamesShort:null)||this._defaults.dayNamesShort,o=(i?i.dayNames:null)||this._defaults.dayNames,a=(i?i.monthNamesShort:null)||this._defaults.monthNamesShort,r=(i?i.monthNames:null)||this._defaults.monthNames,l=function(e){var i=t.length>n+1&&t.charAt(n+1)===e;return i&&n++,i},h=function(t,e,i){var n=""+e;if(l(t))for(;i>n.length;)n="0"+n;return n},c=function(t,e,i,n){return l(t)?n[e]:i[e]},u="",d=!1;if(e)for(n=0;t.length>n;n++)if(d)"'"!==t.charAt(n)||l("'")?u+=t.charAt(n):d=!1;else switch(t.charAt(n)){case"d":u+=h("d",e.getDate(),2);break;case"D":u+=c("D",e.getDay(),s,o);break;case"o":u+=h("o",Math.round((new Date(e.getFullYear(),e.getMonth(),e.getDate()).getTime()-new Date(e.getFullYear(),0,0).getTime())/864e5),3);break;case"m":u+=h("m",e.getMonth()+1,2);break;case"M":u+=c("M",e.getMonth(),a,r);break;case"y":u+=l("y")?e.getFullYear():(10>e.getFullYear()%100?"0":"")+e.getFullYear()%100;break;case"@":u+=e.getTime();break;case"!":u+=1e4*e.getTime()+this._ticksTo1970;break;case"'":l("'")?u+="'":d=!0;break;default:u+=t.charAt(n)}return u},_possibleChars:function(t){var e,i="",n=!1,s=function(i){var n=t.length>e+1&&t.charAt(e+1)===i;return n&&e++,n};for(e=0;t.length>e;e++)if(n)"'"!==t.charAt(e)||s("'")?i+=t.charAt(e):n=!1;else switch(t.charAt(e)){case"d":case"m":case"y":case"@":i+="0123456789";break;case"D":case"M":return null;case"'":s("'")?i+="'":n=!0;break;default:i+=t.charAt(e)}return i},_get:function(t,e){return void 0!==t.settings[e]?t.settings[e]:this._defaults[e]},_setDateFromField:function(t,e){if(t.input.val()!==t.lastVal){var i=this._get(t,"dateFormat"),n=t.lastVal=t.input?t.input.val():null,s=this._getDefaultDate(t),o=s,a=this._getFormatConfig(t);try{o=this.parseDate(i,n,a)||s}catch(t){n=e?"":n}t.selectedDay=o.getDate(),t.drawMonth=t.selectedMonth=o.getMonth(),t.drawYear=t.selectedYear=o.getFullYear(),t.currentDay=n?o.getDate():0,t.currentMonth=n?o.getMonth():0,t.currentYear=n?o.getFullYear():0,this._adjustInstDate(t)}},_getDefaultDate:function(t){return this._restrictMinMax(t,this._determineDate(t,this._get(t,"defaultDate"),new Date))},_determineDate:function(e,i,n){var s=null==i||""===i?n:"string"==typeof i?function(i){try{return t.datepicker.parseDate(t.datepicker._get(e,"dateFormat"),i,t.datepicker._getFormatConfig(e))}catch(t){}for(var n=(i.toLowerCase().match(/^c/)?t.datepicker._getDate(e):null)||new Date,s=n.getFullYear(),o=n.getMonth(),a=n.getDate(),r=/([+\-]?[0-9]+)\s*(d|D|w|W|m|M|y|Y)?/g,l=r.exec(i);l;){switch(l[2]||"d"){case"d":case"D":a+=parseInt(l[1],10);break;case"w":case"W":a+=7*parseInt(l[1],10);break;case"m":case"M":o+=parseInt(l[1],10),a=Math.min(a,t.datepicker._getDaysInMonth(s,o));break;case"y":case"Y":s+=parseInt(l[1],10),a=Math.min(a,t.datepicker._getDaysInMonth(s,o))}l=r.exec(i)}return new Date(s,o,a)}(i):"number"==typeof i?isNaN(i)?n:function(t){var e=new Date;return e.setDate(e.getDate()+t),e}(i):new Date(i.getTime());return(s=s&&"Invalid Date"==""+s?n:s)&&(s.setHours(0),s.setMinutes(0),s.setSeconds(0),s.setMilliseconds(0)),this._daylightSavingAdjust(s)},_daylightSavingAdjust:function(t){return t?(t.setHours(t.getHours()>12?t.getHours()+2:0),t):null},_setDate:function(t,e,i){var n=!e,s=t.selectedMonth,o=t.selectedYear,a=this._restrictMinMax(t,this._determineDate(t,e,new Date));t.selectedDay=t.currentDay=a.getDate(),t.drawMonth=t.selectedMonth=t.currentMonth=a.getMonth(),t.drawYear=t.selectedYear=t.currentYear=a.getFullYear(),s===t.selectedMonth&&o===t.selectedYear||i||this._notifyChange(t),this._adjustInstDate(t),t.input&&t.input.val(n?"":this._formatDate(t))},_getDate:function(t){return!t.currentYear||t.input&&""===t.input.val()?null:this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay))},_attachHandlers:function(e){var i=this._get(e,"stepMonths"),n="#"+e.id.replace(/\\\\/g,"\\");e.dpDiv.find("[data-handler]").map(function(){var e={prev:function(){t.datepicker._adjustDate(n,-i,"M")},next:function(){t.datepicker._adjustDate(n,+i,"M")},hide:function(){t.datepicker._hideDatepicker()},today:function(){t.datepicker._gotoToday(n)},selectDay:function(){return t.datepicker._selectDay(n,+this.getAttribute("data-month"),+this.getAttribute("data-year"),this),!1},selectMonth:function(){return t.datepicker._selectMonthYear(n,this,"M"),!1},selectYear:function(){return t.datepicker._selectMonthYear(n,this,"Y"),!1}};t(this).on(this.getAttribute("data-event"),e[this.getAttribute("data-handler")])})},_generateHTML:function(t){var e,i,n,s,o,a,r,l,h,c,u,d,p,f,g,m,v,_,b,y,w,x,C,D,T,S,k,I,E,A,P,M,O,N,H,R,L,F,j,W=new Date,z=this._daylightSavingAdjust(new Date(W.getFullYear(),W.getMonth(),W.getDate())),B=this._get(t,"isRTL"),$=this._get(t,"showButtonPanel"),q=this._get(t,"hideIfNoPrevNext"),U=this._get(t,"navigationAsDateFormat"),Y=this._getNumberOfMonths(t),V=this._get(t,"showCurrentAtPos"),K=this._get(t,"stepMonths"),X=1!==Y[0]||1!==Y[1],G=this._daylightSavingAdjust(t.currentDay?new Date(t.currentYear,t.currentMonth,t.currentDay):new Date(9999,9,9)),J=this._getMinMaxDate(t,"min"),Z=this._getMinMaxDate(t,"max"),Q=t.drawMonth-V,tt=t.drawYear;if(0>Q&&(Q+=12,tt--),Z)for(e=this._daylightSavingAdjust(new Date(Z.getFullYear(),Z.getMonth()-Y[0]*Y[1]+1,Z.getDate())),e=J&&J>e?J:e;this._daylightSavingAdjust(new Date(tt,Q,1))>e;)0>--Q&&(Q=11,tt--);for(t.drawMonth=Q,t.drawYear=tt,i=this._get(t,"prevText"),i=U?this.formatDate(i,this._daylightSavingAdjust(new Date(tt,Q-K,1)),this._getFormatConfig(t)):i,n=this._canAdjustMonth(t,-1,tt,Q)?"<a class='ui-datepicker-prev ui-corner-all' data-handler='prev' data-event='click' title='"+i+"'><span class='ui-icon ui-icon-circle-triangle-"+(B?"e":"w")+"'>"+i+"</span></a>":q?"":"<a class='ui-datepicker-prev ui-corner-all ui-state-disabled' title='"+i+"'><span class='ui-icon ui-icon-circle-triangle-"+(B?"e":"w")+"'>"+i+"</span></a>",s=this._get(t,"nextText"),s=U?this.formatDate(s,this._daylightSavingAdjust(new Date(tt,Q+K,1)),this._getFormatConfig(t)):s,o=this._canAdjustMonth(t,1,tt,Q)?"<a class='ui-datepicker-next ui-corner-all' data-handler='next' data-event='click' title='"+s+"'><span class='ui-icon ui-icon-circle-triangle-"+(B?"w":"e")+"'>"+s+"</span></a>":q?"":"<a class='ui-datepicker-next ui-corner-all ui-state-disabled' title='"+s+"'><span class='ui-icon ui-icon-circle-triangle-"+(B?"w":"e")+"'>"+s+"</span></a>",a=this._get(t,"currentText"),r=this._get(t,"gotoCurrent")&&t.currentDay?G:z,a=U?this.formatDate(a,r,this._getFormatConfig(t)):a,l=t.inline?"":"<button type='button' class='ui-datepicker-close ui-state-default ui-priority-primary ui-corner-all' data-handler='hide' data-event='click'>"+this._get(t,"closeText")+"</button>",h=$?"<div class='ui-datepicker-buttonpane ui-widget-content'>"+(B?l:"")+(this._isInRange(t,r)?"<button type='button' class='ui-datepicker-current ui-state-default ui-priority-secondary ui-corner-all' data-handler='today' data-event='click'>"+a+"</button>":"")+(B?"":l)+"</div>":"",c=parseInt(this._get(t,"firstDay"),10),c=isNaN(c)?0:c,u=this._get(t,"showWeek"),d=this._get(t,"dayNames"),p=this._get(t,"dayNamesMin"),f=this._get(t,"monthNames"),g=this._get(t,"monthNamesShort"),m=this._get(t,"beforeShowDay"),v=this._get(t,"showOtherMonths"),_=this._get(t,"selectOtherMonths"),b=this._getDefaultDate(t),y="",x=0;Y[0]>x;x++){for(C="",this.maxRows=4,D=0;Y[1]>D;D++){if(T=this._daylightSavingAdjust(new Date(tt,Q,t.selectedDay)),S=" ui-corner-all",k="",X){if(k+="<div class='ui-datepicker-group",Y[1]>1)switch(D){case 0:k+=" ui-datepicker-group-first",S=" ui-corner-"+(B?"right":"left");break;case Y[1]-1:k+=" ui-datepicker-group-last",S=" ui-corner-"+(B?"left":"right");break;default:k+=" ui-datepicker-group-middle",S=""}k+="'>"}for(k+="<div class='ui-datepicker-header ui-widget-header ui-helper-clearfix"+S+"'>"+(/all|left/.test(S)&&0===x?B?o:n:"")+(/all|right/.test(S)&&0===x?B?n:o:"")+this._generateMonthYearHeader(t,Q,tt,J,Z,x>0||D>0,f,g)+"</div><table class='ui-datepicker-calendar'><thead><tr>",I=u?"<th class='ui-datepicker-week-col'>"+this._get(t,"weekHeader")+"</th>":"",w=0;7>w;w++)I+="<th scope='col'"+((w+c+6)%7>=5?" class='ui-datepicker-week-end'":"")+"><span title='"+d[E=(w+c)%7]+"'>"+p[E]+"</span></th>";for(k+=I+"</tr></thead><tbody>",A=this._getDaysInMonth(tt,Q),tt===t.selectedYear&&Q===t.selectedMonth&&(t.selectedDay=Math.min(t.selectedDay,A)),P=(this._getFirstDayOfMonth(tt,Q)-c+7)%7,M=Math.ceil((P+A)/7),O=X&&this.maxRows>M?this.maxRows:M,this.maxRows=O,N=this._daylightSavingAdjust(new Date(tt,Q,1-P)),H=0;O>H;H++){for(k+="<tr>",R=u?"<td class='ui-datepicker-week-col'>"+this._get(t,"calculateWeek")(N)+"</td>":"",w=0;7>w;w++)L=m?m.apply(t.input?t.input[0]:null,[N]):[!0,""],j=(F=N.getMonth()!==Q)&&!_||!L[0]||J&&J>N||Z&&N>Z,R+="<td class='"+((w+c+6)%7>=5?" ui-datepicker-week-end":"")+(F?" ui-datepicker-other-month":"")+(N.getTime()===T.getTime()&&Q===t.selectedMonth&&t._keyEvent||b.getTime()===N.getTime()&&b.getTime()===T.getTime()?" "+this._dayOverClass:"")+(j?" "+this._unselectableClass+" ui-state-disabled":"")+(F&&!v?"":" "+L[1]+(N.getTime()===G.getTime()?" "+this._currentClass:"")+(N.getTime()===z.getTime()?" ui-datepicker-today":""))+"'"+(F&&!v||!L[2]?"":" title='"+L[2].replace(/'/g,"&#39;")+"'")+(j?"":" data-handler='selectDay' data-event='click' data-month='"+N.getMonth()+"' data-year='"+N.getFullYear()+"'")+">"+(F&&!v?"&#xa0;":j?"<span class='ui-state-default'>"+N.getDate()+"</span>":"<a class='ui-state-default"+(N.getTime()===z.getTime()?" ui-state-highlight":"")+(N.getTime()===G.getTime()?" ui-state-active":"")+(F?" ui-priority-secondary":"")+"' href='#'>"+N.getDate()+"</a>")+"</td>",N.setDate(N.getDate()+1),N=this._daylightSavingAdjust(N);k+=R+"</tr>"}++Q>11&&(Q=0,tt++),C+=k+="</tbody></table>"+(X?"</div>"+(Y[0]>0&&D===Y[1]-1?"<div class='ui-datepicker-row-break'></div>":""):"")}y+=C}return y+=h,t._keyEvent=!1,y},_generateMonthYearHeader:function(t,e,i,n,s,o,a,r){var l,h,c,u,d,p,f,g,m=this._get(t,"changeMonth"),v=this._get(t,"changeYear"),_=this._get(t,"showMonthAfterYear"),b="<div class='ui-datepicker-title'>",y="";if(o||!m)y+="<span class='ui-datepicker-month'>"+a[e]+"</span>";else{for(l=n&&n.getFullYear()===i,h=s&&s.getFullYear()===i,y+="<select class='ui-datepicker-month' data-handler='selectMonth' data-event='change'>",c=0;12>c;c++)(!l||c>=n.getMonth())&&(!h||s.getMonth()>=c)&&(y+="<option value='"+c+"'"+(c===e?" selected='selected'":"")+">"+r[c]+"</option>");y+="</select>"}if(_||(b+=y+(!o&&m&&v?"":"&#xa0;")),!t.yearshtml)if(t.yearshtml="",o||!v)b+="<span class='ui-datepicker-year'>"+i+"</span>";else{for(u=this._get(t,"yearRange").split(":"),d=(new Date).getFullYear(),f=(p=function(t){var e=t.match(/c[+\-].*/)?i+parseInt(t.substring(1),10):t.match(/[+\-].*/)?d+parseInt(t,10):parseInt(t,10);return isNaN(e)?d:e})(u[0]),g=Math.max(f,p(u[1]||"")),f=n?Math.max(f,n.getFullYear()):f,g=s?Math.min(g,s.getFullYear()):g,t.yearshtml+="<select class='ui-datepicker-year' data-handler='selectYear' data-event='change'>";g>=f;f++)t.yearshtml+="<option value='"+f+"'"+(f===i?" selected='selected'":"")+">"+f+"</option>";t.yearshtml+="</select>",b+=t.yearshtml,t.yearshtml=null}return b+=this._get(t,"yearSuffix"),_&&(b+=(!o&&m&&v?"":"&#xa0;")+y),b+"</div>"},_adjustInstDate:function(t,e,i){var n=t.selectedYear+("Y"===i?e:0),s=t.selectedMonth+("M"===i?e:0),o=Math.min(t.selectedDay,this._getDaysInMonth(n,s))+("D"===i?e:0),a=this._restrictMinMax(t,this._daylightSavingAdjust(new Date(n,s,o)));t.selectedDay=a.getDate(),t.drawMonth=t.selectedMonth=a.getMonth(),t.drawYear=t.selectedYear=a.getFullYear(),("M"===i||"Y"===i)&&this._notifyChange(t)},_restrictMinMax:function(t,e){var i=this._getMinMaxDate(t,"min"),n=this._getMinMaxDate(t,"max"),s=i&&i>e?i:e;return n&&s>n?n:s},_notifyChange:function(t){var e=this._get(t,"onChangeMonthYear");e&&e.apply(t.input?t.input[0]:null,[t.selectedYear,t.selectedMonth+1,t])},_getNumberOfMonths:function(t){var e=this._get(t,"numberOfMonths");return null==e?[1,1]:"number"==typeof e?[1,e]:e},_getMinMaxDate:function(t,e){return this._determineDate(t,this._get(t,e+"Date"),null)},_getDaysInMonth:function(t,e){return 32-this._daylightSavingAdjust(new Date(t,e,32)).getDate()},_getFirstDayOfMonth:function(t,e){return new Date(t,e,1).getDay()},_canAdjustMonth:function(t,e,i,n){var s=this._getNumberOfMonths(t),o=this._daylightSavingAdjust(new Date(i,n+(0>e?e:s[0]*s[1]),1));return 0>e&&o.setDate(this._getDaysInMonth(o.getFullYear(),o.getMonth())),this._isInRange(t,o)},_isInRange:function(t,e){var i,n,s=this._getMinMaxDate(t,"min"),o=this._getMinMaxDate(t,"max"),a=null,r=null,l=this._get(t,"yearRange");return l&&(i=l.split(":"),n=(new Date).getFullYear(),a=parseInt(i[0],10),r=parseInt(i[1],10),i[0].match(/[+\-].*/)&&(a+=n),i[1].match(/[+\-].*/)&&(r+=n)),(!s||e.getTime()>=s.getTime())&&(!o||e.getTime()<=o.getTime())&&(!a||e.getFullYear()>=a)&&(!r||r>=e.getFullYear())},_getFormatConfig:function(t){var e=this._get(t,"shortYearCutoff");return{shortYearCutoff:e="string"!=typeof e?e:(new Date).getFullYear()%100+parseInt(e,10),dayNamesShort:this._get(t,"dayNamesShort"),dayNames:this._get(t,"dayNames"),monthNamesShort:this._get(t,"monthNamesShort"),monthNames:this._get(t,"monthNames")}},_formatDate:function(t,e,i,n){e||(t.currentDay=t.selectedDay,t.currentMonth=t.selectedMonth,t.currentYear=t.selectedYear);var s=e?"object"==typeof e?e:this._daylightSavingAdjust(new Date(n,i,e)):this._daylightSavingAdjust(new Date(t.currentYear,t.currentMonth,t.currentDay));return this.formatDate(this._get(t,"dateFormat"),s,this._getFormatConfig(t))}}),t.fn.datepicker=function(e){if(!this.length)return this;t.datepicker.initialized||(t(document).on("mousedown",t.datepicker._checkExternalClick),t.datepicker.initialized=!0),0===t("#"+t.datepicker._mainDivId).length&&t("body").append(t.datepicker.dpDiv);var i=Array.prototype.slice.call(arguments,1);return"string"!=typeof e||"isDisabled"!==e&&"getDate"!==e&&"widget"!==e?"option"===e&&2===arguments.length&&"string"==typeof arguments[1]?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this[0]].concat(i)):this.each(function(){"string"==typeof e?t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this].concat(i)):t.datepicker._attachDatepicker(this,e)}):t.datepicker["_"+e+"Datepicker"].apply(t.datepicker,[this[0]].concat(i))},t.datepicker=new e,t.datepicker.initialized=!1,t.datepicker.uuid=(new Date).getTime(),t.datepicker.version="1.12.1",t.datepicker,t.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase());var f=!1;t(document).on("mouseup",function(){f=!1}),t.widget("ui.mouse",{version:"1.12.1",options:{cancel:"input, textarea, button, select, option",distance:1,delay:0},_mouseInit:function(){var e=this;this.element.on("mousedown."+this.widgetName,function(t){return e._mouseDown(t)}).on("click."+this.widgetName,function(i){return!0===t.data(i.target,e.widgetName+".preventClickEvent")?(t.removeData(i.target,e.widgetName+".preventClickEvent"),i.stopImmediatePropagation(),!1):void 0}),this.started=!1},_mouseDestroy:function(){this.element.off("."+this.widgetName),this._mouseMoveDelegate&&this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(e){if(!f){this._mouseMoved=!1,this._mouseStarted&&this._mouseUp(e),this._mouseDownEvent=e;var i=this,n=1===e.which,s=!("string"!=typeof this.options.cancel||!e.target.nodeName)&&t(e.target).closest(this.options.cancel).length;return!(n&&!s&&this._mouseCapture(e))||(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){i.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(e),!this._mouseStarted)?(e.preventDefault(),!0):(!0===t.data(e.target,this.widgetName+".preventClickEvent")&&t.removeData(e.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(t){return i._mouseMove(t)},this._mouseUpDelegate=function(t){return i._mouseUp(t)},this.document.on("mousemove."+this.widgetName,this._mouseMoveDelegate).on("mouseup."+this.widgetName,this._mouseUpDelegate),e.preventDefault(),f=!0,!0))}},_mouseMove:function(e){if(this._mouseMoved){if(t.ui.ie&&(!document.documentMode||9>document.documentMode)&&!e.button)return this._mouseUp(e);if(!e.which)if(e.originalEvent.altKey||e.originalEvent.ctrlKey||e.originalEvent.metaKey||e.originalEvent.shiftKey)this.ignoreMissingWhich=!0;else if(!this.ignoreMissingWhich)return this._mouseUp(e)}return(e.which||e.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(e),e.preventDefault()):(this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,e),this._mouseStarted?this._mouseDrag(e):this._mouseUp(e)),!this._mouseStarted)},_mouseUp:function(e){this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,e.target===this._mouseDownEvent.target&&t.data(e.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(e)),this._mouseDelayTimer&&(clearTimeout(this._mouseDelayTimer),delete this._mouseDelayTimer),this.ignoreMissingWhich=!1,f=!1,e.preventDefault()},_mouseDistanceMet:function(t){return Math.max(Math.abs(this._mouseDownEvent.pageX-t.pageX),Math.abs(this._mouseDownEvent.pageY-t.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}}),t.ui.plugin={add:function(e,i,n){var s,o=t.ui[e].prototype;for(s in n)o.plugins[s]=o.plugins[s]||[],o.plugins[s].push([i,n[s]])},call:function(t,e,i,n){var s,o=t.plugins[e];if(o&&(n||t.element[0].parentNode&&11!==t.element[0].parentNode.nodeType))for(s=0;o.length>s;s++)t.options[o[s][0]]&&o[s][1].apply(t.element,i)}},t.ui.safeBlur=function(e){e&&"body"!==e.nodeName.toLowerCase()&&t(e).trigger("blur")},t.widget("ui.draggable",t.ui.mouse,{version:"1.12.1",widgetEventPrefix:"drag",options:{addClasses:!0,appendTo:"parent",axis:!1,connectToSortable:!1,containment:!1,cursor:"auto",cursorAt:!1,grid:!1,handle:!1,helper:"original",iframeFix:!1,opacity:!1,refreshPositions:!1,revert:!1,revertDuration:500,scope:"default",scroll:!0,scrollSensitivity:20,scrollSpeed:20,snap:!1,snapMode:"both",snapTolerance:20,stack:!1,zIndex:!1,drag:null,start:null,stop:null},_create:function(){"original"===this.options.helper&&this._setPositionRelative(),this.options.addClasses&&this._addClass("ui-draggable"),this._setHandleClassName(),this._mouseInit()},_setOption:function(t,e){this._super(t,e),"handle"===t&&(this._removeHandleClassName(),this._setHandleClassName())},_destroy:function(){return(this.helper||this.element).is(".ui-draggable-dragging")?void(this.destroyOnClear=!0):(this._removeHandleClassName(),void this._mouseDestroy())},_mouseCapture:function(e){var i=this.options;return!(this.helper||i.disabled||t(e.target).closest(".ui-resizable-handle").length>0)&&(this.handle=this._getHandle(e),!!this.handle&&(this._blurActiveElement(e),this._blockFrames(!0===i.iframeFix?"iframe":i.iframeFix),!0))},_blockFrames:function(e){this.iframeBlocks=this.document.find(e).map(function(){var e=t(this);return t("<div>").css("position","absolute").appendTo(e.parent()).outerWidth(e.outerWidth()).outerHeight(e.outerHeight()).offset(e.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_blurActiveElement:function(e){var i=t.ui.safeActiveElement(this.document[0]);t(e.target).closest(i).length||t.ui.safeBlur(i)},_mouseStart:function(e){var i=this.options;return this.helper=this._createHelper(e),this._addClass(this.helper,"ui-draggable-dragging"),this._cacheHelperProportions(),t.ui.ddmanager&&(t.ui.ddmanager.current=this),this._cacheMargins(),this.cssPosition=this.helper.css("position"),this.scrollParent=this.helper.scrollParent(!0),this.offsetParent=this.helper.offsetParent(),this.hasFixedAncestor=this.helper.parents().filter(function(){return"fixed"===t(this).css("position")}).length>0,this.positionAbs=this.element.offset(),this._refreshOffsets(e),this.originalPosition=this.position=this._generatePosition(e,!1),this.originalPageX=e.pageX,this.originalPageY=e.pageY,i.cursorAt&&this._adjustOffsetFromHelper(i.cursorAt),this._setContainment(),!1===this._trigger("start",e)?(this._clear(),!1):(this._cacheHelperProportions(),t.ui.ddmanager&&!i.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this._mouseDrag(e,!0),t.ui.ddmanager&&t.ui.ddmanager.dragStart(this,e),!0)},_refreshOffsets:function(t){this.offset={top:this.positionAbs.top-this.margins.top,left:this.positionAbs.left-this.margins.left,scroll:!1,parent:this._getParentOffset(),relative:this._getRelativeOffset()},this.offset.click={left:t.pageX-this.offset.left,top:t.pageY-this.offset.top}},_mouseDrag:function(e,i){if(this.hasFixedAncestor&&(this.offset.parent=this._getParentOffset()),this.position=this._generatePosition(e,!0),this.positionAbs=this._convertPositionTo("absolute"),!i){var n=this._uiHash();if(!1===this._trigger("drag",e,n))return this._mouseUp(new t.Event("mouseup",e)),!1;this.position=n.position}return this.helper[0].style.left=this.position.left+"px",this.helper[0].style.top=this.position.top+"px",t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),!1},_mouseStop:function(e){var i=this,n=!1;return t.ui.ddmanager&&!this.options.dropBehaviour&&(n=t.ui.ddmanager.drop(this,e)),this.dropped&&(n=this.dropped,this.dropped=!1),"invalid"===this.options.revert&&!n||"valid"===this.options.revert&&n||!0===this.options.revert||t.isFunction(this.options.revert)&&this.options.revert.call(this.element,n)?t(this.helper).animate(this.originalPosition,parseInt(this.options.revertDuration,10),function(){!1!==i._trigger("stop",e)&&i._clear()}):!1!==this._trigger("stop",e)&&this._clear(),!1},_mouseUp:function(e){return this._unblockFrames(),t.ui.ddmanager&&t.ui.ddmanager.dragStop(this,e),this.handleElement.is(e.target)&&this.element.trigger("focus"),t.ui.mouse.prototype._mouseUp.call(this,e)},cancel:function(){return this.helper.is(".ui-draggable-dragging")?this._mouseUp(new t.Event("mouseup",{target:this.element[0]})):this._clear(),this},_getHandle:function(e){return!this.options.handle||!!t(e.target).closest(this.element.find(this.options.handle)).length},_setHandleClassName:function(){this.handleElement=this.options.handle?this.element.find(this.options.handle):this.element,this._addClass(this.handleElement,"ui-draggable-handle")},_removeHandleClassName:function(){this._removeClass(this.handleElement,"ui-draggable-handle")},_createHelper:function(e){var i=this.options,n=t.isFunction(i.helper),s=n?t(i.helper.apply(this.element[0],[e])):"clone"===i.helper?this.element.clone().removeAttr("id"):this.element;return s.parents("body").length||s.appendTo("parent"===i.appendTo?this.element[0].parentNode:i.appendTo),n&&s[0]===this.element[0]&&this._setPositionRelative(),s[0]===this.element[0]||/(fixed|absolute)/.test(s.css("position"))||s.css("position","absolute"),s},_setPositionRelative:function(){/^(?:r|a|f)/.test(this.element.css("position"))||(this.element[0].style.position="relative")},_adjustOffsetFromHelper:function(e){"string"==typeof e&&(e=e.split(" ")),t.isArray(e)&&(e={left:+e[0],top:+e[1]||0}),"left"in e&&(this.offset.click.left=e.left+this.margins.left),"right"in e&&(this.offset.click.left=this.helperProportions.width-e.right+this.margins.left),"top"in e&&(this.offset.click.top=e.top+this.margins.top),"bottom"in e&&(this.offset.click.top=this.helperProportions.height-e.bottom+this.margins.top)},_isRootNode:function(t){return/(html|body)/i.test(t.tagName)||t===this.document[0]},_getParentOffset:function(){var e=this.offsetParent.offset(),i=this.document[0];return"absolute"===this.cssPosition&&this.scrollParent[0]!==i&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),this._isRootNode(this.offsetParent[0])&&(e={top:0,left:0}),{top:e.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"!==this.cssPosition)return{top:0,left:0};var t=this.element.position(),e=this._isRootNode(this.scrollParent[0]);return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+(e?0:this.scrollParent.scrollTop()),left:t.left-(parseInt(this.helper.css("left"),10)||0)+(e?0:this.scrollParent.scrollLeft())}},_cacheMargins:function(){this.margins={left:parseInt(this.element.css("marginLeft"),10)||0,top:parseInt(this.element.css("marginTop"),10)||0,right:parseInt(this.element.css("marginRight"),10)||0,bottom:parseInt(this.element.css("marginBottom"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i,n,s=this.options,o=this.document[0];return this.relativeContainer=null,s.containment?"window"===s.containment?void(this.containment=[t(window).scrollLeft()-this.offset.relative.left-this.offset.parent.left,t(window).scrollTop()-this.offset.relative.top-this.offset.parent.top,t(window).scrollLeft()+t(window).width()-this.helperProportions.width-this.margins.left,t(window).scrollTop()+(t(window).height()||o.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]):"document"===s.containment?void(this.containment=[0,0,t(o).width()-this.helperProportions.width-this.margins.left,(t(o).height()||o.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]):s.containment.constructor===Array?void(this.containment=s.containment):("parent"===s.containment&&(s.containment=this.helper[0].parentNode),void((n=(i=t(s.containment))[0])&&(e=/(scroll|auto)/.test(i.css("overflow")),this.containment=[(parseInt(i.css("borderLeftWidth"),10)||0)+(parseInt(i.css("paddingLeft"),10)||0),(parseInt(i.css("borderTopWidth"),10)||0)+(parseInt(i.css("paddingTop"),10)||0),(e?Math.max(n.scrollWidth,n.offsetWidth):n.offsetWidth)-(parseInt(i.css("borderRightWidth"),10)||0)-(parseInt(i.css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left-this.margins.right,(e?Math.max(n.scrollHeight,n.offsetHeight):n.offsetHeight)-(parseInt(i.css("borderBottomWidth"),10)||0)-(parseInt(i.css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top-this.margins.bottom],this.relativeContainer=i))):void(this.containment=null)},_convertPositionTo:function(t,e){e||(e=this.position);var i="absolute"===t?1:-1,n=this._isRootNode(this.scrollParent[0]);return{top:e.top+this.offset.relative.top*i+this.offset.parent.top*i-("fixed"===this.cssPosition?-this.offset.scroll.top:n?0:this.offset.scroll.top)*i,left:e.left+this.offset.relative.left*i+this.offset.parent.left*i-("fixed"===this.cssPosition?-this.offset.scroll.left:n?0:this.offset.scroll.left)*i}},_generatePosition:function(t,e){var i,n,s,o,a=this.options,r=this._isRootNode(this.scrollParent[0]),l=t.pageX,h=t.pageY;return r&&this.offset.scroll||(this.offset.scroll={top:this.scrollParent.scrollTop(),left:this.scrollParent.scrollLeft()}),e&&(this.containment&&(this.relativeContainer?(n=this.relativeContainer.offset(),i=[this.containment[0]+n.left,this.containment[1]+n.top,this.containment[2]+n.left,this.containment[3]+n.top]):i=this.containment,t.pageX-this.offset.click.left<i[0]&&(l=i[0]+this.offset.click.left),t.pageY-this.offset.click.top<i[1]&&(h=i[1]+this.offset.click.top),t.pageX-this.offset.click.left>i[2]&&(l=i[2]+this.offset.click.left),t.pageY-this.offset.click.top>i[3]&&(h=i[3]+this.offset.click.top)),a.grid&&(s=a.grid[1]?this.originalPageY+Math.round((h-this.originalPageY)/a.grid[1])*a.grid[1]:this.originalPageY,h=i?s-this.offset.click.top>=i[1]||s-this.offset.click.top>i[3]?s:s-this.offset.click.top>=i[1]?s-a.grid[1]:s+a.grid[1]:s,o=a.grid[0]?this.originalPageX+Math.round((l-this.originalPageX)/a.grid[0])*a.grid[0]:this.originalPageX,l=i?o-this.offset.click.left>=i[0]||o-this.offset.click.left>i[2]?o:o-this.offset.click.left>=i[0]?o-a.grid[0]:o+a.grid[0]:o),"y"===a.axis&&(l=this.originalPageX),"x"===a.axis&&(h=this.originalPageY)),{top:h-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.offset.scroll.top:r?0:this.offset.scroll.top),left:l-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.offset.scroll.left:r?0:this.offset.scroll.left)}},_clear:function(){this._removeClass(this.helper,"ui-draggable-dragging"),this.helper[0]===this.element[0]||this.cancelHelperRemoval||this.helper.remove(),this.helper=null,this.cancelHelperRemoval=!1,this.destroyOnClear&&this.destroy()},_trigger:function(e,i,n){return n=n||this._uiHash(),t.ui.plugin.call(this,e,[i,n,this],!0),/^(drag|start|stop)/.test(e)&&(this.positionAbs=this._convertPositionTo("absolute"),n.offset=this.positionAbs),t.Widget.prototype._trigger.call(this,e,i,n)},plugins:{},_uiHash:function(){return{helper:this.helper,position:this.position,originalPosition:this.originalPosition,offset:this.positionAbs}}}),t.ui.plugin.add("draggable","connectToSortable",{start:function(e,i,n){var s=t.extend({},i,{item:n.element});n.sortables=[],t(n.options.connectToSortable).each(function(){var i=t(this).sortable("instance");i&&!i.options.disabled&&(n.sortables.push(i),i.refreshPositions(),i._trigger("activate",e,s))})},stop:function(e,i,n){var s=t.extend({},i,{item:n.element});n.cancelHelperRemoval=!1,t.each(n.sortables,function(){var t=this;t.isOver?(t.isOver=0,n.cancelHelperRemoval=!0,t.cancelHelperRemoval=!1,t._storedCSS={position:t.placeholder.css("position"),top:t.placeholder.css("top"),left:t.placeholder.css("left")},t._mouseStop(e),t.options.helper=t.options._helper):(t.cancelHelperRemoval=!0,t._trigger("deactivate",e,s))})},drag:function(e,i,n){t.each(n.sortables,function(){var s=!1,o=this;o.positionAbs=n.positionAbs,o.helperProportions=n.helperProportions,o.offset.click=n.offset.click,o._intersectsWith(o.containerCache)&&(s=!0,t.each(n.sortables,function(){return this.positionAbs=n.positionAbs,this.helperProportions=n.helperProportions,this.offset.click=n.offset.click,this!==o&&this._intersectsWith(this.containerCache)&&t.contains(o.element[0],this.element[0])&&(s=!1),s})),s?(o.isOver||(o.isOver=1,n._parent=i.helper.parent(),o.currentItem=i.helper.appendTo(o.element).data("ui-sortable-item",!0),o.options._helper=o.options.helper,o.options.helper=function(){return i.helper[0]},e.target=o.currentItem[0],o._mouseCapture(e,!0),o._mouseStart(e,!0,!0),o.offset.click.top=n.offset.click.top,o.offset.click.left=n.offset.click.left,o.offset.parent.left-=n.offset.parent.left-o.offset.parent.left,o.offset.parent.top-=n.offset.parent.top-o.offset.parent.top,n._trigger("toSortable",e),n.dropped=o.element,t.each(n.sortables,function(){this.refreshPositions()}),n.currentItem=n.element,o.fromOutside=n),o.currentItem&&(o._mouseDrag(e),i.position=o.position)):o.isOver&&(o.isOver=0,o.cancelHelperRemoval=!0,o.options._revert=o.options.revert,o.options.revert=!1,o._trigger("out",e,o._uiHash(o)),o._mouseStop(e,!0),o.options.revert=o.options._revert,o.options.helper=o.options._helper,o.placeholder&&o.placeholder.remove(),i.helper.appendTo(n._parent),n._refreshOffsets(e),i.position=n._generatePosition(e,!0),n._trigger("fromSortable",e),n.dropped=!1,t.each(n.sortables,function(){this.refreshPositions()}))})}}),t.ui.plugin.add("draggable","cursor",{start:function(e,i,n){var s=t("body"),o=n.options;s.css("cursor")&&(o._cursor=s.css("cursor")),s.css("cursor",o.cursor)},stop:function(e,i,n){var s=n.options;s._cursor&&t("body").css("cursor",s._cursor)}}),t.ui.plugin.add("draggable","opacity",{start:function(e,i,n){var s=t(i.helper),o=n.options;s.css("opacity")&&(o._opacity=s.css("opacity")),s.css("opacity",o.opacity)},stop:function(e,i,n){var s=n.options;s._opacity&&t(i.helper).css("opacity",s._opacity)}}),t.ui.plugin.add("draggable","scroll",{start:function(t,e,i){i.scrollParentNotHidden||(i.scrollParentNotHidden=i.helper.scrollParent(!1)),i.scrollParentNotHidden[0]!==i.document[0]&&"HTML"!==i.scrollParentNotHidden[0].tagName&&(i.overflowOffset=i.scrollParentNotHidden.offset())},drag:function(e,i,n){var s=n.options,o=!1,a=n.scrollParentNotHidden[0],r=n.document[0];a!==r&&"HTML"!==a.tagName?(s.axis&&"x"===s.axis||(n.overflowOffset.top+a.offsetHeight-e.pageY<s.scrollSensitivity?a.scrollTop=o=a.scrollTop+s.scrollSpeed:e.pageY-n.overflowOffset.top<s.scrollSensitivity&&(a.scrollTop=o=a.scrollTop-s.scrollSpeed)),s.axis&&"y"===s.axis||(n.overflowOffset.left+a.offsetWidth-e.pageX<s.scrollSensitivity?a.scrollLeft=o=a.scrollLeft+s.scrollSpeed:e.pageX-n.overflowOffset.left<s.scrollSensitivity&&(a.scrollLeft=o=a.scrollLeft-s.scrollSpeed))):(s.axis&&"x"===s.axis||(e.pageY-t(r).scrollTop()<s.scrollSensitivity?o=t(r).scrollTop(t(r).scrollTop()-s.scrollSpeed):t(window).height()-(e.pageY-t(r).scrollTop())<s.scrollSensitivity&&(o=t(r).scrollTop(t(r).scrollTop()+s.scrollSpeed))),s.axis&&"y"===s.axis||(e.pageX-t(r).scrollLeft()<s.scrollSensitivity?o=t(r).scrollLeft(t(r).scrollLeft()-s.scrollSpeed):t(window).width()-(e.pageX-t(r).scrollLeft())<s.scrollSensitivity&&(o=t(r).scrollLeft(t(r).scrollLeft()+s.scrollSpeed)))),!1!==o&&t.ui.ddmanager&&!s.dropBehaviour&&t.ui.ddmanager.prepareOffsets(n,e)}}),t.ui.plugin.add("draggable","snap",{start:function(e,i,n){var s=n.options;n.snapElements=[],t(s.snap.constructor!==String?s.snap.items||":data(ui-draggable)":s.snap).each(function(){var e=t(this),i=e.offset();this!==n.element[0]&&n.snapElements.push({item:this,width:e.outerWidth(),height:e.outerHeight(),top:i.top,left:i.left})})},drag:function(e,i,n){var s,o,a,r,l,h,c,u,d,p,f=n.options,g=f.snapTolerance,m=i.offset.left,v=m+n.helperProportions.width,_=i.offset.top,b=_+n.helperProportions.height;for(d=n.snapElements.length-1;d>=0;d--)h=(l=n.snapElements[d].left-n.margins.left)+n.snapElements[d].width,u=(c=n.snapElements[d].top-n.margins.top)+n.snapElements[d].height,l-g>v||m>h+g||c-g>b||_>u+g||!t.contains(n.snapElements[d].item.ownerDocument,n.snapElements[d].item)?(n.snapElements[d].snapping&&n.options.snap.release&&n.options.snap.release.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[d].item})),n.snapElements[d].snapping=!1):("inner"!==f.snapMode&&(s=g>=Math.abs(c-b),o=g>=Math.abs(u-_),a=g>=Math.abs(l-v),r=g>=Math.abs(h-m),s&&(i.position.top=n._convertPositionTo("relative",{top:c-n.helperProportions.height,left:0}).top),o&&(i.position.top=n._convertPositionTo("relative",{top:u,left:0}).top),a&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l-n.helperProportions.width}).left),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:h}).left)),p=s||o||a||r,"outer"!==f.snapMode&&(s=g>=Math.abs(c-_),o=g>=Math.abs(u-b),a=g>=Math.abs(l-m),r=g>=Math.abs(h-v),s&&(i.position.top=n._convertPositionTo("relative",{top:c,left:0}).top),o&&(i.position.top=n._convertPositionTo("relative",{top:u-n.helperProportions.height,left:0}).top),a&&(i.position.left=n._convertPositionTo("relative",{top:0,left:l}).left),r&&(i.position.left=n._convertPositionTo("relative",{top:0,left:h-n.helperProportions.width}).left)),!n.snapElements[d].snapping&&(s||o||a||r||p)&&n.options.snap.snap&&n.options.snap.snap.call(n.element,e,t.extend(n._uiHash(),{snapItem:n.snapElements[d].item})),n.snapElements[d].snapping=s||o||a||r||p)}}),t.ui.plugin.add("draggable","stack",{start:function(e,i,n){var s,o=n.options,a=t.makeArray(t(o.stack)).sort(function(e,i){return(parseInt(t(e).css("zIndex"),10)||0)-(parseInt(t(i).css("zIndex"),10)||0)});a.length&&(s=parseInt(t(a[0]).css("zIndex"),10)||0,t(a).each(function(e){t(this).css("zIndex",s+e)}),this.css("zIndex",s+a.length))}}),t.ui.plugin.add("draggable","zIndex",{start:function(e,i,n){var s=t(i.helper),o=n.options;s.css("zIndex")&&(o._zIndex=s.css("zIndex")),s.css("zIndex",o.zIndex)},stop:function(e,i,n){var s=n.options;s._zIndex&&t(i.helper).css("zIndex",s._zIndex)}}),t.ui.draggable,t.widget("ui.resizable",t.ui.mouse,{version:"1.12.1",widgetEventPrefix:"resize",options:{alsoResize:!1,animate:!1,animateDuration:"slow",animateEasing:"swing",aspectRatio:!1,autoHide:!1,classes:{"ui-resizable-se":"ui-icon ui-icon-gripsmall-diagonal-se"},containment:!1,ghost:!1,grid:!1,handles:"e,s,se",helper:!1,maxHeight:null,maxWidth:null,minHeight:10,minWidth:10,zIndex:90,resize:null,start:null,stop:null},_num:function(t){return parseFloat(t)||0},_isNumber:function(t){return!isNaN(parseFloat(t))},_hasScroll:function(e,i){if("hidden"===t(e).css("overflow"))return!1;var n=i&&"left"===i?"scrollLeft":"scrollTop",s=!1;return e[n]>0||(e[n]=1,s=e[n]>0,e[n]=0,s)},_create:function(){var e,i=this.options,n=this;this._addClass("ui-resizable"),t.extend(this,{_aspectRatio:!!i.aspectRatio,aspectRatio:i.aspectRatio,originalElement:this.element,_proportionallyResizeElements:[],_helper:i.helper||i.ghost||i.animate?i.helper||"ui-resizable-helper":null}),this.element[0].nodeName.match(/^(canvas|textarea|input|select|button|img)$/i)&&(this.element.wrap(t("<div class='ui-wrapper' style='overflow: hidden;'></div>").css({position:this.element.css("position"),width:this.element.outerWidth(),height:this.element.outerHeight(),top:this.element.css("top"),left:this.element.css("left")})),this.element=this.element.parent().data("ui-resizable",this.element.resizable("instance")),this.elementIsWrapper=!0,e={marginTop:this.originalElement.css("marginTop"),marginRight:this.originalElement.css("marginRight"),marginBottom:this.originalElement.css("marginBottom"),marginLeft:this.originalElement.css("marginLeft")},this.element.css(e),this.originalElement.css("margin",0),this.originalResizeStyle=this.originalElement.css("resize"),this.originalElement.css("resize","none"),this._proportionallyResizeElements.push(this.originalElement.css({position:"static",zoom:1,display:"block"})),this.originalElement.css(e),this._proportionallyResize()),this._setupHandles(),i.autoHide&&t(this.element).on("mouseenter",function(){i.disabled||(n._removeClass("ui-resizable-autohide"),n._handles.show())}).on("mouseleave",function(){i.disabled||n.resizing||(n._addClass("ui-resizable-autohide"),n._handles.hide())}),this._mouseInit()},_destroy:function(){this._mouseDestroy();var e,i=function(e){t(e).removeData("resizable").removeData("ui-resizable").off(".resizable").find(".ui-resizable-handle").remove()};return this.elementIsWrapper&&(i(this.element),e=this.element,this.originalElement.css({position:e.css("position"),width:e.outerWidth(),height:e.outerHeight(),top:e.css("top"),left:e.css("left")}).insertAfter(e),e.remove()),this.originalElement.css("resize",this.originalResizeStyle),i(this.originalElement),this},_setOption:function(t,e){switch(this._super(t,e),t){case"handles":this._removeHandles(),this._setupHandles()}},_setupHandles:function(){var e,i,n,s,o,a=this.options,r=this;if(this.handles=a.handles||(t(".ui-resizable-handle",this.element).length?{n:".ui-resizable-n",e:".ui-resizable-e",s:".ui-resizable-s",w:".ui-resizable-w",se:".ui-resizable-se",sw:".ui-resizable-sw",ne:".ui-resizable-ne",nw:".ui-resizable-nw"}:"e,s,se"),this._handles=t(),this.handles.constructor===String)for("all"===this.handles&&(this.handles="n,e,s,w,se,sw,ne,nw"),n=this.handles.split(","),this.handles={},i=0;n.length>i;i++)s="ui-resizable-"+(e=t.trim(n[i])),o=t("<div>"),this._addClass(o,"ui-resizable-handle "+s),o.css({zIndex:a.zIndex}),this.handles[e]=".ui-resizable-"+e,this.element.append(o);this._renderAxis=function(e){var i,n,s,o;for(i in e=e||this.element,this.handles)this.handles[i].constructor===String?this.handles[i]=this.element.children(this.handles[i]).first().show():(this.handles[i].jquery||this.handles[i].nodeType)&&(this.handles[i]=t(this.handles[i]),this._on(this.handles[i],{mousedown:r._mouseDown})),this.elementIsWrapper&&this.originalElement[0].nodeName.match(/^(textarea|input|select|button)$/i)&&(n=t(this.handles[i],this.element),o=/sw|ne|nw|se|n|s/.test(i)?n.outerHeight():n.outerWidth(),s=["padding",/ne|nw|n/.test(i)?"Top":/se|sw|s/.test(i)?"Bottom":/^e$/.test(i)?"Right":"Left"].join(""),e.css(s,o),this._proportionallyResize()),this._handles=this._handles.add(this.handles[i])},this._renderAxis(this.element),this._handles=this._handles.add(this.element.find(".ui-resizable-handle")),this._handles.disableSelection(),this._handles.on("mouseover",function(){r.resizing||(this.className&&(o=this.className.match(/ui-resizable-(se|sw|ne|nw|n|e|s|w)/i)),r.axis=o&&o[1]?o[1]:"se")}),a.autoHide&&(this._handles.hide(),this._addClass("ui-resizable-autohide"))},_removeHandles:function(){this._handles.remove()},_mouseCapture:function(e){var i,n,s=!1;for(i in this.handles)((n=t(this.handles[i])[0])===e.target||t.contains(n,e.target))&&(s=!0);return!this.options.disabled&&s},_mouseStart:function(e){var i,n,s,o=this.options,a=this.element;return this.resizing=!0,this._renderProxy(),i=this._num(this.helper.css("left")),n=this._num(this.helper.css("top")),o.containment&&(i+=t(o.containment).scrollLeft()||0,n+=t(o.containment).scrollTop()||0),this.offset=this.helper.offset(),this.position={left:i,top:n},this.size=this._helper?{width:this.helper.width(),height:this.helper.height()}:{width:a.width(),height:a.height()},this.originalSize=this._helper?{width:a.outerWidth(),height:a.outerHeight()}:{width:a.width(),height:a.height()},this.sizeDiff={width:a.outerWidth()-a.width(),height:a.outerHeight()-a.height()},this.originalPosition={left:i,top:n},this.originalMousePosition={left:e.pageX,top:e.pageY},this.aspectRatio="number"==typeof o.aspectRatio?o.aspectRatio:this.originalSize.width/this.originalSize.height||1,s=t(".ui-resizable-"+this.axis).css("cursor"),t("body").css("cursor","auto"===s?this.axis+"-resize":s),this._addClass("ui-resizable-resizing"),this._propagate("start",e),!0},_mouseDrag:function(e){var i,n,s=this.originalMousePosition,o=this.axis,a=e.pageX-s.left||0,r=e.pageY-s.top||0,l=this._change[o];return this._updatePrevProperties(),!!l&&(i=l.apply(this,[e,a,r]),this._updateVirtualBoundaries(e.shiftKey),(this._aspectRatio||e.shiftKey)&&(i=this._updateRatio(i,e)),i=this._respectSize(i,e),this._updateCache(i),this._propagate("resize",e),n=this._applyChanges(),!this._helper&&this._proportionallyResizeElements.length&&this._proportionallyResize(),t.isEmptyObject(n)||(this._updatePrevProperties(),this._trigger("resize",e,this.ui()),this._applyChanges()),!1)},_mouseStop:function(e){this.resizing=!1;var i,n,s,o,a,r,l,h=this.options,c=this;return this._helper&&(s=(n=(i=this._proportionallyResizeElements).length&&/textarea/i.test(i[0].nodeName))&&this._hasScroll(i[0],"left")?0:c.sizeDiff.height,o=n?0:c.sizeDiff.width,a={width:c.helper.width()-o,height:c.helper.height()-s},r=parseFloat(c.element.css("left"))+(c.position.left-c.originalPosition.left)||null,l=parseFloat(c.element.css("top"))+(c.position.top-c.originalPosition.top)||null,h.animate||this.element.css(t.extend(a,{top:l,left:r})),c.helper.height(c.size.height),c.helper.width(c.size.width),this._helper&&!h.animate&&this._proportionallyResize()),t("body").css("cursor","auto"),this._removeClass("ui-resizable-resizing"),this._propagate("stop",e),this._helper&&this.helper.remove(),!1},_updatePrevProperties:function(){this.prevPosition={top:this.position.top,left:this.position.left},this.prevSize={width:this.size.width,height:this.size.height}},_applyChanges:function(){var t={};return this.position.top!==this.prevPosition.top&&(t.top=this.position.top+"px"),this.position.left!==this.prevPosition.left&&(t.left=this.position.left+"px"),this.size.width!==this.prevSize.width&&(t.width=this.size.width+"px"),this.size.height!==this.prevSize.height&&(t.height=this.size.height+"px"),this.helper.css(t),t},_updateVirtualBoundaries:function(t){var e,i,n,s,o,a=this.options;o={minWidth:this._isNumber(a.minWidth)?a.minWidth:0,maxWidth:this._isNumber(a.maxWidth)?a.maxWidth:1/0,minHeight:this._isNumber(a.minHeight)?a.minHeight:0,maxHeight:this._isNumber(a.maxHeight)?a.maxHeight:1/0},(this._aspectRatio||t)&&(e=o.minHeight*this.aspectRatio,n=o.minWidth/this.aspectRatio,i=o.maxHeight*this.aspectRatio,s=o.maxWidth/this.aspectRatio,e>o.minWidth&&(o.minWidth=e),n>o.minHeight&&(o.minHeight=n),o.maxWidth>i&&(o.maxWidth=i),o.maxHeight>s&&(o.maxHeight=s)),this._vBoundaries=o},_updateCache:function(t){this.offset=this.helper.offset(),this._isNumber(t.left)&&(this.position.left=t.left),this._isNumber(t.top)&&(this.position.top=t.top),this._isNumber(t.height)&&(this.size.height=t.height),this._isNumber(t.width)&&(this.size.width=t.width)},_updateRatio:function(t){var e=this.position,i=this.size,n=this.axis;return this._isNumber(t.height)?t.width=t.height*this.aspectRatio:this._isNumber(t.width)&&(t.height=t.width/this.aspectRatio),"sw"===n&&(t.left=e.left+(i.width-t.width),t.top=null),"nw"===n&&(t.top=e.top+(i.height-t.height),t.left=e.left+(i.width-t.width)),t},_respectSize:function(t){var e=this._vBoundaries,i=this.axis,n=this._isNumber(t.width)&&e.maxWidth&&e.maxWidth<t.width,s=this._isNumber(t.height)&&e.maxHeight&&e.maxHeight<t.height,o=this._isNumber(t.width)&&e.minWidth&&e.minWidth>t.width,a=this._isNumber(t.height)&&e.minHeight&&e.minHeight>t.height,r=this.originalPosition.left+this.originalSize.width,l=this.originalPosition.top+this.originalSize.height,h=/sw|nw|w/.test(i),c=/nw|ne|n/.test(i);return o&&(t.width=e.minWidth),a&&(t.height=e.minHeight),n&&(t.width=e.maxWidth),s&&(t.height=e.maxHeight),o&&h&&(t.left=r-e.minWidth),n&&h&&(t.left=r-e.maxWidth),a&&c&&(t.top=l-e.minHeight),s&&c&&(t.top=l-e.maxHeight),t.width||t.height||t.left||!t.top?t.width||t.height||t.top||!t.left||(t.left=null):t.top=null,t},_getPaddingPlusBorderDimensions:function(t){for(var e=0,i=[],n=[t.css("borderTopWidth"),t.css("borderRightWidth"),t.css("borderBottomWidth"),t.css("borderLeftWidth")],s=[t.css("paddingTop"),t.css("paddingRight"),t.css("paddingBottom"),t.css("paddingLeft")];4>e;e++)i[e]=parseFloat(n[e])||0,i[e]+=parseFloat(s[e])||0;return{height:i[0]+i[2],width:i[1]+i[3]}},_proportionallyResize:function(){if(this._proportionallyResizeElements.length)for(var t,e=0,i=this.helper||this.element;this._proportionallyResizeElements.length>e;e++)t=this._proportionallyResizeElements[e],this.outerDimensions||(this.outerDimensions=this._getPaddingPlusBorderDimensions(t)),t.css({height:i.height()-this.outerDimensions.height||0,width:i.width()-this.outerDimensions.width||0})},_renderProxy:function(){var e=this.element,i=this.options;this.elementOffset=e.offset(),this._helper?(this.helper=this.helper||t("<div style='overflow:hidden;'></div>"),this._addClass(this.helper,this._helper),this.helper.css({width:this.element.outerWidth(),height:this.element.outerHeight(),position:"absolute",left:this.elementOffset.left+"px",top:this.elementOffset.top+"px",zIndex:++i.zIndex}),this.helper.appendTo("body").disableSelection()):this.helper=this.element},_change:{e:function(t,e){return{width:this.originalSize.width+e}},w:function(t,e){var i=this.originalSize;return{left:this.originalPosition.left+e,width:i.width-e}},n:function(t,e,i){var n=this.originalSize;return{top:this.originalPosition.top+i,height:n.height-i}},s:function(t,e,i){return{height:this.originalSize.height+i}},se:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},sw:function(e,i,n){return t.extend(this._change.s.apply(this,arguments),this._change.w.apply(this,[e,i,n]))},ne:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.e.apply(this,[e,i,n]))},nw:function(e,i,n){return t.extend(this._change.n.apply(this,arguments),this._change.w.apply(this,[e,i,n]))}},_propagate:function(e,i){t.ui.plugin.call(this,e,[i,this.ui()]),"resize"!==e&&this._trigger(e,i,this.ui())},plugins:{},ui:function(){return{originalElement:this.originalElement,element:this.element,helper:this.helper,position:this.position,size:this.size,originalSize:this.originalSize,originalPosition:this.originalPosition}}}),t.ui.plugin.add("resizable","animate",{stop:function(e){var i=t(this).resizable("instance"),n=i.options,s=i._proportionallyResizeElements,o=s.length&&/textarea/i.test(s[0].nodeName),a=o&&i._hasScroll(s[0],"left")?0:i.sizeDiff.height,r=o?0:i.sizeDiff.width,l={width:i.size.width-r,height:i.size.height-a},h=parseFloat(i.element.css("left"))+(i.position.left-i.originalPosition.left)||null,c=parseFloat(i.element.css("top"))+(i.position.top-i.originalPosition.top)||null;i.element.animate(t.extend(l,c&&h?{top:c,left:h}:{}),{duration:n.animateDuration,easing:n.animateEasing,step:function(){var n={width:parseFloat(i.element.css("width")),height:parseFloat(i.element.css("height")),top:parseFloat(i.element.css("top")),left:parseFloat(i.element.css("left"))};s&&s.length&&t(s[0]).css({width:n.width,height:n.height}),i._updateCache(n),i._propagate("resize",e)}})}}),t.ui.plugin.add("resizable","containment",{start:function(){var e,i,n,s,o,a,r,l=t(this).resizable("instance"),h=l.options,c=l.element,u=h.containment,d=u instanceof t?u.get(0):/parent/.test(u)?c.parent().get(0):u;d&&(l.containerElement=t(d),/document/.test(u)||u===document?(l.containerOffset={left:0,top:0},l.containerPosition={left:0,top:0},l.parentData={element:t(document),left:0,top:0,width:t(document).width(),height:t(document).height()||document.body.parentNode.scrollHeight}):(e=t(d),i=[],t(["Top","Right","Left","Bottom"]).each(function(t,n){i[t]=l._num(e.css("padding"+n))}),l.containerOffset=e.offset(),l.containerPosition=e.position(),l.containerSize={height:e.innerHeight()-i[3],width:e.innerWidth()-i[1]},n=l.containerOffset,s=l.containerSize.height,o=l.containerSize.width,a=l._hasScroll(d,"left")?d.scrollWidth:o,r=l._hasScroll(d)?d.scrollHeight:s,l.parentData={element:d,left:n.left,top:n.top,width:a,height:r}))},resize:function(e){var i,n,s,o,a=t(this).resizable("instance"),r=a.options,l=a.containerOffset,h=a.position,c=a._aspectRatio||e.shiftKey,u={top:0,left:0},d=a.containerElement,p=!0;d[0]!==document&&/static/.test(d.css("position"))&&(u=l),h.left<(a._helper?l.left:0)&&(a.size.width=a.size.width+(a._helper?a.position.left-l.left:a.position.left-u.left),c&&(a.size.height=a.size.width/a.aspectRatio,p=!1),a.position.left=r.helper?l.left:0),h.top<(a._helper?l.top:0)&&(a.size.height=a.size.height+(a._helper?a.position.top-l.top:a.position.top),c&&(a.size.width=a.size.height*a.aspectRatio,p=!1),a.position.top=a._helper?l.top:0),s=a.containerElement.get(0)===a.element.parent().get(0),o=/relative|absolute/.test(a.containerElement.css("position")),s&&o?(a.offset.left=a.parentData.left+a.position.left,a.offset.top=a.parentData.top+a.position.top):(a.offset.left=a.element.offset().left,a.offset.top=a.element.offset().top),i=Math.abs(a.sizeDiff.width+(a._helper?a.offset.left-u.left:a.offset.left-l.left)),n=Math.abs(a.sizeDiff.height+(a._helper?a.offset.top-u.top:a.offset.top-l.top)),i+a.size.width>=a.parentData.width&&(a.size.width=a.parentData.width-i,c&&(a.size.height=a.size.width/a.aspectRatio,p=!1)),n+a.size.height>=a.parentData.height&&(a.size.height=a.parentData.height-n,c&&(a.size.width=a.size.height*a.aspectRatio,p=!1)),p||(a.position.left=a.prevPosition.left,a.position.top=a.prevPosition.top,a.size.width=a.prevSize.width,a.size.height=a.prevSize.height)},stop:function(){var e=t(this).resizable("instance"),i=e.options,n=e.containerOffset,s=e.containerPosition,o=e.containerElement,a=t(e.helper),r=a.offset(),l=a.outerWidth()-e.sizeDiff.width,h=a.outerHeight()-e.sizeDiff.height;e._helper&&!i.animate&&/relative/.test(o.css("position"))&&t(this).css({left:r.left-s.left-n.left,width:l,height:h}),e._helper&&!i.animate&&/static/.test(o.css("position"))&&t(this).css({left:r.left-s.left-n.left,width:l,height:h})}}),t.ui.plugin.add("resizable","alsoResize",{start:function(){var e=t(this).resizable("instance").options;t(e.alsoResize).each(function(){var e=t(this);e.data("ui-resizable-alsoresize",{width:parseFloat(e.width()),height:parseFloat(e.height()),left:parseFloat(e.css("left")),top:parseFloat(e.css("top"))})})},resize:function(e,i){var n=t(this).resizable("instance"),s=n.options,o=n.originalSize,a=n.originalPosition,r={height:n.size.height-o.height||0,width:n.size.width-o.width||0,top:n.position.top-a.top||0,left:n.position.left-a.left||0};t(s.alsoResize).each(function(){var e=t(this),n=t(this).data("ui-resizable-alsoresize"),s={},o=e.parents(i.originalElement[0]).length?["width","height"]:["width","height","top","left"];t.each(o,function(t,e){var i=(n[e]||0)+(r[e]||0);i&&i>=0&&(s[e]=i||null)}),e.css(s)})},stop:function(){t(this).removeData("ui-resizable-alsoresize")}}),t.ui.plugin.add("resizable","ghost",{start:function(){var e=t(this).resizable("instance"),i=e.size;e.ghost=e.originalElement.clone(),e.ghost.css({opacity:.25,display:"block",position:"relative",height:i.height,width:i.width,margin:0,left:0,top:0}),e._addClass(e.ghost,"ui-resizable-ghost"),!1!==t.uiBackCompat&&"string"==typeof e.options.ghost&&e.ghost.addClass(this.options.ghost),e.ghost.appendTo(e.helper)},resize:function(){var e=t(this).resizable("instance");e.ghost&&e.ghost.css({position:"relative",height:e.size.height,width:e.size.width})},stop:function(){var e=t(this).resizable("instance");e.ghost&&e.helper&&e.helper.get(0).removeChild(e.ghost.get(0))}}),t.ui.plugin.add("resizable","grid",{resize:function(){var e,i=t(this).resizable("instance"),n=i.options,s=i.size,o=i.originalSize,a=i.originalPosition,r=i.axis,l="number"==typeof n.grid?[n.grid,n.grid]:n.grid,h=l[0]||1,c=l[1]||1,u=Math.round((s.width-o.width)/h)*h,d=Math.round((s.height-o.height)/c)*c,p=o.width+u,f=o.height+d,g=n.maxWidth&&p>n.maxWidth,m=n.maxHeight&&f>n.maxHeight,v=n.minWidth&&n.minWidth>p,_=n.minHeight&&n.minHeight>f;n.grid=l,v&&(p+=h),_&&(f+=c),g&&(p-=h),m&&(f-=c),/^(se|s|e)$/.test(r)?(i.size.width=p,i.size.height=f):/^(ne)$/.test(r)?(i.size.width=p,i.size.height=f,i.position.top=a.top-d):/^(sw)$/.test(r)?(i.size.width=p,i.size.height=f,i.position.left=a.left-u):((0>=f-c||0>=p-h)&&(e=i._getPaddingPlusBorderDimensions(this)),f-c>0?(i.size.height=f,i.position.top=a.top-d):(f=c-e.height,i.size.height=f,i.position.top=a.top+o.height-f),p-h>0?(i.size.width=p,i.position.left=a.left-u):(p=h-e.width,i.size.width=p,i.position.left=a.left+o.width-p))}}),t.ui.resizable,t.widget("ui.dialog",{version:"1.12.1",options:{appendTo:"body",autoOpen:!0,buttons:[],classes:{"ui-dialog":"ui-corner-all","ui-dialog-titlebar":"ui-corner-all"},closeOnEscape:!0,closeText:"Close",draggable:!0,hide:null,height:"auto",maxHeight:null,maxWidth:null,minHeight:150,minWidth:150,modal:!1,position:{my:"center",at:"center",of:window,collision:"fit",using:function(e){var i=t(this).css(e).offset().top;0>i&&t(this).css("top",e.top-i)}},resizable:!0,show:null,title:null,width:300,beforeClose:null,close:null,drag:null,dragStart:null,dragStop:null,focus:null,open:null,resize:null,resizeStart:null,resizeStop:null},sizeRelatedOptions:{buttons:!0,height:!0,maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0,width:!0},resizableRelatedOptions:{maxHeight:!0,maxWidth:!0,minHeight:!0,minWidth:!0},_create:function(){this.originalCss={display:this.element[0].style.display,width:this.element[0].style.width,minHeight:this.element[0].style.minHeight,maxHeight:this.element[0].style.maxHeight,height:this.element[0].style.height},this.originalPosition={parent:this.element.parent(),index:this.element.parent().children().index(this.element)},this.originalTitle=this.element.attr("title"),null==this.options.title&&null!=this.originalTitle&&(this.options.title=this.originalTitle),this.options.disabled&&(this.options.disabled=!1),this._createWrapper(),this.element.show().removeAttr("title").appendTo(this.uiDialog),this._addClass("ui-dialog-content","ui-widget-content"),this._createTitlebar(),this._createButtonPane(),this.options.draggable&&t.fn.draggable&&this._makeDraggable(),this.options.resizable&&t.fn.resizable&&this._makeResizable(),this._isOpen=!1,this._trackFocus()},_init:function(){this.options.autoOpen&&this.open()},_appendTo:function(){var e=this.options.appendTo;return e&&(e.jquery||e.nodeType)?t(e):this.document.find(e||"body").eq(0)},_destroy:function(){var t,e=this.originalPosition;this._untrackInstance(),this._destroyOverlay(),this.element.removeUniqueId().css(this.originalCss).detach(),this.uiDialog.remove(),this.originalTitle&&this.element.attr("title",this.originalTitle),(t=e.parent.children().eq(e.index)).length&&t[0]!==this.element[0]?t.before(this.element):e.parent.append(this.element)},widget:function(){return this.uiDialog},disable:t.noop,enable:t.noop,close:function(e){var i=this;this._isOpen&&!1!==this._trigger("beforeClose",e)&&(this._isOpen=!1,this._focusedElement=null,this._destroyOverlay(),this._untrackInstance(),this.opener.filter(":focusable").trigger("focus").length||t.ui.safeBlur(t.ui.safeActiveElement(this.document[0])),this._hide(this.uiDialog,this.options.hide,function(){i._trigger("close",e)}))},isOpen:function(){return this._isOpen},moveToTop:function(){this._moveToTop()},_moveToTop:function(e,i){var n=!1,s=this.uiDialog.siblings(".ui-front:visible").map(function(){return+t(this).css("z-index")}).get(),o=Math.max.apply(null,s);return o>=+this.uiDialog.css("z-index")&&(this.uiDialog.css("z-index",o+1),n=!0),n&&!i&&this._trigger("focus",e),n},open:function(){var e=this;return this._isOpen?void(this._moveToTop()&&this._focusTabbable()):(this._isOpen=!0,this.opener=t(t.ui.safeActiveElement(this.document[0])),this._size(),this._position(),this._createOverlay(),this._moveToTop(null,!0),this.overlay&&this.overlay.css("z-index",this.uiDialog.css("z-index")-1),this._show(this.uiDialog,this.options.show,function(){e._focusTabbable(),e._trigger("focus")}),this._makeFocusTarget(),void this._trigger("open"))},_focusTabbable:function(){var t=this._focusedElement;t||(t=this.element.find("[autofocus]")),t.length||(t=this.element.find(":tabbable")),t.length||(t=this.uiDialogButtonPane.find(":tabbable")),t.length||(t=this.uiDialogTitlebarClose.filter(":tabbable")),t.length||(t=this.uiDialog),t.eq(0).trigger("focus")},_keepFocus:function(e){function i(){var e=t.ui.safeActiveElement(this.document[0]);this.uiDialog[0]===e||t.contains(this.uiDialog[0],e)||this._focusTabbable()}e.preventDefault(),i.call(this),this._delay(i)},_createWrapper:function(){this.uiDialog=t("<div>").hide().attr({tabIndex:-1,role:"dialog"}).appendTo(this._appendTo()),this._addClass(this.uiDialog,"ui-dialog","ui-widget ui-widget-content ui-front"),this._on(this.uiDialog,{keydown:function(e){if(this.options.closeOnEscape&&!e.isDefaultPrevented()&&e.keyCode&&e.keyCode===t.ui.keyCode.ESCAPE)return e.preventDefault(),void this.close(e);if(e.keyCode===t.ui.keyCode.TAB&&!e.isDefaultPrevented()){var i=this.uiDialog.find(":tabbable"),n=i.filter(":first"),s=i.filter(":last");e.target!==s[0]&&e.target!==this.uiDialog[0]||e.shiftKey?e.target!==n[0]&&e.target!==this.uiDialog[0]||!e.shiftKey||(this._delay(function(){s.trigger("focus")}),e.preventDefault()):(this._delay(function(){n.trigger("focus")}),e.preventDefault())}},mousedown:function(t){this._moveToTop(t)&&this._focusTabbable()}}),this.element.find("[aria-describedby]").length||this.uiDialog.attr({"aria-describedby":this.element.uniqueId().attr("id")})},_createTitlebar:function(){var e;this.uiDialogTitlebar=t("<div>"),this._addClass(this.uiDialogTitlebar,"ui-dialog-titlebar","ui-widget-header ui-helper-clearfix"),this._on(this.uiDialogTitlebar,{mousedown:function(e){t(e.target).closest(".ui-dialog-titlebar-close")||this.uiDialog.trigger("focus")}}),this.uiDialogTitlebarClose=t("<button type='button'></button>").button({label:t("<a>").text(this.options.closeText).html(),icon:"ui-icon-closethick",showLabel:!1}).appendTo(this.uiDialogTitlebar),this._addClass(this.uiDialogTitlebarClose,"ui-dialog-titlebar-close"),this._on(this.uiDialogTitlebarClose,{click:function(t){t.preventDefault(),this.close(t)}}),e=t("<span>").uniqueId().prependTo(this.uiDialogTitlebar),this._addClass(e,"ui-dialog-title"),this._title(e),this.uiDialogTitlebar.prependTo(this.uiDialog),this.uiDialog.attr({"aria-labelledby":e.attr("id")})},_title:function(t){this.options.title?t.text(this.options.title):t.html("&#160;")},_createButtonPane:function(){this.uiDialogButtonPane=t("<div>"),this._addClass(this.uiDialogButtonPane,"ui-dialog-buttonpane","ui-widget-content ui-helper-clearfix"),this.uiButtonSet=t("<div>").appendTo(this.uiDialogButtonPane),this._addClass(this.uiButtonSet,"ui-dialog-buttonset"),this._createButtons()},_createButtons:function(){var e=this,i=this.options.buttons;return this.uiDialogButtonPane.remove(),this.uiButtonSet.empty(),t.isEmptyObject(i)||t.isArray(i)&&!i.length?void this._removeClass(this.uiDialog,"ui-dialog-buttons"):(t.each(i,function(i,n){var s,o;n=t.isFunction(n)?{click:n,text:i}:n,n=t.extend({type:"button"},n),s=n.click,o={icon:n.icon,iconPosition:n.iconPosition,showLabel:n.showLabel,icons:n.icons,text:n.text},delete n.click,delete n.icon,delete n.iconPosition,delete n.showLabel,delete n.icons,"boolean"==typeof n.text&&delete n.text,t("<button></button>",n).button(o).appendTo(e.uiButtonSet).on("click",function(){s.apply(e.element[0],arguments)})}),this._addClass(this.uiDialog,"ui-dialog-buttons"),void this.uiDialogButtonPane.appendTo(this.uiDialog))},_makeDraggable:function(){function e(t){return{position:t.position,offset:t.offset}}var i=this,n=this.options;this.uiDialog.draggable({cancel:".ui-dialog-content, .ui-dialog-titlebar-close",handle:".ui-dialog-titlebar",containment:"document",start:function(n,s){i._addClass(t(this),"ui-dialog-dragging"),i._blockFrames(),i._trigger("dragStart",n,e(s))},drag:function(t,n){i._trigger("drag",t,e(n))},stop:function(s,o){var a=o.offset.left-i.document.scrollLeft(),r=o.offset.top-i.document.scrollTop();n.position={my:"left top",at:"left"+(a>=0?"+":"")+a+" top"+(r>=0?"+":"")+r,of:i.window},i._removeClass(t(this),"ui-dialog-dragging"),i._unblockFrames(),i._trigger("dragStop",s,e(o))}})},_makeResizable:function(){function e(t){return{originalPosition:t.originalPosition,originalSize:t.originalSize,position:t.position,size:t.size}}var i=this,n=this.options,s=n.resizable,o=this.uiDialog.css("position"),a="string"==typeof s?s:"n,e,s,w,se,sw,ne,nw";this.uiDialog.resizable({cancel:".ui-dialog-content",containment:"document",alsoResize:this.element,maxWidth:n.maxWidth,maxHeight:n.maxHeight,minWidth:n.minWidth,minHeight:this._minHeight(),handles:a,start:function(n,s){i._addClass(t(this),"ui-dialog-resizing"),i._blockFrames(),i._trigger("resizeStart",n,e(s))},resize:function(t,n){i._trigger("resize",t,e(n))},stop:function(s,o){var a=i.uiDialog.offset(),r=a.left-i.document.scrollLeft(),l=a.top-i.document.scrollTop();n.height=i.uiDialog.height(),n.width=i.uiDialog.width(),n.position={my:"left top",at:"left"+(r>=0?"+":"")+r+" top"+(l>=0?"+":"")+l,of:i.window},i._removeClass(t(this),"ui-dialog-resizing"),i._unblockFrames(),i._trigger("resizeStop",s,e(o))}}).css("position",o)},_trackFocus:function(){this._on(this.widget(),{focusin:function(e){this._makeFocusTarget(),this._focusedElement=t(e.target)}})},_makeFocusTarget:function(){this._untrackInstance(),this._trackingInstances().unshift(this)},_untrackInstance:function(){var e=this._trackingInstances(),i=t.inArray(this,e);-1!==i&&e.splice(i,1)},_trackingInstances:function(){var t=this.document.data("ui-dialog-instances");return t||(t=[],this.document.data("ui-dialog-instances",t)),t},_minHeight:function(){var t=this.options;return"auto"===t.height?t.minHeight:Math.min(t.minHeight,t.height)},_position:function(){var t=this.uiDialog.is(":visible");t||this.uiDialog.show(),this.uiDialog.position(this.options.position),t||this.uiDialog.hide()},_setOptions:function(e){var i=this,n=!1,s={};t.each(e,function(t,e){i._setOption(t,e),t in i.sizeRelatedOptions&&(n=!0),t in i.resizableRelatedOptions&&(s[t]=e)}),n&&(this._size(),this._position()),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option",s)},_setOption:function(e,i){var n,s,o=this.uiDialog;"disabled"!==e&&(this._super(e,i),"appendTo"===e&&this.uiDialog.appendTo(this._appendTo()),"buttons"===e&&this._createButtons(),"closeText"===e&&this.uiDialogTitlebarClose.button({label:t("<a>").text(""+this.options.closeText).html()}),"draggable"===e&&((n=o.is(":data(ui-draggable)"))&&!i&&o.draggable("destroy"),!n&&i&&this._makeDraggable()),"position"===e&&this._position(),"resizable"===e&&((s=o.is(":data(ui-resizable)"))&&!i&&o.resizable("destroy"),s&&"string"==typeof i&&o.resizable("option","handles",i),s||!1===i||this._makeResizable()),"title"===e&&this._title(this.uiDialogTitlebar.find(".ui-dialog-title")))},_size:function(){var t,e,i,n=this.options;this.element.show().css({width:"auto",minHeight:0,maxHeight:"none",height:0}),n.minWidth>n.width&&(n.width=n.minWidth),t=this.uiDialog.css({height:"auto",width:n.width}).outerHeight(),e=Math.max(0,n.minHeight-t),i="number"==typeof n.maxHeight?Math.max(0,n.maxHeight-t):"none","auto"===n.height?this.element.css({minHeight:e,maxHeight:i,height:"auto"}):this.element.height(Math.max(0,n.height-t)),this.uiDialog.is(":data(ui-resizable)")&&this.uiDialog.resizable("option","minHeight",this._minHeight())},_blockFrames:function(){this.iframeBlocks=this.document.find("iframe").map(function(){var e=t(this);return t("<div>").css({position:"absolute",width:e.outerWidth(),height:e.outerHeight()}).appendTo(e.parent()).offset(e.offset())[0]})},_unblockFrames:function(){this.iframeBlocks&&(this.iframeBlocks.remove(),delete this.iframeBlocks)},_allowInteraction:function(e){return!!t(e.target).closest(".ui-dialog").length||!!t(e.target).closest(".ui-datepicker").length},_createOverlay:function(){if(this.options.modal){var e=!0;this._delay(function(){e=!1}),this.document.data("ui-dialog-overlays")||this._on(this.document,{focusin:function(t){e||this._allowInteraction(t)||(t.preventDefault(),this._trackingInstances()[0]._focusTabbable())}}),this.overlay=t("<div>").appendTo(this._appendTo()),this._addClass(this.overlay,null,"ui-widget-overlay ui-front"),this._on(this.overlay,{mousedown:"_keepFocus"}),this.document.data("ui-dialog-overlays",(this.document.data("ui-dialog-overlays")||0)+1)}},_destroyOverlay:function(){if(this.options.modal&&this.overlay){var t=this.document.data("ui-dialog-overlays")-1;t?this.document.data("ui-dialog-overlays",t):(this._off(this.document,"focusin"),this.document.removeData("ui-dialog-overlays")),this.overlay.remove(),this.overlay=null}}}),!1!==t.uiBackCompat&&t.widget("ui.dialog",t.ui.dialog,{options:{dialogClass:""},_createWrapper:function(){this._super(),this.uiDialog.addClass(this.options.dialogClass)},_setOption:function(t,e){"dialogClass"===t&&this.uiDialog.removeClass(this.options.dialogClass).addClass(e),this._superApply(arguments)}}),t.ui.dialog,t.widget("ui.droppable",{version:"1.12.1",widgetEventPrefix:"drop",options:{accept:"*",addClasses:!0,greedy:!1,scope:"default",tolerance:"intersect",activate:null,deactivate:null,drop:null,out:null,over:null},_create:function(){var e,i=this.options,n=i.accept;this.isover=!1,this.isout=!0,this.accept=t.isFunction(n)?n:function(t){return t.is(n)},this.proportions=function(){return arguments.length?void(e=arguments[0]):e||(e={width:this.element[0].offsetWidth,height:this.element[0].offsetHeight})},this._addToManager(i.scope),i.addClasses&&this._addClass("ui-droppable")},_addToManager:function(e){t.ui.ddmanager.droppables[e]=t.ui.ddmanager.droppables[e]||[],t.ui.ddmanager.droppables[e].push(this)},_splice:function(t){for(var e=0;t.length>e;e++)t[e]===this&&t.splice(e,1)},_destroy:function(){var e=t.ui.ddmanager.droppables[this.options.scope];this._splice(e)},_setOption:function(e,i){if("accept"===e)this.accept=t.isFunction(i)?i:function(t){return t.is(i)};else if("scope"===e){var n=t.ui.ddmanager.droppables[this.options.scope];this._splice(n),this._addToManager(i)}this._super(e,i)},_activate:function(e){var i=t.ui.ddmanager.current;this._addActiveClass(),i&&this._trigger("activate",e,this.ui(i))},_deactivate:function(e){var i=t.ui.ddmanager.current;this._removeActiveClass(),i&&this._trigger("deactivate",e,this.ui(i))},_over:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this._addHoverClass(),this._trigger("over",e,this.ui(i)))},_out:function(e){var i=t.ui.ddmanager.current;i&&(i.currentItem||i.element)[0]!==this.element[0]&&this.accept.call(this.element[0],i.currentItem||i.element)&&(this._removeHoverClass(),this._trigger("out",e,this.ui(i)))},_drop:function(e,i){var n=i||t.ui.ddmanager.current,s=!1;return!(!n||(n.currentItem||n.element)[0]===this.element[0])&&(this.element.find(":data(ui-droppable)").not(".ui-draggable-dragging").each(function(){var i=t(this).droppable("instance");return i.options.greedy&&!i.options.disabled&&i.options.scope===n.options.scope&&i.accept.call(i.element[0],n.currentItem||n.element)&&g(n,t.extend(i,{offset:i.element.offset()}),i.options.tolerance,e)?(s=!0,!1):void 0}),!s&&(!!this.accept.call(this.element[0],n.currentItem||n.element)&&(this._removeActiveClass(),this._removeHoverClass(),this._trigger("drop",e,this.ui(n)),this.element)))},ui:function(t){return{draggable:t.currentItem||t.element,helper:t.helper,position:t.position,offset:t.positionAbs}},_addHoverClass:function(){this._addClass("ui-droppable-hover")},_removeHoverClass:function(){this._removeClass("ui-droppable-hover")},_addActiveClass:function(){this._addClass("ui-droppable-active")},_removeActiveClass:function(){this._removeClass("ui-droppable-active")}});var g=t.ui.intersect=function(){function t(t,e,i){return t>=e&&e+i>t}return function(e,i,n,s){if(!i.offset)return!1;var o=(e.positionAbs||e.position.absolute).left+e.margins.left,a=(e.positionAbs||e.position.absolute).top+e.margins.top,r=o+e.helperProportions.width,l=a+e.helperProportions.height,h=i.offset.left,c=i.offset.top,u=h+i.proportions().width,d=c+i.proportions().height;switch(n){case"fit":return o>=h&&u>=r&&a>=c&&d>=l;case"intersect":return o+e.helperProportions.width/2>h&&u>r-e.helperProportions.width/2&&a+e.helperProportions.height/2>c&&d>l-e.helperProportions.height/2;case"pointer":return t(s.pageY,c,i.proportions().height)&&t(s.pageX,h,i.proportions().width);case"touch":return(a>=c&&d>=a||l>=c&&d>=l||c>a&&l>d)&&(o>=h&&u>=o||r>=h&&u>=r||h>o&&r>u);default:return!1}}}();t.ui.ddmanager={current:null,droppables:{default:[]},prepareOffsets:function(e,i){var n,s,o=t.ui.ddmanager.droppables[e.options.scope]||[],a=i?i.type:null,r=(e.currentItem||e.element).find(":data(ui-droppable)").addBack();t:for(n=0;o.length>n;n++)if(!(o[n].options.disabled||e&&!o[n].accept.call(o[n].element[0],e.currentItem||e.element))){for(s=0;r.length>s;s++)if(r[s]===o[n].element[0]){o[n].proportions().height=0;continue t}o[n].visible="none"!==o[n].element.css("display"),o[n].visible&&("mousedown"===a&&o[n]._activate.call(o[n],i),o[n].offset=o[n].element.offset(),o[n].proportions({width:o[n].element[0].offsetWidth,height:o[n].element[0].offsetHeight}))}},drop:function(e,i){var n=!1;return t.each((t.ui.ddmanager.droppables[e.options.scope]||[]).slice(),function(){this.options&&(!this.options.disabled&&this.visible&&g(e,this,this.options.tolerance,i)&&(n=this._drop.call(this,i)||n),!this.options.disabled&&this.visible&&this.accept.call(this.element[0],e.currentItem||e.element)&&(this.isout=!0,this.isover=!1,this._deactivate.call(this,i)))}),n},dragStart:function(e,i){e.element.parentsUntil("body").on("scroll.droppable",function(){e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)})},drag:function(e,i){e.options.refreshPositions&&t.ui.ddmanager.prepareOffsets(e,i),t.each(t.ui.ddmanager.droppables[e.options.scope]||[],function(){if(!this.options.disabled&&!this.greedyChild&&this.visible){var n,s,o,a=g(e,this,this.options.tolerance,i),r=!a&&this.isover?"isout":a&&!this.isover?"isover":null;r&&(this.options.greedy&&(s=this.options.scope,(o=this.element.parents(":data(ui-droppable)").filter(function(){return t(this).droppable("instance").options.scope===s})).length&&((n=t(o[0]).droppable("instance")).greedyChild="isover"===r)),n&&"isover"===r&&(n.isover=!1,n.isout=!0,n._out.call(n,i)),this[r]=!0,this["isout"===r?"isover":"isout"]=!1,this["isover"===r?"_over":"_out"].call(this,i),n&&"isout"===r&&(n.isout=!1,n.isover=!0,n._over.call(n,i)))}})},dragStop:function(e,i){e.element.parentsUntil("body").off("scroll.droppable"),e.options.refreshPositions||t.ui.ddmanager.prepareOffsets(e,i)}},!1!==t.uiBackCompat&&t.widget("ui.droppable",t.ui.droppable,{options:{hoverClass:!1,activeClass:!1},_addActiveClass:function(){this._super(),this.options.activeClass&&this.element.addClass(this.options.activeClass)},_removeActiveClass:function(){this._super(),this.options.activeClass&&this.element.removeClass(this.options.activeClass)},_addHoverClass:function(){this._super(),this.options.hoverClass&&this.element.addClass(this.options.hoverClass)},_removeHoverClass:function(){this._super(),this.options.hoverClass&&this.element.removeClass(this.options.hoverClass)}}),t.ui.droppable,t.widget("ui.progressbar",{version:"1.12.1",options:{classes:{"ui-progressbar":"ui-corner-all","ui-progressbar-value":"ui-corner-left","ui-progressbar-complete":"ui-corner-right"},max:100,value:0,change:null,complete:null},min:0,_create:function(){this.oldValue=this.options.value=this._constrainedValue(),this.element.attr({role:"progressbar","aria-valuemin":this.min}),this._addClass("ui-progressbar","ui-widget ui-widget-content"),this.valueDiv=t("<div>").appendTo(this.element),this._addClass(this.valueDiv,"ui-progressbar-value","ui-widget-header"),this._refreshValue()},_destroy:function(){this.element.removeAttr("role aria-valuemin aria-valuemax aria-valuenow"),this.valueDiv.remove()},value:function(t){return void 0===t?this.options.value:(this.options.value=this._constrainedValue(t),void this._refreshValue())},_constrainedValue:function(t){return void 0===t&&(t=this.options.value),this.indeterminate=!1===t,"number"!=typeof t&&(t=0),!this.indeterminate&&Math.min(this.options.max,Math.max(this.min,t))},_setOptions:function(t){var e=t.value;delete t.value,this._super(t),this.options.value=this._constrainedValue(e),this._refreshValue()},_setOption:function(t,e){"max"===t&&(e=Math.max(this.min,e)),this._super(t,e)},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",t),this._toggleClass(null,"ui-state-disabled",!!t)},_percentage:function(){return this.indeterminate?100:100*(this.options.value-this.min)/(this.options.max-this.min)},_refreshValue:function(){var e=this.options.value,i=this._percentage();this.valueDiv.toggle(this.indeterminate||e>this.min).width(i.toFixed(0)+"%"),this._toggleClass(this.valueDiv,"ui-progressbar-complete",null,e===this.options.max)._toggleClass("ui-progressbar-indeterminate",null,this.indeterminate),this.indeterminate?(this.element.removeAttr("aria-valuenow"),this.overlayDiv||(this.overlayDiv=t("<div>").appendTo(this.valueDiv),this._addClass(this.overlayDiv,"ui-progressbar-overlay"))):(this.element.attr({"aria-valuemax":this.options.max,"aria-valuenow":e}),this.overlayDiv&&(this.overlayDiv.remove(),this.overlayDiv=null)),this.oldValue!==e&&(this.oldValue=e,this._trigger("change")),e===this.options.max&&this._trigger("complete")}}),t.widget("ui.selectable",t.ui.mouse,{version:"1.12.1",options:{appendTo:"body",autoRefresh:!0,distance:0,filter:"*",tolerance:"touch",selected:null,selecting:null,start:null,stop:null,unselected:null,unselecting:null},_create:function(){var e=this;this._addClass("ui-selectable"),this.dragged=!1,this.refresh=function(){e.elementPos=t(e.element[0]).offset(),e.selectees=t(e.options.filter,e.element[0]),e._addClass(e.selectees,"ui-selectee"),e.selectees.each(function(){var i=t(this),n=i.offset(),s={left:n.left-e.elementPos.left,top:n.top-e.elementPos.top};t.data(this,"selectable-item",{element:this,$element:i,left:s.left,top:s.top,right:s.left+i.outerWidth(),bottom:s.top+i.outerHeight(),startselected:!1,selected:i.hasClass("ui-selected"),selecting:i.hasClass("ui-selecting"),unselecting:i.hasClass("ui-unselecting")})})},this.refresh(),this._mouseInit(),this.helper=t("<div>"),this._addClass(this.helper,"ui-selectable-helper")},_destroy:function(){this.selectees.removeData("selectable-item"),this._mouseDestroy()},_mouseStart:function(e){var i=this,n=this.options;this.opos=[e.pageX,e.pageY],this.elementPos=t(this.element[0]).offset(),this.options.disabled||(this.selectees=t(n.filter,this.element[0]),this._trigger("start",e),t(n.appendTo).append(this.helper),this.helper.css({left:e.pageX,top:e.pageY,width:0,height:0}),n.autoRefresh&&this.refresh(),this.selectees.filter(".ui-selected").each(function(){var n=t.data(this,"selectable-item");n.startselected=!0,e.metaKey||e.ctrlKey||(i._removeClass(n.$element,"ui-selected"),n.selected=!1,i._addClass(n.$element,"ui-unselecting"),n.unselecting=!0,i._trigger("unselecting",e,{unselecting:n.element}))}),t(e.target).parents().addBack().each(function(){var n,s=t.data(this,"selectable-item");return s?(n=!e.metaKey&&!e.ctrlKey||!s.$element.hasClass("ui-selected"),i._removeClass(s.$element,n?"ui-unselecting":"ui-selected")._addClass(s.$element,n?"ui-selecting":"ui-unselecting"),s.unselecting=!n,s.selecting=n,s.selected=n,n?i._trigger("selecting",e,{selecting:s.element}):i._trigger("unselecting",e,{unselecting:s.element}),!1):void 0}))},_mouseDrag:function(e){if(this.dragged=!0,!this.options.disabled){var i,n=this,s=this.options,o=this.opos[0],a=this.opos[1],r=e.pageX,l=e.pageY;return o>r&&(i=r,r=o,o=i),a>l&&(i=l,l=a,a=i),this.helper.css({left:o,top:a,width:r-o,height:l-a}),this.selectees.each(function(){var i=t.data(this,"selectable-item"),h=!1,c={};i&&i.element!==n.element[0]&&(c.left=i.left+n.elementPos.left,c.right=i.right+n.elementPos.left,c.top=i.top+n.elementPos.top,c.bottom=i.bottom+n.elementPos.top,"touch"===s.tolerance?h=!(c.left>r||o>c.right||c.top>l||a>c.bottom):"fit"===s.tolerance&&(h=c.left>o&&r>c.right&&c.top>a&&l>c.bottom),h?(i.selected&&(n._removeClass(i.$element,"ui-selected"),i.selected=!1),i.unselecting&&(n._removeClass(i.$element,"ui-unselecting"),i.unselecting=!1),i.selecting||(n._addClass(i.$element,"ui-selecting"),i.selecting=!0,n._trigger("selecting",e,{selecting:i.element}))):(i.selecting&&((e.metaKey||e.ctrlKey)&&i.startselected?(n._removeClass(i.$element,"ui-selecting"),i.selecting=!1,n._addClass(i.$element,"ui-selected"),i.selected=!0):(n._removeClass(i.$element,"ui-selecting"),i.selecting=!1,i.startselected&&(n._addClass(i.$element,"ui-unselecting"),i.unselecting=!0),n._trigger("unselecting",e,{unselecting:i.element}))),i.selected&&(e.metaKey||e.ctrlKey||i.startselected||(n._removeClass(i.$element,"ui-selected"),i.selected=!1,n._addClass(i.$element,"ui-unselecting"),i.unselecting=!0,n._trigger("unselecting",e,{unselecting:i.element})))))}),!1}},_mouseStop:function(e){var i=this;return this.dragged=!1,t(".ui-unselecting",this.element[0]).each(function(){var n=t.data(this,"selectable-item");i._removeClass(n.$element,"ui-unselecting"),n.unselecting=!1,n.startselected=!1,i._trigger("unselected",e,{unselected:n.element})}),t(".ui-selecting",this.element[0]).each(function(){var n=t.data(this,"selectable-item");i._removeClass(n.$element,"ui-selecting")._addClass(n.$element,"ui-selected"),n.selecting=!1,n.selected=!0,n.startselected=!0,i._trigger("selected",e,{selected:n.element})}),this._trigger("stop",e),this.helper.remove(),!1}}),t.widget("ui.selectmenu",[t.ui.formResetMixin,{version:"1.12.1",defaultElement:"<select>",options:{appendTo:null,classes:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"},disabled:null,icons:{button:"ui-icon-triangle-1-s"},position:{my:"left top",at:"left bottom",collision:"none"},width:!1,change:null,close:null,focus:null,open:null,select:null},_create:function(){var e=this.element.uniqueId().attr("id");this.ids={element:e,button:e+"-button",menu:e+"-menu"},this._drawButton(),this._drawMenu(),this._bindFormResetHandler(),this._rendered=!1,this.menuItems=t()},_drawButton:function(){var e,i=this,n=this._parseOption(this.element.find("option:selected"),this.element[0].selectedIndex);this.labels=this.element.labels().attr("for",this.ids.button),this._on(this.labels,{click:function(t){this.button.focus(),t.preventDefault()}}),this.element.hide(),this.button=t("<span>",{tabindex:this.options.disabled?-1:0,id:this.ids.button,role:"combobox","aria-expanded":"false","aria-autocomplete":"list","aria-owns":this.ids.menu,"aria-haspopup":"true",title:this.element.attr("title")}).insertAfter(this.element),this._addClass(this.button,"ui-selectmenu-button ui-selectmenu-button-closed","ui-button ui-widget"),e=t("<span>").appendTo(this.button),this._addClass(e,"ui-selectmenu-icon","ui-icon "+this.options.icons.button),this.buttonItem=this._renderButtonItem(n).appendTo(this.button),!1!==this.options.width&&this._resizeButton(),this._on(this.button,this._buttonEvents),this.button.one("focusin",function(){i._rendered||i._refreshMenu()})},_drawMenu:function(){var e=this;this.menu=t("<ul>",{"aria-hidden":"true","aria-labelledby":this.ids.button,id:this.ids.menu}),this.menuWrap=t("<div>").append(this.menu),this._addClass(this.menuWrap,"ui-selectmenu-menu","ui-front"),this.menuWrap.appendTo(this._appendTo()),this.menuInstance=this.menu.menu({classes:{"ui-menu":"ui-corner-bottom"},role:"listbox",select:function(t,i){t.preventDefault(),e._setSelection(),e._select(i.item.data("ui-selectmenu-item"),t)},focus:function(t,i){var n=i.item.data("ui-selectmenu-item");null!=e.focusIndex&&n.index!==e.focusIndex&&(e._trigger("focus",t,{item:n}),e.isOpen||e._select(n,t)),e.focusIndex=n.index,e.button.attr("aria-activedescendant",e.menuItems.eq(n.index).attr("id"))}}).menu("instance"),this.menuInstance._off(this.menu,"mouseleave"),this.menuInstance._closeOnDocumentClick=function(){return!1},this.menuInstance._isDivider=function(){return!1}},refresh:function(){this._refreshMenu(),this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(this._getSelectedItem().data("ui-selectmenu-item")||{})),null===this.options.width&&this._resizeButton()},_refreshMenu:function(){var t,e=this.element.find("option");this.menu.empty(),this._parseOptions(e),this._renderMenu(this.menu,this.items),this.menuInstance.refresh(),this.menuItems=this.menu.find("li").not(".ui-selectmenu-optgroup").find(".ui-menu-item-wrapper"),this._rendered=!0,e.length&&(t=this._getSelectedItem(),this.menuInstance.focus(null,t),this._setAria(t.data("ui-selectmenu-item")),this._setOption("disabled",this.element.prop("disabled")))},open:function(t){this.options.disabled||(this._rendered?(this._removeClass(this.menu.find(".ui-state-active"),null,"ui-state-active"),this.menuInstance.focus(null,this._getSelectedItem())):this._refreshMenu(),this.menuItems.length&&(this.isOpen=!0,this._toggleAttr(),this._resizeMenu(),this._position(),this._on(this.document,this._documentClick),this._trigger("open",t)))},_position:function(){this.menuWrap.position(t.extend({of:this.button},this.options.position))},close:function(t){this.isOpen&&(this.isOpen=!1,this._toggleAttr(),this.range=null,this._off(this.document),this._trigger("close",t))},widget:function(){return this.button},menuWidget:function(){return this.menu},_renderButtonItem:function(e){var i=t("<span>");return this._setText(i,e.label),this._addClass(i,"ui-selectmenu-text"),i},_renderMenu:function(e,i){var n=this,s="";t.each(i,function(i,o){var a;o.optgroup!==s&&(a=t("<li>",{text:o.optgroup}),n._addClass(a,"ui-selectmenu-optgroup","ui-menu-divider"+(o.element.parent("optgroup").prop("disabled")?" ui-state-disabled":"")),a.appendTo(e),s=o.optgroup),n._renderItemData(e,o)})},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-selectmenu-item",e)},_renderItem:function(e,i){var n=t("<li>"),s=t("<div>",{title:i.element.attr("title")});return i.disabled&&this._addClass(n,null,"ui-state-disabled"),this._setText(s,i.label),n.append(s).appendTo(e)},_setText:function(t,e){e?t.text(e):t.html("&#160;")},_move:function(t,e){var i,n,s=".ui-menu-item";this.isOpen?i=this.menuItems.eq(this.focusIndex).parent("li"):(i=this.menuItems.eq(this.element[0].selectedIndex).parent("li"),s+=":not(.ui-state-disabled)"),(n="first"===t||"last"===t?i["first"===t?"prevAll":"nextAll"](s).eq(-1):i[t+"All"](s).eq(0)).length&&this.menuInstance.focus(e,n)},_getSelectedItem:function(){return this.menuItems.eq(this.element[0].selectedIndex).parent("li")},_toggle:function(t){this[this.isOpen?"close":"open"](t)},_setSelection:function(){var t;this.range&&(window.getSelection?((t=window.getSelection()).removeAllRanges(),t.addRange(this.range)):this.range.select(),this.button.focus())},_documentClick:{mousedown:function(e){this.isOpen&&(t(e.target).closest(".ui-selectmenu-menu, #"+t.ui.escapeSelector(this.ids.button)).length||this.close(e))}},_buttonEvents:{mousedown:function(){var t;window.getSelection?(t=window.getSelection()).rangeCount&&(this.range=t.getRangeAt(0)):this.range=document.selection.createRange()},click:function(t){this._setSelection(),this._toggle(t)},keydown:function(e){var i=!0;switch(e.keyCode){case t.ui.keyCode.TAB:case t.ui.keyCode.ESCAPE:this.close(e),i=!1;break;case t.ui.keyCode.ENTER:this.isOpen&&this._selectFocusedItem(e);break;case t.ui.keyCode.UP:e.altKey?this._toggle(e):this._move("prev",e);break;case t.ui.keyCode.DOWN:e.altKey?this._toggle(e):this._move("next",e);break;case t.ui.keyCode.SPACE:this.isOpen?this._selectFocusedItem(e):this._toggle(e);break;case t.ui.keyCode.LEFT:this._move("prev",e);break;case t.ui.keyCode.RIGHT:this._move("next",e);break;case t.ui.keyCode.HOME:case t.ui.keyCode.PAGE_UP:this._move("first",e);break;case t.ui.keyCode.END:case t.ui.keyCode.PAGE_DOWN:this._move("last",e);break;default:this.menu.trigger(e),i=!1}i&&e.preventDefault()}},_selectFocusedItem:function(t){var e=this.menuItems.eq(this.focusIndex).parent("li");e.hasClass("ui-state-disabled")||this._select(e.data("ui-selectmenu-item"),t)},_select:function(t,e){var i=this.element[0].selectedIndex;this.element[0].selectedIndex=t.index,this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(t)),this._setAria(t),this._trigger("select",e,{item:t}),t.index!==i&&this._trigger("change",e,{item:t}),this.close(e)},_setAria:function(t){var e=this.menuItems.eq(t.index).attr("id");this.button.attr({"aria-labelledby":e,"aria-activedescendant":e}),this.menu.attr("aria-activedescendant",e)},_setOption:function(t,e){if("icons"===t){var i=this.button.find("span.ui-icon");this._removeClass(i,null,this.options.icons.button)._addClass(i,null,e.button)}this._super(t,e),"appendTo"===t&&this.menuWrap.appendTo(this._appendTo()),"width"===t&&this._resizeButton()},_setOptionDisabled:function(t){this._super(t),this.menuInstance.option("disabled",t),this.button.attr("aria-disabled",t),this._toggleClass(this.button,null,"ui-state-disabled",t),this.element.prop("disabled",t),t?(this.button.attr("tabindex",-1),this.close()):this.button.attr("tabindex",0)},_appendTo:function(){var e=this.options.appendTo;return e&&(e=e.jquery||e.nodeType?t(e):this.document.find(e).eq(0)),e&&e[0]||(e=this.element.closest(".ui-front, dialog")),e.length||(e=this.document[0].body),e},_toggleAttr:function(){this.button.attr("aria-expanded",this.isOpen),this._removeClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"closed":"open"))._addClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"open":"closed"))._toggleClass(this.menuWrap,"ui-selectmenu-open",null,this.isOpen),this.menu.attr("aria-hidden",!this.isOpen)},_resizeButton:function(){var t=this.options.width;return!1===t?void this.button.css("width",""):(null===t&&(t=this.element.show().outerWidth(),this.element.hide()),void this.button.outerWidth(t))},_resizeMenu:function(){this.menu.outerWidth(Math.max(this.button.outerWidth(),this.menu.width("").outerWidth()+1))},_getCreateOptions:function(){var t=this._super();return t.disabled=this.element.prop("disabled"),t},_parseOptions:function(e){var i=this,n=[];e.each(function(e,s){n.push(i._parseOption(t(s),e))}),this.items=n},_parseOption:function(t,e){var i=t.parent("optgroup");return{element:t,index:e,value:t.val(),label:t.text(),optgroup:i.attr("label")||"",disabled:i.prop("disabled")||t.prop("disabled")}},_destroy:function(){this._unbindFormResetHandler(),this.menuWrap.remove(),this.button.remove(),this.element.show(),this.element.removeUniqueId(),this.labels.attr("for",this.ids.element)}}]),t.widget("ui.slider",t.ui.mouse,{version:"1.12.1",widgetEventPrefix:"slide",options:{animate:!1,classes:{"ui-slider":"ui-corner-all","ui-slider-handle":"ui-corner-all","ui-slider-range":"ui-corner-all ui-widget-header"},distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},numPages:5,_create:function(){this._keySliding=!1,this._mouseSliding=!1,this._animateOff=!0,this._handleIndex=null,this._detectOrientation(),this._mouseInit(),this._calculateNewMax(),this._addClass("ui-slider ui-slider-"+this.orientation,"ui-widget ui-widget-content"),this._refresh(),this._animateOff=!1},_refresh:function(){this._createRange(),this._createHandles(),this._setupEvents(),this._refreshValue()},_createHandles:function(){var e,i,n=this.options,s=this.element.find(".ui-slider-handle"),o=[];for(i=n.values&&n.values.length||1,s.length>i&&(s.slice(i).remove(),s=s.slice(0,i)),e=s.length;i>e;e++)o.push("<span tabindex='0'></span>");this.handles=s.add(t(o.join("")).appendTo(this.element)),this._addClass(this.handles,"ui-slider-handle","ui-state-default"),this.handle=this.handles.eq(0),this.handles.each(function(e){t(this).data("ui-slider-handle-index",e).attr("tabIndex",0)})},_createRange:function(){var e=this.options;e.range?(!0===e.range&&(e.values?e.values.length&&2!==e.values.length?e.values=[e.values[0],e.values[0]]:t.isArray(e.values)&&(e.values=e.values.slice(0)):e.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?(this._removeClass(this.range,"ui-slider-range-min ui-slider-range-max"),this.range.css({left:"",bottom:""})):(this.range=t("<div>").appendTo(this.element),this._addClass(this.range,"ui-slider-range")),("min"===e.range||"max"===e.range)&&this._addClass(this.range,"ui-slider-range-"+e.range)):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){this._off(this.handles),this._on(this.handles,this._handleEvents),this._hoverable(this.handles),this._focusable(this.handles)},_destroy:function(){this.handles.remove(),this.range&&this.range.remove(),this._mouseDestroy()},_mouseCapture:function(e){var i,n,s,o,a,r,l,h=this,c=this.options;return!c.disabled&&(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),i={x:e.pageX,y:e.pageY},n=this._normValueFromMouse(i),s=this._valueMax()-this._valueMin()+1,this.handles.each(function(e){var i=Math.abs(n-h.values(e));(s>i||s===i&&(e===h._lastChangedValue||h.values(e)===c.min))&&(s=i,o=t(this),a=e)}),!1!==this._start(e,a)&&(this._mouseSliding=!0,this._handleIndex=a,this._addClass(o,null,"ui-state-active"),o.trigger("focus"),r=o.offset(),l=!t(e.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=l?{left:0,top:0}:{left:e.pageX-r.left-o.width()/2,top:e.pageY-r.top-o.height()/2-(parseInt(o.css("borderTopWidth"),10)||0)-(parseInt(o.css("borderBottomWidth"),10)||0)+(parseInt(o.css("marginTop"),10)||0)},this.handles.hasClass("ui-state-hover")||this._slide(e,a,n),this._animateOff=!0,!0))},_mouseStart:function(){return!0},_mouseDrag:function(t){var e={x:t.pageX,y:t.pageY},i=this._normValueFromMouse(e);return this._slide(t,this._handleIndex,i),!1},_mouseStop:function(t){return this._removeClass(this.handles,null,"ui-state-active"),this._mouseSliding=!1,this._stop(t,this._handleIndex),this._change(t,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1,!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(t){var e,i,n,s,o;return"horizontal"===this.orientation?(e=this.elementSize.width,i=t.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(e=this.elementSize.height,i=t.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0)),(n=i/e)>1&&(n=1),0>n&&(n=0),"vertical"===this.orientation&&(n=1-n),s=this._valueMax()-this._valueMin(),o=this._valueMin()+n*s,this._trimAlignValue(o)},_uiHash:function(t,e,i){var n={handle:this.handles[t],handleIndex:t,value:void 0!==e?e:this.value()};return this._hasMultipleValues()&&(n.value=void 0!==e?e:this.values(t),n.values=i||this.values()),n},_hasMultipleValues:function(){return this.options.values&&this.options.values.length},_start:function(t,e){return this._trigger("start",t,this._uiHash(e))},_slide:function(t,e,i){var n,s=this.value(),o=this.values();this._hasMultipleValues()&&(n=this.values(e?0:1),s=this.values(e),2===this.options.values.length&&!0===this.options.range&&(i=0===e?Math.min(n,i):Math.max(n,i)),o[e]=i),i!==s&&(!1!==this._trigger("slide",t,this._uiHash(e,i,o))&&(this._hasMultipleValues()?this.values(e,i):this.value(i)))},_stop:function(t,e){this._trigger("stop",t,this._uiHash(e))},_change:function(t,e){this._keySliding||this._mouseSliding||(this._lastChangedValue=e,this._trigger("change",t,this._uiHash(e)))},value:function(t){return arguments.length?(this.options.value=this._trimAlignValue(t),this._refreshValue(),void this._change(null,0)):this._value()},values:function(e,i){var n,s,o;if(arguments.length>1)return this.options.values[e]=this._trimAlignValue(i),this._refreshValue(),void this._change(null,e);if(!arguments.length)return this._values();if(!t.isArray(arguments[0]))return this._hasMultipleValues()?this._values(e):this.value();for(n=this.options.values,s=arguments[0],o=0;n.length>o;o+=1)n[o]=this._trimAlignValue(s[o]),this._change(null,o);this._refreshValue()},_setOption:function(e,i){var n,s=0;switch("range"===e&&!0===this.options.range&&("min"===i?(this.options.value=this._values(0),this.options.values=null):"max"===i&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),t.isArray(this.options.values)&&(s=this.options.values.length),this._super(e,i),e){case"orientation":this._detectOrientation(),this._removeClass("ui-slider-horizontal ui-slider-vertical")._addClass("ui-slider-"+this.orientation),this._refreshValue(),this.options.range&&this._refreshRange(i),this.handles.css("horizontal"===i?"bottom":"left","");break;case"value":this._animateOff=!0,this._refreshValue(),this._change(null,0),this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),n=s-1;n>=0;n--)this._change(null,n);this._animateOff=!1;break;case"step":case"min":case"max":this._animateOff=!0,this._calculateNewMax(),this._refreshValue(),this._animateOff=!1;break;case"range":this._animateOff=!0,this._refresh(),this._animateOff=!1}},_setOptionDisabled:function(t){this._super(t),this._toggleClass(null,"ui-state-disabled",!!t)},_value:function(){var t=this.options.value;return this._trimAlignValue(t)},_values:function(t){var e,i,n;if(arguments.length)return e=this.options.values[t],this._trimAlignValue(e);if(this._hasMultipleValues()){for(i=this.options.values.slice(),n=0;i.length>n;n+=1)i[n]=this._trimAlignValue(i[n]);return i}return[]},_trimAlignValue:function(t){if(this._valueMin()>=t)return this._valueMin();if(t>=this._valueMax())return this._valueMax();var e=this.options.step>0?this.options.step:1,i=(t-this._valueMin())%e,n=t-i;return 2*Math.abs(i)>=e&&(n+=i>0?e:-e),parseFloat(n.toFixed(5))},_calculateNewMax:function(){var t=this.options.max,e=this._valueMin(),i=this.options.step;(t=Math.round((t-e)/i)*i+e)>this.options.max&&(t-=i),this.max=parseFloat(t.toFixed(this._precision()))},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min&&(t=Math.max(t,this._precisionOf(this.options.min))),t},_precisionOf:function(t){var e=""+t,i=e.indexOf(".");return-1===i?0:e.length-i-1},_valueMin:function(){return this.options.min},_valueMax:function(){return this.max},_refreshRange:function(t){"vertical"===t&&this.range.css({width:"",left:""}),"horizontal"===t&&this.range.css({height:"",bottom:""})},_refreshValue:function(){var e,i,n,s,o,a=this.options.range,r=this.options,l=this,h=!this._animateOff&&r.animate,c={};this._hasMultipleValues()?this.handles.each(function(n){i=(l.values(n)-l._valueMin())/(l._valueMax()-l._valueMin())*100,c["horizontal"===l.orientation?"left":"bottom"]=i+"%",t(this).stop(1,1)[h?"animate":"css"](c,r.animate),!0===l.options.range&&("horizontal"===l.orientation?(0===n&&l.range.stop(1,1)[h?"animate":"css"]({left:i+"%"},r.animate),1===n&&l.range[h?"animate":"css"]({width:i-e+"%"},{queue:!1,duration:r.animate})):(0===n&&l.range.stop(1,1)[h?"animate":"css"]({bottom:i+"%"},r.animate),1===n&&l.range[h?"animate":"css"]({height:i-e+"%"},{queue:!1,duration:r.animate}))),e=i}):(n=this.value(),s=this._valueMin(),o=this._valueMax(),i=o!==s?(n-s)/(o-s)*100:0,c["horizontal"===this.orientation?"left":"bottom"]=i+"%",this.handle.stop(1,1)[h?"animate":"css"](c,r.animate),"min"===a&&"horizontal"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({width:i+"%"},r.animate),"max"===a&&"horizontal"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({width:100-i+"%"},r.animate),"min"===a&&"vertical"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({height:i+"%"},r.animate),"max"===a&&"vertical"===this.orientation&&this.range.stop(1,1)[h?"animate":"css"]({height:100-i+"%"},r.animate))},_handleEvents:{keydown:function(e){var i,n,s,o=t(e.target).data("ui-slider-handle-index");switch(e.keyCode){case t.ui.keyCode.HOME:case t.ui.keyCode.END:case t.ui.keyCode.PAGE_UP:case t.ui.keyCode.PAGE_DOWN:case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(e.preventDefault(),!this._keySliding&&(this._keySliding=!0,this._addClass(t(e.target),null,"ui-state-active"),!1===this._start(e,o)))return}switch(s=this.options.step,i=n=this._hasMultipleValues()?this.values(o):this.value(),e.keyCode){case t.ui.keyCode.HOME:n=this._valueMin();break;case t.ui.keyCode.END:n=this._valueMax();break;case t.ui.keyCode.PAGE_UP:n=this._trimAlignValue(i+(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.PAGE_DOWN:n=this._trimAlignValue(i-(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:if(i===this._valueMax())return;n=this._trimAlignValue(i+s);break;case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(i===this._valueMin())return;n=this._trimAlignValue(i-s)}this._slide(e,o,n)},keyup:function(e){var i=t(e.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(e,i),this._change(e,i),this._removeClass(t(e.target),null,"ui-state-active"))}}}),t.widget("ui.sortable",t.ui.mouse,{version:"1.12.1",widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1e3,activate:null,beforeStop:null,change:null,deactivate:null,out:null,over:null,receive:null,remove:null,sort:null,start:null,stop:null,update:null},_isOverAxis:function(t,e,i){return t>=e&&e+i>t},_isFloating:function(t){return/left|right/.test(t.css("float"))||/inline|table-cell/.test(t.css("display"))},_create:function(){this.containerCache={},this._addClass("ui-sortable"),this.refresh(),this.offset=this.element.offset(),this._mouseInit(),this._setHandleClassName(),this.ready=!0},_setOption:function(t,e){this._super(t,e),"handle"===t&&this._setHandleClassName()},_setHandleClassName:function(){var e=this;this._removeClass(this.element.find(".ui-sortable-handle"),"ui-sortable-handle"),t.each(this.items,function(){e._addClass(this.instance.options.handle?this.item.find(this.instance.options.handle):this.item,"ui-sortable-handle")})},_destroy:function(){this._mouseDestroy();for(var t=this.items.length-1;t>=0;t--)this.items[t].item.removeData(this.widgetName+"-item");return this},_mouseCapture:function(e,i){var n=null,s=!1,o=this;return!this.reverting&&(!this.options.disabled&&"static"!==this.options.type&&(this._refreshItems(e),t(e.target).parents().each(function(){return t.data(this,o.widgetName+"-item")===o?(n=t(this),!1):void 0}),t.data(e.target,o.widgetName+"-item")===o&&(n=t(e.target)),!!n&&(!(this.options.handle&&!i&&(t(this.options.handle,n).find("*").addBack().each(function(){this===e.target&&(s=!0)}),!s))&&(this.currentItem=n,this._removeCurrentsFromItems(),!0))))},_mouseStart:function(e,i,n){var s,o,a=this.options;if(this.currentContainer=this,this.refreshPositions(),this.helper=this._createHelper(e),this._cacheHelperProportions(),this._cacheMargins(),this.scrollParent=this.helper.scrollParent(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},t.extend(this.offset,{click:{left:e.pageX-this.offset.left,top:e.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()}),this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),this.originalPosition=this._generatePosition(e),this.originalPageX=e.pageX,this.originalPageY=e.pageY,a.cursorAt&&this._adjustOffsetFromHelper(a.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!==this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),a.containment&&this._setContainment(),a.cursor&&"auto"!==a.cursor&&(o=this.document.find("body"),this.storedCursor=o.css("cursor"),o.css("cursor",a.cursor),this.storedStylesheet=t("<style>*{ cursor: "+a.cursor+" !important; }</style>").appendTo(o)),a.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",a.opacity)),a.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",a.zIndex)),this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",e,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions(),!n)for(s=this.containers.length-1;s>=0;s--)this.containers[s]._trigger("activate",e,this._uiHash(this));return t.ui.ddmanager&&(t.ui.ddmanager.current=this),t.ui.ddmanager&&!a.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e),this.dragging=!0,this._addClass(this.helper,"ui-sortable-helper"),this._mouseDrag(e),!0},_mouseDrag:function(e){var i,n,s,o,a=this.options,r=!1;for(this.position=this._generatePosition(e),this.positionAbs=this._convertPositionTo("absolute"),this.lastPositionAbs||(this.lastPositionAbs=this.positionAbs),this.options.scroll&&(this.scrollParent[0]!==this.document[0]&&"HTML"!==this.scrollParent[0].tagName?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-e.pageY<a.scrollSensitivity?this.scrollParent[0].scrollTop=r=this.scrollParent[0].scrollTop+a.scrollSpeed:e.pageY-this.overflowOffset.top<a.scrollSensitivity&&(this.scrollParent[0].scrollTop=r=this.scrollParent[0].scrollTop-a.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-e.pageX<a.scrollSensitivity?this.scrollParent[0].scrollLeft=r=this.scrollParent[0].scrollLeft+a.scrollSpeed:e.pageX-this.overflowOffset.left<a.scrollSensitivity&&(this.scrollParent[0].scrollLeft=r=this.scrollParent[0].scrollLeft-a.scrollSpeed)):(e.pageY-this.document.scrollTop()<a.scrollSensitivity?r=this.document.scrollTop(this.document.scrollTop()-a.scrollSpeed):this.window.height()-(e.pageY-this.document.scrollTop())<a.scrollSensitivity&&(r=this.document.scrollTop(this.document.scrollTop()+a.scrollSpeed)),e.pageX-this.document.scrollLeft()<a.scrollSensitivity?r=this.document.scrollLeft(this.document.scrollLeft()-a.scrollSpeed):this.window.width()-(e.pageX-this.document.scrollLeft())<a.scrollSensitivity&&(r=this.document.scrollLeft(this.document.scrollLeft()+a.scrollSpeed))),!1!==r&&t.ui.ddmanager&&!a.dropBehaviour&&t.ui.ddmanager.prepareOffsets(this,e)),this.positionAbs=this._convertPositionTo("absolute"),this.options.axis&&"y"===this.options.axis||(this.helper[0].style.left=this.position.left+"px"),this.options.axis&&"x"===this.options.axis||(this.helper[0].style.top=this.position.top+"px"),i=this.items.length-1;i>=0;i--)if(s=(n=this.items[i]).item[0],(o=this._intersectsWithPointer(n))&&n.instance===this.currentContainer&&s!==this.currentItem[0]&&this.placeholder[1===o?"next":"prev"]()[0]!==s&&!t.contains(this.placeholder[0],s)&&("semi-dynamic"!==this.options.type||!t.contains(this.element[0],s))){if(this.direction=1===o?"down":"up","pointer"!==this.options.tolerance&&!this._intersectsWithSides(n))break;this._rearrange(e,n),this._trigger("change",e,this._uiHash());break}return this._contactContainers(e),t.ui.ddmanager&&t.ui.ddmanager.drag(this,e),this._trigger("sort",e,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(e,i){if(e){if(t.ui.ddmanager&&!this.options.dropBehaviour&&t.ui.ddmanager.drop(this,e),this.options.revert){var n=this,s=this.placeholder.offset(),o=this.options.axis,a={};o&&"x"!==o||(a.left=s.left-this.offset.parent.left-this.margins.left+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollLeft)),o&&"y"!==o||(a.top=s.top-this.offset.parent.top-this.margins.top+(this.offsetParent[0]===this.document[0].body?0:this.offsetParent[0].scrollTop)),this.reverting=!0,t(this.helper).animate(a,parseInt(this.options.revert,10)||500,function(){n._clear(e)})}else this._clear(e,i);return!1}},cancel:function(){if(this.dragging){this._mouseUp(new t.Event("mouseup",{target:null})),"original"===this.options.helper?(this.currentItem.css(this._storedCSS),this._removeClass(this.currentItem,"ui-sortable-helper")):this.currentItem.show();for(var e=this.containers.length-1;e>=0;e--)this.containers[e]._trigger("deactivate",null,this._uiHash(this)),this.containers[e].containerCache.over&&(this.containers[e]._trigger("out",null,this._uiHash(this)),this.containers[e].containerCache.over=0)}return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),"original"!==this.options.helper&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),t.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?t(this.domPosition.prev).after(this.currentItem):t(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},t(i).each(function(){var i=(t(e.item||this).attr(e.attribute||"id")||"").match(e.expression||/(.+)[\-=_](.+)/);i&&n.push((e.key||i[1]+"[]")+"="+(e.key&&e.expression?i[1]:i[2]))}),!n.length&&e.key&&n.push(e.key+"="),n.join("&")},toArray:function(e){var i=this._getItemsAsjQuery(e&&e.connected),n=[];return e=e||{},i.each(function(){n.push(t(e.item||this).attr(e.attribute||"id")||"")}),n},_intersectsWith:function(t){var e=this.positionAbs.left,i=e+this.helperProportions.width,n=this.positionAbs.top,s=n+this.helperProportions.height,o=t.left,a=o+t.width,r=t.top,l=r+t.height,h=this.offset.click.top,c=this.offset.click.left,u="x"===this.options.axis||n+h>r&&l>n+h,d="y"===this.options.axis||e+c>o&&a>e+c,p=u&&d;return"pointer"===this.options.tolerance||this.options.forcePointerForContainers||"pointer"!==this.options.tolerance&&this.helperProportions[this.floating?"width":"height"]>t[this.floating?"width":"height"]?p:e+this.helperProportions.width/2>o&&a>i-this.helperProportions.width/2&&n+this.helperProportions.height/2>r&&l>s-this.helperProportions.height/2},_intersectsWithPointer:function(t){var e,i,n="x"===this.options.axis||this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top,t.height),s="y"===this.options.axis||this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left,t.width);return!!(n&&s)&&(e=this._getDragVerticalDirection(),i=this._getDragHorizontalDirection(),this.floating?"right"===i||"down"===e?2:1:e&&("down"===e?2:1))},_intersectsWithSides:function(t){var e=this._isOverAxis(this.positionAbs.top+this.offset.click.top,t.top+t.height/2,t.height),i=this._isOverAxis(this.positionAbs.left+this.offset.click.left,t.left+t.width/2,t.width),n=this._getDragVerticalDirection(),s=this._getDragHorizontalDirection();return this.floating&&s?"right"===s&&i||"left"===s&&!i:n&&("down"===n&&e||"up"===n&&!e)},_getDragVerticalDirection:function(){var t=this.positionAbs.top-this.lastPositionAbs.top;return 0!==t&&(t>0?"down":"up")},_getDragHorizontalDirection:function(){var t=this.positionAbs.left-this.lastPositionAbs.left;return 0!==t&&(t>0?"right":"left")},refresh:function(t){return this._refreshItems(t),this._setHandleClassName(),this.refreshPositions(),this},_connectWith:function(){var t=this.options;return t.connectWith.constructor===String?[t.connectWith]:t.connectWith},_getItemsAsjQuery:function(e){function i(){r.push(this)}var n,s,o,a,r=[],l=[],h=this._connectWith();if(h&&e)for(n=h.length-1;n>=0;n--)for(s=(o=t(h[n],this.document[0])).length-1;s>=0;s--)(a=t.data(o[s],this.widgetFullName))&&a!==this&&!a.options.disabled&&l.push([t.isFunction(a.options.items)?a.options.items.call(a.element):t(a.options.items,a.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),a]);for(l.push([t.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):t(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]),n=l.length-1;n>=0;n--)l[n][0].each(i);return t(r)},_removeCurrentsFromItems:function(){var e=this.currentItem.find(":data("+this.widgetName+"-item)");this.items=t.grep(this.items,function(t){for(var i=0;e.length>i;i++)if(e[i]===t.item[0])return!1;return!0})},_refreshItems:function(e){this.items=[],this.containers=[this];var i,n,s,o,a,r,l,h,c=this.items,u=[[t.isFunction(this.options.items)?this.options.items.call(this.element[0],e,{item:this.currentItem}):t(this.options.items,this.element),this]],d=this._connectWith();if(d&&this.ready)for(i=d.length-1;i>=0;i--)for(n=(s=t(d[i],this.document[0])).length-1;n>=0;n--)(o=t.data(s[n],this.widgetFullName))&&o!==this&&!o.options.disabled&&(u.push([t.isFunction(o.options.items)?o.options.items.call(o.element[0],e,{item:this.currentItem}):t(o.options.items,o.element),o]),this.containers.push(o));for(i=u.length-1;i>=0;i--)for(a=u[i][1],n=0,h=(r=u[i][0]).length;h>n;n++)(l=t(r[n])).data(this.widgetName+"-item",a),c.push({item:l,instance:a,width:0,height:0,left:0,top:0})},refreshPositions:function(e){var i,n,s,o;for(this.floating=!!this.items.length&&("x"===this.options.axis||this._isFloating(this.items[0].item)),this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset()),i=this.items.length-1;i>=0;i--)(n=this.items[i]).instance!==this.currentContainer&&this.currentContainer&&n.item[0]!==this.currentItem[0]||(s=this.options.toleranceElement?t(this.options.toleranceElement,n.item):n.item,e||(n.width=s.outerWidth(),n.height=s.outerHeight()),o=s.offset(),n.left=o.left,n.top=o.top);if(this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);else for(i=this.containers.length-1;i>=0;i--)o=this.containers[i].element.offset(),this.containers[i].containerCache.left=o.left,this.containers[i].containerCache.top=o.top,this.containers[i].containerCache.width=this.containers[i].element.outerWidth(),this.containers[i].containerCache.height=this.containers[i].element.outerHeight();return this},_createPlaceholder:function(e){var i,n=(e=e||this).options;n.placeholder&&n.placeholder.constructor!==String||(i=n.placeholder,n.placeholder={element:function(){var n=e.currentItem[0].nodeName.toLowerCase(),s=t("<"+n+">",e.document[0]);return e._addClass(s,"ui-sortable-placeholder",i||e.currentItem[0].className)._removeClass(s,"ui-sortable-helper"),"tbody"===n?e._createTrPlaceholder(e.currentItem.find("tr").eq(0),t("<tr>",e.document[0]).appendTo(s)):"tr"===n?e._createTrPlaceholder(e.currentItem,s):"img"===n&&s.attr("src",e.currentItem.attr("src")),i||s.css("visibility","hidden"),s},update:function(t,s){(!i||n.forcePlaceholderSize)&&(s.height()||s.height(e.currentItem.innerHeight()-parseInt(e.currentItem.css("paddingTop")||0,10)-parseInt(e.currentItem.css("paddingBottom")||0,10)),s.width()||s.width(e.currentItem.innerWidth()-parseInt(e.currentItem.css("paddingLeft")||0,10)-parseInt(e.currentItem.css("paddingRight")||0,10)))}}),e.placeholder=t(n.placeholder.element.call(e.element,e.currentItem)),e.currentItem.after(e.placeholder),n.placeholder.update(e,e.placeholder)},_createTrPlaceholder:function(e,i){var n=this;e.children().each(function(){t("<td>&#160;</td>",n.document[0]).attr("colspan",t(this).attr("colspan")||1).appendTo(i)})},_contactContainers:function(e){var i,n,s,o,a,r,l,h,c,u,d=null,p=null;for(i=this.containers.length-1;i>=0;i--)if(!t.contains(this.currentItem[0],this.containers[i].element[0]))if(this._intersectsWith(this.containers[i].containerCache)){if(d&&t.contains(this.containers[i].element[0],d.element[0]))continue;d=this.containers[i],p=i}else this.containers[i].containerCache.over&&(this.containers[i]._trigger("out",e,this._uiHash(this)),this.containers[i].containerCache.over=0);if(d)if(1===this.containers.length)this.containers[p].containerCache.over||(this.containers[p]._trigger("over",e,this._uiHash(this)),this.containers[p].containerCache.over=1);else{for(s=1e4,o=null,a=(c=d.floating||this._isFloating(this.currentItem))?"left":"top",r=c?"width":"height",u=c?"pageX":"pageY",n=this.items.length-1;n>=0;n--)t.contains(this.containers[p].element[0],this.items[n].item[0])&&this.items[n].item[0]!==this.currentItem[0]&&(l=this.items[n].item.offset()[a],h=!1,e[u]-l>this.items[n][r]/2&&(h=!0),s>Math.abs(e[u]-l)&&(s=Math.abs(e[u]-l),o=this.items[n],this.direction=h?"up":"down"));if(!o&&!this.options.dropOnEmpty)return;if(this.currentContainer===this.containers[p])return void(this.currentContainer.containerCache.over||(this.containers[p]._trigger("over",e,this._uiHash()),this.currentContainer.containerCache.over=1));o?this._rearrange(e,o,null,!0):this._rearrange(e,null,this.containers[p].element,!0),this._trigger("change",e,this._uiHash()),this.containers[p]._trigger("change",e,this._uiHash(this)),this.currentContainer=this.containers[p],this.options.placeholder.update(this.currentContainer,this.placeholder),this.containers[p]._trigger("over",e,this._uiHash(this)),this.containers[p].containerCache.over=1}},_createHelper:function(e){var i=this.options,n=t.isFunction(i.helper)?t(i.helper.apply(this.element[0],[e,this.currentItem])):"clone"===i.helper?this.currentItem.clone():this.currentItem;return n.parents("body").length||t("parent"!==i.appendTo?i.appendTo:this.currentItem[0].parentNode)[0].appendChild(n[0]),n[0]===this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),(!n[0].style.width||i.forceHelperSize)&&n.width(this.currentItem.width()),(!n[0].style.height||i.forceHelperSize)&&n.height(this.currentItem.height()),n},_adjustOffsetFromHelper:function(e){"string"==typeof e&&(e=e.split(" ")),t.isArray(e)&&(e={left:+e[0],top:+e[1]||0}),"left"in e&&(this.offset.click.left=e.left+this.margins.left),"right"in e&&(this.offset.click.left=this.helperProportions.width-e.right+this.margins.left),"top"in e&&(this.offset.click.top=e.top+this.margins.top),"bottom"in e&&(this.offset.click.top=this.helperProportions.height-e.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var e=this.offsetParent.offset();return"absolute"===this.cssPosition&&this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])&&(e.left+=this.scrollParent.scrollLeft(),e.top+=this.scrollParent.scrollTop()),(this.offsetParent[0]===this.document[0].body||this.offsetParent[0].tagName&&"html"===this.offsetParent[0].tagName.toLowerCase()&&t.ui.ie)&&(e={top:0,left:0}),{top:e.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:e.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if("relative"===this.cssPosition){var t=this.currentItem.position();return{top:t.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:t.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var e,i,n,s=this.options;"parent"===s.containment&&(s.containment=this.helper[0].parentNode),("document"===s.containment||"window"===s.containment)&&(this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,"document"===s.containment?this.document.width():this.window.width()-this.helperProportions.width-this.margins.left,("document"===s.containment?this.document.height()||document.body.parentNode.scrollHeight:this.window.height()||this.document[0].body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top]),/^(document|window|parent)$/.test(s.containment)||(e=t(s.containment)[0],i=t(s.containment).offset(),n="hidden"!==t(e).css("overflow"),this.containment=[i.left+(parseInt(t(e).css("borderLeftWidth"),10)||0)+(parseInt(t(e).css("paddingLeft"),10)||0)-this.margins.left,i.top+(parseInt(t(e).css("borderTopWidth"),10)||0)+(parseInt(t(e).css("paddingTop"),10)||0)-this.margins.top,i.left+(n?Math.max(e.scrollWidth,e.offsetWidth):e.offsetWidth)-(parseInt(t(e).css("borderLeftWidth"),10)||0)-(parseInt(t(e).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,i.top+(n?Math.max(e.scrollHeight,e.offsetHeight):e.offsetHeight)-(parseInt(t(e).css("borderTopWidth"),10)||0)-(parseInt(t(e).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top])},_convertPositionTo:function(e,i){i||(i=this.position);var n="absolute"===e?1:-1,s="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,o=/(html|body)/i.test(s[0].tagName);return{top:i.top+this.offset.relative.top*n+this.offset.parent.top*n-("fixed"===this.cssPosition?-this.scrollParent.scrollTop():o?0:s.scrollTop())*n,left:i.left+this.offset.relative.left*n+this.offset.parent.left*n-("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():o?0:s.scrollLeft())*n}},_generatePosition:function(e){var i,n,s=this.options,o=e.pageX,a=e.pageY,r="absolute"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&t.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,l=/(html|body)/i.test(r[0].tagName);return"relative"!==this.cssPosition||this.scrollParent[0]!==this.document[0]&&this.scrollParent[0]!==this.offsetParent[0]||(this.offset.relative=this._getRelativeOffset()),this.originalPosition&&(this.containment&&(e.pageX-this.offset.click.left<this.containment[0]&&(o=this.containment[0]+this.offset.click.left),e.pageY-this.offset.click.top<this.containment[1]&&(a=this.containment[1]+this.offset.click.top),e.pageX-this.offset.click.left>this.containment[2]&&(o=this.containment[2]+this.offset.click.left),e.pageY-this.offset.click.top>this.containment[3]&&(a=this.containment[3]+this.offset.click.top)),s.grid&&(i=this.originalPageY+Math.round((a-this.originalPageY)/s.grid[1])*s.grid[1],a=this.containment?i-this.offset.click.top>=this.containment[1]&&i-this.offset.click.top<=this.containment[3]?i:i-this.offset.click.top>=this.containment[1]?i-s.grid[1]:i+s.grid[1]:i,n=this.originalPageX+Math.round((o-this.originalPageX)/s.grid[0])*s.grid[0],o=this.containment?n-this.offset.click.left>=this.containment[0]&&n-this.offset.click.left<=this.containment[2]?n:n-this.offset.click.left>=this.containment[0]?n-s.grid[0]:n+s.grid[0]:n)),{top:a-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+("fixed"===this.cssPosition?-this.scrollParent.scrollTop():l?0:r.scrollTop()),left:o-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+("fixed"===this.cssPosition?-this.scrollParent.scrollLeft():l?0:r.scrollLeft())}},_rearrange:function(t,e,i,n){i?i[0].appendChild(this.placeholder[0]):e.item[0].parentNode.insertBefore(this.placeholder[0],"down"===this.direction?e.item[0]:e.item[0].nextSibling),this.counter=this.counter?++this.counter:1;var s=this.counter;this._delay(function(){s===this.counter&&this.refreshPositions(!n)})},_clear:function(t,e){function i(t,e,i){return function(n){i._trigger(t,n,e._uiHash(e))}}this.reverting=!1;var n,s=[];if(!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem),this._noFinalSort=null,this.helper[0]===this.currentItem[0]){for(n in this._storedCSS)("auto"===this._storedCSS[n]||"static"===this._storedCSS[n])&&(this._storedCSS[n]="");this.currentItem.css(this._storedCSS),this._removeClass(this.currentItem,"ui-sortable-helper")}else this.currentItem.show();for(this.fromOutside&&!e&&s.push(function(t){this._trigger("receive",t,this._uiHash(this.fromOutside))}),!this.fromOutside&&this.domPosition.prev===this.currentItem.prev().not(".ui-sortable-helper")[0]&&this.domPosition.parent===this.currentItem.parent()[0]||e||s.push(function(t){this._trigger("update",t,this._uiHash())}),this!==this.currentContainer&&(e||(s.push(function(t){this._trigger("remove",t,this._uiHash())}),s.push(function(t){return function(e){t._trigger("receive",e,this._uiHash(this))}}.call(this,this.currentContainer)),s.push(function(t){return function(e){t._trigger("update",e,this._uiHash(this))}}.call(this,this.currentContainer)))),n=this.containers.length-1;n>=0;n--)e||s.push(i("deactivate",this,this.containers[n])),this.containers[n].containerCache.over&&(s.push(i("out",this,this.containers[n])),this.containers[n].containerCache.over=0);if(this.storedCursor&&(this.document.find("body").css("cursor",this.storedCursor),this.storedStylesheet.remove()),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex","auto"===this._storedZIndex?"":this._storedZIndex),this.dragging=!1,e||this._trigger("beforeStop",t,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.cancelHelperRemoval||(this.helper[0]!==this.currentItem[0]&&this.helper.remove(),this.helper=null),!e){for(n=0;s.length>n;n++)s[n].call(this,t);this._trigger("stop",t,this._uiHash())}return this.fromOutside=!1,!this.cancelHelperRemoval},_trigger:function(){!1===t.Widget.prototype._trigger.apply(this,arguments)&&this.cancel()},_uiHash:function(e){var i=e||this;return{helper:i.helper,placeholder:i.placeholder||t([]),position:i.position,originalPosition:i.originalPosition,offset:i.positionAbs,item:i.currentItem,sender:e?e.element:null}}}),t.widget("ui.spinner",{version:"1.12.1",defaultElement:"<input>",widgetEventPrefix:"spin",options:{classes:{"ui-spinner":"ui-corner-all","ui-spinner-down":"ui-corner-br","ui-spinner-up":"ui-corner-tr"},culture:null,icons:{down:"ui-icon-triangle-1-s",up:"ui-icon-triangle-1-n"},incremental:!0,max:null,min:null,numberFormat:null,page:10,step:1,change:null,spin:null,start:null,stop:null},_create:function(){this._setOption("max",this.options.max),this._setOption("min",this.options.min),this._setOption("step",this.options.step),""!==this.value()&&this._value(this.element.val(),!0),this._draw(),this._on(this._events),this._refresh(),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_getCreateOptions:function(){var e=this._super(),i=this.element;return t.each(["min","max","step"],function(t,n){var s=i.attr(n);null!=s&&s.length&&(e[n]=s)}),e},_events:{keydown:function(t){this._start(t)&&this._keydown(t)&&t.preventDefault()},keyup:"_stop",focus:function(){this.previous=this.element.val()},blur:function(t){return this.cancelBlur?void delete this.cancelBlur:(this._stop(),this._refresh(),void(this.previous!==this.element.val()&&this._trigger("change",t)))},mousewheel:function(t,e){if(e){if(!this.spinning&&!this._start(t))return!1;this._spin((e>0?1:-1)*this.options.step,t),clearTimeout(this.mousewheelTimer),this.mousewheelTimer=this._delay(function(){this.spinning&&this._stop(t)},100),t.preventDefault()}},"mousedown .ui-spinner-button":function(e){function i(){this.element[0]===t.ui.safeActiveElement(this.document[0])||(this.element.trigger("focus"),this.previous=n,this._delay(function(){this.previous=n}))}var n;n=this.element[0]===t.ui.safeActiveElement(this.document[0])?this.previous:this.element.val(),e.preventDefault(),i.call(this),this.cancelBlur=!0,this._delay(function(){delete this.cancelBlur,i.call(this)}),!1!==this._start(e)&&this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e)},"mouseup .ui-spinner-button":"_stop","mouseenter .ui-spinner-button":function(e){return t(e.currentTarget).hasClass("ui-state-active")?!1!==this._start(e)&&void this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e):void 0},"mouseleave .ui-spinner-button":"_stop"},_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap("<span>").parent().append("<a></a><a></a>")},_draw:function(){this._enhance(),this._addClass(this.uiSpinner,"ui-spinner","ui-widget ui-widget-content"),this._addClass("ui-spinner-input"),this.element.attr("role","spinbutton"),this.buttons=this.uiSpinner.children("a").attr("tabIndex",-1).attr("aria-hidden",!0).button({classes:{"ui-button":""}}),this._removeClass(this.buttons,"ui-corner-all"),this._addClass(this.buttons.first(),"ui-spinner-button ui-spinner-up"),this._addClass(this.buttons.last(),"ui-spinner-button ui-spinner-down"),this.buttons.first().button({icon:this.options.icons.up,showLabel:!1}),this.buttons.last().button({icon:this.options.icons.down,showLabel:!1}),this.buttons.height()>Math.ceil(.5*this.uiSpinner.height())&&this.uiSpinner.height()>0&&this.uiSpinner.height(this.uiSpinner.height())},_keydown:function(e){var i=this.options,n=t.ui.keyCode;switch(e.keyCode){case n.UP:return this._repeat(null,1,e),!0;case n.DOWN:return this._repeat(null,-1,e),!0;case n.PAGE_UP:return this._repeat(null,i.page,e),!0;case n.PAGE_DOWN:return this._repeat(null,-i.page,e),!0}return!1},_start:function(t){return!(!this.spinning&&!1===this._trigger("start",t))&&(this.counter||(this.counter=1),this.spinning=!0,!0)},_repeat:function(t,e,i){t=t||500,clearTimeout(this.timer),this.timer=this._delay(function(){this._repeat(40,e,i)},t),this._spin(e*this.options.step,i)},_spin:function(t,e){var i=this.value()||0;this.counter||(this.counter=1),i=this._adjustValue(i+t*this._increment(this.counter)),this.spinning&&!1===this._trigger("spin",e,{value:i})||(this._value(i),this.counter++)},_increment:function(e){var i=this.options.incremental;return i?t.isFunction(i)?i(e):Math.floor(e*e*e/5e4-e*e/500+17*e/200+1):1},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min&&(t=Math.max(t,this._precisionOf(this.options.min))),t},_precisionOf:function(t){var e=""+t,i=e.indexOf(".");return-1===i?0:e.length-i-1},_adjustValue:function(t){var e,i,n=this.options;return i=t-(e=null!==n.min?n.min:0),t=e+(i=Math.round(i/n.step)*n.step),t=parseFloat(t.toFixed(this._precision())),null!==n.max&&t>n.max?n.max:null!==n.min&&n.min>t?n.min:t},_stop:function(t){this.spinning&&(clearTimeout(this.timer),clearTimeout(this.mousewheelTimer),this.counter=0,this.spinning=!1,this._trigger("stop",t))},_setOption:function(t,e){var i,n,s;return"culture"===t||"numberFormat"===t?(i=this._parse(this.element.val()),this.options[t]=e,void this.element.val(this._format(i))):(("max"===t||"min"===t||"step"===t)&&"string"==typeof e&&(e=this._parse(e)),"icons"===t&&(n=this.buttons.first().find(".ui-icon"),this._removeClass(n,null,this.options.icons.up),this._addClass(n,null,e.up),s=this.buttons.last().find(".ui-icon"),this._removeClass(s,null,this.options.icons.down),this._addClass(s,null,e.down)),void this._super(t,e))},_setOptionDisabled:function(t){this._super(t),this._toggleClass(this.uiSpinner,null,"ui-state-disabled",!!t),this.element.prop("disabled",!!t),this.buttons.button(t?"disable":"enable")},_setOptions:o(function(t){this._super(t)}),_parse:function(t){return"string"==typeof t&&""!==t&&(t=window.Globalize&&this.options.numberFormat?Globalize.parseFloat(t,10,this.options.culture):+t),""===t||isNaN(t)?null:t},_format:function(t){return""===t?"":window.Globalize&&this.options.numberFormat?Globalize.format(t,this.options.numberFormat,this.options.culture):t},_refresh:function(){this.element.attr({"aria-valuemin":this.options.min,"aria-valuemax":this.options.max,"aria-valuenow":this._parse(this.element.val())})},isValid:function(){var t=this.value();return null!==t&&t===this._adjustValue(t)},_value:function(t,e){var i;""!==t&&(null!==(i=this._parse(t))&&(e||(i=this._adjustValue(i)),t=this._format(i))),this.element.val(t),this._refresh()},_destroy:function(){this.element.prop("disabled",!1).removeAttr("autocomplete role aria-valuemin aria-valuemax aria-valuenow"),this.uiSpinner.replaceWith(this.element)},stepUp:o(function(t){this._stepUp(t)}),_stepUp:function(t){this._start()&&(this._spin((t||1)*this.options.step),this._stop())},stepDown:o(function(t){this._stepDown(t)}),_stepDown:function(t){this._start()&&(this._spin((t||1)*-this.options.step),this._stop())},pageUp:o(function(t){this._stepUp((t||1)*this.options.page)}),pageDown:o(function(t){this._stepDown((t||1)*this.options.page)}),value:function(t){return arguments.length?void o(this._value).call(this,t):this._parse(this.element.val())},widget:function(){return this.uiSpinner}}),!1!==t.uiBackCompat&&t.widget("ui.spinner",t.ui.spinner,{_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap(this._uiSpinnerHtml()).parent().append(this._buttonHtml())},_uiSpinnerHtml:function(){return"<span>"},_buttonHtml:function(){return"<a></a><a></a>"}}),t.ui.spinner,t.widget("ui.tabs",{version:"1.12.1",delay:300,options:{active:null,classes:{"ui-tabs":"ui-corner-all","ui-tabs-nav":"ui-corner-all","ui-tabs-panel":"ui-corner-bottom","ui-tabs-tab":"ui-corner-top"},collapsible:!1,event:"click",heightStyle:"content",hide:null,show:null,activate:null,beforeActivate:null,beforeLoad:null,load:null},_isLocal:function(){var t=/#.*$/;return function(e){var i,n;i=e.href.replace(t,""),n=location.href.replace(t,"");try{i=decodeURIComponent(i)}catch(t){}try{n=decodeURIComponent(n)}catch(t){}return e.hash.length>1&&i===n}}(),_create:function(){var e=this,i=this.options;this.running=!1,this._addClass("ui-tabs","ui-widget ui-widget-content"),this._toggleClass("ui-tabs-collapsible",null,i.collapsible),this._processTabs(),i.active=this._initialActive(),t.isArray(i.disabled)&&(i.disabled=t.unique(i.disabled.concat(t.map(this.tabs.filter(".ui-state-disabled"),function(t){return e.tabs.index(t)}))).sort()),this.active=!1!==this.options.active&&this.anchors.length?this._findActive(i.active):t(),this._refresh(),this.active.length&&this.load(i.active)},_initialActive:function(){var e=this.options.active,i=this.options.collapsible,n=location.hash.substring(1);return null===e&&(n&&this.tabs.each(function(i,s){return t(s).attr("aria-controls")===n?(e=i,!1):void 0}),null===e&&(e=this.tabs.index(this.tabs.filter(".ui-tabs-active"))),(null===e||-1===e)&&(e=!!this.tabs.length&&0)),!1!==e&&(-1===(e=this.tabs.index(this.tabs.eq(e)))&&(e=!i&&0)),!i&&!1===e&&this.anchors.length&&(e=0),e},_getCreateEventData:function(){return{tab:this.active,panel:this.active.length?this._getPanelForTab(this.active):t()}},_tabKeydown:function(e){var i=t(t.ui.safeActiveElement(this.document[0])).closest("li"),n=this.tabs.index(i),s=!0;if(!this._handlePageNav(e)){switch(e.keyCode){case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:n++;break;case t.ui.keyCode.UP:case t.ui.keyCode.LEFT:s=!1,n--;break;case t.ui.keyCode.END:n=this.anchors.length-1;break;case t.ui.keyCode.HOME:n=0;break;case t.ui.keyCode.SPACE:return e.preventDefault(),clearTimeout(this.activating),void this._activate(n);case t.ui.keyCode.ENTER:return e.preventDefault(),clearTimeout(this.activating),void this._activate(n!==this.options.active&&n);default:return}e.preventDefault(),clearTimeout(this.activating),n=this._focusNextTab(n,s),e.ctrlKey||e.metaKey||(i.attr("aria-selected","false"),this.tabs.eq(n).attr("aria-selected","true"),this.activating=this._delay(function(){this.option("active",n)},this.delay))}},_panelKeydown:function(e){this._handlePageNav(e)||e.ctrlKey&&e.keyCode===t.ui.keyCode.UP&&(e.preventDefault(),this.active.trigger("focus"))},_handlePageNav:function(e){return e.altKey&&e.keyCode===t.ui.keyCode.PAGE_UP?(this._activate(this._focusNextTab(this.options.active-1,!1)),!0):e.altKey&&e.keyCode===t.ui.keyCode.PAGE_DOWN?(this._activate(this._focusNextTab(this.options.active+1,!0)),!0):void 0},_findNextTab:function(e,i){for(var n=this.tabs.length-1;-1!==t.inArray((e>n&&(e=0),0>e&&(e=n),e),this.options.disabled);)e=i?e+1:e-1;return e},_focusNextTab:function(t,e){return t=this._findNextTab(t,e),this.tabs.eq(t).trigger("focus"),t},_setOption:function(t,e){return"active"===t?void this._activate(e):(this._super(t,e),"collapsible"===t&&(this._toggleClass("ui-tabs-collapsible",null,e),e||!1!==this.options.active||this._activate(0)),"event"===t&&this._setupEvents(e),void("heightStyle"===t&&this._setupHeightStyle(e)))},_sanitizeSelector:function(t){return t?t.replace(/[!"$%&'()*+,.\/:;<=>?@\[\]\^`{|}~]/g,"\\$&"):""},refresh:function(){var e=this.options,i=this.tablist.children(":has(a[href])");e.disabled=t.map(i.filter(".ui-state-disabled"),function(t){return i.index(t)}),this._processTabs(),!1!==e.active&&this.anchors.length?this.active.length&&!t.contains(this.tablist[0],this.active[0])?this.tabs.length===e.disabled.length?(e.active=!1,this.active=t()):this._activate(this._findNextTab(Math.max(0,e.active-1),!1)):e.active=this.tabs.index(this.active):(e.active=!1,this.active=t()),this._refresh()},_refresh:function(){this._setOptionDisabled(this.options.disabled),this._setupEvents(this.options.event),this._setupHeightStyle(this.options.heightStyle),this.tabs.not(this.active).attr({"aria-selected":"false","aria-expanded":"false",tabIndex:-1}),this.panels.not(this._getPanelForTab(this.active)).hide().attr({"aria-hidden":"true"}),this.active.length?(this.active.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0}),this._addClass(this.active,"ui-tabs-active","ui-state-active"),this._getPanelForTab(this.active).show().attr({"aria-hidden":"false"})):this.tabs.eq(0).attr("tabIndex",0)},_processTabs:function(){var e=this,i=this.tabs,n=this.anchors,s=this.panels;this.tablist=this._getList().attr("role","tablist"),this._addClass(this.tablist,"ui-tabs-nav","ui-helper-reset ui-helper-clearfix ui-widget-header"),this.tablist.on("mousedown"+this.eventNamespace,"> li",function(e){t(this).is(".ui-state-disabled")&&e.preventDefault()}).on("focus"+this.eventNamespace,".ui-tabs-anchor",function(){t(this).closest("li").is(".ui-state-disabled")&&this.blur()}),this.tabs=this.tablist.find("> li:has(a[href])").attr({role:"tab",tabIndex:-1}),this._addClass(this.tabs,"ui-tabs-tab","ui-state-default"),this.anchors=this.tabs.map(function(){return t("a",this)[0]}).attr({role:"presentation",tabIndex:-1}),this._addClass(this.anchors,"ui-tabs-anchor"),this.panels=t(),this.anchors.each(function(i,n){var s,o,a,r=t(n).uniqueId().attr("id"),l=t(n).closest("li"),h=l.attr("aria-controls");e._isLocal(n)?(a=(s=n.hash).substring(1),o=e.element.find(e._sanitizeSelector(s))):(s="#"+(a=l.attr("aria-controls")||t({}).uniqueId()[0].id),(o=e.element.find(s)).length||(o=e._createPanel(a)).insertAfter(e.panels[i-1]||e.tablist),o.attr("aria-live","polite")),o.length&&(e.panels=e.panels.add(o)),h&&l.data("ui-tabs-aria-controls",h),l.attr({"aria-controls":a,"aria-labelledby":r}),o.attr("aria-labelledby",r)}),this.panels.attr("role","tabpanel"),this._addClass(this.panels,"ui-tabs-panel","ui-widget-content"),i&&(this._off(i.not(this.tabs)),this._off(n.not(this.anchors)),this._off(s.not(this.panels)))},_getList:function(){return this.tablist||this.element.find("ol, ul").eq(0)},_createPanel:function(e){return t("<div>").attr("id",e).data("ui-tabs-destroy",!0)},_setOptionDisabled:function(e){var i,n,s;for(t.isArray(e)&&(e.length?e.length===this.anchors.length&&(e=!0):e=!1),s=0;n=this.tabs[s];s++)i=t(n),!0===e||-1!==t.inArray(s,e)?(i.attr("aria-disabled","true"),this._addClass(i,null,"ui-state-disabled")):(i.removeAttr("aria-disabled"),this._removeClass(i,null,"ui-state-disabled"));this.options.disabled=e,this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!0===e)},_setupEvents:function(e){var i={};e&&t.each(e.split(" "),function(t,e){i[e]="_eventHandler"}),this._off(this.anchors.add(this.tabs).add(this.panels)),this._on(!0,this.anchors,{click:function(t){t.preventDefault()}}),this._on(this.anchors,i),this._on(this.tabs,{keydown:"_tabKeydown"}),this._on(this.panels,{keydown:"_panelKeydown"}),this._focusable(this.tabs),this._hoverable(this.tabs)},_setupHeightStyle:function(e){var i,n=this.element.parent();"fill"===e?(i=n.height(),i-=this.element.outerHeight()-this.element.height(),this.element.siblings(":visible").each(function(){var e=t(this),n=e.css("position");"absolute"!==n&&"fixed"!==n&&(i-=e.outerHeight(!0))}),this.element.children().not(this.panels).each(function(){i-=t(this).outerHeight(!0)}),this.panels.each(function(){t(this).height(Math.max(0,i-t(this).innerHeight()+t(this).height()))}).css("overflow","auto")):"auto"===e&&(i=0,this.panels.each(function(){i=Math.max(i,t(this).height("").height())}).height(i))},_eventHandler:function(e){var i=this.options,n=this.active,s=t(e.currentTarget).closest("li"),o=s[0]===n[0],a=o&&i.collapsible,r=a?t():this._getPanelForTab(s),l=n.length?this._getPanelForTab(n):t(),h={oldTab:n,oldPanel:l,newTab:a?t():s,newPanel:r};e.preventDefault(),s.hasClass("ui-state-disabled")||s.hasClass("ui-tabs-loading")||this.running||o&&!i.collapsible||!1===this._trigger("beforeActivate",e,h)||(i.active=!a&&this.tabs.index(s),this.active=o?t():s,this.xhr&&this.xhr.abort(),l.length||r.length||t.error("jQuery UI Tabs: Mismatching fragment identifier."),r.length&&this.load(this.tabs.index(s),e),this._toggle(e,h))},_toggle:function(e,i){function n(){o.running=!1,o._trigger("activate",e,i)}function s(){o._addClass(i.newTab.closest("li"),"ui-tabs-active","ui-state-active"),a.length&&o.options.show?o._show(a,o.options.show,n):(a.show(),n())}var o=this,a=i.newPanel,r=i.oldPanel;this.running=!0,r.length&&this.options.hide?this._hide(r,this.options.hide,function(){o._removeClass(i.oldTab.closest("li"),"ui-tabs-active","ui-state-active"),s()}):(this._removeClass(i.oldTab.closest("li"),"ui-tabs-active","ui-state-active"),r.hide(),s()),r.attr("aria-hidden","true"),i.oldTab.attr({"aria-selected":"false","aria-expanded":"false"}),a.length&&r.length?i.oldTab.attr("tabIndex",-1):a.length&&this.tabs.filter(function(){return 0===t(this).attr("tabIndex")}).attr("tabIndex",-1),a.attr("aria-hidden","false"),i.newTab.attr({"aria-selected":"true","aria-expanded":"true",tabIndex:0})},_activate:function(e){var i,n=this._findActive(e);n[0]!==this.active[0]&&(n.length||(n=this.active),i=n.find(".ui-tabs-anchor")[0],this._eventHandler({target:i,currentTarget:i,preventDefault:t.noop}))},_findActive:function(e){return!1===e?t():this.tabs.eq(e)},_getIndex:function(e){return"string"==typeof e&&(e=this.anchors.index(this.anchors.filter("[href$='"+t.ui.escapeSelector(e)+"']"))),e},_destroy:function(){this.xhr&&this.xhr.abort(),this.tablist.removeAttr("role").off(this.eventNamespace),this.anchors.removeAttr("role tabIndex").removeUniqueId(),this.tabs.add(this.panels).each(function(){t.data(this,"ui-tabs-destroy")?t(this).remove():t(this).removeAttr("role tabIndex aria-live aria-busy aria-selected aria-labelledby aria-hidden aria-expanded")}),this.tabs.each(function(){var e=t(this),i=e.data("ui-tabs-aria-controls");i?e.attr("aria-controls",i).removeData("ui-tabs-aria-controls"):e.removeAttr("aria-controls")}),this.panels.show(),"content"!==this.options.heightStyle&&this.panels.css("height","")},enable:function(e){var i=this.options.disabled;!1!==i&&(void 0===e?i=!1:(e=this._getIndex(e),i=t.isArray(i)?t.map(i,function(t){return t!==e?t:null}):t.map(this.tabs,function(t,i){return i!==e?i:null})),this._setOptionDisabled(i))},disable:function(e){var i=this.options.disabled;if(!0!==i){if(void 0===e)i=!0;else{if(e=this._getIndex(e),-1!==t.inArray(e,i))return;i=t.isArray(i)?t.merge([e],i).sort():[e]}this._setOptionDisabled(i)}},load:function(e,i){e=this._getIndex(e);var n=this,s=this.tabs.eq(e),o=s.find(".ui-tabs-anchor"),a=this._getPanelForTab(s),r={tab:s,panel:a},l=function(t,e){"abort"===e&&n.panels.stop(!1,!0),n._removeClass(s,"ui-tabs-loading"),a.removeAttr("aria-busy"),t===n.xhr&&delete n.xhr};this._isLocal(o[0])||(this.xhr=t.ajax(this._ajaxSettings(o,i,r)),this.xhr&&"canceled"!==this.xhr.statusText&&(this._addClass(s,"ui-tabs-loading"),a.attr("aria-busy","true"),this.xhr.done(function(t,e,s){setTimeout(function(){a.html(t),n._trigger("load",i,r),l(s,e)},1)}).fail(function(t,e){setTimeout(function(){l(t,e)},1)})))},_ajaxSettings:function(e,i,n){var s=this;return{url:e.attr("href").replace(/#.*$/,""),beforeSend:function(e,o){return s._trigger("beforeLoad",i,t.extend({jqXHR:e,ajaxSettings:o},n))}}},_getPanelForTab:function(e){var i=t(e).attr("aria-controls");return this.element.find(this._sanitizeSelector("#"+i))}}),!1!==t.uiBackCompat&&t.widget("ui.tabs",t.ui.tabs,{_processTabs:function(){this._superApply(arguments),this._addClass(this.tabs,"ui-tab")}}),t.ui.tabs,t.widget("ui.tooltip",{version:"1.12.1",options:{classes:{"ui-tooltip":"ui-corner-all ui-widget-shadow"},content:function(){var e=t(this).attr("title")||"";return t("<a>").text(e).html()},hide:!0,items:"[title]:not([disabled])",position:{my:"left top+15",at:"left bottom",collision:"flipfit flip"},show:!0,track:!1,close:null,open:null},_addDescribedBy:function(e,i){var n=(e.attr("aria-describedby")||"").split(/\s+/);n.push(i),e.data("ui-tooltip-id",i).attr("aria-describedby",t.trim(n.join(" ")))},_removeDescribedBy:function(e){var i=e.data("ui-tooltip-id"),n=(e.attr("aria-describedby")||"").split(/\s+/),s=t.inArray(i,n);-1!==s&&n.splice(s,1),e.removeData("ui-tooltip-id"),(n=t.trim(n.join(" ")))?e.attr("aria-describedby",n):e.removeAttr("aria-describedby")},_create:function(){this._on({mouseover:"open",focusin:"open"}),this.tooltips={},this.parents={},this.liveRegion=t("<div>").attr({role:"log","aria-live":"assertive","aria-relevant":"additions"}).appendTo(this.document[0].body),this._addClass(this.liveRegion,null,"ui-helper-hidden-accessible"),this.disabledTitles=t([])},_setOption:function(e,i){var n=this;this._super(e,i),"content"===e&&t.each(this.tooltips,function(t,e){n._updateContent(e.element)})},_setOptionDisabled:function(t){this[t?"_disable":"_enable"]()},_disable:function(){var e=this;t.each(this.tooltips,function(i,n){var s=t.Event("blur");s.target=s.currentTarget=n.element[0],e.close(s,!0)}),this.disabledTitles=this.disabledTitles.add(this.element.find(this.options.items).addBack().filter(function(){var e=t(this);return e.is("[title]")?e.data("ui-tooltip-title",e.attr("title")).removeAttr("title"):void 0}))},_enable:function(){this.disabledTitles.each(function(){var e=t(this);e.data("ui-tooltip-title")&&e.attr("title",e.data("ui-tooltip-title"))}),this.disabledTitles=t([])},open:function(e){var i=this,n=t(e?e.target:this.element).closest(this.options.items);n.length&&!n.data("ui-tooltip-id")&&(n.attr("title")&&n.data("ui-tooltip-title",n.attr("title")),n.data("ui-tooltip-open",!0),e&&"mouseover"===e.type&&n.parents().each(function(){var e,n=t(this);n.data("ui-tooltip-open")&&((e=t.Event("blur")).target=e.currentTarget=this,i.close(e,!0)),n.attr("title")&&(n.uniqueId(),i.parents[this.id]={element:this,title:n.attr("title")},n.attr("title",""))}),this._registerCloseHandlers(e,n),this._updateContent(n,e))},_updateContent:function(t,e){var i,n=this.options.content,s=this,o=e?e.type:null;return"string"==typeof n||n.nodeType||n.jquery?this._open(e,t,n):void((i=n.call(t[0],function(i){s._delay(function(){t.data("ui-tooltip-open")&&(e&&(e.type=o),this._open(e,t,i))})}))&&this._open(e,t,i))},_open:function(e,i,n){function s(t){h.of=t,a.is(":hidden")||a.position(h)}var o,a,r,l,h=t.extend({},this.options.position);if(n){if(o=this._find(i))return void o.tooltip.find(".ui-tooltip-content").html(n);i.is("[title]")&&(e&&"mouseover"===e.type?i.attr("title",""):i.removeAttr("title")),o=this._tooltip(i),a=o.tooltip,this._addDescribedBy(i,a.attr("id")),a.find(".ui-tooltip-content").html(n),this.liveRegion.children().hide(),(l=t("<div>").html(a.find(".ui-tooltip-content").html())).removeAttr("name").find("[name]").removeAttr("name"),l.removeAttr("id").find("[id]").removeAttr("id"),l.appendTo(this.liveRegion),this.options.track&&e&&/^mouse/.test(e.type)?(this._on(this.document,{mousemove:s}),s(e)):a.position(t.extend({of:i},this.options.position)),a.hide(),this._show(a,this.options.show),this.options.track&&this.options.show&&this.options.show.delay&&(r=this.delayedShow=setInterval(function(){a.is(":visible")&&(s(h.of),clearInterval(r))},t.fx.interval)),this._trigger("open",e,{tooltip:a})}},_registerCloseHandlers:function(e,i){var n={keyup:function(e){if(e.keyCode===t.ui.keyCode.ESCAPE){var n=t.Event(e);n.currentTarget=i[0],this.close(n,!0)}}};i[0]!==this.element[0]&&(n.remove=function(){this._removeTooltip(this._find(i).tooltip)}),e&&"mouseover"!==e.type||(n.mouseleave="close"),e&&"focusin"!==e.type||(n.focusout="close"),this._on(!0,i,n)},close:function(e){var i,n=this,s=t(e?e.currentTarget:this.element),o=this._find(s);return o?(i=o.tooltip,void(o.closing||(clearInterval(this.delayedShow),s.data("ui-tooltip-title")&&!s.attr("title")&&s.attr("title",s.data("ui-tooltip-title")),this._removeDescribedBy(s),o.hiding=!0,i.stop(!0),this._hide(i,this.options.hide,function(){n._removeTooltip(t(this))}),s.removeData("ui-tooltip-open"),this._off(s,"mouseleave focusout keyup"),s[0]!==this.element[0]&&this._off(s,"remove"),this._off(this.document,"mousemove"),e&&"mouseleave"===e.type&&t.each(this.parents,function(e,i){t(i.element).attr("title",i.title),delete n.parents[e]}),o.closing=!0,this._trigger("close",e,{tooltip:i}),o.hiding||(o.closing=!1)))):void s.removeData("ui-tooltip-open")},_tooltip:function(e){var i=t("<div>").attr("role","tooltip"),n=t("<div>").appendTo(i),s=i.uniqueId().attr("id");return this._addClass(n,"ui-tooltip-content"),this._addClass(i,"ui-tooltip","ui-widget ui-widget-content"),i.appendTo(this._appendTo(e)),this.tooltips[s]={element:e,tooltip:i}},_find:function(t){var e=t.data("ui-tooltip-id");return e?this.tooltips[e]:null},_removeTooltip:function(t){t.remove(),delete this.tooltips[t.attr("id")]},_appendTo:function(t){var e=t.closest(".ui-front, dialog");return e.length||(e=this.document[0].body),e},_destroy:function(){var e=this;t.each(this.tooltips,function(i,n){var s=t.Event("blur"),o=n.element;s.target=s.currentTarget=o[0],e.close(s,!0),t("#"+i).remove(),o.data("ui-tooltip-title")&&(o.attr("title")||o.attr("title",o.data("ui-tooltip-title")),o.removeData("ui-tooltip-title"))}),this.liveRegion.remove()}}),!1!==t.uiBackCompat&&t.widget("ui.tooltip",t.ui.tooltip,{options:{tooltipClass:null},_tooltip:function(){var t=this._superApply(arguments);return this.options.tooltipClass&&t.tooltip.addClass(this.options.tooltipClass),t}}),t.ui.tooltip}),"undefined"==typeof jQuery)throw new Error("Bootstrap's JavaScript requires jQuery");!function(t){"use strict";var e=jQuery.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1==e[0]&&9==e[1]&&e[2]<1||e[0]>3)throw new Error("Bootstrap's JavaScript requires jQuery version 1.9.1 or higher, but lower than version 4")}(),function(t){"use strict";t.fn.emulateTransitionEnd=function(e){var i=!1,n=this;t(this).one("bsTransitionEnd",function(){i=!0});return setTimeout(function(){i||t(n).trigger(t.support.transition.end)},e),this},t(function(){t.support.transition=function(){var t=document.createElement("bootstrap"),e={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var i in e)if(void 0!==t.style[i])return{end:e[i]};return!1}(),t.support.transition&&(t.event.special.bsTransitionEnd={bindType:t.support.transition.end,delegateType:t.support.transition.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}})})}(jQuery),function(t){"use strict";var e='[data-dismiss="alert"]',i=function(i){t(i).on("click",e,this.close)};i.VERSION="3.3.7",i.TRANSITION_DURATION=150,i.prototype.close=function(e){function n(){a.detach().trigger("closed.bs.alert").remove()}var s=t(this),o=s.attr("data-target");o||(o=(o=s.attr("href"))&&o.replace(/.*(?=#[^\s]*$)/,""));var a=t("#"===o?[]:o);e&&e.preventDefault(),a.length||(a=s.closest(".alert")),a.trigger(e=t.Event("close.bs.alert")),e.isDefaultPrevented()||(a.removeClass("in"),t.support.transition&&a.hasClass("fade")?a.one("bsTransitionEnd",n).emulateTransitionEnd(i.TRANSITION_DURATION):n())};var n=t.fn.alert;t.fn.alert=function(e){return this.each(function(){var n=t(this),s=n.data("bs.alert");s||n.data("bs.alert",s=new i(this)),"string"==typeof e&&s[e].call(n)})},t.fn.alert.Constructor=i,t.fn.alert.noConflict=function(){return t.fn.alert=n,this},t(document).on("click.bs.alert.data-api",e,i.prototype.close)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),s=n.data("bs.button"),o="object"==typeof e&&e;s||n.data("bs.button",s=new i(this,o)),"toggle"==e?s.toggle():e&&s.setState(e)})}var i=function(e,n){this.$element=t(e),this.options=t.extend({},i.DEFAULTS,n),this.isLoading=!1};i.VERSION="3.3.7",i.DEFAULTS={loadingText:"loading..."},i.prototype.setState=function(e){var i="disabled",n=this.$element,s=n.is("input")?"val":"html",o=n.data();e+="Text",null==o.resetText&&n.data("resetText",n[s]()),setTimeout(t.proxy(function(){n[s](null==o[e]?this.options[e]:o[e]),"loadingText"==e?(this.isLoading=!0,n.addClass(i).attr(i,i).prop(i,!0)):this.isLoading&&(this.isLoading=!1,n.removeClass(i).removeAttr(i).prop(i,!1))},this),0)},i.prototype.toggle=function(){var t=!0,e=this.$element.closest('[data-toggle="buttons"]');if(e.length){var i=this.$element.find("input");"radio"==i.prop("type")?(i.prop("checked")&&(t=!1),e.find(".active").removeClass("active"),this.$element.addClass("active")):"checkbox"==i.prop("type")&&(i.prop("checked")!==this.$element.hasClass("active")&&(t=!1),this.$element.toggleClass("active")),i.prop("checked",this.$element.hasClass("active")),t&&i.trigger("change")}else this.$element.attr("aria-pressed",!this.$element.hasClass("active")),this.$element.toggleClass("active")};var n=t.fn.button;t.fn.button=e,t.fn.button.Constructor=i,t.fn.button.noConflict=function(){return t.fn.button=n,this},t(document).on("click.bs.button.data-api",'[data-toggle^="button"]',function(i){var n=t(i.target).closest(".btn");e.call(n,"toggle"),t(i.target).is('input[type="radio"], input[type="checkbox"]')||(i.preventDefault(),n.is("input,button")?n.trigger("focus"):n.find("input:visible,button:visible").first().trigger("focus"))}).on("focus.bs.button.data-api blur.bs.button.data-api",'[data-toggle^="button"]',function(e){t(e.target).closest(".btn").toggleClass("focus",/^focus(in)?$/.test(e.type))})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),s=n.data("bs.carousel"),o=t.extend({},i.DEFAULTS,n.data(),"object"==typeof e&&e),a="string"==typeof e?e:o.slide;s||n.data("bs.carousel",s=new i(this,o)),"number"==typeof e?s.to(e):a?s[a]():o.interval&&s.pause().cycle()})}var i=function(e,i){this.$element=t(e),this.$indicators=this.$element.find(".carousel-indicators"),this.options=i,this.paused=null,this.sliding=null,this.interval=null,this.$active=null,this.$items=null,this.options.keyboard&&this.$element.on("keydown.bs.carousel",t.proxy(this.keydown,this)),"hover"==this.options.pause&&!("ontouchstart"in document.documentElement)&&this.$element.on("mouseenter.bs.carousel",t.proxy(this.pause,this)).on("mouseleave.bs.carousel",t.proxy(this.cycle,this))};i.VERSION="3.3.7",i.TRANSITION_DURATION=600,i.DEFAULTS={interval:5e3,pause:"hover",wrap:!0,keyboard:!0},i.prototype.keydown=function(t){if(!/input|textarea/i.test(t.target.tagName)){switch(t.which){case 37:this.prev();break;case 39:this.next();break;default:return}t.preventDefault()}},i.prototype.cycle=function(e){return e||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(t.proxy(this.next,this),this.options.interval)),this},i.prototype.getItemIndex=function(t){return this.$items=t.parent().children(".item"),this.$items.index(t||this.$active)},i.prototype.getItemForDirection=function(t,e){var i=this.getItemIndex(e);if(("prev"==t&&0===i||"next"==t&&i==this.$items.length-1)&&!this.options.wrap)return e;var n=(i+("prev"==t?-1:1))%this.$items.length;return this.$items.eq(n)},i.prototype.to=function(t){var e=this,i=this.getItemIndex(this.$active=this.$element.find(".item.active"));if(!(t>this.$items.length-1||t<0))return this.sliding?this.$element.one("slid.bs.carousel",function(){e.to(t)}):i==t?this.pause().cycle():this.slide(t>i?"next":"prev",this.$items.eq(t))},i.prototype.pause=function(e){return e||(this.paused=!0),this.$element.find(".next, .prev").length&&t.support.transition&&(this.$element.trigger(t.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},i.prototype.next=function(){if(!this.sliding)return this.slide("next")},i.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},i.prototype.slide=function(e,n){var s=this.$element.find(".item.active"),o=n||this.getItemForDirection(e,s),a=this.interval,r="next"==e?"left":"right",l=this;if(o.hasClass("active"))return this.sliding=!1;var h=o[0],c=t.Event("slide.bs.carousel",{relatedTarget:h,direction:r});if(this.$element.trigger(c),!c.isDefaultPrevented()){if(this.sliding=!0,a&&this.pause(),this.$indicators.length){this.$indicators.find(".active").removeClass("active");var u=t(this.$indicators.children()[this.getItemIndex(o)]);u&&u.addClass("active")}var d=t.Event("slid.bs.carousel",{relatedTarget:h,direction:r});return t.support.transition&&this.$element.hasClass("slide")?(o.addClass(e),o[0].offsetWidth,s.addClass(r),o.addClass(r),s.one("bsTransitionEnd",function(){o.removeClass([e,r].join(" ")).addClass("active"),s.removeClass(["active",r].join(" ")),l.sliding=!1,setTimeout(function(){l.$element.trigger(d)},0)}).emulateTransitionEnd(i.TRANSITION_DURATION)):(s.removeClass("active"),o.addClass("active"),this.sliding=!1,this.$element.trigger(d)),a&&this.cycle(),this}};var n=t.fn.carousel;t.fn.carousel=e,t.fn.carousel.Constructor=i,t.fn.carousel.noConflict=function(){return t.fn.carousel=n,this};var s=function(i){var n,s=t(this),o=t(s.attr("data-target")||(n=s.attr("href"))&&n.replace(/.*(?=#[^\s]+$)/,""));if(o.hasClass("carousel")){var a=t.extend({},o.data(),s.data()),r=s.attr("data-slide-to");r&&(a.interval=!1),e.call(o,a),r&&o.data("bs.carousel").to(r),i.preventDefault()}};t(document).on("click.bs.carousel.data-api","[data-slide]",s).on("click.bs.carousel.data-api","[data-slide-to]",s),t(window).on("load",function(){t('[data-ride="carousel"]').each(function(){var i=t(this);e.call(i,i.data())})})}(jQuery),function(t){"use strict";function e(e){var i,n=e.attr("data-target")||(i=e.attr("href"))&&i.replace(/.*(?=#[^\s]+$)/,"");return t(n)}function i(e){return this.each(function(){var i=t(this),s=i.data("bs.collapse"),o=t.extend({},n.DEFAULTS,i.data(),"object"==typeof e&&e);!s&&o.toggle&&/show|hide/.test(e)&&(o.toggle=!1),s||i.data("bs.collapse",s=new n(this,o)),"string"==typeof e&&s[e]()})}var n=function(e,i){this.$element=t(e),this.options=t.extend({},n.DEFAULTS,i),this.$trigger=t('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'),this.transitioning=null,this.options.parent?this.$parent=this.getParent():this.addAriaAndCollapsedClass(this.$element,this.$trigger),this.options.toggle&&this.toggle()};n.VERSION="3.3.7",n.TRANSITION_DURATION=350,n.DEFAULTS={toggle:!0},n.prototype.dimension=function(){return this.$element.hasClass("width")?"width":"height"},n.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var e,s=this.$parent&&this.$parent.children(".panel").children(".in, .collapsing");if(!(s&&s.length&&(e=s.data("bs.collapse"),e&&e.transitioning))){var o=t.Event("show.bs.collapse");if(this.$element.trigger(o),!o.isDefaultPrevented()){s&&s.length&&(i.call(s,"hide"),e||s.data("bs.collapse",null));var a=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[a](0).attr("aria-expanded",!0),this.$trigger.removeClass("collapsed").attr("aria-expanded",!0),this.transitioning=1;var r=function(){this.$element.removeClass("collapsing").addClass("collapse in")[a](""),this.transitioning=0,this.$element.trigger("shown.bs.collapse")};if(!t.support.transition)return r.call(this);var l=t.camelCase(["scroll",a].join("-"));this.$element.one("bsTransitionEnd",t.proxy(r,this)).emulateTransitionEnd(n.TRANSITION_DURATION)[a](this.$element[0][l])}}}},n.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var e=t.Event("hide.bs.collapse");if(this.$element.trigger(e),!e.isDefaultPrevented()){var i=this.dimension();this.$element[i](this.$element[i]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse in").attr("aria-expanded",!1),this.$trigger.addClass("collapsed").attr("aria-expanded",!1),this.transitioning=1;var s=function(){this.transitioning=0,this.$element.removeClass("collapsing").addClass("collapse").trigger("hidden.bs.collapse")};return t.support.transition?void this.$element[i](0).one("bsTransitionEnd",t.proxy(s,this)).emulateTransitionEnd(n.TRANSITION_DURATION):s.call(this)}}},n.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]()},n.prototype.getParent=function(){return t(this.options.parent).find('[data-toggle="collapse"][data-parent="'+this.options.parent+'"]').each(t.proxy(function(i,n){var s=t(n);this.addAriaAndCollapsedClass(e(s),s)},this)).end()},n.prototype.addAriaAndCollapsedClass=function(t,e){var i=t.hasClass("in");t.attr("aria-expanded",i),e.toggleClass("collapsed",!i).attr("aria-expanded",i)};var s=t.fn.collapse;t.fn.collapse=i,t.fn.collapse.Constructor=n,t.fn.collapse.noConflict=function(){return t.fn.collapse=s,this},t(document).on("click.bs.collapse.data-api",'[data-toggle="collapse"]',function(n){var s=t(this);s.attr("data-target")||n.preventDefault();var o=e(s),a=o.data("bs.collapse")?"toggle":s.data();i.call(o,a)})}(jQuery),function(t){"use strict";function e(e){var i=e.attr("data-target");i||(i=(i=e.attr("href"))&&/#[A-Za-z]/.test(i)&&i.replace(/.*(?=#[^\s]*$)/,""));var n=i&&t(i);return n&&n.length?n:e.parent()}function i(i){i&&3===i.which||(t(n).remove(),t(s).each(function(){var n=t(this),s=e(n),o={relatedTarget:this};s.hasClass("open")&&(i&&"click"==i.type&&/input|textarea/i.test(i.target.tagName)&&t.contains(s[0],i.target)||(s.trigger(i=t.Event("hide.bs.dropdown",o)),i.isDefaultPrevented()||(n.attr("aria-expanded","false"),s.removeClass("open").trigger(t.Event("hidden.bs.dropdown",o)))))}))}var n=".dropdown-backdrop",s='[data-toggle="dropdown"]',o=function(e){t(e).on("click.bs.dropdown",this.toggle)};o.VERSION="3.3.7",o.prototype.toggle=function(n){var s=t(this);if(!s.is(".disabled, :disabled")){var o=e(s),a=o.hasClass("open");if(i(),!a){"ontouchstart"in document.documentElement&&!o.closest(".navbar-nav").length&&t(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(t(this)).on("click",i);var r={relatedTarget:this};if(o.trigger(n=t.Event("show.bs.dropdown",r)),n.isDefaultPrevented())return;s.trigger("focus").attr("aria-expanded","true"),o.toggleClass("open").trigger(t.Event("shown.bs.dropdown",r))}return!1}},o.prototype.keydown=function(i){if(/(38|40|27|32)/.test(i.which)&&!/input|textarea/i.test(i.target.tagName)){var n=t(this);if(i.preventDefault(),i.stopPropagation(),!n.is(".disabled, :disabled")){var o=e(n),a=o.hasClass("open");if(!a&&27!=i.which||a&&27==i.which)return 27==i.which&&o.find(s).trigger("focus"),n.trigger("click");var r=o.find(".dropdown-menu li:not(.disabled):visible a");if(r.length){var l=r.index(i.target);38==i.which&&l>0&&l--,40==i.which&&l<r.length-1&&l++,~l||(l=0),r.eq(l).trigger("focus")}}}};var a=t.fn.dropdown;t.fn.dropdown=function(e){return this.each(function(){var i=t(this),n=i.data("bs.dropdown");n||i.data("bs.dropdown",n=new o(this)),"string"==typeof e&&n[e].call(i)})},t.fn.dropdown.Constructor=o,t.fn.dropdown.noConflict=function(){return t.fn.dropdown=a,this},t(document).on("click.bs.dropdown.data-api",i).on("click.bs.dropdown.data-api",".dropdown form",function(t){t.stopPropagation()}).on("click.bs.dropdown.data-api",s,o.prototype.toggle).on("keydown.bs.dropdown.data-api",s,o.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",o.prototype.keydown)}(jQuery),function(t){"use strict";function e(e,n){return this.each(function(){var s=t(this),o=s.data("bs.modal"),a=t.extend({},i.DEFAULTS,s.data(),"object"==typeof e&&e);o||s.data("bs.modal",o=new i(this,a)),"string"==typeof e?o[e](n):a.show&&o.show(n)})}var i=function(e,i){this.options=i,this.$body=t(document.body),this.$element=t(e),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,t.proxy(function(){this.$element.trigger("loaded.bs.modal")},this))};i.VERSION="3.3.7",i.TRANSITION_DURATION=300,i.BACKDROP_TRANSITION_DURATION=150,i.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},i.prototype.toggle=function(t){return this.isShown?this.hide():this.show(t)},i.prototype.show=function(e){var n=this,s=t.Event("show.bs.modal",{relatedTarget:e});this.$element.trigger(s),this.isShown||s.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',t.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",function(){n.$element.one("mouseup.dismiss.bs.modal",function(e){t(e.target).is(n.$element)&&(n.ignoreBackdropClick=!0)})}),this.backdrop(function(){var s=t.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(n.$body),n.$element.show().scrollTop(0),n.adjustDialog(),s&&n.$element[0].offsetWidth,n.$element.addClass("in"),n.enforceFocus();var o=t.Event("shown.bs.modal",{relatedTarget:e});s?n.$dialog.one("bsTransitionEnd",function(){n.$element.trigger("focus").trigger(o)}).emulateTransitionEnd(i.TRANSITION_DURATION):n.$element.trigger("focus").trigger(o)}))},i.prototype.hide=function(e){e&&e.preventDefault(),e=t.Event("hide.bs.modal"),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),t(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(i.TRANSITION_DURATION):this.hideModal())},i.prototype.enforceFocus=function(){t(document).off("focusin.bs.modal").on("focusin.bs.modal",t.proxy(function(t){document===t.target||this.$element[0]===t.target||this.$element.has(t.target).length||this.$element.trigger("focus")},this))},i.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",t.proxy(function(t){27==t.which&&this.hide()},this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},i.prototype.resize=function(){this.isShown?t(window).on("resize.bs.modal",t.proxy(this.handleUpdate,this)):t(window).off("resize.bs.modal")},i.prototype.hideModal=function(){var t=this;this.$element.hide(),this.backdrop(function(){t.$body.removeClass("modal-open"),t.resetAdjustments(),t.resetScrollbar(),t.$element.trigger("hidden.bs.modal")})},i.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},i.prototype.backdrop=function(e){var n=this,s=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var o=t.support.transition&&s;if(this.$backdrop=t(document.createElement("div")).addClass("modal-backdrop "+s).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",t.proxy(function(t){return this.ignoreBackdropClick?void(this.ignoreBackdropClick=!1):void(t.target===t.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide()))},this)),o&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!e)return;o?this.$backdrop.one("bsTransitionEnd",e).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):e()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var a=function(){n.removeBackdrop(),e&&e()};t.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",a).emulateTransitionEnd(i.BACKDROP_TRANSITION_DURATION):a()}else e&&e()},i.prototype.handleUpdate=function(){this.adjustDialog()},i.prototype.adjustDialog=function(){var t=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&t?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!t?this.scrollbarWidth:""})},i.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},i.prototype.checkScrollbar=function(){var t=window.innerWidth;if(!t){var e=document.documentElement.getBoundingClientRect();t=e.right-Math.abs(e.left)}this.bodyIsOverflowing=document.body.clientWidth<t,this.scrollbarWidth=this.measureScrollbar()},i.prototype.setScrollbar=function(){var t=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"",this.bodyIsOverflowing&&this.$body.css("padding-right",t+this.scrollbarWidth)},i.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad)},i.prototype.measureScrollbar=function(){var t=document.createElement("div");t.className="modal-scrollbar-measure",this.$body.append(t);var e=t.offsetWidth-t.clientWidth;return this.$body[0].removeChild(t),e};var n=t.fn.modal;t.fn.modal=e,t.fn.modal.Constructor=i,t.fn.modal.noConflict=function(){return t.fn.modal=n,this},t(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(i){var n=t(this),s=n.attr("href"),o=t(n.attr("data-target")||s&&s.replace(/.*(?=#[^\s]+$)/,"")),a=o.data("bs.modal")?"toggle":t.extend({remote:!/#/.test(s)&&s},o.data(),n.data());n.is("a")&&i.preventDefault(),o.one("show.bs.modal",function(t){t.isDefaultPrevented()||o.one("hidden.bs.modal",function(){n.is(":visible")&&n.trigger("focus")})}),e.call(o,a,this)})}(jQuery),function(t){"use strict";var e=function(t,e){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",t,e)};e.VERSION="3.3.7",e.TRANSITION_DURATION=150,e.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0}},e.prototype.init=function(e,i,n){if(this.enabled=!0,this.type=e,this.$element=t(i),this.options=this.getOptions(n),this.$viewport=this.options.viewport&&t(t.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var s=this.options.trigger.split(" "),o=s.length;o--;){var a=s[o];if("click"==a)this.$element.on("click."+this.type,this.options.selector,t.proxy(this.toggle,this));else if("manual"!=a){var r="hover"==a?"mouseenter":"focusin",l="hover"==a?"mouseleave":"focusout";this.$element.on(r+"."+this.type,this.options.selector,t.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,t.proxy(this.leave,this))}}this.options.selector?this._options=t.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.getOptions=function(e){return(e=t.extend({},this.getDefaults(),this.$element.data(),e)).delay&&"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),e},e.prototype.getDelegateOptions=function(){var e={},i=this.getDefaults();return this._options&&t.each(this._options,function(t,n){i[t]!=n&&(e[t]=n)}),e},e.prototype.enter=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);return i||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i)),e instanceof t.Event&&(i.inState["focusin"==e.type?"focus":"hover"]=!0),i.tip().hasClass("in")||"in"==i.hoverState?void(i.hoverState="in"):(clearTimeout(i.timeout),i.hoverState="in",i.options.delay&&i.options.delay.show?void(i.timeout=setTimeout(function(){"in"==i.hoverState&&i.show()},i.options.delay.show)):i.show())},e.prototype.isInStateTrue=function(){for(var t in this.inState)if(this.inState[t])return!0;return!1},e.prototype.leave=function(e){var i=e instanceof this.constructor?e:t(e.currentTarget).data("bs."+this.type);if(i||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i)),e instanceof t.Event&&(i.inState["focusout"==e.type?"focus":"hover"]=!1),!i.isInStateTrue())return clearTimeout(i.timeout),i.hoverState="out",i.options.delay&&i.options.delay.hide?void(i.timeout=setTimeout(function(){"out"==i.hoverState&&i.hide()},i.options.delay.hide)):i.hide()},e.prototype.show=function(){var i=t.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(i);var n=t.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(i.isDefaultPrevented()||!n)return;var s=this,o=this.tip(),a=this.getUID(this.type);this.setContent(),o.attr("id",a),this.$element.attr("aria-describedby",a),this.options.animation&&o.addClass("fade");var r="function"==typeof this.options.placement?this.options.placement.call(this,o[0],this.$element[0]):this.options.placement,l=/\s?auto?\s?/i,h=l.test(r);h&&(r=r.replace(l,"")||"top"),o.detach().css({top:0,left:0,display:"block"}).addClass(r).data("bs."+this.type,this),this.options.container?o.appendTo(this.options.container):o.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var c=this.getPosition(),u=o[0].offsetWidth,d=o[0].offsetHeight;if(h){var p=r,f=this.getPosition(this.$viewport);r="bottom"==r&&c.bottom+d>f.bottom?"top":"top"==r&&c.top-d<f.top?"bottom":"right"==r&&c.right+u>f.width?"left":"left"==r&&c.left-u<f.left?"right":r,o.removeClass(p).addClass(r)}var g=this.getCalculatedOffset(r,c,u,d);this.applyPlacement(g,r);var m=function(){var t=s.hoverState;s.$element.trigger("shown.bs."+s.type),s.hoverState=null,"out"==t&&s.leave(s)};t.support.transition&&this.$tip.hasClass("fade")?o.one("bsTransitionEnd",m).emulateTransitionEnd(e.TRANSITION_DURATION):m()}},e.prototype.applyPlacement=function(e,i){var n=this.tip(),s=n[0].offsetWidth,o=n[0].offsetHeight,a=parseInt(n.css("margin-top"),10),r=parseInt(n.css("margin-left"),10);isNaN(a)&&(a=0),isNaN(r)&&(r=0),e.top+=a,e.left+=r,t.offset.setOffset(n[0],t.extend({using:function(t){n.css({top:Math.round(t.top),left:Math.round(t.left)})}},e),0),n.addClass("in");var l=n[0].offsetWidth,h=n[0].offsetHeight;"top"==i&&h!=o&&(e.top=e.top+o-h);var c=this.getViewportAdjustedDelta(i,e,l,h);c.left?e.left+=c.left:e.top+=c.top;var u=/top|bottom/.test(i),d=u?2*c.left-s+l:2*c.top-o+h,p=u?"offsetWidth":"offsetHeight";n.offset(e),this.replaceArrow(d,n[0][p],u)},e.prototype.replaceArrow=function(t,e,i){this.arrow().css(i?"left":"top",50*(1-t/e)+"%").css(i?"top":"left","")},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle();t.find(".tooltip-inner")[this.options.html?"html":"text"](e),t.removeClass("fade in top bottom left right")},e.prototype.hide=function(i){function n(){"in"!=s.hoverState&&o.detach(),s.$element&&s.$element.removeAttr("aria-describedby").trigger("hidden.bs."+s.type),i&&i()}var s=this,o=t(this.$tip),a=t.Event("hide.bs."+this.type);if(this.$element.trigger(a),!a.isDefaultPrevented())return o.removeClass("in"),t.support.transition&&o.hasClass("fade")?o.one("bsTransitionEnd",n).emulateTransitionEnd(e.TRANSITION_DURATION):n(),this.hoverState=null,this},e.prototype.fixTitle=function(){var t=this.$element;(t.attr("title")||"string"!=typeof t.attr("data-original-title"))&&t.attr("data-original-title",t.attr("title")||"").attr("title","")},e.prototype.hasContent=function(){return this.getTitle()},e.prototype.getPosition=function(e){var i=(e=e||this.$element)[0],n="BODY"==i.tagName,s=i.getBoundingClientRect();null==s.width&&(s=t.extend({},s,{width:s.right-s.left,height:s.bottom-s.top}));var o=window.SVGElement&&i instanceof window.SVGElement,a=n?{top:0,left:0}:o?null:e.offset(),r={scroll:n?document.documentElement.scrollTop||document.body.scrollTop:e.scrollTop()},l=n?{width:t(window).width(),height:t(window).height()}:null;return t.extend({},s,r,l,a)},e.prototype.getCalculatedOffset=function(t,e,i,n){return"bottom"==t?{top:e.top+e.height,left:e.left+e.width/2-i/2}:"top"==t?{top:e.top-n,left:e.left+e.width/2-i/2}:"left"==t?{top:e.top+e.height/2-n/2,left:e.left-i}:{top:e.top+e.height/2-n/2,left:e.left+e.width}},e.prototype.getViewportAdjustedDelta=function(t,e,i,n){var s={top:0,left:0};if(!this.$viewport)return s;var o=this.options.viewport&&this.options.viewport.padding||0,a=this.getPosition(this.$viewport);if(/right|left/.test(t)){var r=e.top-o-a.scroll,l=e.top+o-a.scroll+n;r<a.top?s.top=a.top-r:l>a.top+a.height&&(s.top=a.top+a.height-l)}else{var h=e.left-o,c=e.left+o+i;h<a.left?s.left=a.left-h:c>a.right&&(s.left=a.left+a.width-c)}return s},e.prototype.getTitle=function(){var t=this.$element,e=this.options;return t.attr("data-original-title")||("function"==typeof e.title?e.title.call(t[0]):e.title)},e.prototype.getUID=function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},e.prototype.tip=function(){if(!this.$tip&&(this.$tip=t(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},e.prototype.enable=function(){this.enabled=!0},e.prototype.disable=function(){this.enabled=!1},e.prototype.toggleEnabled=function(){this.enabled=!this.enabled},e.prototype.toggle=function(e){var i=this;e&&((i=t(e.currentTarget).data("bs."+this.type))||(i=new this.constructor(e.currentTarget,this.getDelegateOptions()),t(e.currentTarget).data("bs."+this.type,i))),e?(i.inState.click=!i.inState.click,i.isInStateTrue()?i.enter(i):i.leave(i)):i.tip().hasClass("in")?i.leave(i):i.enter(i)},e.prototype.destroy=function(){var t=this;clearTimeout(this.timeout),this.hide(function(){t.$element.off("."+t.type).removeData("bs."+t.type),t.$tip&&t.$tip.detach(),t.$tip=null,t.$arrow=null,t.$viewport=null,t.$element=null})};var i=t.fn.tooltip;t.fn.tooltip=function(i){return this.each(function(){var n=t(this),s=n.data("bs.tooltip"),o="object"==typeof i&&i;!s&&/destroy|hide/.test(i)||(s||n.data("bs.tooltip",s=new e(this,o)),"string"==typeof i&&s[i]())})},t.fn.tooltip.Constructor=e,t.fn.tooltip.noConflict=function(){return t.fn.tooltip=i,this}}(jQuery),function(t){"use strict";var e=function(t,e){this.init("popover",t,e)};if(!t.fn.tooltip)throw new Error("Popover requires tooltip.js");e.VERSION="3.3.7",e.DEFAULTS=t.extend({},t.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),e.prototype=t.extend({},t.fn.tooltip.Constructor.prototype),e.prototype.constructor=e,e.prototype.getDefaults=function(){return e.DEFAULTS},e.prototype.setContent=function(){var t=this.tip(),e=this.getTitle(),i=this.getContent();t.find(".popover-title")[this.options.html?"html":"text"](e),t.find(".popover-content").children().detach().end()[this.options.html?"string"==typeof i?"html":"append":"text"](i),t.removeClass("fade top bottom left right in"),t.find(".popover-title").html()||t.find(".popover-title").hide()},e.prototype.hasContent=function(){return this.getTitle()||this.getContent()},e.prototype.getContent=function(){var t=this.$element,e=this.options;return t.attr("data-content")||("function"==typeof e.content?e.content.call(t[0]):e.content)},e.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow")};var i=t.fn.popover;t.fn.popover=function(i){return this.each(function(){var n=t(this),s=n.data("bs.popover"),o="object"==typeof i&&i;!s&&/destroy|hide/.test(i)||(s||n.data("bs.popover",s=new e(this,o)),"string"==typeof i&&s[i]())})},t.fn.popover.Constructor=e,t.fn.popover.noConflict=function(){return t.fn.popover=i,this}}(jQuery),function(t){"use strict";function e(i,n){this.$body=t(document.body),this.$scrollElement=t(t(i).is(document.body)?window:i),this.options=t.extend({},e.DEFAULTS,n),this.selector=(this.options.target||"")+" .nav li > a",this.offsets=[],this.targets=[],this.activeTarget=null,this.scrollHeight=0,this.$scrollElement.on("scroll.bs.scrollspy",t.proxy(this.process,this)),this.refresh(),this.process()}function i(i){return this.each(function(){var n=t(this),s=n.data("bs.scrollspy"),o="object"==typeof i&&i;s||n.data("bs.scrollspy",s=new e(this,o)),"string"==typeof i&&s[i]()})}e.VERSION="3.3.7",e.DEFAULTS={offset:10},e.prototype.getScrollHeight=function(){return this.$scrollElement[0].scrollHeight||Math.max(this.$body[0].scrollHeight,document.documentElement.scrollHeight)},e.prototype.refresh=function(){var e=this,i="offset",n=0;this.offsets=[],this.targets=[],this.scrollHeight=this.getScrollHeight(),t.isWindow(this.$scrollElement[0])||(i="position",n=this.$scrollElement.scrollTop()),this.$body.find(this.selector).map(function(){var e=t(this),s=e.data("target")||e.attr("href"),o=/^#./.test(s)&&t(s);return o&&o.length&&o.is(":visible")&&[[o[i]().top+n,s]]||null}).sort(function(t,e){return t[0]-e[0]}).each(function(){e.offsets.push(this[0]),e.targets.push(this[1])})},e.prototype.process=function(){var t,e=this.$scrollElement.scrollTop()+this.options.offset,i=this.getScrollHeight(),n=this.options.offset+i-this.$scrollElement.height(),s=this.offsets,o=this.targets,a=this.activeTarget;if(this.scrollHeight!=i&&this.refresh(),e>=n)return a!=(t=o[o.length-1])&&this.activate(t);if(a&&e<s[0])return this.activeTarget=null,this.clear();for(t=s.length;t--;)a!=o[t]&&e>=s[t]&&(void 0===s[t+1]||e<s[t+1])&&this.activate(o[t])},e.prototype.activate=function(e){this.activeTarget=e,this.clear();var i=this.selector+'[data-target="'+e+'"],'+this.selector+'[href="'+e+'"]',n=t(i).parents("li").addClass("active");n.parent(".dropdown-menu").length&&(n=n.closest("li.dropdown").addClass("active")),n.trigger("activate.bs.scrollspy")},e.prototype.clear=function(){t(this.selector).parentsUntil(this.options.target,".active").removeClass("active")};var n=t.fn.scrollspy;t.fn.scrollspy=i,t.fn.scrollspy.Constructor=e,t.fn.scrollspy.noConflict=function(){return t.fn.scrollspy=n,this},t(window).on("load.bs.scrollspy.data-api",function(){t('[data-spy="scroll"]').each(function(){var e=t(this);i.call(e,e.data())})})}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),s=n.data("bs.tab");s||n.data("bs.tab",s=new i(this)),"string"==typeof e&&s[e]()})}var i=function(e){this.element=t(e)};i.VERSION="3.3.7",i.TRANSITION_DURATION=150,i.prototype.show=function(){var e=this.element,i=e.closest("ul:not(.dropdown-menu)"),n=e.data("target");if(n||(n=(n=e.attr("href"))&&n.replace(/.*(?=#[^\s]*$)/,"")),!e.parent("li").hasClass("active")){var s=i.find(".active:last a"),o=t.Event("hide.bs.tab",{relatedTarget:e[0]}),a=t.Event("show.bs.tab",{relatedTarget:s[0]});if(s.trigger(o),e.trigger(a),!a.isDefaultPrevented()&&!o.isDefaultPrevented()){var r=t(n);this.activate(e.closest("li"),i),this.activate(r,r.parent(),function(){s.trigger({type:"hidden.bs.tab",relatedTarget:e[0]}),e.trigger({type:"shown.bs.tab",relatedTarget:s[0]})})}}},i.prototype.activate=function(e,n,s){function o(){a.removeClass("active").find("> .dropdown-menu > .active").removeClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!1),e.addClass("active").find('[data-toggle="tab"]').attr("aria-expanded",!0),r?(e[0].offsetWidth,e.addClass("in")):e.removeClass("fade"),e.parent(".dropdown-menu").length&&e.closest("li.dropdown").addClass("active").end().find('[data-toggle="tab"]').attr("aria-expanded",!0),s&&s()}var a=n.find("> .active"),r=s&&t.support.transition&&(a.length&&a.hasClass("fade")||!!n.find("> .fade").length);a.length&&r?a.one("bsTransitionEnd",o).emulateTransitionEnd(i.TRANSITION_DURATION):o(),a.removeClass("in")};var n=t.fn.tab;t.fn.tab=e,t.fn.tab.Constructor=i,t.fn.tab.noConflict=function(){return t.fn.tab=n,this};var s=function(i){i.preventDefault(),e.call(t(this),"show")};t(document).on("click.bs.tab.data-api",'[data-toggle="tab"]',s).on("click.bs.tab.data-api",'[data-toggle="pill"]',s)}(jQuery),function(t){"use strict";function e(e){return this.each(function(){var n=t(this),s=n.data("bs.affix"),o="object"==typeof e&&e;s||n.data("bs.affix",s=new i(this,o)),"string"==typeof e&&s[e]()})}var i=function(e,n){this.options=t.extend({},i.DEFAULTS,n),this.$target=t(this.options.target).on("scroll.bs.affix.data-api",t.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",t.proxy(this.checkPositionWithEventLoop,this)),this.$element=t(e),this.affixed=null,this.unpin=null,this.pinnedOffset=null,this.checkPosition()};i.VERSION="3.3.7",i.RESET="affix affix-top affix-bottom",i.DEFAULTS={offset:0,target:window},i.prototype.getState=function(t,e,i,n){var s=this.$target.scrollTop(),o=this.$element.offset(),a=this.$target.height();if(null!=i&&"top"==this.affixed)return s<i&&"top";if("bottom"==this.affixed)return null!=i?!(s+this.unpin<=o.top)&&"bottom":!(s+a<=t-n)&&"bottom";var r=null==this.affixed,l=r?s:o.top;return null!=i&&s<=i?"top":null!=n&&l+(r?a:e)>=t-n&&"bottom"},i.prototype.getPinnedOffset=function(){if(this.pinnedOffset)return this.pinnedOffset;this.$element.removeClass(i.RESET).addClass("affix");var t=this.$target.scrollTop(),e=this.$element.offset();return this.pinnedOffset=e.top-t},i.prototype.checkPositionWithEventLoop=function(){setTimeout(t.proxy(this.checkPosition,this),1)},i.prototype.checkPosition=function(){if(this.$element.is(":visible")){var e=this.$element.height(),n=this.options.offset,s=n.top,o=n.bottom,a=Math.max(t(document).height(),t(document.body).height());"object"!=typeof n&&(o=s=n),"function"==typeof s&&(s=n.top(this.$element)),"function"==typeof o&&(o=n.bottom(this.$element));var r=this.getState(a,e,s,o);if(this.affixed!=r){null!=this.unpin&&this.$element.css("top","");var l="affix"+(r?"-"+r:""),h=t.Event(l+".bs.affix");if(this.$element.trigger(h),h.isDefaultPrevented())return;this.affixed=r,this.unpin="bottom"==r?this.getPinnedOffset():null,this.$element.removeClass(i.RESET).addClass(l).trigger(l.replace("affix","affixed")+".bs.affix")}"bottom"==r&&this.$element.offset({top:a-e-o})}};var n=t.fn.affix;t.fn.affix=e,t.fn.affix.Constructor=i,t.fn.affix.noConflict=function(){return t.fn.affix=n,this},t(window).on("load",function(){t('[data-spy="affix"]').each(function(){var i=t(this),n=i.data();n.offset=n.offset||{},null!=n.offsetBottom&&(n.offset.bottom=n.offsetBottom),null!=n.offsetTop&&(n.offset.top=n.offsetTop),e.call(i,n)})})}(jQuery),function(t,e){"use strict";"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery"),require("bootstrap")):"function"==typeof define&&define.amd?define("bootstrap-dialog",["jquery","bootstrap"],function(t){return e(t)}):t.BootstrapDialog=e(t.jQuery)}(this,function(t){"use strict";var e=t.fn.modal.Constructor,i=function(t,i){e.call(this,t,i)};i.getModalVersion=function(){return void 0===t.fn.modal.Constructor.VERSION?"v3.1":/3\.2\.\d+/.test(t.fn.modal.Constructor.VERSION)?"v3.2":/3\.3\.[1,2]/.test(t.fn.modal.Constructor.VERSION)?"v3.3":"v3.3.4"},i.ORIGINAL_BODY_PADDING=parseInt(t("body").css("padding-right")||0,10),(i.METHODS_TO_OVERRIDE={})["v3.1"]={},i.METHODS_TO_OVERRIDE["v3.2"]={hide:function(e){(e&&e.preventDefault(),e=t.Event("hide.bs.modal"),this.$element.trigger(e),this.isShown&&!e.isDefaultPrevented())&&(this.isShown=!1,0===this.getGlobalOpenedDialogs().length&&this.$body.removeClass("modal-open"),this.resetScrollbar(),this.escape(),t(document).off("focusin.bs.modal"),this.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss.bs.modal"),t.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",t.proxy(this.hideModal,this)).emulateTransitionEnd(300):this.hideModal())}},i.METHODS_TO_OVERRIDE["v3.3"]={setScrollbar:function(){var t=i.ORIGINAL_BODY_PADDING;this.bodyIsOverflowing&&this.$body.css("padding-right",t+this.scrollbarWidth)},resetScrollbar:function(){0===this.getGlobalOpenedDialogs().length&&this.$body.css("padding-right",i.ORIGINAL_BODY_PADDING)},hideModal:function(){this.$element.hide(),this.backdrop(t.proxy(function(){0===this.getGlobalOpenedDialogs().length&&this.$body.removeClass("modal-open"),this.resetAdjustments(),this.resetScrollbar(),this.$element.trigger("hidden.bs.modal")},this))}},i.METHODS_TO_OVERRIDE["v3.3.4"]=t.extend({},i.METHODS_TO_OVERRIDE["v3.3"]),i.prototype={constructor:i,getGlobalOpenedDialogs:function(){var e=[];return t.each(n.dialogs,function(t,i){i.isRealized()&&i.isOpened()&&e.push(i)}),e}},i.prototype=t.extend(i.prototype,e.prototype,i.METHODS_TO_OVERRIDE[i.getModalVersion()]);var n=function(e){this.defaultOptions=t.extend(!0,{id:n.newGuid(),buttons:[],data:{},onshow:null,onshown:null,onhide:null,onhidden:null},n.defaultOptions),this.indexedButtons={},this.registeredButtonHotkeys={},this.draggableData={isMouseDown:!1,mouseOffset:{}},this.realized=!1,this.opened=!1,this.initOptions(e),this.holdThisInstance()};return n.BootstrapDialogModal=i,n.NAMESPACE="bootstrap-dialog",n.TYPE_DEFAULT="type-default",n.TYPE_INFO="type-info",n.TYPE_PRIMARY="type-primary",n.TYPE_SUCCESS="type-success",n.TYPE_WARNING="type-warning",n.TYPE_DANGER="type-danger",n.DEFAULT_TEXTS={},n.DEFAULT_TEXTS[n.TYPE_DEFAULT]="Information",n.DEFAULT_TEXTS[n.TYPE_INFO]="Information",n.DEFAULT_TEXTS[n.TYPE_PRIMARY]="Information",n.DEFAULT_TEXTS[n.TYPE_SUCCESS]="Success",n.DEFAULT_TEXTS[n.TYPE_WARNING]="Warning",n.DEFAULT_TEXTS[n.TYPE_DANGER]="Danger",n.DEFAULT_TEXTS.OK="OK",n.DEFAULT_TEXTS.CANCEL="Cancel",n.DEFAULT_TEXTS.CONFIRM="Confirmation",n.SIZE_NORMAL="size-normal",n.SIZE_SMALL="size-small",n.SIZE_WIDE="size-wide",n.SIZE_LARGE="size-large",n.BUTTON_SIZES={},n.BUTTON_SIZES[n.SIZE_NORMAL]="",n.BUTTON_SIZES[n.SIZE_SMALL]="",n.BUTTON_SIZES[n.SIZE_WIDE]="",n.BUTTON_SIZES[n.SIZE_LARGE]="btn-lg",n.ICON_SPINNER="glyphicon glyphicon-asterisk",n.BUTTONS_ORDER_CANCEL_OK="btns-order-cancel-ok",n.BUTTONS_ORDER_OK_CANCEL="btns-order-ok-cancel",n.defaultOptions={type:n.TYPE_PRIMARY,size:n.SIZE_NORMAL,cssClass:"",title:null,message:null,nl2br:!0,closable:!0,closeByBackdrop:!0,closeByKeyboard:!0,closeIcon:"&#215;",spinicon:n.ICON_SPINNER,autodestroy:!0,draggable:!1,animate:!0,description:"",tabindex:-1,btnsOrder:n.BUTTONS_ORDER_CANCEL_OK},n.configDefaultOptions=function(e){n.defaultOptions=t.extend(!0,n.defaultOptions,e)},n.dialogs={},n.openAll=function(){t.each(n.dialogs,function(t,e){e.open()})},n.closeAll=function(){t.each(n.dialogs,function(t,e){e.close()})},n.getDialog=function(t){var e=null;return void 0!==n.dialogs[t]&&(e=n.dialogs[t]),e},n.setDialog=function(t){return n.dialogs[t.getId()]=t,t},n.addDialog=function(t){return n.setDialog(t)},n.moveFocus=function(){var e=null;t.each(n.dialogs,function(t,i){i.isRealized()&&i.isOpened()&&(e=i)}),null!==e&&e.getModal().focus()},n.METHODS_TO_OVERRIDE={},n.METHODS_TO_OVERRIDE["v3.1"]={handleModalBackdropEvent:function(){return this.getModal().on("click",{dialog:this},function(t){t.target===this&&t.data.dialog.isClosable()&&t.data.dialog.canCloseByBackdrop()&&t.data.dialog.close()}),this},updateZIndex:function(){if(this.isOpened()){var e=0;t.each(n.dialogs,function(t,i){i.isRealized()&&i.isOpened()&&e++});var i=this.getModal(),s=i.data("bs.modal").$backdrop;i.css("z-index",1050+20*(e-1)),s.css("z-index",1040+20*(e-1))}return this},open:function(){return!this.isRealized()&&this.realize(),this.getModal().modal("show"),this.updateZIndex(),this}},n.METHODS_TO_OVERRIDE["v3.2"]={handleModalBackdropEvent:n.METHODS_TO_OVERRIDE["v3.1"].handleModalBackdropEvent,updateZIndex:n.METHODS_TO_OVERRIDE["v3.1"].updateZIndex,open:n.METHODS_TO_OVERRIDE["v3.1"].open},n.METHODS_TO_OVERRIDE["v3.3"]={},n.METHODS_TO_OVERRIDE["v3.3.4"]=t.extend({},n.METHODS_TO_OVERRIDE["v3.1"]),n.prototype={constructor:n,initOptions:function(e){return this.options=t.extend(!0,this.defaultOptions,e),this},holdThisInstance:function(){return n.addDialog(this),this},initModalStuff:function(){return this.setModal(this.createModal()).setModalDialog(this.createModalDialog()).setModalContent(this.createModalContent()).setModalHeader(this.createModalHeader()).setModalBody(this.createModalBody()).setModalFooter(this.createModalFooter()),this.getModal().append(this.getModalDialog()),this.getModalDialog().append(this.getModalContent()),this.getModalContent().append(this.getModalHeader()).append(this.getModalBody()).append(this.getModalFooter()),this},createModal:function(){var e=t('<div class="modal" role="dialog" aria-hidden="true"></div>');return e.prop("id",this.getId()),e.attr("aria-labelledby",this.getId()+"_title"),e},getModal:function(){return this.$modal},setModal:function(t){return this.$modal=t,this},createModalDialog:function(){return t('<div class="modal-dialog"></div>')},getModalDialog:function(){return this.$modalDialog},setModalDialog:function(t){return this.$modalDialog=t,this},createModalContent:function(){return t('<div class="modal-content"></div>')},getModalContent:function(){return this.$modalContent},setModalContent:function(t){return this.$modalContent=t,this},createModalHeader:function(){return t('<div class="modal-header"></div>')},getModalHeader:function(){return this.$modalHeader},setModalHeader:function(t){return this.$modalHeader=t,this},createModalBody:function(){return t('<div class="modal-body"></div>')},getModalBody:function(){return this.$modalBody},setModalBody:function(t){return this.$modalBody=t,this},createModalFooter:function(){return t('<div class="modal-footer"></div>')},getModalFooter:function(){return this.$modalFooter},setModalFooter:function(t){return this.$modalFooter=t,this},createDynamicContent:function(t){var e=null;return"string"==typeof(e="function"==typeof t?t.call(t,this):t)&&(e=this.formatStringContent(e)),e},formatStringContent:function(t){return this.options.nl2br?t.replace(/\r\n/g,"<br />").replace(/[\r\n]/g,"<br />"):t},setData:function(t,e){return this.options.data[t]=e,this},getData:function(t){return this.options.data[t]},setId:function(t){return this.options.id=t,this},getId:function(){return this.options.id},getType:function(){return this.options.type},setType:function(t){return this.options.type=t,this.updateType(),this},updateType:function(){if(this.isRealized()){var t=[n.TYPE_DEFAULT,n.TYPE_INFO,n.TYPE_PRIMARY,n.TYPE_SUCCESS,n.TYPE_WARNING,n.TYPE_DANGER];this.getModal().removeClass(t.join(" ")).addClass(this.getType())}return this},getSize:function(){return this.options.size},setSize:function(t){return this.options.size=t,this.updateSize(),this},updateSize:function(){if(this.isRealized()){var e=this;this.getModal().removeClass(n.SIZE_NORMAL).removeClass(n.SIZE_SMALL).removeClass(n.SIZE_WIDE).removeClass(n.SIZE_LARGE),this.getModal().addClass(this.getSize()),this.getModalDialog().removeClass("modal-sm"),this.getSize()===n.SIZE_SMALL&&this.getModalDialog().addClass("modal-sm"),this.getModalDialog().removeClass("modal-lg"),this.getSize()===n.SIZE_WIDE&&this.getModalDialog().addClass("modal-lg"),t.each(this.options.buttons,function(i,n){var s=e.getButton(n.id),o=["btn-lg","btn-sm","btn-xs"],a=!1;if("string"==typeof n.cssClass){var r=n.cssClass.split(" ");t.each(r,function(e,i){-1!==t.inArray(i,o)&&(a=!0)})}a||(s.removeClass(o.join(" ")),s.addClass(e.getButtonSize()))})}return this},getCssClass:function(){return this.options.cssClass},setCssClass:function(t){return this.options.cssClass=t,this},getTitle:function(){return this.options.title},setTitle:function(t){return this.options.title=t,this.updateTitle(),this},updateTitle:function(){if(this.isRealized()){var t=null!==this.getTitle()?this.createDynamicContent(this.getTitle()):this.getDefaultText();this.getModalHeader().find("."+this.getNamespace("title")).html("").append(t).prop("id",this.getId()+"_title")}return this},getMessage:function(){return this.options.message},setMessage:function(t){return this.options.message=t,this.updateMessage(),this},updateMessage:function(){if(this.isRealized()){var t=this.createDynamicContent(this.getMessage());this.getModalBody().find("."+this.getNamespace("message")).html("").append(t)}return this},isClosable:function(){return this.options.closable},setClosable:function(t){return this.options.closable=t,this.updateClosable(),this},setCloseByBackdrop:function(t){return this.options.closeByBackdrop=t,this},canCloseByBackdrop:function(){return this.options.closeByBackdrop},setCloseByKeyboard:function(t){return this.options.closeByKeyboard=t,this},canCloseByKeyboard:function(){return this.options.closeByKeyboard},isAnimate:function(){return this.options.animate},setAnimate:function(t){return this.options.animate=t,this},updateAnimate:function(){return this.isRealized()&&this.getModal().toggleClass("fade",this.isAnimate()),this},getSpinicon:function(){return this.options.spinicon},setSpinicon:function(t){return this.options.spinicon=t,this},addButton:function(t){return this.options.buttons.push(t),this},addButtons:function(e){var i=this;return t.each(e,function(t,e){i.addButton(e)}),this},getButtons:function(){return this.options.buttons},setButtons:function(t){return this.options.buttons=t,this.updateButtons(),this},getButton:function(t){return void 0!==this.indexedButtons[t]?this.indexedButtons[t]:null},getButtonSize:function(){return void 0!==n.BUTTON_SIZES[this.getSize()]?n.BUTTON_SIZES[this.getSize()]:""},updateButtons:function(){return this.isRealized()&&(0===this.getButtons().length?this.getModalFooter().hide():this.getModalFooter().show().find("."+this.getNamespace("footer")).html("").append(this.createFooterButtons())),this},isAutodestroy:function(){return this.options.autodestroy},setAutodestroy:function(t){this.options.autodestroy=t},getDescription:function(){return this.options.description},setDescription:function(t){return this.options.description=t,this},setTabindex:function(t){return this.options.tabindex=t,this},getTabindex:function(){return this.options.tabindex},updateTabindex:function(){return this.isRealized()&&this.getModal().attr("tabindex",this.getTabindex()),this},getDefaultText:function(){return n.DEFAULT_TEXTS[this.getType()]},getNamespace:function(t){return n.NAMESPACE+"-"+t},createHeaderContent:function(){var e=t("<div></div>");return e.addClass(this.getNamespace("header")),e.append(this.createTitleContent()),e.prepend(this.createCloseButton()),e},createTitleContent:function(){var e=t("<div></div>");return e.addClass(this.getNamespace("title")),e},createCloseButton:function(){var e=t("<div></div>");e.addClass(this.getNamespace("close-button"));var i=t('<button class="close" aria-label="close"></button>');return i.append(this.options.closeIcon),e.append(i),e.on("click",{dialog:this},function(t){t.data.dialog.close()}),e},createBodyContent:function(){var e=t("<div></div>");return e.addClass(this.getNamespace("body")),e.append(this.createMessageContent()),e},createMessageContent:function(){var e=t("<div></div>");return e.addClass(this.getNamespace("message")),e},createFooterContent:function(){var e=t("<div></div>");return e.addClass(this.getNamespace("footer")),e},createFooterButtons:function(){var e=this,i=t("<div></div>");return i.addClass(this.getNamespace("footer-buttons")),this.indexedButtons={},t.each(this.options.buttons,function(t,s){s.id||(s.id=n.newGuid());var o=e.createButton(s);e.indexedButtons[s.id]=o,i.append(o)}),i},createButton:function(e){var i=t('<button class="btn"></button>');return i.prop("id",e.id),i.data("button",e),void 0!==e.icon&&""!==t.trim(e.icon)&&i.append(this.createButtonIcon(e.icon)),void 0!==e.label&&i.append(e.label),void 0!==e.title&&i.attr("title",e.title),i.addClass(void 0!==e.cssClass&&""!==t.trim(e.cssClass)?e.cssClass:"btn-default"),"object"==typeof e.data&&e.data.constructor==={}.constructor&&t.each(e.data,function(t,e){i.attr("data-"+t,e)}),void 0!==e.hotkey&&(this.registeredButtonHotkeys[e.hotkey]=i),i.on("click",{dialog:this,$button:i,button:e},function(t){var e=t.data.dialog,i=t.data.$button,n=i.data("button");return n.autospin&&i.toggleSpin(!0),"function"==typeof n.action?n.action.call(i,e,t):void 0}),this.enhanceButton(i),void 0!==e.enabled&&i.toggleEnable(e.enabled),i},enhanceButton:function(t){return t.dialog=this,t.toggleEnable=function(t){var e=this;return void 0!==t?e.prop("disabled",!t).toggleClass("disabled",!t):e.prop("disabled",!e.prop("disabled")),e},t.enable=function(){return this.toggleEnable(!0),this},t.disable=function(){return this.toggleEnable(!1),this},t.toggleSpin=function(e){var i=this,n=i.dialog,s=i.find("."+n.getNamespace("button-icon"));return void 0===e&&(e=!(t.find(".icon-spin").length>0)),e?(s.hide(),t.prepend(n.createButtonIcon(n.getSpinicon()).addClass("icon-spin"))):(s.show(),t.find(".icon-spin").remove()),i},t.spin=function(){return this.toggleSpin(!0),this},t.stopSpin=function(){return this.toggleSpin(!1),this},this},createButtonIcon:function(e){var i=t("<span></span>");return i.addClass(this.getNamespace("button-icon")).addClass(e),i},enableButtons:function(e){return t.each(this.indexedButtons,function(t,i){i.toggleEnable(e)}),this},updateClosable:function(){return this.isRealized()&&this.getModalHeader().find("."+this.getNamespace("close-button")).toggle(this.isClosable()),this},onShow:function(t){return this.options.onshow=t,this},onShown:function(t){return this.options.onshown=t,this},onHide:function(t){return this.options.onhide=t,this},onHidden:function(t){return this.options.onhidden=t,this},isRealized:function(){return this.realized},setRealized:function(t){return this.realized=t,this},isOpened:function(){return this.opened},setOpened:function(t){return this.opened=t,this},handleModalEvents:function(){return this.getModal().on("show.bs.modal",{dialog:this},function(t){var e=t.data.dialog;if(e.setOpened(!0),e.isModalEvent(t)&&"function"==typeof e.options.onshow){var i=e.options.onshow(e);return!1===i&&e.setOpened(!1),i}}),this.getModal().on("shown.bs.modal",{dialog:this},function(t){var e=t.data.dialog;e.isModalEvent(t)&&"function"==typeof e.options.onshown&&e.options.onshown(e)}),this.getModal().on("hide.bs.modal",{dialog:this},function(t){var e=t.data.dialog;if(e.setOpened(!1),e.isModalEvent(t)&&"function"==typeof e.options.onhide){var i=e.options.onhide(e);return!1===i&&e.setOpened(!0),i}}),this.getModal().on("hidden.bs.modal",{dialog:this},function(e){var i=e.data.dialog;i.isModalEvent(e)&&"function"==typeof i.options.onhidden&&i.options.onhidden(i),i.isAutodestroy()&&(i.setRealized(!1),delete n.dialogs[i.getId()],t(this).remove()),n.moveFocus(),t(".modal").hasClass("in")&&t("body").addClass("modal-open")}),this.handleModalBackdropEvent(),this.getModal().on("keyup",{dialog:this},function(t){27===t.which&&t.data.dialog.isClosable()&&t.data.dialog.canCloseByKeyboard()&&t.data.dialog.close()}),this.getModal().on("keyup",{dialog:this},function(e){var i=e.data.dialog;if(void 0!==i.registeredButtonHotkeys[e.which]){var n=t(i.registeredButtonHotkeys[e.which]);!n.prop("disabled")&&n.focus().trigger("click")}}),this},handleModalBackdropEvent:function(){return this.getModal().on("click",{dialog:this},function(e){t(e.target).hasClass("modal-backdrop")&&e.data.dialog.isClosable()&&e.data.dialog.canCloseByBackdrop()&&e.data.dialog.close()}),this},isModalEvent:function(t){return void 0!==t.namespace&&"bs.modal"===t.namespace},makeModalDraggable:function(){return this.options.draggable&&(this.getModalHeader().addClass(this.getNamespace("draggable")).on("mousedown",{dialog:this},function(t){var e=t.data.dialog;e.draggableData.isMouseDown=!0;var i=e.getModalDialog().offset();e.draggableData.mouseOffset={top:t.clientY-i.top,left:t.clientX-i.left}}),this.getModal().on("mouseup mouseleave",{dialog:this},function(t){t.data.dialog.draggableData.isMouseDown=!1}),t("body").on("mousemove",{dialog:this},function(t){var e=t.data.dialog;e.draggableData.isMouseDown&&e.getModalDialog().offset({top:t.clientY-e.draggableData.mouseOffset.top,left:t.clientX-e.draggableData.mouseOffset.left})})),this},realize:function(){return this.initModalStuff(),this.getModal().addClass(n.NAMESPACE).addClass(this.getCssClass()),this.updateSize(),this.getDescription()&&this.getModal().attr("aria-describedby",this.getDescription()),this.getModalFooter().append(this.createFooterContent()),this.getModalHeader().append(this.createHeaderContent()),this.getModalBody().append(this.createBodyContent()),this.getModal().data("bs.modal",new i(this.getModal(),{backdrop:"static",keyboard:!1,show:!1})),this.makeModalDraggable(),this.handleModalEvents(),this.setRealized(!0),this.updateButtons(),this.updateType(),this.updateTitle(),this.updateMessage(),this.updateClosable(),this.updateAnimate(),this.updateSize(),this.updateTabindex(),this},open:function(){return!this.isRealized()&&this.realize(),this.getModal().modal("show"),this},close:function(){return!this.isRealized()&&this.realize(),this.getModal().modal("hide"),this}},n.prototype=t.extend(n.prototype,n.METHODS_TO_OVERRIDE[i.getModalVersion()]),n.newGuid=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"===t?e:3&e|8).toString(16)})},n.show=function(t){return new n(t).open()},n.alert=function(){var e,i={type:n.TYPE_PRIMARY,title:null,message:null,closable:!1,draggable:!1,buttonLabel:n.DEFAULT_TEXTS.OK,buttonHotkey:null,callback:null};e="object"==typeof arguments[0]&&arguments[0].constructor==={}.constructor?t.extend(!0,i,arguments[0]):t.extend(!0,i,{message:arguments[0],callback:void 0!==arguments[1]?arguments[1]:null});var s=new n(e);return s.setData("callback",e.callback),s.addButton({label:e.buttonLabel,hotkey:e.buttonHotkey,action:function(t){return("function"!=typeof t.getData("callback")||!1!==t.getData("callback").call(this,!0))&&(t.setData("btnClicked",!0),t.close())}}),s.onHide("function"==typeof s.options.onhide?function(t){var e=!0;return!t.getData("btnClicked")&&t.isClosable()&&"function"==typeof t.getData("callback")&&(e=t.getData("callback")(!1)),!1!==e&&(e=this.onhide(t))}.bind({onhide:s.options.onhide}):function(t){var e=!0;return!t.getData("btnClicked")&&t.isClosable()&&"function"==typeof t.getData("callback")&&(e=t.getData("callback")(!1)),e}),s.open()},n.confirm=function(){var e={},i={type:n.TYPE_PRIMARY,title:null,message:null,closable:!1,draggable:!1,btnCancelLabel:n.DEFAULT_TEXTS.CANCEL,btnCancelClass:null,btnCancelHotkey:null,btnOKLabel:n.DEFAULT_TEXTS.OK,btnOKClass:null,btnOKHotkey:null,btnsOrder:n.defaultOptions.btnsOrder,callback:null};null===(e="object"==typeof arguments[0]&&arguments[0].constructor==={}.constructor?t.extend(!0,i,arguments[0]):t.extend(!0,i,{message:arguments[0],callback:void 0!==arguments[1]?arguments[1]:null})).btnOKClass&&(e.btnOKClass=["btn",e.type.split("-")[1]].join("-"));var s=new n(e);s.setData("callback",e.callback);var o=[{label:e.btnCancelLabel,cssClass:e.btnCancelClass,hotkey:e.btnCancelHotkey,action:function(t){return("function"!=typeof t.getData("callback")||!1!==t.getData("callback").call(this,!1))&&t.close()}},{label:e.btnOKLabel,cssClass:e.btnOKClass,hotkey:e.btnOKHotkey,action:function(t){return("function"!=typeof t.getData("callback")||!1!==t.getData("callback").call(this,!0))&&t.close()}}];return e.btnsOrder===n.BUTTONS_ORDER_OK_CANCEL&&o.reverse(),s.addButtons(o),s.open()},n.warning=function(t,e){return new n({type:n.TYPE_WARNING,message:t}).open()},n.danger=function(t,e){return new n({type:n.TYPE_DANGER,message:t}).open()},n.success=function(t,e){return new n({type:n.TYPE_SUCCESS,message:t}).open()},n}),function(t){"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e,window,document)}):"object"==typeof exports?module.exports=function(e,i){return e||(e=window),i||(i="undefined"!=typeof window?require("jquery"):require("jquery")(e)),t(i,e,e.document)}:t(jQuery,window,document)}(function(t,e,i,n){function s(e){var i,n,o={};t.each(e,function(t){(i=t.match(/^([^A-Z]+?)([A-Z])/))&&-1!=="a aa ai ao as b fn i m o s ".indexOf(i[1]+" ")&&(n=t.replace(i[0],i[2].toLowerCase()),o[n]=t,"o"===i[1]&&s(e[t]))}),e._hungarianMap=o}function o(e,i,a){var r;e._hungarianMap||s(e),t.each(i,function(s){(r=e._hungarianMap[s])===n||!a&&i[r]!==n||("o"===r.charAt(0)?(i[r]||(i[r]={}),t.extend(!0,i[r],i[s]),o(e[r],i[r],a)):i[r]=i[s])})}function a(t){var e=Yt.defaults.oLanguage,i=t.sZeroRecords;!t.sEmptyTable&&i&&"No data available in table"===e.sEmptyTable&&Pt(t,t,"sZeroRecords","sEmptyTable"),!t.sLoadingRecords&&i&&"Loading..."===e.sLoadingRecords&&Pt(t,t,"sZeroRecords","sLoadingRecords"),t.sInfoThousands&&(t.sThousands=t.sInfoThousands),(t=t.sDecimal)&&Wt(t)}function r(t){if(he(t,"ordering","bSort"),he(t,"orderMulti","bSortMulti"),he(t,"orderClasses","bSortClasses"),he(t,"orderCellsTop","bSortCellsTop"),he(t,"order","aaSorting"),he(t,"orderFixed","aaSortingFixed"),he(t,"paging","bPaginate"),he(t,"pagingType","sPaginationType"),he(t,"pageLength","iDisplayLength"),he(t,"searching","bFilter"),"boolean"==typeof t.sScrollX&&(t.sScrollX=t.sScrollX?"100%":""),"boolean"==typeof t.scrollX&&(t.scrollX=t.scrollX?"100%":""),t=t.aoSearchCols)for(var e=0,i=t.length;e<i;e++)t[e]&&o(Yt.models.oSearch,t[e])}function l(e){he(e,"orderable","bSortable"),he(e,"orderData","aDataSort"),he(e,"orderSequence","asSorting"),he(e,"orderDataType","sortDataType");var i=e.aDataSort;"number"==typeof i&&!t.isArray(i)&&(e.aDataSort=[i])}function h(i){if(!Yt.__browser){var n={};Yt.__browser=n;var s=t("<div/>").css({position:"fixed",top:0,left:-1*t(e).scrollLeft(),height:1,width:1,overflow:"hidden"}).append(t("<div/>").css({position:"absolute",top:1,left:1,width:100,overflow:"scroll"}).append(t("<div/>").css({width:"100%",height:10}))).appendTo("body"),o=s.children(),a=o.children();n.barWidth=o[0].offsetWidth-o[0].clientWidth,n.bScrollOversize=100===a[0].offsetWidth&&100!==o[0].clientWidth,n.bScrollbarLeft=1!==Math.round(a.offset().left),n.bBounding=!!s[0].getBoundingClientRect().width,s.remove()}t.extend(i.oBrowser,Yt.__browser),i.oScroll.iBarWidth=Yt.__browser.barWidth}function c(t,e,i,s,o,a){var r,l=!1;for(i!==n&&(r=i,l=!0);s!==o;)t.hasOwnProperty(s)&&(r=l?e(r,t[s],s,t):t[s],l=!0,s+=a);return r}function u(e,n){var s=Yt.defaults.column,o=e.aoColumns.length;s=t.extend({},Yt.models.oColumn,s,{nTh:n||i.createElement("th"),sTitle:s.sTitle?s.sTitle:n?n.innerHTML:"",aDataSort:s.aDataSort?s.aDataSort:[o],mData:s.mData?s.mData:o,idx:o});e.aoColumns.push(s),(s=e.aoPreSearchCols)[o]=t.extend({},Yt.models.oSearch,s[o]),d(e,o,t(n).data())}function d(e,i,s){i=e.aoColumns[i];var a=e.oClasses,r=t(i.nTh);if(!i.sWidthOrig){i.sWidthOrig=r.attr("width")||null;var h=(r.attr("style")||"").match(/width:\s*(\d+[pxem%]+)/);h&&(i.sWidthOrig=h[1])}s!==n&&null!==s&&(l(s),o(Yt.defaults.column,s),s.mDataProp!==n&&!s.mData&&(s.mData=s.mDataProp),s.sType&&(i._sManualType=s.sType),s.className&&!s.sClass&&(s.sClass=s.className),s.sClass&&r.addClass(s.sClass),t.extend(i,s),Pt(i,s,"sWidth","sWidthOrig"),s.iDataSort!==n&&(i.aDataSort=[s.iDataSort]),Pt(i,s,"aDataSort"));var c=i.mData,u=T(c),d=i.mRender?T(i.mRender):null;s=function(t){return"string"==typeof t&&-1!==t.indexOf("@")};i._bAttrSrc=t.isPlainObject(c)&&(s(c.sort)||s(c.type)||s(c.filter)),i._setter=null,i.fnGetData=function(t,e,i){var s=u(t,e,n,i);return d&&e?d(s,e,t,i):s},i.fnSetData=function(t,e,i){return S(c)(t,e,i)},"number"!=typeof c&&(e._rowReadObject=!0),e.oFeatures.bSort||(i.bSortable=!1,r.addClass(a.sSortableNone)),e=-1!==t.inArray("asc",i.asSorting),s=-1!==t.inArray("desc",i.asSorting),i.bSortable&&(e||s)?e&&!s?(i.sSortingClass=a.sSortableAsc,i.sSortingClassJUI=a.sSortJUIAscAllowed):!e&&s?(i.sSortingClass=a.sSortableDesc,i.sSortingClassJUI=a.sSortJUIDescAllowed):(i.sSortingClass=a.sSortable,i.sSortingClassJUI=a.sSortJUI):(i.sSortingClass=a.sSortableNone,i.sSortingClassJUI="")}function p(t){if(!1!==t.oFeatures.bAutoWidth){var e=t.aoColumns;gt(t);for(var i=0,n=e.length;i<n;i++)e[i].nTh.style.width=e[i].sWidth}(""!==(e=t.oScroll).sY||""!==e.sX)&&pt(t),Ht(t,null,"column-sizing",[t])}function f(t,e){var i=v(t,"bVisible");return"number"==typeof i[e]?i[e]:null}function g(e,i){var n=v(e,"bVisible");return-1!==(n=t.inArray(i,n))?n:null}function m(e){var i=0;return t.each(e.aoColumns,function(e,n){n.bVisible&&"none"!==t(n.nTh).css("display")&&i++}),i}function v(e,i){var n=[];return t.map(e.aoColumns,function(t,e){t[i]&&n.push(e)}),n}function _(t){var e,i,s,o,a,r,l,h,c,u=t.aoColumns,d=t.aoData,p=Yt.ext.type.detect;for(e=0,i=u.length;e<i;e++)if(c=[],!(l=u[e]).sType&&l._sManualType)l.sType=l._sManualType;else if(!l.sType){for(s=0,o=p.length;s<o;s++){for(a=0,r=d.length;a<r&&(c[a]===n&&(c[a]=x(t,a,e,"type")),(h=p[s](c[a],t))||s===p.length-1)&&"html"!==h;a++);if(h){l.sType=h;break}}l.sType||(l.sType="string")}}function b(e,i,s,o){var a,r,l,h,c,d,p=e.aoColumns;if(i)for(a=i.length-1;0<=a;a--){var f=(d=i[a]).targets!==n?d.targets:d.aTargets;for(t.isArray(f)||(f=[f]),r=0,l=f.length;r<l;r++)if("number"==typeof f[r]&&0<=f[r]){for(;p.length<=f[r];)u(e);o(f[r],d)}else if("number"==typeof f[r]&&0>f[r])o(p.length+f[r],d);else if("string"==typeof f[r])for(h=0,c=p.length;h<c;h++)("_all"==f[r]||t(p[h].nTh).hasClass(f[r]))&&o(h,d)}if(s)for(a=0,e=s.length;a<e;a++)o(a,s[a])}function y(e,i,s,o){var a=e.aoData.length,r=t.extend(!0,{},Yt.models.oRow,{src:s?"dom":"data",idx:a});r._aData=i,e.aoData.push(r);for(var l=e.aoColumns,h=0,c=l.length;h<c;h++)l[h].sType=null;return e.aiDisplayMaster.push(a),(i=e.rowIdFn(i))!==n&&(e.aIds[i]=r),(s||!e.oFeatures.bDeferRender)&&M(e,a,s,o),a}function w(e,i){var n;return i instanceof t||(i=t(i)),i.map(function(t,i){return n=P(e,i),y(e,n.data,i,n.cells)})}function x(t,e,i,s){var o=t.iDraw,a=t.aoColumns[i],r=t.aoData[e]._aData,l=a.sDefaultContent,h=a.fnGetData(r,s,{settings:t,row:e,col:i});if(h===n)return t.iDrawError!=o&&null===l&&(At(t,0,"Requested unknown parameter "+("function"==typeof a.mData?"{function}":"'"+a.mData+"'")+" for row "+e+", column "+i,4),t.iDrawError=o),l;if(h!==r&&null!==h||null===l||s===n){if("function"==typeof h)return h.call(r)}else h=l;return null===h&&"display"==s?"":h}function C(t,e,i,n){t.aoColumns[i].fnSetData(t.aoData[e]._aData,n,{settings:t,row:e,col:i})}function D(e){return t.map(e.match(/(\\.|[^\.])+/g)||[""],function(t){return t.replace(/\\\./g,".")})}function T(e){if(t.isPlainObject(e)){var i={};return t.each(e,function(t,e){e&&(i[t]=T(e))}),function(t,e,s,o){var a=i[e]||i._;return a!==n?a(t,e,s,o):t}}if(null===e)return function(t){return t};if("function"==typeof e)return function(t,i,n,s){return e(t,i,n,s)};if("string"==typeof e&&(-1!==e.indexOf(".")||-1!==e.indexOf("[")||-1!==e.indexOf("("))){var s=function(e,i,o){var a,r;if(""!==o)for(var l=0,h=(r=D(o)).length;l<h;l++){if(o=r[l].match(ce),a=r[l].match(ue),o){if(r[l]=r[l].replace(ce,""),""!==r[l]&&(e=e[r[l]]),a=[],r.splice(0,l+1),r=r.join("."),t.isArray(e))for(l=0,h=e.length;l<h;l++)a.push(s(e[l],i,r));e=""===(e=o[0].substring(1,o[0].length-1))?a:a.join(e);break}if(a)r[l]=r[l].replace(ue,""),e=e[r[l]]();else{if(null===e||e[r[l]]===n)return n;e=e[r[l]]}}return e};return function(t,i){return s(t,i,e)}}return function(t){return t[e]}}function S(e){if(t.isPlainObject(e))return S(e._);if(null===e)return function(){};if("function"==typeof e)return function(t,i,n){e(t,"set",i,n)};if("string"==typeof e&&(-1!==e.indexOf(".")||-1!==e.indexOf("[")||-1!==e.indexOf("("))){var i=function(e,s,o){var a;a=(o=D(o))[o.length-1];for(var r,l,h=0,c=o.length-1;h<c;h++){if(r=o[h].match(ce),l=o[h].match(ue),r){if(o[h]=o[h].replace(ce,""),e[o[h]]=[],(a=o.slice()).splice(0,h+1),r=a.join("."),t.isArray(s))for(l=0,c=s.length;l<c;l++)i(a={},s[l],r),e[o[h]].push(a);else e[o[h]]=s;return}l&&(o[h]=o[h].replace(ue,""),e=e[o[h]](s)),null!==e[o[h]]&&e[o[h]]!==n||(e[o[h]]={}),e=e[o[h]]}a.match(ue)?e[a.replace(ue,"")](s):e[a.replace(ce,"")]=s};return function(t,n){return i(t,n,e)}}return function(t,i){t[e]=i}}function k(t){return se(t.aoData,"_aData")}function I(t){t.aoData.length=0,t.aiDisplayMaster.length=0,t.aiDisplay.length=0,t.aIds={}}function E(t,e,i){for(var s=-1,o=0,a=t.length;o<a;o++)t[o]==e?s=o:t[o]>e&&t[o]--;-1!=s&&i===n&&t.splice(s,1)}function A(t,e,i,s){var o,a=t.aoData[e],r=function(i,n){for(;i.childNodes.length;)i.removeChild(i.firstChild);i.innerHTML=x(t,e,n,"display")};if("dom"!==i&&(i&&"auto"!==i||"dom"!==a.src)){var l=a.anCells;if(l)if(s!==n)r(l[s],s);else for(i=0,o=l.length;i<o;i++)r(l[i],i)}else a._aData=P(t,a,s,s===n?n:a._aData).data;if(a._aSortData=null,a._aFilterData=null,r=t.aoColumns,s!==n)r[s].sType=null;else{for(i=0,o=r.length;i<o;i++)r[i].sType=null;O(t,a)}}function P(e,i,s,o){var a,r,l,h=[],c=i.firstChild,u=0,d=e.aoColumns,p=e._rowReadObject,f=(o=o!==n?o:p?{}:[],function(t,e){if("string"==typeof t){var i=t.indexOf("@");-1!==i&&(i=t.substring(i+1),S(t)(o,e.getAttribute(i)))}}),g=function(e){s!==n&&s!==u||(r=d[u],l=t.trim(e.innerHTML),r&&r._bAttrSrc?(S(r.mData._)(o,l),f(r.mData.sort,e),f(r.mData.type,e),f(r.mData.filter,e)):p?(r._setter||(r._setter=S(r.mData)),r._setter(o,l)):o[u]=l),u++};if(c)for(;c;)"TD"!=(a=c.nodeName.toUpperCase())&&"TH"!=a||(g(c),h.push(c)),c=c.nextSibling;else for(c=0,a=(h=i.anCells).length;c<a;c++)g(h[c]);return(i=i.firstChild?i:i.nTr)&&(i=i.getAttribute("id"))&&S(e.rowId)(o,i),{data:o,cells:h}}function M(e,n,s,o){var a,r,l,h,c,u=e.aoData[n],d=u._aData,p=[];if(null===u.nTr){for(a=s||i.createElement("tr"),u.nTr=a,u.anCells=p,a._DT_RowIndex=n,O(e,u),h=0,c=e.aoColumns.length;h<c;h++)l=e.aoColumns[h],(r=s?o[h]:i.createElement(l.sCellType))._DT_CellIndex={row:n,column:h},p.push(r),s&&!l.mRender&&l.mData===h||t.isPlainObject(l.mData)&&l.mData._===h+".display"||(r.innerHTML=x(e,n,h,"display")),l.sClass&&(r.className+=" "+l.sClass),l.bVisible&&!s?a.appendChild(r):!l.bVisible&&s&&r.parentNode.removeChild(r),l.fnCreatedCell&&l.fnCreatedCell.call(e.oInstance,r,x(e,n,h),d,n,h);Ht(e,"aoRowCreatedCallback",null,[a,d,n])}u.nTr.setAttribute("role","row")}function O(e,i){var n=i.nTr,s=i._aData;if(n){var o=e.rowIdFn(s);o&&(n.id=o),s.DT_RowClass&&(o=s.DT_RowClass.split(" "),i.__rowc=i.__rowc?le(i.__rowc.concat(o)):o,t(n).removeClass(i.__rowc.join(" ")).addClass(s.DT_RowClass)),s.DT_RowAttr&&t(n).attr(s.DT_RowAttr),s.DT_RowData&&t(n).data(s.DT_RowData)}}function N(e){var i,n,s,o,a,r=e.nTHead,l=e.nTFoot,h=0===t("th, td",r).length,c=e.oClasses,u=e.aoColumns;for(h&&(o=t("<tr/>").appendTo(r)),i=0,n=u.length;i<n;i++)a=u[i],s=t(a.nTh).addClass(a.sClass),h&&s.appendTo(o),e.oFeatures.bSort&&(s.addClass(a.sSortingClass),!1!==a.bSortable&&(s.attr("tabindex",e.iTabIndex).attr("aria-controls",e.sTableId),Dt(e,a.nTh,i))),a.sTitle!=s[0].innerHTML&&s.html(a.sTitle),Lt(e,"header")(e,s,a,c);if(h&&j(e.aoHeader,r),t(r).find(">tr").attr("role","row"),t(r).find(">tr>th, >tr>td").addClass(c.sHeaderTH),t(l).find(">tr>th, >tr>td").addClass(c.sFooterTH),null!==l)for(i=0,n=(e=e.aoFooter[0]).length;i<n;i++)(a=u[i]).nTf=e[i].cell,a.sClass&&t(a.nTf).addClass(a.sClass)}function H(e,i,s){var o,a,r,l,h=[],c=[],u=e.aoColumns.length;if(i){for(s===n&&(s=!1),o=0,a=i.length;o<a;o++){for(h[o]=i[o].slice(),h[o].nTr=i[o].nTr,r=u-1;0<=r;r--)!e.aoColumns[r].bVisible&&!s&&h[o].splice(r,1);c.push([])}for(o=0,a=h.length;o<a;o++){if(e=h[o].nTr)for(;r=e.firstChild;)e.removeChild(r);for(r=0,i=h[o].length;r<i;r++)if(l=u=1,c[o][r]===n){for(e.appendChild(h[o][r].cell),c[o][r]=1;h[o+u]!==n&&h[o][r].cell==h[o+u][r].cell;)c[o+u][r]=1,u++;for(;h[o][r+l]!==n&&h[o][r].cell==h[o][r+l].cell;){for(s=0;s<u;s++)c[o+s][r+l]=1;l++}t(h[o][r].cell).attr("rowspan",u).attr("colspan",l)}}}}function R(e){var i=Ht(e,"aoPreDrawCallback","preDraw",[e]);if(-1!==t.inArray(!1,i))ut(e,!1);else{i=[];var s=0,o=e.asStripeClasses,a=o.length,r=e.oLanguage,l=e.iInitDisplayStart,h="ssp"==Ft(e),c=e.aiDisplay;e.bDrawing=!0,l!==n&&-1!==l&&(e._iDisplayStart=h?l:l>=e.fnRecordsDisplay()?0:l,e.iInitDisplayStart=-1);l=e._iDisplayStart;var u=e.fnDisplayEnd();if(e.bDeferLoading)e.bDeferLoading=!1,e.iDraw++,ut(e,!1);else if(h){if(!e.bDestroying&&!B(e))return}else e.iDraw++;if(0!==c.length)for(r=h?e.aoData.length:u,h=h?0:l;h<r;h++){var d=c[h],p=e.aoData[d];if(null===p.nTr&&M(e,d),d=p.nTr,0!==a){var f=o[s%a];p._sRowStripe!=f&&(t(d).removeClass(p._sRowStripe).addClass(f),p._sRowStripe=f)}Ht(e,"aoRowCallback",null,[d,p._aData,s,h]),i.push(d),s++}else s=r.sZeroRecords,1==e.iDraw&&"ajax"==Ft(e)?s=r.sLoadingRecords:r.sEmptyTable&&0===e.fnRecordsTotal()&&(s=r.sEmptyTable),i[0]=t("<tr/>",{class:a?o[0]:""}).append(t("<td />",{valign:"top",colSpan:m(e),class:e.oClasses.sRowEmpty}).html(s))[0];Ht(e,"aoHeaderCallback","header",[t(e.nTHead).children("tr")[0],k(e),l,u,c]),Ht(e,"aoFooterCallback","footer",[t(e.nTFoot).children("tr")[0],k(e),l,u,c]),(o=t(e.nTBody)).children().detach(),o.append(t(i)),Ht(e,"aoDrawCallback","draw",[e]),e.bSorted=!1,e.bFiltered=!1,e.bDrawing=!1}}function L(t,e){var i=t.oFeatures,n=i.bFilter;i.bSort&&wt(t),n?V(t,t.oPreviousSearch):t.aiDisplay=t.aiDisplayMaster.slice(),!0!==e&&(t._iDisplayStart=0),t._drawHold=e,R(t),t._drawHold=!1}function F(e){var i=e.oClasses,n=t(e.nTable),s=(n=t("<div/>").insertBefore(n),e.oFeatures),o=t("<div/>",{id:e.sTableId+"_wrapper",class:i.sWrapper+(e.nTFoot?"":" "+i.sNoFooter)});e.nHolding=n[0],e.nTableWrapper=o[0],e.nTableReinsertBefore=e.nTable.nextSibling;for(var a,r,l,h,c,u,d=e.sDom.split(""),p=0;p<d.length;p++){if(a=null,"<"==(r=d[p])){if(l=t("<div/>")[0],"'"==(h=d[p+1])||'"'==h){for(c="",u=2;d[p+u]!=h;)c+=d[p+u],u++;"H"==c?c=i.sJUIHeader:"F"==c&&(c=i.sJUIFooter),-1!=c.indexOf(".")?(h=c.split("."),l.id=h[0].substr(1,h[0].length-1),l.className=h[1]):"#"==c.charAt(0)?l.id=c.substr(1,c.length-1):l.className=c,p+=u}o.append(l),o=t(l)}else if(">"==r)o=o.parent();else if("l"==r&&s.bPaginate&&s.bLengthChange)a=rt(e);else if("f"==r&&s.bFilter)a=Y(e);else if("r"==r&&s.bProcessing)a=ct(e);else if("t"==r)a=dt(e);else if("i"==r&&s.bInfo)a=et(e);else if("p"==r&&s.bPaginate)a=lt(e);else if(0!==Yt.ext.feature.length)for(u=0,h=(l=Yt.ext.feature).length;u<h;u++)if(r==l[u].cFeature){a=l[u].fnInit(e);break}a&&((l=e.aanFeatures)[r]||(l[r]=[]),l[r].push(a),o.append(a))}n.replaceWith(o),e.nHolding=null}function j(e,i){var n,s,o,a,r,l,h,c,u,d,p=t(i).children("tr");for(e.splice(0,e.length),o=0,l=p.length;o<l;o++)e.push([]);for(o=0,l=p.length;o<l;o++)for(s=(n=p[o]).firstChild;s;){if("TD"==s.nodeName.toUpperCase()||"TH"==s.nodeName.toUpperCase()){for(c=(c=1*s.getAttribute("colspan"))&&0!==c&&1!==c?c:1,u=(u=1*s.getAttribute("rowspan"))&&0!==u&&1!==u?u:1,a=0,r=e[o];r[a];)a++;for(h=a,d=1===c,r=0;r<c;r++)for(a=0;a<u;a++)e[o+a][h+r]={cell:s,unique:d},e[o+a].nTr=n}s=s.nextSibling}}function W(t,e,i){var n=[];i||(i=t.aoHeader,e&&j(i=[],e));e=0;for(var s=i.length;e<s;e++)for(var o=0,a=i[e].length;o<a;o++)!i[e][o].unique||n[o]&&t.bSortCellsTop||(n[o]=i[e][o].cell);return n}function z(e,i,n){if(Ht(e,"aoServerParams","serverParams",[i]),i&&t.isArray(i)){var s={},o=/(.*?)\[\]$/;t.each(i,function(t,e){var i=e.name.match(o);i?(i=i[0],s[i]||(s[i]=[]),s[i].push(e.value)):s[e.name]=e.value}),i=s}var a,r=e.ajax,l=e.oInstance,h=function(t){Ht(e,null,"xhr",[e,t,e.jqXHR]),n(t)};if(t.isPlainObject(r)&&r.data){a=r.data;var c=t.isFunction(a)?a(i,e):a;i=t.isFunction(a)&&c?c:t.extend(!0,i,c);delete r.data}c={data:i,success:function(t){var i=t.error||t.sError;i&&At(e,0,i),e.json=t,h(t)},dataType:"json",cache:!1,type:e.sServerMethod,error:function(i,n){var s=Ht(e,null,"xhr",[e,null,e.jqXHR]);-1===t.inArray(!0,s)&&("parsererror"==n?At(e,0,"Invalid JSON response",1):4===i.readyState&&At(e,0,"Ajax error",7)),ut(e,!1)}},e.oAjaxData=i,Ht(e,null,"preXhr",[e,i]),e.fnServerData?e.fnServerData.call(l,e.sAjaxSource,t.map(i,function(t,e){return{name:e,value:t}}),h,e):e.sAjaxSource||"string"==typeof r?e.jqXHR=t.ajax(t.extend(c,{url:r||e.sAjaxSource})):t.isFunction(r)?e.jqXHR=r.call(l,i,h,e):(e.jqXHR=t.ajax(t.extend(c,r)),r.data=a)}function B(t){return!t.bAjaxDataGet||(t.iDraw++,ut(t,!0),z(t,$(t),function(e){q(t,e)}),!1)}function $(e){var i,n,s,o,a=e.aoColumns,r=a.length,l=e.oFeatures,h=e.oPreviousSearch,c=e.aoPreSearchCols,u=[],d=yt(e);i=e._iDisplayStart,n=!1!==l.bPaginate?e._iDisplayLength:-1;var p=function(t,e){u.push({name:t,value:e})};p("sEcho",e.iDraw),p("iColumns",r),p("sColumns",se(a,"sName").join(",")),p("iDisplayStart",i),p("iDisplayLength",n);var f={draw:e.iDraw,columns:[],order:[],start:i,length:n,search:{value:h.sSearch,regex:h.bRegex}};for(i=0;i<r;i++)s=a[i],o=c[i],n="function"==typeof s.mData?"function":s.mData,f.columns.push({data:n,name:s.sName,searchable:s.bSearchable,orderable:s.bSortable,search:{value:o.sSearch,regex:o.bRegex}}),p("mDataProp_"+i,n),l.bFilter&&(p("sSearch_"+i,o.sSearch),p("bRegex_"+i,o.bRegex),p("bSearchable_"+i,s.bSearchable)),l.bSort&&p("bSortable_"+i,s.bSortable);return l.bFilter&&(p("sSearch",h.sSearch),p("bRegex",h.bRegex)),l.bSort&&(t.each(d,function(t,e){f.order.push({column:e.col,dir:e.dir}),p("iSortCol_"+t,e.col),p("sSortDir_"+t,e.dir)}),p("iSortingCols",d.length)),null===(a=Yt.ext.legacy.ajax)?e.sAjaxSource?u:f:a?u:f}function q(t,e){var i=U(t,e),s=e.sEcho!==n?e.sEcho:e.draw,o=e.iTotalRecords!==n?e.iTotalRecords:e.recordsTotal,a=e.iTotalDisplayRecords!==n?e.iTotalDisplayRecords:e.recordsFiltered;if(s){if(1*s<t.iDraw)return;t.iDraw=1*s}for(I(t),t._iRecordsTotal=parseInt(o,10),t._iRecordsDisplay=parseInt(a,10),s=0,o=i.length;s<o;s++)y(t,i[s]);t.aiDisplay=t.aiDisplayMaster.slice(),t.bAjaxDataGet=!1,R(t),t._bInitComplete||ot(t,e),t.bAjaxDataGet=!0,ut(t,!1)}function U(e,i){var s=t.isPlainObject(e.ajax)&&e.ajax.dataSrc!==n?e.ajax.dataSrc:e.sAjaxDataProp;return"data"===s?i.aaData||i[s]:""!==s?T(s)(i):i}function Y(e){var n=e.oClasses,s=e.sTableId,o=e.oLanguage,a=e.oPreviousSearch,r=e.aanFeatures,l='<input type="search" class="'+n.sFilterInput+'"/>',h=(h=o.sSearch).match(/_INPUT_/)?h.replace("_INPUT_",l):h+l,c=(n=t("<div/>",{id:r.f?null:s+"_filter",class:n.sFilter}).append(t("<label/>").append(h)),r=function(){var t=this.value?this.value:"";t!=a.sSearch&&(V(e,{sSearch:t,bRegex:a.bRegex,bSmart:a.bSmart,bCaseInsensitive:a.bCaseInsensitive}),e._iDisplayStart=0,R(e))},l=null!==e.searchDelay?e.searchDelay:"ssp"===Ft(e)?400:0,t("input",n).val(a.sSearch).attr("placeholder",o.sSearchPlaceholder).on("keyup.DT search.DT input.DT paste.DT cut.DT",l?me(r,l):r).on("keypress.DT",function(t){if(13==t.keyCode)return!1}).attr("aria-controls",s));return t(e.nTable).on("search.dt.DT",function(t,n){if(e===n)try{c[0]!==i.activeElement&&c.val(a.sSearch)}catch(t){}}),n[0]}function V(t,e,i){var s=t.oPreviousSearch,o=t.aoPreSearchCols,a=function(t){s.sSearch=t.sSearch,s.bRegex=t.bRegex,s.bSmart=t.bSmart,s.bCaseInsensitive=t.bCaseInsensitive};if(_(t),"ssp"!=Ft(t)){for(G(t,e.sSearch,i,e.bEscapeRegex!==n?!e.bEscapeRegex:e.bRegex,e.bSmart,e.bCaseInsensitive),a(e),e=0;e<o.length;e++)X(t,o[e].sSearch,e,o[e].bEscapeRegex!==n?!o[e].bEscapeRegex:o[e].bRegex,o[e].bSmart,o[e].bCaseInsensitive);K(t)}else a(e);t.bFiltered=!0,Ht(t,null,"search",[t])}function K(e){for(var i,n,s=Yt.ext.search,o=e.aiDisplay,a=0,r=s.length;a<r;a++){for(var l=[],h=0,c=o.length;h<c;h++)n=o[h],i=e.aoData[n],s[a](e,i._aFilterData,n,i._aData,h)&&l.push(n);o.length=0,t.merge(o,l)}}function X(t,e,i,n,s,o){if(""!==e){var a=[],r=t.aiDisplay;for(n=J(e,n,s,o),s=0;s<r.length;s++)e=t.aoData[r[s]]._aFilterData[i],n.test(e)&&a.push(r[s]);t.aiDisplay=a}}function G(t,e,i,n,s,o){n=J(e,n,s,o),o=t.oPreviousSearch.sSearch;var a,r=t.aiDisplayMaster;s=[];if(0!==Yt.ext.search.length&&(i=!0),a=Z(t),0>=e.length)t.aiDisplay=r.slice();else{for((a||i||o.length>e.length||0!==e.indexOf(o)||t.bSorted)&&(t.aiDisplay=r.slice()),e=t.aiDisplay,i=0;i<e.length;i++)n.test(t.aoData[e[i]]._sFilterRow)&&s.push(e[i]);t.aiDisplay=s}}function J(e,i,n,s){return e=i?e:de(e),n&&(e="^(?=.*?"+t.map(e.match(/"[^"]+"|[^ ]+/g)||[""],function(t){if('"'===t.charAt(0)){var e=t.match(/^"(.*)"$/);t=e?e[1]:t}return t.replace('"',"")}).join(")(?=.*?")+").*$"),RegExp(e,s?"i":"")}function Z(t){var e,i,n,s,o,a,r,l,h=t.aoColumns,c=Yt.ext.type.search;for(e=!1,i=0,s=t.aoData.length;i<s;i++)if(!(l=t.aoData[i])._aFilterData){for(a=[],n=0,o=h.length;n<o;n++)(e=h[n]).bSearchable?(r=x(t,i,n,"filter"),c[e.sType]&&(r=c[e.sType](r)),null===r&&(r=""),"string"!=typeof r&&r.toString&&(r=r.toString())):r="",r.indexOf&&-1!==r.indexOf("&")&&(pe.innerHTML=r,r=fe?pe.textContent:pe.innerText),r.replace&&(r=r.replace(/[\r\n]/g,"")),a.push(r);l._aFilterData=a,l._sFilterRow=a.join("  "),e=!0}return e}function Q(t){return{search:t.sSearch,smart:t.bSmart,regex:t.bRegex,caseInsensitive:t.bCaseInsensitive}}function tt(t){return{sSearch:t.search,bSmart:t.smart,bRegex:t.regex,bCaseInsensitive:t.caseInsensitive}}function et(e){var i=e.sTableId,n=e.aanFeatures.i,s=t("<div/>",{class:e.oClasses.sInfo,id:n?null:i+"_info"});return n||(e.aoDrawCallback.push({fn:it,sName:"information"}),s.attr("role","status").attr("aria-live","polite"),t(e.nTable).attr("aria-describedby",i+"_info")),s[0]}function it(e){var i=e.aanFeatures.i;if(0!==i.length){var n=e.oLanguage,s=e._iDisplayStart+1,o=e.fnDisplayEnd(),a=e.fnRecordsTotal(),r=e.fnRecordsDisplay(),l=r?n.sInfo:n.sInfoEmpty;r!==a&&(l+=" "+n.sInfoFiltered),l=nt(e,l+=n.sInfoPostFix),null!==(n=n.fnInfoCallback)&&(l=n.call(e.oInstance,e,s,o,a,r,l)),t(i).html(l)}}function nt(t,e){var i=t.fnFormatNumber,n=t._iDisplayStart+1,s=t._iDisplayLength,o=t.fnRecordsDisplay(),a=-1===s;return e.replace(/_START_/g,i.call(t,n)).replace(/_END_/g,i.call(t,t.fnDisplayEnd())).replace(/_MAX_/g,i.call(t,t.fnRecordsTotal())).replace(/_TOTAL_/g,i.call(t,o)).replace(/_PAGE_/g,i.call(t,a?1:Math.ceil(n/s))).replace(/_PAGES_/g,i.call(t,a?1:Math.ceil(o/s)))}function st(t){var e,i,n,s=t.iInitDisplayStart,o=t.aoColumns;i=t.oFeatures;var a=t.bDeferLoading;if(t.bInitialised){for(F(t),N(t),H(t,t.aoHeader),H(t,t.aoFooter),ut(t,!0),i.bAutoWidth&&gt(t),e=0,i=o.length;e<i;e++)(n=o[e]).sWidth&&(n.nTh.style.width=bt(n.sWidth));Ht(t,null,"preInit",[t]),L(t),("ssp"!=(o=Ft(t))||a)&&("ajax"==o?z(t,[],function(i){var n=U(t,i);for(e=0;e<n.length;e++)y(t,n[e]);t.iInitDisplayStart=s,L(t),ut(t,!1),ot(t,i)}):(ut(t,!1),ot(t)))}else setTimeout(function(){st(t)},200)}function ot(t,e){t._bInitComplete=!0,(e||t.oInit.aaData)&&p(t),Ht(t,null,"plugin-init",[t,e]),Ht(t,"aoInitComplete","init",[t,e])}function at(t,e){var i=parseInt(e,10);t._iDisplayLength=i,Rt(t),Ht(t,null,"length",[t,i])}function rt(e){for(var i=e.oClasses,n=e.sTableId,s=e.aLengthMenu,o=(a=t.isArray(s[0]))?s[0]:s,a=(s=a?s[1]:s,t("<select/>",{name:n+"_length","aria-controls":n,class:i.sLengthSelect})),r=0,l=o.length;r<l;r++)a[0][r]=new Option("number"==typeof s[r]?e.fnFormatNumber(s[r]):s[r],o[r]);var h=t("<div><label/></div>").addClass(i.sLength);return e.aanFeatures.l||(h[0].id=n+"_length"),h.children().append(e.oLanguage.sLengthMenu.replace("_MENU_",a[0].outerHTML)),t("select",h).val(e._iDisplayLength).on("change.DT",function(){at(e,t(this).val()),R(e)}),t(e.nTable).on("length.dt.DT",function(i,n,s){e===n&&t("select",h).val(s)}),h[0]}function lt(e){var i=e.sPaginationType,n=Yt.ext.pager[i],s="function"==typeof n,o=function(t){R(t)},a=(i=t("<div/>").addClass(e.oClasses.sPaging+i)[0],e.aanFeatures);return s||n.fnInit(e,i,o),a.p||(i.id=e.sTableId+"_paginate",e.aoDrawCallback.push({fn:function(t){if(s){var e,i=t._iDisplayStart,r=t._iDisplayLength,l=t.fnRecordsDisplay(),h=(i=(h=-1===r)?0:Math.ceil(i/r),r=h?1:Math.ceil(l/r),l=n(i,r),0);for(e=a.p.length;h<e;h++)Lt(t,"pageButton")(t,a.p[h],h,l,i,r)}else n.fnUpdate(t,o)},sName:"pagination"})),i}function ht(t,e,i){var n=t._iDisplayStart,s=t._iDisplayLength,o=t.fnRecordsDisplay();return 0===o||-1===s?n=0:"number"==typeof e?(n=e*s)>o&&(n=0):"first"==e?n=0:"previous"==e?0>(n=0<=s?n-s:0)&&(n=0):"next"==e?n+s<o&&(n+=s):"last"==e?n=Math.floor((o-1)/s)*s:At(t,0,"Unknown paging action: "+e,5),e=t._iDisplayStart!==n,t._iDisplayStart=n,e&&(Ht(t,null,"page",[t]),i&&R(t)),e}function ct(e){return t("<div/>",{id:e.aanFeatures.r?null:e.sTableId+"_processing",class:e.oClasses.sProcessing}).html(e.oLanguage.sProcessing).insertBefore(e.nTable)[0]}function ut(e,i){e.oFeatures.bProcessing&&t(e.aanFeatures.r).css("display",i?"block":"none"),Ht(e,null,"processing",[e,i])}function dt(e){(u=t(e.nTable)).attr("role","grid");var i=e.oScroll;if(""===i.sX&&""===i.sY)return e.nTable;var n=i.sX,s=i.sY,o=e.oClasses,a=u.children("caption"),r=a.length?a[0]._captionSide:null,l=t(u[0].cloneNode(!1)),h=t(u[0].cloneNode(!1)),c=u.children("tfoot");c.length||(c=null),l=t("<div/>",{class:o.sScrollWrapper}).append(t("<div/>",{class:o.sScrollHead}).css({overflow:"hidden",position:"relative",border:0,width:n?n?bt(n):null:"100%"}).append(t("<div/>",{class:o.sScrollHeadInner}).css({"box-sizing":"content-box",width:i.sXInner||"100%"}).append(l.removeAttr("id").css("margin-left",0).append("top"===r?a:null).append(u.children("thead"))))).append(t("<div/>",{class:o.sScrollBody}).css({position:"relative",overflow:"auto",width:n?bt(n):null}).append(u)),c&&l.append(t("<div/>",{class:o.sScrollFoot}).css({overflow:"hidden",border:0,width:n?n?bt(n):null:"100%"}).append(t("<div/>",{class:o.sScrollFootInner}).append(h.removeAttr("id").css("margin-left",0).append("bottom"===r?a:null).append(u.children("tfoot")))));var u,d=(u=l.children())[0],p=(o=u[1],c?u[2]:null);return n&&t(o).on("scroll.DT",function(){var t=this.scrollLeft;d.scrollLeft=t,c&&(p.scrollLeft=t)}),t(o).css(s&&i.bCollapse?"max-height":"height",s),e.nScrollHead=d,e.nScrollBody=o,e.nScrollFoot=p,e.aoDrawCallback.push({fn:pt,sName:"scrolling"}),l[0]}function pt(e){var i,s,o,a,r,l=(u=e.oScroll).sX,h=u.sXInner,c=u.sY,u=u.iBarWidth,d=t(e.nScrollHead),g=d[0].style,m=(_=d.children("div"))[0].style,v=_.children("table"),_=e.nScrollBody,b=t(_),y=_.style,w=t(e.nScrollFoot).children("div"),x=w.children("table"),C=t(e.nTHead),D=t(e.nTable),T=D[0],S=T.style,k=e.nTFoot?t(e.nTFoot):null,I=e.oBrowser,E=I.bScrollOversize,A=se(e.aoColumns,"nTh"),P=[],M=[],O=[],N=[],H=function(t){(t=t.style).paddingTop="0",t.paddingBottom="0",t.borderTopWidth="0",t.borderBottomWidth="0",t.height=0};s=_.scrollHeight>_.clientHeight,e.scrollBarVis!==s&&e.scrollBarVis!==n?(e.scrollBarVis=s,p(e)):(e.scrollBarVis=s,D.children("thead, tfoot").remove(),k&&(o=k.clone().prependTo(D),i=k.find("tr"),o=o.find("tr")),a=C.clone().prependTo(D),C=C.find("tr"),s=a.find("tr"),a.find("th, td").removeAttr("tabindex"),l||(y.width="100%",d[0].style.width="100%"),t.each(W(e,a),function(t,i){r=f(e,t),i.style.width=e.aoColumns[r].sWidth}),k&&ft(function(t){t.style.width=""},o),d=D.outerWidth(),""===l?(S.width="100%",E&&(D.find("tbody").height()>_.offsetHeight||"scroll"==b.css("overflow-y"))&&(S.width=bt(D.outerWidth()-u)),d=D.outerWidth()):""!==h&&(S.width=bt(h),d=D.outerWidth()),ft(H,s),ft(function(e){O.push(e.innerHTML),P.push(bt(t(e).css("width")))},s),ft(function(e,i){-1!==t.inArray(e,A)&&(e.style.width=P[i])},C),t(s).height(0),k&&(ft(H,o),ft(function(e){N.push(e.innerHTML),M.push(bt(t(e).css("width")))},o),ft(function(t,e){t.style.width=M[e]},i),t(o).height(0)),ft(function(t,e){t.innerHTML='<div class="dataTables_sizing" style="height:0;overflow:hidden;">'+O[e]+"</div>",t.style.width=P[e]},s),k&&ft(function(t,e){t.innerHTML='<div class="dataTables_sizing" style="height:0;overflow:hidden;">'+N[e]+"</div>",t.style.width=M[e]},o),D.outerWidth()<d?(i=_.scrollHeight>_.offsetHeight||"scroll"==b.css("overflow-y")?d+u:d,E&&(_.scrollHeight>_.offsetHeight||"scroll"==b.css("overflow-y"))&&(S.width=bt(i-u)),(""===l||""!==h)&&At(e,1,"Possible column misalignment",6)):i="100%",y.width=bt(i),g.width=bt(i),k&&(e.nScrollFoot.style.width=bt(i)),!c&&E&&(y.height=bt(T.offsetHeight+u)),l=D.outerWidth(),v[0].style.width=bt(l),m.width=bt(l),h=D.height()>_.clientHeight||"scroll"==b.css("overflow-y"),m[c="padding"+(I.bScrollbarLeft?"Left":"Right")]=h?u+"px":"0px",k&&(x[0].style.width=bt(l),w[0].style.width=bt(l),w[0].style[c]=h?u+"px":"0px"),D.children("colgroup").insertBefore(D.children("thead")),b.scroll(),!e.bSorted&&!e.bFiltered||e._drawHold||(_.scrollTop=0))}function ft(t,e,i){for(var n,s,o=0,a=0,r=e.length;a<r;){for(n=e[a].firstChild,s=i?i[a].firstChild:null;n;)1===n.nodeType&&(i?t(n,s,o):t(n,o),o++),n=n.nextSibling,s=i?s.nextSibling:null;a++}}function gt(i){var n,s,o=i.nTable,a=i.aoColumns,r=(w=i.oScroll).sY,l=w.sX,h=w.sXInner,c=a.length,u=v(i,"bVisible"),d=t("th",i.nTHead),g=o.getAttribute("width"),_=o.parentNode,b=!1,y=i.oBrowser,w=y.bScrollOversize;for((n=o.style.width)&&-1!==n.indexOf("%")&&(g=n),n=0;n<u.length;n++)null!==(s=a[u[n]]).sWidth&&(s.sWidth=mt(s.sWidthOrig,_),b=!0);if(w||!b&&!l&&!r&&c==m(i)&&c==d.length)for(n=0;n<c;n++)null!==(u=f(i,n))&&(a[u].sWidth=bt(d.eq(n).width()));else{(c=t(o).clone().css("visibility","hidden").removeAttr("id")).find("tbody tr").remove();var x=t("<tr/>").appendTo(c.find("tbody"));for(c.find("thead, tfoot").remove(),c.append(t(i.nTHead).clone()).append(t(i.nTFoot).clone()),c.find("tfoot th, tfoot td").css("width",""),d=W(i,c.find("thead")[0]),n=0;n<u.length;n++)s=a[u[n]],d[n].style.width=null!==s.sWidthOrig&&""!==s.sWidthOrig?bt(s.sWidthOrig):"",s.sWidthOrig&&l&&t(d[n]).append(t("<div/>").css({width:s.sWidthOrig,margin:0,padding:0,border:0,height:1}));if(i.aoData.length)for(n=0;n<u.length;n++)s=a[b=u[n]],t(vt(i,b)).clone(!1).append(s.sContentPadding).appendTo(x);for(t("[name]",c).removeAttr("name"),s=t("<div/>").css(l||r?{position:"absolute",top:0,left:0,height:1,right:0,overflow:"hidden"}:{}).append(c).appendTo(_),l&&h?c.width(h):l?(c.css("width","auto"),c.removeAttr("width"),c.width()<_.clientWidth&&g&&c.width(_.clientWidth)):r?c.width(_.clientWidth):g&&c.width(g),n=r=0;n<u.length;n++)h=(_=t(d[n])).outerWidth()-_.width(),r+=_=y.bBounding?Math.ceil(d[n].getBoundingClientRect().width):_.outerWidth(),a[u[n]].sWidth=bt(_-h);o.style.width=bt(r),s.remove()}g&&(o.style.width=bt(g)),!g&&!l||i._reszEvt||(o=function(){t(e).on("resize.DT-"+i.sInstance,me(function(){p(i)}))},w?setTimeout(o,1e3):o(),i._reszEvt=!0)}function mt(e,n){if(!e)return 0;var s=t("<div/>").css("width",bt(e)).appendTo(n||i.body),o=s[0].offsetWidth;return s.remove(),o}function vt(e,i){var n=_t(e,i);if(0>n)return null;var s=e.aoData[n];return s.nTr?s.anCells[i]:t("<td/>").html(x(e,n,i,"display"))[0]}function _t(t,e){for(var i,n=-1,s=-1,o=0,a=t.aoData.length;o<a;o++)(i=(i=(i=x(t,o,e,"display")+"").replace(ge,"")).replace(/&nbsp;/g," ")).length>n&&(n=i.length,s=o);return s}function bt(t){return null===t?"0px":"number"==typeof t?0>t?"0px":t+"px":t.match(/\d$/)?t+"px":t}function yt(e){var i,s,o,a,r,l,h=[],c=e.aoColumns;i=e.aaSortingFixed,s=t.isPlainObject(i);var u=[];for(o=function(e){e.length&&!t.isArray(e[0])?u.push(e):t.merge(u,e)},t.isArray(i)&&o(i),s&&i.pre&&o(i.pre),o(e.aaSorting),s&&i.post&&o(i.post),e=0;e<u.length;e++)for(i=0,s=(o=c[l=u[e][0]].aDataSort).length;i<s;i++)r=c[a=o[i]].sType||"string",u[e]._idx===n&&(u[e]._idx=t.inArray(u[e][1],c[a].asSorting)),h.push({src:l,col:a,dir:u[e][1],index:u[e]._idx,type:r,formatter:Yt.ext.type.order[r+"-pre"]});return h}function wt(t){var e,i,n,s,o=[],a=Yt.ext.type.order,r=t.aoData,l=0,h=t.aiDisplayMaster;for(_(t),e=0,i=(s=yt(t)).length;e<i;e++)(n=s[e]).formatter&&l++,St(t,n.col);if("ssp"!=Ft(t)&&0!==s.length){for(e=0,i=h.length;e<i;e++)o[h[e]]=e;l===s.length?h.sort(function(t,e){var i,n,a,l,h=s.length,c=r[t]._aSortData,u=r[e]._aSortData;for(a=0;a<h;a++)if(0!==(i=(i=c[(l=s[a]).col])<(n=u[l.col])?-1:i>n?1:0))return"asc"===l.dir?i:-i;return(i=o[t])<(n=o[e])?-1:i>n?1:0}):h.sort(function(t,e){var i,n,l,h,c=s.length,u=r[t]._aSortData,d=r[e]._aSortData;for(l=0;l<c;l++)if(i=u[(h=s[l]).col],n=d[h.col],0!==(i=(h=a[h.type+"-"+h.dir]||a["string-"+h.dir])(i,n)))return i;return(i=o[t])<(n=o[e])?-1:i>n?1:0})}t.bSorted=!0}function xt(t){for(var e,i,n=t.aoColumns,s=yt(t),o=(t=t.oLanguage.oAria,0),a=n.length;o<a;o++){var r=(i=n[o]).asSorting;e=i.sTitle.replace(/<.*?>/g,"");var l=i.nTh;l.removeAttribute("aria-sort"),i.bSortable&&(0<s.length&&s[0].col==o?(l.setAttribute("aria-sort","asc"==s[0].dir?"ascending":"descending"),i=r[s[0].index+1]||r[0]):i=r[0],e+="asc"===i?t.sSortAscending:t.sSortDescending),l.setAttribute("aria-label",e)}}function Ct(e,i,s,o){var a=e.aaSorting,r=e.aoColumns[i].asSorting,l=function(e,i){var s=e._idx;return s===n&&(s=t.inArray(e[1],r)),s+1<r.length?s+1:i?null:0};"number"==typeof a[0]&&(a=e.aaSorting=[a]),s&&e.oFeatures.bSortMulti?-1!==(s=t.inArray(i,se(a,"0")))?(null===(i=l(a[s],!0))&&1===a.length&&(i=0),null===i?a.splice(s,1):(a[s][1]=r[i],a[s]._idx=i)):(a.push([i,r[0],0]),a[a.length-1]._idx=0):a.length&&a[0][0]==i?(i=l(a[0]),a.length=1,a[0][1]=r[i],a[0]._idx=i):(a.length=0,a.push([i,r[0]]),a[0]._idx=0),L(e),"function"==typeof o&&o(e)}function Dt(t,e,i,n){var s=t.aoColumns[i];Ot(e,{},function(e){!1!==s.bSortable&&(t.oFeatures.bProcessing?(ut(t,!0),setTimeout(function(){Ct(t,i,e.shiftKey,n),"ssp"!==Ft(t)&&ut(t,!1)},0)):Ct(t,i,e.shiftKey,n))})}function Tt(e){var i,n,s=e.aLastSort,o=e.oClasses.sSortColumn,a=yt(e),r=e.oFeatures;if(r.bSort&&r.bSortClasses){for(r=0,i=s.length;r<i;r++)n=s[r].src,t(se(e.aoData,"anCells",n)).removeClass(o+(2>r?r+1:3));for(r=0,i=a.length;r<i;r++)n=a[r].src,t(se(e.aoData,"anCells",n)).addClass(o+(2>r?r+1:3))}e.aLastSort=a}function St(t,e){var i,n=t.aoColumns[e],s=Yt.ext.order[n.sSortDataType];s&&(i=s.call(t.oInstance,t,e,g(t,e)));for(var o,a=Yt.ext.type.order[n.sType+"-pre"],r=0,l=t.aoData.length;r<l;r++)(n=t.aoData[r])._aSortData||(n._aSortData=[]),(!n._aSortData[e]||s)&&(o=s?i[r]:x(t,r,e,"sort"),n._aSortData[e]=a?a(o):o)}function kt(e){if(e.oFeatures.bStateSave&&!e.bDestroying){var i={time:+new Date,start:e._iDisplayStart,length:e._iDisplayLength,order:t.extend(!0,[],e.aaSorting),search:Q(e.oPreviousSearch),columns:t.map(e.aoColumns,function(t,i){return{visible:t.bVisible,search:Q(e.aoPreSearchCols[i])}})};Ht(e,"aoStateSaveParams","stateSaveParams",[e,i]),e.oSavedState=i,e.fnStateSaveCallback.call(e.oInstance,e,i)}}function It(e,i,s){var o,a,r=e.aoColumns;i=function(i){if(i&&i.time){var l=Ht(e,"aoStateLoadParams","stateLoadParams",[e,i]);if(-1===t.inArray(!1,l)&&!(0<(l=e.iStateDuration)&&i.time<+new Date-1e3*l||i.columns&&r.length!==i.columns.length)){if(e.oLoadedState=t.extend(!0,{},i),i.start!==n&&(e._iDisplayStart=i.start,e.iInitDisplayStart=i.start),i.length!==n&&(e._iDisplayLength=i.length),i.order!==n&&(e.aaSorting=[],t.each(i.order,function(t,i){e.aaSorting.push(i[0]>=r.length?[0,i[1]]:i)})),i.search!==n&&t.extend(e.oPreviousSearch,tt(i.search)),i.columns)for(o=0,a=i.columns.length;o<a;o++)(l=i.columns[o]).visible!==n&&(r[o].bVisible=l.visible),l.search!==n&&t.extend(e.aoPreSearchCols[o],tt(l.search));Ht(e,"aoStateLoaded","stateLoaded",[e,i])}}s()};if(e.oFeatures.bStateSave){var l=e.fnStateLoadCallback.call(e.oInstance,e,i);l!==n&&i(l)}else s()}function Et(e){var i=Yt.settings;return-1!==(e=t.inArray(e,se(i,"nTable")))?i[e]:null}function At(t,i,n,s){if(n="DataTables warning: "+(t?"table id="+t.sTableId+" - ":"")+n,s&&(n+=". For more information about this error, please see http://datatables.net/tn/"+s),i)e.console&&console.log&&console.log(n);else if(i=(i=Yt.ext).sErrMode||i.errMode,t&&Ht(t,null,"error",[t,s,n]),"alert"==i)alert(n);else{if("throw"==i)throw Error(n);"function"==typeof i&&i(t,s,n)}}function Pt(e,i,s,o){t.isArray(s)?t.each(s,function(n,s){t.isArray(s)?Pt(e,i,s[0],s[1]):Pt(e,i,s)}):(o===n&&(o=s),i[s]!==n&&(e[o]=i[s]))}function Mt(e,i,n){var s,o;for(o in i)i.hasOwnProperty(o)&&(s=i[o],t.isPlainObject(s)?(t.isPlainObject(e[o])||(e[o]={}),t.extend(!0,e[o],s)):e[o]=n&&"data"!==o&&"aaData"!==o&&t.isArray(s)?s.slice():s);return e}function Ot(e,i,n){t(e).on("click.DT",i,function(t){e.blur(),n(t)}).on("keypress.DT",i,function(t){13===t.which&&(t.preventDefault(),n(t))}).on("selectstart.DT",function(){return!1})}function Nt(t,e,i,n){i&&t[e].push({fn:i,sName:n})}function Ht(e,i,n,s){var o=[];return i&&(o=t.map(e[i].slice().reverse(),function(t){return t.fn.apply(e.oInstance,s)})),null!==n&&(i=t.Event(n+".dt"),t(e.nTable).trigger(i,s),o.push(i.result)),o}function Rt(t){var e=t._iDisplayStart,i=t.fnDisplayEnd(),n=t._iDisplayLength;e>=i&&(e=i-n),e-=e%n,(-1===n||0>e)&&(e=0),t._iDisplayStart=e}function Lt(e,i){var n=e.renderer,s=Yt.ext.renderer[i];return t.isPlainObject(n)&&n[i]?s[n[i]]||s._:"string"==typeof n&&s[n]||s._}function Ft(t){return t.oFeatures.bServerSide?"ssp":t.ajax||t.sAjaxSource?"ajax":"dom"}function jt(t,e){var i=[],n=(i=Ie.numbers_length,Math.floor(i/2));return e<=i?i=ae(0,e):t<=n?((i=ae(0,i-2)).push("ellipsis"),i.push(e-1)):(t>=e-1-n?i=ae(e-(i-2),e):((i=ae(t-n+2,t+n-1)).push("ellipsis"),i.push(e-1)),i.splice(0,0,"ellipsis"),i.splice(0,0,0)),i.DT_el="span",i}function Wt(e){t.each({num:function(t){return Ee(t,e)},"num-fmt":function(t){return Ee(t,e,Zt)},"html-num":function(t){return Ee(t,e,Xt)},"html-num-fmt":function(t){return Ee(t,e,Xt,Zt)}},function(t,i){Bt.type.order[t+e+"-pre"]=i,t.match(/^html\-/)&&(Bt.type.search[t+e]=Bt.type.search.html)})}function zt(t){return function(){var e=[Et(this[Yt.ext.iApiIndex])].concat(Array.prototype.slice.call(arguments));return Yt.ext.internal[t].apply(this,e)}}var Bt,$t,qt,Ut,Yt=function(e){this.$=function(t,e){return this.api(!0).$(t,e)},this._=function(t,e){return this.api(!0).rows(t,e).data()},this.api=function(t){return new $t(t?Et(this[Bt.iApiIndex]):this)},this.fnAddData=function(e,i){var s=this.api(!0),o=t.isArray(e)&&(t.isArray(e[0])||t.isPlainObject(e[0]))?s.rows.add(e):s.row.add(e);return(i===n||i)&&s.draw(),o.flatten().toArray()},this.fnAdjustColumnSizing=function(t){var e=this.api(!0).columns.adjust(),i=e.settings()[0],s=i.oScroll;t===n||t?e.draw(!1):(""!==s.sX||""!==s.sY)&&pt(i)},this.fnClearTable=function(t){var e=this.api(!0).clear();(t===n||t)&&e.draw()},this.fnClose=function(t){this.api(!0).row(t).child.hide()},this.fnDeleteRow=function(t,e,i){var s=this.api(!0),o=(t=s.rows(t)).settings()[0],a=o.aoData[t[0][0]];return t.remove(),e&&e.call(this,o,a),(i===n||i)&&s.draw(),a},this.fnDestroy=function(t){this.api(!0).destroy(t)},this.fnDraw=function(t){this.api(!0).draw(t)},this.fnFilter=function(t,e,i,s,o,a){o=this.api(!0),null===e||e===n?o.search(t,i,s,a):o.column(e).search(t,i,s,a),o.draw()},this.fnGetData=function(t,e){var i=this.api(!0);if(t!==n){var s=t.nodeName?t.nodeName.toLowerCase():"";return e!==n||"td"==s||"th"==s?i.cell(t,e).data():i.row(t).data()||null}return i.data().toArray()},this.fnGetNodes=function(t){var e=this.api(!0);return t!==n?e.row(t).node():e.rows().nodes().flatten().toArray()},this.fnGetPosition=function(t){var e=this.api(!0),i=t.nodeName.toUpperCase();return"TR"==i?e.row(t).index():"TD"==i||"TH"==i?[(t=e.cell(t).index()).row,t.columnVisible,t.column]:null},this.fnIsOpen=function(t){return this.api(!0).row(t).child.isShown()},this.fnOpen=function(t,e,i){return this.api(!0).row(t).child(e,i).show().child()[0]},this.fnPageChange=function(t,e){var i=this.api(!0).page(t);(e===n||e)&&i.draw(!1)},this.fnSetColumnVis=function(t,e,i){t=this.api(!0).column(t).visible(e),(i===n||i)&&t.columns.adjust().draw()},this.fnSettings=function(){return Et(this[Bt.iApiIndex])},this.fnSort=function(t){this.api(!0).order(t).draw()},this.fnSortListener=function(t,e,i){this.api(!0).order.listener(t,e,i)},this.fnUpdate=function(t,e,i,s,o){var a=this.api(!0);return i===n||null===i?a.row(e).data(t):a.cell(e,i).data(t),(o===n||o)&&a.columns.adjust(),(s===n||s)&&a.draw(),0},this.fnVersionCheck=Bt.fnVersionCheck;var i=this,s=e===n,c=this.length;for(var p in s&&(e={}),this.oApi=this.internal=Bt.internal,Yt.ext.internal)p&&(this[p]=zt(p));return this.each(function(){var p,f={},g=1<c?Mt(f,e,!0):e,m=0,v=(f=this.getAttribute("id"),!1),_=Yt.defaults,x=t(this);if("table"!=this.nodeName.toLowerCase())At(null,0,"Non-table node initialisation ("+this.nodeName+")",2);else{r(_),l(_.column),o(_,_,!0),o(_.column,_.column,!0),o(_,t.extend(g,x.data()));var C=Yt.settings;m=0;for(p=C.length;m<p;m++){var D=C[m];if(D.nTable==this||D.nTHead.parentNode==this||D.nTFoot&&D.nTFoot.parentNode==this){var S=g.bRetrieve!==n?g.bRetrieve:_.bRetrieve;if(s||S)return D.oInstance;if(g.bDestroy!==n?g.bDestroy:_.bDestroy){D.oInstance.fnDestroy();break}return void At(D,0,"Cannot reinitialise DataTable",3)}if(D.sTableId==this.id){C.splice(m,1);break}}null!==f&&""!==f||(this.id=f="DataTables_Table_"+Yt.ext._unique++);var k=t.extend(!0,{},Yt.models.oSettings,{sDestroyWidth:x[0].style.width,sInstance:f,sTableId:f});k.nTable=this,k.oApi=i.internal,k.oInit=g,C.push(k),k.oInstance=1===i.length?i:x.dataTable(),r(g),g.oLanguage&&a(g.oLanguage),g.aLengthMenu&&!g.iDisplayLength&&(g.iDisplayLength=t.isArray(g.aLengthMenu[0])?g.aLengthMenu[0][0]:g.aLengthMenu[0]),g=Mt(t.extend(!0,{},_),g),Pt(k.oFeatures,g,"bPaginate bLengthChange bFilter bSort bSortMulti bInfo bProcessing bAutoWidth bSortClasses bServerSide bDeferRender".split(" ")),Pt(k,g,["asStripeClasses","ajax","fnServerData","fnFormatNumber","sServerMethod","aaSorting","aaSortingFixed","aLengthMenu","sPaginationType","sAjaxSource","sAjaxDataProp","iStateDuration","sDom","bSortCellsTop","iTabIndex","fnStateLoadCallback","fnStateSaveCallback","renderer","searchDelay","rowId",["iCookieDuration","iStateDuration"],["oSearch","oPreviousSearch"],["aoSearchCols","aoPreSearchCols"],["iDisplayLength","_iDisplayLength"]]),Pt(k.oScroll,g,[["sScrollX","sX"],["sScrollXInner","sXInner"],["sScrollY","sY"],["bScrollCollapse","bCollapse"]]),Pt(k.oLanguage,g,"fnInfoCallback"),Nt(k,"aoDrawCallback",g.fnDrawCallback,"user"),Nt(k,"aoServerParams",g.fnServerParams,"user"),Nt(k,"aoStateSaveParams",g.fnStateSaveParams,"user"),Nt(k,"aoStateLoadParams",g.fnStateLoadParams,"user"),Nt(k,"aoStateLoaded",g.fnStateLoaded,"user"),Nt(k,"aoRowCallback",g.fnRowCallback,"user"),Nt(k,"aoRowCreatedCallback",g.fnCreatedRow,"user"),Nt(k,"aoHeaderCallback",g.fnHeaderCallback,"user"),Nt(k,"aoFooterCallback",g.fnFooterCallback,"user"),Nt(k,"aoInitComplete",g.fnInitComplete,"user"),Nt(k,"aoPreDrawCallback",g.fnPreDrawCallback,"user"),k.rowIdFn=T(g.rowId),h(k);var I=k.oClasses;t.extend(I,Yt.ext.classes,g.oClasses),x.addClass(I.sTable),k.iInitDisplayStart===n&&(k.iInitDisplayStart=g.iDisplayStart,k._iDisplayStart=g.iDisplayStart),null!==g.iDeferLoading&&(k.bDeferLoading=!0,f=t.isArray(g.iDeferLoading),k._iRecordsDisplay=f?g.iDeferLoading[0]:g.iDeferLoading,k._iRecordsTotal=f?g.iDeferLoading[1]:g.iDeferLoading);var E=k.oLanguage;t.extend(!0,E,g.oLanguage),E.sUrl&&(t.ajax({dataType:"json",url:E.sUrl,success:function(e){a(e),o(_.oLanguage,e),t.extend(!0,E,e),st(k)},error:function(){st(k)}}),v=!0),null===g.asStripeClasses&&(k.asStripeClasses=[I.sStripeOdd,I.sStripeEven]);f=k.asStripeClasses;var A=x.children("tbody").find("tr").eq(0);if(-1!==t.inArray(!0,t.map(f,function(t){return A.hasClass(t)}))&&(t("tbody tr",this).removeClass(f.join(" ")),k.asDestroyStripes=f.slice()),f=[],0!==(C=this.getElementsByTagName("thead")).length&&(j(k.aoHeader,C[0]),f=W(k)),null===g.aoColumns)for(C=[],m=0,p=f.length;m<p;m++)C.push(null);else C=g.aoColumns;for(m=0,p=C.length;m<p;m++)u(k,f?f[m]:null);if(b(k,g.aoColumnDefs,C,function(t,e){d(k,t,e)}),A.length){var P=function(t,e){return null!==t.getAttribute("data-"+e)?e:null};t(A[0]).children("th, td").each(function(t,e){var i=k.aoColumns[t];if(i.mData===t){var s=P(e,"sort")||P(e,"order"),o=P(e,"filter")||P(e,"search");null===s&&null===o||(i.mData={_:t+".display",sort:null!==s?t+".@data-"+s:n,type:null!==s?t+".@data-"+s:n,filter:null!==o?t+".@data-"+o:n},d(k,t))}})}var M=k.oFeatures;f=function(){if(g.aaSorting===n){var e=k.aaSorting;for(m=0,p=e.length;m<p;m++)e[m][1]=k.aoColumns[m].asSorting[0]}Tt(k),M.bSort&&Nt(k,"aoDrawCallback",function(){if(k.bSorted){var e=yt(k),i={};t.each(e,function(t,e){i[e.src]=e.dir}),Ht(k,null,"order",[k,e,i]),xt(k)}}),Nt(k,"aoDrawCallback",function(){(k.bSorted||"ssp"===Ft(k)||M.bDeferRender)&&Tt(k)},"sc");e=x.children("caption").each(function(){this._captionSide=t(this).css("caption-side")});var i=x.children("thead");if(0===i.length&&(i=t("<thead/>").appendTo(x)),k.nTHead=i[0],0===(i=x.children("tbody")).length&&(i=t("<tbody/>").appendTo(x)),k.nTBody=i[0],0===(i=x.children("tfoot")).length&&e.length>0&&(""!==k.oScroll.sX||""!==k.oScroll.sY)&&(i=t("<tfoot/>").appendTo(x)),0===i.length||0===i.children().length?x.addClass(I.sNoFooter):i.length>0&&(k.nTFoot=i[0],j(k.aoFooter,k.nTFoot)),g.aaData)for(m=0;m<g.aaData.length;m++)y(k,g.aaData[m]);else(k.bDeferLoading||"dom"==Ft(k))&&w(k,t(k.nTBody).children("tr"));k.aiDisplay=k.aiDisplayMaster.slice(),k.bInitialised=!0,!1===v&&st(k)};g.bStateSave?(M.bStateSave=!0,Nt(k,"aoDrawCallback",kt,"state_save"),It(k,g,f)):f()}}),i=null,this},Vt={},Kt=/[\r\n]/g,Xt=/<.*?>/g,Gt=/^\d{2,4}[\.\/\-]\d{1,2}[\.\/\-]\d{1,2}([T ]{1}\d{1,2}[:\.]\d{2}([\.:]\d{2})?)?$/,Jt=RegExp("(\\/|\\.|\\*|\\+|\\?|\\||\\(|\\)|\\[|\\]|\\{|\\}|\\\\|\\$|\\^|\\-)","g"),Zt=/[',$£€¥%\u2009\u202F\u20BD\u20a9\u20BArfk]/gi,Qt=function(t){return!t||!0===t||"-"===t},te=function(t){var e=parseInt(t,10);return!isNaN(e)&&isFinite(t)?e:null},ee=function(t,e){return Vt[e]||(Vt[e]=RegExp(de(e),"g")),"string"==typeof t&&"."!==e?t.replace(/\./g,"").replace(Vt[e],"."):t},ie=function(t,e,i){var n="string"==typeof t;return!!Qt(t)||(e&&n&&(t=ee(t,e)),i&&n&&(t=t.replace(Zt,"")),!isNaN(parseFloat(t))&&isFinite(t))},ne=function(t,e,i){return!!Qt(t)||((Qt(t)||"string"==typeof t)&&!!ie(t.replace(Xt,""),e,i)||null)},se=function(t,e,i){var s=[],o=0,a=t.length;if(i!==n)for(;o<a;o++)t[o]&&t[o][e]&&s.push(t[o][e][i]);else for(;o<a;o++)t[o]&&s.push(t[o][e]);return s},oe=function(t,e,i,s){var o=[],a=0,r=e.length;if(s!==n)for(;a<r;a++)t[e[a]][i]&&o.push(t[e[a]][i][s]);else for(;a<r;a++)o.push(t[e[a]][i]);return o},ae=function(t,e){var i,s=[];e===n?(e=0,i=t):(i=e,e=t);for(var o=e;o<i;o++)s.push(o);return s},re=function(t){for(var e=[],i=0,n=t.length;i<n;i++)t[i]&&e.push(t[i]);return e},le=function(t){var e;t:{if(!(2>t.length))for(var i=(e=t.slice().sort())[0],n=1,s=e.length;n<s;n++){if(e[n]===i){e=!1;break t}i=e[n]}e=!0}if(e)return t.slice();e=[];s=t.length;var o,a=0;n=0;t:for(;n<s;n++){for(i=t[n],o=0;o<a;o++)if(e[o]===i)continue t;e.push(i),a++}return e};Yt.util={throttle:function(t,e){var i,s,o=e!==n?e:200;return function(){var e=this,a=+new Date,r=arguments;i&&a<i+o?(clearTimeout(s),s=setTimeout(function(){i=n,t.apply(e,r)},o)):(i=a,t.apply(e,r))}},escapeRegex:function(t){return t.replace(Jt,"\\$1")}};var he=function(t,e,i){t[e]!==n&&(t[i]=t[e])},ce=/\[.*?\]$/,ue=/\(\)$/,de=Yt.util.escapeRegex,pe=t("<div>")[0],fe=pe.textContent!==n,ge=/<.*?>/g,me=Yt.util.throttle,ve=[],_e=Array.prototype;$t=function(e,i){if(!(this instanceof $t))return new $t(e,i);var n=[],s=function(e){(e=function(e){var i,n,s=Yt.settings,o=t.map(s,function(t){return t.nTable});return e?e.nTable&&e.oApi?[e]:e.nodeName&&"table"===e.nodeName.toLowerCase()?-1!==(i=t.inArray(e,o))?[s[i]]:null:e&&"function"==typeof e.settings?e.settings().toArray():("string"==typeof e?n=t(e):e instanceof t&&(n=e),n?n.map(function(){return-1!==(i=t.inArray(this,o))?s[i]:null}).toArray():void 0):[]}(e))&&(n=n.concat(e))};if(t.isArray(e))for(var o=0,a=e.length;o<a;o++)s(e[o]);else s(e);this.context=le(n),i&&t.merge(this,i),this.selector={rows:null,cols:null,opts:null},$t.extend(this,this,ve)},Yt.Api=$t,t.extend($t.prototype,{any:function(){return 0!==this.count()},concat:_e.concat,context:[],count:function(){return this.flatten().length},each:function(t){for(var e=0,i=this.length;e<i;e++)t.call(this,this[e],e,this);return this},eq:function(t){var e=this.context;return e.length>t?new $t(e[t],this[t]):null},filter:function(t){var e=[];if(_e.filter)e=_e.filter.call(this,t,this);else for(var i=0,n=this.length;i<n;i++)t.call(this,this[i],i,this)&&e.push(this[i]);return new $t(this.context,e)},flatten:function(){var t=[];return new $t(this.context,t.concat.apply(t,this.toArray()))},join:_e.join,indexOf:_e.indexOf||function(t,e){for(var i=e||0,n=this.length;i<n;i++)if(this[i]===t)return i;return-1},iterator:function(t,e,i,s){var o,a,r,l,h,c,u,d=[],p=this.context,f=this.selector;for("string"==typeof t&&(s=i,i=e,e=t,t=!1),a=0,r=p.length;a<r;a++){var g=new $t(p[a]);if("table"===e)(o=i.call(g,p[a],a))!==n&&d.push(o);else if("columns"===e||"rows"===e)(o=i.call(g,p[a],this[a],a))!==n&&d.push(o);else if("column"===e||"column-rows"===e||"row"===e||"cell"===e)for(u=this[a],"column-rows"===e&&(c=Ce(p[a],f.opts)),l=0,h=u.length;l<h;l++)o=u[l],(o="cell"===e?i.call(g,p[a],o.row,o.column,a,l):i.call(g,p[a],o,a,l,c))!==n&&d.push(o)}return d.length||s?((e=(t=new $t(p,t?d.concat.apply([],d):d)).selector).rows=f.rows,e.cols=f.cols,e.opts=f.opts,t):this},lastIndexOf:_e.lastIndexOf||function(t,e){return this.indexOf.apply(this.toArray.reverse(),arguments)},length:0,map:function(t){var e=[];if(_e.map)e=_e.map.call(this,t,this);else for(var i=0,n=this.length;i<n;i++)e.push(t.call(this,this[i],i));return new $t(this.context,e)},pluck:function(t){return this.map(function(e){return e[t]})},pop:_e.pop,push:_e.push,reduce:_e.reduce||function(t,e){return c(this,t,e,0,this.length,1)},reduceRight:_e.reduceRight||function(t,e){return c(this,t,e,this.length-1,-1,-1)},reverse:_e.reverse,selector:null,shift:_e.shift,slice:function(){return new $t(this.context,this)},sort:_e.sort,splice:_e.splice,toArray:function(){return _e.slice.call(this)},to$:function(){return t(this)},toJQuery:function(){return t(this)},unique:function(){return new $t(this.context,le(this))},unshift:_e.unshift}),$t.extend=function(e,i,n){if(n.length&&i&&(i instanceof $t||i.__dt_wrapper)){var s,o,a,r=function(t,e,i){return function(){var n=e.apply(t,arguments);return $t.extend(n,n,i.methodExt),n}};for(s=0,o=n.length;s<o;s++)i[(a=n[s]).name]="function"==typeof a.val?r(e,a.val,a):t.isPlainObject(a.val)?{}:a.val,i[a.name].__dt_wrapper=!0,$t.extend(e,i[a.name],a.propExt)}},$t.register=qt=function(e,i){if(t.isArray(e))for(var n=0,s=e.length;n<s;n++)$t.register(e[n],i);else{var o,a,r=e.split("."),l=ve;for(n=0,s=r.length;n<s;n++){var h;o=(a=-1!==r[n].indexOf("()"))?r[n].replace("()",""):r[n];t:{h=0;for(var c=l.length;h<c;h++)if(l[h].name===o){h=l[h];break t}h=null}h||(h={name:o,val:{},methodExt:[],propExt:[]},l.push(h)),n===s-1?h.val=i:l=a?h.methodExt:h.propExt}}},$t.registerPlural=Ut=function(e,i,s){$t.register(e,s),$t.register(i,function(){var e=s.apply(this,arguments);return e===this?this:e instanceof $t?e.length?t.isArray(e[0])?new $t(e.context,e[0]):e[0]:n:e})},qt("tables()",function(e){var i;if(e){i=$t;var n=this.context;if("number"==typeof e)e=[n[e]];else{var s=t.map(n,function(t){return t.nTable});e=t(s).filter(e).map(function(){var e=t.inArray(this,s);return n[e]}).toArray()}i=new i(e)}else i=this;return i}),qt("table()",function(t){var e=(t=this.tables(t)).context;return e.length?new $t(e[0]):t}),Ut("tables().nodes()","table().node()",function(){return this.iterator("table",function(t){return t.nTable},1)}),Ut("tables().body()","table().body()",function(){return this.iterator("table",function(t){return t.nTBody},1)}),Ut("tables().header()","table().header()",function(){return this.iterator("table",function(t){return t.nTHead},1)}),Ut("tables().footer()","table().footer()",function(){return this.iterator("table",function(t){return t.nTFoot},1)}),Ut("tables().containers()","table().container()",function(){return this.iterator("table",function(t){return t.nTableWrapper},1)}),qt("draw()",function(t){return this.iterator("table",function(e){"page"===t?R(e):("string"==typeof t&&(t="full-hold"!==t),L(e,!1===t))})}),qt("page()",function(t){return t===n?this.page.info().page:this.iterator("table",function(e){ht(e,t)})}),qt("page.info()",function(){if(0===this.context.length)return n;var t=this.context[0],e=t._iDisplayStart,i=t.oFeatures.bPaginate?t._iDisplayLength:-1,s=t.fnRecordsDisplay(),o=-1===i;return{page:o?0:Math.floor(e/i),pages:o?1:Math.ceil(s/i),start:e,end:t.fnDisplayEnd(),length:i,recordsTotal:t.fnRecordsTotal(),recordsDisplay:s,serverSide:"ssp"===Ft(t)}}),qt("page.len()",function(t){return t===n?0!==this.context.length?this.context[0]._iDisplayLength:n:this.iterator("table",function(e){at(e,t)})});var be=function(t,e,i){if(i){var n=new $t(t);n.one("draw",function(){i(n.ajax.json())})}if("ssp"==Ft(t))L(t,e);else{ut(t,!0);var s=t.jqXHR;s&&4!==s.readyState&&s.abort(),z(t,[],function(i){I(t);for(var n=0,s=(i=U(t,i)).length;n<s;n++)y(t,i[n]);L(t,e),ut(t,!1)})}};qt("ajax.json()",function(){var t=this.context;if(0<t.length)return t[0].json}),qt("ajax.params()",function(){var t=this.context;if(0<t.length)return t[0].oAjaxData}),qt("ajax.reload()",function(t,e){return this.iterator("table",function(i){be(i,!1===e,t)})}),qt("ajax.url()",function(e){var i=this.context;return e===n?0===i.length?n:(i=i[0]).ajax?t.isPlainObject(i.ajax)?i.ajax.url:i.ajax:i.sAjaxSource:this.iterator("table",function(i){t.isPlainObject(i.ajax)?i.ajax.url=e:i.ajax=e})}),qt("ajax.url().load()",function(t,e){return this.iterator("table",function(i){be(i,!1===e,t)})});var ye=function(e,i,s,o,a){var r,l,h,c,u,d,p=[];for(h=typeof i,i&&"string"!==h&&"function"!==h&&i.length!==n||(i=[i]),h=0,c=i.length;h<c;h++)for(u=0,d=(l=i[h]&&i[h].split&&!i[h].match(/[\[\(:]/)?i[h].split(","):[i[h]]).length;u<d;u++)(r=s("string"==typeof l[u]?t.trim(l[u]):l[u]))&&r.length&&(p=p.concat(r));if((e=Bt.selector[e]).length)for(h=0,c=e.length;h<c;h++)p=e[h](o,a,p);return le(p)},we=function(e){return e||(e={}),e.filter&&e.search===n&&(e.search=e.filter),t.extend({search:"none",order:"current",page:"all"},e)},xe=function(t){for(var e=0,i=t.length;e<i;e++)if(0<t[e].length)return t[0]=t[e],t[0].length=1,t.length=1,t.context=[t.context[e]],t;return t.length=0,t},Ce=function(e,i){var n,s,o,a=[],r=e.aiDisplay;n=e.aiDisplayMaster;var l=i.search;if(s=i.order,o=i.page,"ssp"==Ft(e))return"removed"===l?[]:ae(0,n.length);if("current"==o)for(n=e._iDisplayStart,s=e.fnDisplayEnd();n<s;n++)a.push(r[n]);else if("current"==s||"applied"==s)a="none"==l?n.slice():"applied"==l?r.slice():t.map(n,function(e){return-1===t.inArray(e,r)?e:null});else if("index"==s||"original"==s)for(n=0,s=e.aoData.length;n<s;n++)"none"==l?a.push(n):(-1===(o=t.inArray(n,r))&&"removed"==l||0<=o&&"applied"==l)&&a.push(n);return a};qt("rows()",function(e,i){e===n?e="":t.isPlainObject(e)&&(i=e,e="");i=we(i);var s=this.iterator("table",function(s){var o,a=i;return ye("row",e,function(e){var i=te(e);if(null!==i&&!a)return[i];if(o||(o=Ce(s,a)),null!==i&&-1!==t.inArray(i,o))return[i];if(null===e||e===n||""===e)return o;if("function"==typeof e)return t.map(o,function(t){var i=s.aoData[t];return e(t,i._aData,i.nTr)?t:null});if(i=re(oe(s.aoData,o,"nTr")),e.nodeName)return e._DT_RowIndex!==n?[e._DT_RowIndex]:e._DT_CellIndex?[e._DT_CellIndex.row]:(i=t(e).closest("*[data-dt-row]")).length?[i.data("dt-row")]:[];if("string"==typeof e&&"#"===e.charAt(0)){var r=s.aIds[e.replace(/^#/,"")];if(r!==n)return[r.idx]}return t(i).filter(e).map(function(){return this._DT_RowIndex}).toArray()},s,a)},1);return s.selector.rows=e,s.selector.opts=i,s}),qt("rows().nodes()",function(){return this.iterator("row",function(t,e){return t.aoData[e].nTr||n},1)}),qt("rows().data()",function(){return this.iterator(!0,"rows",function(t,e){return oe(t.aoData,e,"_aData")},1)}),Ut("rows().cache()","row().cache()",function(t){return this.iterator("row",function(e,i){var n=e.aoData[i];return"search"===t?n._aFilterData:n._aSortData},1)}),Ut("rows().invalidate()","row().invalidate()",function(t){return this.iterator("row",function(e,i){A(e,i,t)})}),Ut("rows().indexes()","row().index()",function(){return this.iterator("row",function(t,e){return e},1)}),Ut("rows().ids()","row().id()",function(t){for(var e=[],i=this.context,n=0,s=i.length;n<s;n++)for(var o=0,a=this[n].length;o<a;o++){var r=i[n].rowIdFn(i[n].aoData[this[n][o]]._aData);e.push((!0===t?"#":"")+r)}return new $t(i,e)}),Ut("rows().remove()","row().remove()",function(){var t=this;return this.iterator("row",function(e,i,s){var o,a,r,l,h,c=e.aoData,u=c[i];for(c.splice(i,1),o=0,a=c.length;o<a;o++)if(h=(r=c[o]).anCells,null!==r.nTr&&(r.nTr._DT_RowIndex=o),null!==h)for(r=0,l=h.length;r<l;r++)h[r]._DT_CellIndex.row=o;E(e.aiDisplayMaster,i),E(e.aiDisplay,i),E(t[s],i,!1),0<e._iRecordsDisplay&&e._iRecordsDisplay--,Rt(e),(i=e.rowIdFn(u._aData))!==n&&delete e.aIds[i]}),this.iterator("table",function(t){for(var e=0,i=t.aoData.length;e<i;e++)t.aoData[e].idx=e}),this}),qt("rows.add()",function(e){var i=this.iterator("table",function(t){var i,n,s,o=[];for(n=0,s=e.length;n<s;n++)(i=e[n]).nodeName&&"TR"===i.nodeName.toUpperCase()?o.push(w(t,i)[0]):o.push(y(t,i));return o},1),n=this.rows(-1);return n.pop(),t.merge(n,i),n}),qt("row()",function(t,e){return xe(this.rows(t,e))}),qt("row().data()",function(t){var e=this.context;return t===n?e.length&&this.length?e[0].aoData[this[0]]._aData:n:(e[0].aoData[this[0]]._aData=t,A(e[0],this[0],"data"),this)}),qt("row().node()",function(){var t=this.context;return t.length&&this.length&&t[0].aoData[this[0]].nTr||null}),qt("row.add()",function(e){e instanceof t&&e.length&&(e=e[0]);var i=this.iterator("table",function(t){return e.nodeName&&"TR"===e.nodeName.toUpperCase()?w(t,e)[0]:y(t,e)});return this.row(i[0])});var De=function(t,e){var i=t.context;i.length&&(i=i[0].aoData[e!==n?e:t[0]])&&i._details&&(i._details.remove(),i._detailsShow=n,i._details=n)},Te=function(t,e){var i=t.context;if(i.length&&t.length){var n=i[0].aoData[t[0]];if(n._details){(n._detailsShow=e)?n._details.insertAfter(n.nTr):n._details.detach();var s=i[0],o=new $t(s),a=s.aoData;o.off("draw.dt.DT_details column-visibility.dt.DT_details destroy.dt.DT_details"),0<se(a,"_details").length&&(o.on("draw.dt.DT_details",function(t,e){s===e&&o.rows({page:"current"}).eq(0).each(function(t){(t=a[t])._detailsShow&&t._details.insertAfter(t.nTr)})}),o.on("column-visibility.dt.DT_details",function(t,e){if(s===e)for(var i,n=m(e),o=0,r=a.length;o<r;o++)(i=a[o])._details&&i._details.children("td[colspan]").attr("colspan",n)}),o.on("destroy.dt.DT_details",function(t,e){if(s===e)for(var i=0,n=a.length;i<n;i++)a[i]._details&&De(o,i)}))}}};qt("row().child()",function(e,i){var s=this.context;if(e===n)return s.length&&this.length?s[0].aoData[this[0]]._details:n;if(!0===e)this.child.show();else if(!1===e)De(this);else if(s.length&&this.length){var o=s[0],a=(s=s[0].aoData[this[0]],[]),r=function(e,i){if(t.isArray(e)||e instanceof t)for(var n=0,s=e.length;n<s;n++)r(e[n],i);else e.nodeName&&"tr"===e.nodeName.toLowerCase()?a.push(e):(n=t("<tr><td/></tr>").addClass(i),t("td",n).addClass(i).html(e)[0].colSpan=m(o),a.push(n[0]))};r(e,i),s._details&&s._details.detach(),s._details=t(a),s._detailsShow&&s._details.insertAfter(s.nTr)}return this}),qt(["row().child.show()","row().child().show()"],function(){return Te(this,!0),this}),qt(["row().child.hide()","row().child().hide()"],function(){return Te(this,!1),this}),qt(["row().child.remove()","row().child().remove()"],function(){return De(this),this}),qt("row().child.isShown()",function(){var t=this.context;return t.length&&this.length&&t[0].aoData[this[0]]._detailsShow||!1});var Se=/^([^:]+):(name|visIdx|visible)$/,ke=function(t,e,i,n,s){i=[],n=0;for(var o=s.length;n<o;n++)i.push(x(t,s[n],e));return i};qt("columns()",function(e,i){e===n?e="":t.isPlainObject(e)&&(i=e,e="");i=we(i);var s=this.iterator("table",function(n){var s=e,o=i,a=n.aoColumns,r=se(a,"sName"),l=se(a,"nTh");return ye("column",s,function(e){var i=te(e);if(""===e)return ae(a.length);if(null!==i)return[i>=0?i:a.length+i];if("function"==typeof e){var s=Ce(n,o);return t.map(a,function(t,i){return e(i,ke(n,i,0,0,s),l[i])?i:null})}var h="string"==typeof e?e.match(Se):"";if(h)switch(h[2]){case"visIdx":case"visible":if((i=parseInt(h[1],10))<0){var c=t.map(a,function(t,e){return t.bVisible?e:null});return[c[c.length+i]]}return[f(n,i)];case"name":return t.map(r,function(t,e){return t===h[1]?e:null});default:return[]}return e.nodeName&&e._DT_CellIndex?[e._DT_CellIndex.column]:(i=t(l).filter(e).map(function(){return t.inArray(this,l)}).toArray()).length||!e.nodeName?i:(i=t(e).closest("*[data-dt-column]")).length?[i.data("dt-column")]:[]},n,o)},1);return s.selector.cols=e,s.selector.opts=i,s}),Ut("columns().header()","column().header()",function(){return this.iterator("column",function(t,e){return t.aoColumns[e].nTh},1)}),Ut("columns().footer()","column().footer()",function(){return this.iterator("column",function(t,e){return t.aoColumns[e].nTf},1)}),Ut("columns().data()","column().data()",function(){return this.iterator("column-rows",ke,1)}),Ut("columns().dataSrc()","column().dataSrc()",function(){return this.iterator("column",function(t,e){return t.aoColumns[e].mData},1)}),Ut("columns().cache()","column().cache()",function(t){return this.iterator("column-rows",function(e,i,n,s,o){return oe(e.aoData,o,"search"===t?"_aFilterData":"_aSortData",i)},1)}),Ut("columns().nodes()","column().nodes()",function(){return this.iterator("column-rows",function(t,e,i,n,s){return oe(t.aoData,s,"anCells",e)},1)}),Ut("columns().visible()","column().visible()",function(e,i){var s=this.iterator("column",function(i,s){if(e===n)return i.aoColumns[s].bVisible;var o,a,r,l=i.aoColumns,h=l[s],c=i.aoData;if(e!==n&&h.bVisible!==e){if(e){var u=t.inArray(!0,se(l,"bVisible"),s+1);for(o=0,a=c.length;o<a;o++)r=c[o].nTr,l=c[o].anCells,r&&r.insertBefore(l[s],l[u]||null)}else t(se(i.aoData,"anCells",s)).detach();h.bVisible=e,H(i,i.aoHeader),H(i,i.aoFooter),kt(i)}});return e!==n&&(this.iterator("column",function(t,n){Ht(t,null,"column-visibility",[t,n,e,i])}),(i===n||i)&&this.columns.adjust()),s}),Ut("columns().indexes()","column().index()",function(t){return this.iterator("column",function(e,i){return"visible"===t?g(e,i):i},1)}),qt("columns.adjust()",function(){return this.iterator("table",function(t){p(t)},1)}),qt("column.index()",function(t,e){if(0!==this.context.length){var i=this.context[0];if("fromVisible"===t||"toData"===t)return f(i,e);if("fromData"===t||"toVisible"===t)return g(i,e)}}),qt("column()",function(t,e){return xe(this.columns(t,e))}),qt("cells()",function(e,i,s){if(t.isPlainObject(e)&&(e.row===n?(s=e,e=null):(s=i,i=null)),t.isPlainObject(i)&&(s=i,i=null),null===i||i===n)return this.iterator("table",function(i){var o,a,r,l,h,c,u,d=e,p=we(s),f=i.aoData,g=Ce(i,p),m=re(oe(f,g,"anCells")),v=t([].concat.apply([],m)),_=i.aoColumns.length;return ye("cell",d,function(e){var s="function"==typeof e;if(null===e||e===n||s){for(a=[],r=0,l=g.length;r<l;r++)for(o=g[r],h=0;h<_;h++)c={row:o,column:h},s?(u=f[o],e(c,x(i,o,h),u.anCells?u.anCells[h]:null)&&a.push(c)):a.push(c);return a}return t.isPlainObject(e)?[e]:(s=v.filter(e).map(function(t,e){return{row:e._DT_CellIndex.row,column:e._DT_CellIndex.column}}).toArray()).length||!e.nodeName?s:(u=t(e).closest("*[data-dt-row]")).length?[{row:u.data("dt-row"),column:u.data("dt-column")}]:[]},i,p)});var o,a,r,l,h,c=this.columns(i,s),u=this.rows(e,s),d=this.iterator("table",function(t,e){for(o=[],a=0,r=u[e].length;a<r;a++)for(l=0,h=c[e].length;l<h;l++)o.push({row:u[e][a],column:c[e][l]});return o},1);return t.extend(d.selector,{cols:i,rows:e,opts:s}),d}),Ut("cells().nodes()","cell().node()",function(){return this.iterator("cell",function(t,e,i){return(t=t.aoData[e])&&t.anCells?t.anCells[i]:n},1)}),qt("cells().data()",function(){return this.iterator("cell",function(t,e,i){return x(t,e,i)},1)}),Ut("cells().cache()","cell().cache()",function(t){return t="search"===t?"_aFilterData":"_aSortData",this.iterator("cell",function(e,i,n){return e.aoData[i][t][n]},1)}),Ut("cells().render()","cell().render()",function(t){return this.iterator("cell",function(e,i,n){return x(e,i,n,t)},1)}),Ut("cells().indexes()","cell().index()",function(){return this.iterator("cell",function(t,e,i){return{row:e,column:i,columnVisible:g(t,i)}},1)}),Ut("cells().invalidate()","cell().invalidate()",function(t){return this.iterator("cell",function(e,i,n){A(e,i,t,n)})}),qt("cell()",function(t,e,i){return xe(this.cells(t,e,i))}),qt("cell().data()",function(t){var e=this.context,i=this[0];return t===n?e.length&&i.length?x(e[0],i[0].row,i[0].column):n:(C(e[0],i[0].row,i[0].column,t),A(e[0],i[0].row,"data",i[0].column),this)}),qt("order()",function(e,i){var s=this.context;return e===n?0!==s.length?s[0].aaSorting:n:("number"==typeof e?e=[[e,i]]:e.length&&!t.isArray(e[0])&&(e=Array.prototype.slice.call(arguments)),this.iterator("table",function(t){t.aaSorting=e.slice()}))}),qt("order.listener()",function(t,e,i){return this.iterator("table",function(n){Dt(n,t,e,i)})}),qt("order.fixed()",function(e){if(!e){var i=(i=this.context).length?i[0].aaSortingFixed:n;return t.isArray(i)?{pre:i}:i}return this.iterator("table",function(i){i.aaSortingFixed=t.extend(!0,{},e)})}),qt(["columns().order()","column().order()"],function(e){var i=this;return this.iterator("table",function(n,s){var o=[];t.each(i[s],function(t,i){o.push([i,e])}),n.aaSorting=o})}),qt("search()",function(e,i,s,o){var a=this.context;return e===n?0!==a.length?a[0].oPreviousSearch.sSearch:n:this.iterator("table",function(n){n.oFeatures.bFilter&&V(n,t.extend({},n.oPreviousSearch,{sSearch:e+"",bRegex:null!==i&&i,bSmart:null===s||s,bCaseInsensitive:null===o||o}),1)})}),Ut("columns().search()","column().search()",function(e,i,s,o){return this.iterator("column",function(a,r){var l=a.aoPreSearchCols;if(e===n)return l[r].sSearch;a.oFeatures.bFilter&&(t.extend(l[r],{sSearch:e+"",bRegex:null!==i&&i,bSmart:null===s||s,bCaseInsensitive:null===o||o}),V(a,a.oPreviousSearch,1))})}),qt("state()",function(){return this.context.length?this.context[0].oSavedState:null}),qt("state.clear()",function(){return this.iterator("table",function(t){t.fnStateSaveCallback.call(t.oInstance,t,{})})}),qt("state.loaded()",function(){return this.context.length?this.context[0].oLoadedState:null}),qt("state.save()",function(){return this.iterator("table",function(t){kt(t)})}),Yt.versionCheck=Yt.fnVersionCheck=function(t){for(var e,i,n=Yt.version.split("."),s=0,o=(t=t.split(".")).length;s<o;s++)if((e=parseInt(n[s],10)||0)!==(i=parseInt(t[s],10)||0))return e>i;return!0},Yt.isDataTable=Yt.fnIsDataTable=function(e){var i=t(e).get(0),n=!1;return e instanceof Yt.Api||(t.each(Yt.settings,function(e,s){var o=s.nScrollHead?t("table",s.nScrollHead)[0]:null,a=s.nScrollFoot?t("table",s.nScrollFoot)[0]:null;s.nTable!==i&&o!==i&&a!==i||(n=!0)}),n)},Yt.tables=Yt.fnTables=function(e){var i=!1;t.isPlainObject(e)&&(i=e.api,e=e.visible);var n=t.map(Yt.settings,function(i){if(!e||e&&t(i.nTable).is(":visible"))return i.nTable});return i?new $t(n):n},Yt.camelToHungarian=o,qt("$()",function(e,i){var n=this.rows(i).nodes();n=t(n);return t([].concat(n.filter(e).toArray(),n.find(e).toArray()))}),t.each(["on","one","off"],function(e,i){qt(i+"()",function(){var e=Array.prototype.slice.call(arguments);e[0]=t.map(e[0].split(/\s/),function(t){return t.match(/\.dt\b/)?t:t+".dt"}).join(" ");var n=t(this.tables().nodes());return n[i].apply(n,e),this})}),qt("clear()",function(){return this.iterator("table",function(t){I(t)})}),qt("settings()",function(){return new $t(this.context,this.context)}),qt("init()",function(){var t=this.context;return t.length?t[0].oInit:null}),qt("data()",function(){return this.iterator("table",function(t){return se(t.aoData,"_aData")}).flatten()}),qt("destroy()",function(i){return i=i||!1,this.iterator("table",function(n){var s,o=n.nTableWrapper.parentNode,a=n.oClasses,r=n.nTable,l=n.nTBody,h=n.nTHead,c=n.nTFoot,u=t(r),d=(l=t(l),t(n.nTableWrapper)),p=t.map(n.aoData,function(t){return t.nTr});n.bDestroying=!0,Ht(n,"aoDestroyCallback","destroy",[n]),i||new $t(n).columns().visible(!0),d.off(".DT").find(":not(tbody *)").off(".DT"),t(e).off(".DT-"+n.sInstance),r!=h.parentNode&&(u.children("thead").detach(),u.append(h)),c&&r!=c.parentNode&&(u.children("tfoot").detach(),u.append(c)),n.aaSorting=[],n.aaSortingFixed=[],Tt(n),t(p).removeClass(n.asStripeClasses.join(" ")),t("th, td",h).removeClass(a.sSortable+" "+a.sSortableAsc+" "+a.sSortableDesc+" "+a.sSortableNone),l.children().detach(),l.append(p),u[h=i?"remove":"detach"](),d[h](),!i&&o&&(o.insertBefore(r,n.nTableReinsertBefore),u.css("width",n.sDestroyWidth).removeClass(a.sTable),(s=n.asDestroyStripes.length)&&l.children().each(function(e){t(this).addClass(n.asDestroyStripes[e%s])})),-1!==(o=t.inArray(n,Yt.settings))&&Yt.settings.splice(o,1)})}),t.each(["column","row","cell"],function(t,e){qt(e+"s().every()",function(t){var i=this.selector.opts,s=this;return this.iterator(e,function(o,a,r,l,h){t.call(s[e](a,"cell"===e?r:i,"cell"===e?i:n),a,r,l,h)})})}),qt("i18n()",function(e,i,s){var o=this.context[0];return(e=T(e)(o.oLanguage))===n&&(e=i),s!==n&&t.isPlainObject(e)&&(e=e[s]!==n?e[s]:e._),e.replace("%d",s)}),Yt.version="1.10.16",Yt.settings=[],Yt.models={},Yt.models.oSearch={bCaseInsensitive:!0,sSearch:"",bRegex:!1,bSmart:!0},Yt.models.oRow={nTr:null,anCells:null,_aData:[],_aSortData:null,_aFilterData:null,_sFilterRow:null,_sRowStripe:"",src:null,idx:-1},Yt.models.oColumn={idx:null,aDataSort:null,asSorting:null,bSearchable:null,bSortable:null,bVisible:null,_sManualType:null,_bAttrSrc:!1,fnCreatedCell:null,fnGetData:null,fnSetData:null,mData:null,mRender:null,nTh:null,nTf:null,sClass:null,sContentPadding:null,sDefaultContent:null,sName:null,sSortDataType:"std",sSortingClass:null,sSortingClassJUI:null,sTitle:null,sType:null,sWidth:null,sWidthOrig:null},Yt.defaults={aaData:null,aaSorting:[[0,"asc"]],aaSortingFixed:[],ajax:null,aLengthMenu:[10,25,50,100],aoColumns:null,aoColumnDefs:null,aoSearchCols:[],asStripeClasses:null,bAutoWidth:!0,bDeferRender:!1,bDestroy:!1,bFilter:!0,bInfo:!0,bLengthChange:!0,bPaginate:!0,bProcessing:!1,bRetrieve:!1,bScrollCollapse:!1,bServerSide:!1,bSort:!0,bSortMulti:!0,bSortCellsTop:!1,bSortClasses:!0,bStateSave:!1,fnCreatedRow:null,fnDrawCallback:null,fnFooterCallback:null,fnFormatNumber:function(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.oLanguage.sThousands)},fnHeaderCallback:null,fnInfoCallback:null,fnInitComplete:null,fnPreDrawCallback:null,fnRowCallback:null,fnServerData:null,fnServerParams:null,fnStateLoadCallback:function(t){try{return JSON.parse((-1===t.iStateDuration?sessionStorage:localStorage).getItem("DataTables_"+t.sInstance+"_"+location.pathname))}catch(t){}},fnStateLoadParams:null,fnStateLoaded:null,fnStateSaveCallback:function(t,e){try{(-1===t.iStateDuration?sessionStorage:localStorage).setItem("DataTables_"+t.sInstance+"_"+location.pathname,JSON.stringify(e))}catch(t){}},fnStateSaveParams:null,iStateDuration:7200,iDeferLoading:null,iDisplayLength:10,iDisplayStart:0,iTabIndex:0,oClasses:{},oLanguage:{oAria:{sSortAscending:": activate to sort column ascending",sSortDescending:": activate to sort column descending"},oPaginate:{sFirst:"First",sLast:"Last",sNext:"Next",sPrevious:"Previous"},sEmptyTable:"No data available in table",sInfo:"Showing _START_ to _END_ of _TOTAL_ entries",sInfoEmpty:"Showing 0 to 0 of 0 entries",sInfoFiltered:"(filtered from _MAX_ total entries)",sInfoPostFix:"",sDecimal:"",sThousands:",",sLengthMenu:"Show _MENU_ entries",sLoadingRecords:"Loading...",sProcessing:"Processing...",sSearch:"Search:",sSearchPlaceholder:"",sUrl:"",sZeroRecords:"No matching records found"},oSearch:t.extend({},Yt.models.oSearch),sAjaxDataProp:"data",sAjaxSource:null,sDom:"lfrtip",searchDelay:null,sPaginationType:"simple_numbers",sScrollX:"",sScrollXInner:"",sScrollY:"",sServerMethod:"GET",renderer:null,rowId:"DT_RowId"},s(Yt.defaults),Yt.defaults.column={aDataSort:null,iDataSort:-1,asSorting:["asc","desc"],bSearchable:!0,bSortable:!0,bVisible:!0,fnCreatedCell:null,mData:null,mRender:null,sCellType:"td",sClass:"",sContentPadding:"",sDefaultContent:null,sName:"",sSortDataType:"std",sTitle:null,sType:null,sWidth:null},s(Yt.defaults.column),Yt.models.oSettings={oFeatures:{bAutoWidth:null,bDeferRender:null,bFilter:null,bInfo:null,bLengthChange:null,bPaginate:null,bProcessing:null,bServerSide:null,bSort:null,bSortMulti:null,bSortClasses:null,bStateSave:null},oScroll:{bCollapse:null,iBarWidth:0,sX:null,sXInner:null,sY:null},oLanguage:{fnInfoCallback:null},oBrowser:{bScrollOversize:!1,bScrollbarLeft:!1,bBounding:!1,barWidth:0},ajax:null,aanFeatures:[],aoData:[],aiDisplay:[],aiDisplayMaster:[],aIds:{},aoColumns:[],aoHeader:[],aoFooter:[],oPreviousSearch:{},aoPreSearchCols:[],aaSorting:null,aaSortingFixed:[],asStripeClasses:null,asDestroyStripes:[],sDestroyWidth:0,aoRowCallback:[],aoHeaderCallback:[],aoFooterCallback:[],aoDrawCallback:[],aoRowCreatedCallback:[],aoPreDrawCallback:[],aoInitComplete:[],aoStateSaveParams:[],aoStateLoadParams:[],aoStateLoaded:[],sTableId:"",nTable:null,nTHead:null,nTFoot:null,nTBody:null,nTableWrapper:null,bDeferLoading:!1,bInitialised:!1,aoOpenRows:[],sDom:null,searchDelay:null,sPaginationType:"two_button",iStateDuration:0,aoStateSave:[],aoStateLoad:[],oSavedState:null,oLoadedState:null,sAjaxSource:null,sAjaxDataProp:null,bAjaxDataGet:!0,jqXHR:null,json:n,oAjaxData:n,fnServerData:null,aoServerParams:[],sServerMethod:null,fnFormatNumber:null,aLengthMenu:null,iDraw:0,bDrawing:!1,iDrawError:-1,_iDisplayLength:10,_iDisplayStart:0,_iRecordsTotal:0,_iRecordsDisplay:0,oClasses:{},bFiltered:!1,bSorted:!1,bSortCellsTop:null,oInit:null,aoDestroyCallback:[],fnRecordsTotal:function(){return"ssp"==Ft(this)?1*this._iRecordsTotal:this.aiDisplayMaster.length},fnRecordsDisplay:function(){return"ssp"==Ft(this)?1*this._iRecordsDisplay:this.aiDisplay.length},fnDisplayEnd:function(){var t=this._iDisplayLength,e=this._iDisplayStart,i=e+t,n=this.aiDisplay.length,s=this.oFeatures,o=s.bPaginate;return s.bServerSide?!1===o||-1===t?e+n:Math.min(e+t,this._iRecordsDisplay):!o||i>n||-1===t?n:i},oInstance:null,sInstance:null,iTabIndex:0,nScrollHead:null,nScrollFoot:null,aLastSort:[],oPlugins:{},rowIdFn:null,rowId:null},Yt.ext=Bt={buttons:{},classes:{},builder:"-source-",errMode:"alert",feature:[],search:[],selector:{cell:[],column:[],row:[]},internal:{},legacy:{ajax:null},pager:{},renderer:{pageButton:{},header:{}},order:{},type:{detect:[],search:{},order:{}},_unique:0,fnVersionCheck:Yt.fnVersionCheck,iApiIndex:0,oJUIClasses:{},sVersion:Yt.version},t.extend(Bt,{afnFiltering:Bt.search,aTypes:Bt.type.detect,ofnSearch:Bt.type.search,oSort:Bt.type.order,afnSortData:Bt.order,aoFeatures:Bt.feature,oApi:Bt.internal,oStdClasses:Bt.classes,oPagination:Bt.pager}),t.extend(Yt.ext.classes,{sTable:"dataTable",sNoFooter:"no-footer",sPageButton:"paginate_button",sPageButtonActive:"current",sPageButtonDisabled:"disabled",sStripeOdd:"odd",sStripeEven:"even",sRowEmpty:"dataTables_empty",sWrapper:"dataTables_wrapper",sFilter:"dataTables_filter",sInfo:"dataTables_info",sPaging:"dataTables_paginate paging_",sLength:"dataTables_length",sProcessing:"dataTables_processing",sSortAsc:"sorting_asc",sSortDesc:"sorting_desc",sSortable:"sorting",sSortableAsc:"sorting_asc_disabled",sSortableDesc:"sorting_desc_disabled",sSortableNone:"sorting_disabled",sSortColumn:"sorting_",sFilterInput:"",sLengthSelect:"",sScrollWrapper:"dataTables_scroll",sScrollHead:"dataTables_scrollHead",sScrollHeadInner:"dataTables_scrollHeadInner",sScrollBody:"dataTables_scrollBody",sScrollFoot:"dataTables_scrollFoot",sScrollFootInner:"dataTables_scrollFootInner",sHeaderTH:"",sFooterTH:"",sSortJUIAsc:"",sSortJUIDesc:"",sSortJUI:"",sSortJUIAscAllowed:"",sSortJUIDescAllowed:"",sSortJUIWrapper:"",sSortIcon:"",sJUIHeader:"",sJUIFooter:""});var Ie=Yt.ext.pager;t.extend(Ie,{simple:function(){return["previous","next"]},full:function(){return["first","previous","next","last"]},numbers:function(t,e){return[jt(t,e)]},simple_numbers:function(t,e){return["previous",jt(t,e),"next"]},full_numbers:function(t,e){return["first","previous",jt(t,e),"next","last"]},first_last_numbers:function(t,e){return["first",jt(t,e),"last"]},_numbers:jt,numbers_length:7}),t.extend(!0,Yt.ext.renderer,{pageButton:{_:function(e,s,o,a,r,l){var h,c,u,d=e.oClasses,p=e.oLanguage.oPaginate,f=e.oLanguage.oAria.paginate||{},g=0,m=function(i,n){var s,a,u,v,_=function(t){ht(e,t.data.action,!0)};for(s=0,a=n.length;s<a;s++)if(v=n[s],t.isArray(v))u=t("<"+(v.DT_el||"div")+"/>").appendTo(i),m(u,v);else{switch(h=null,c="",v){case"ellipsis":i.append('<span class="ellipsis">&#x2026;</span>');break;case"first":h=p.sFirst,c=v+(r>0?"":" "+d.sPageButtonDisabled);break;case"previous":h=p.sPrevious,c=v+(r>0?"":" "+d.sPageButtonDisabled);break;case"next":h=p.sNext,c=v+(r<l-1?"":" "+d.sPageButtonDisabled);break;case"last":h=p.sLast,c=v+(r<l-1?"":" "+d.sPageButtonDisabled);break;default:h=v+1,c=r===v?d.sPageButtonActive:""}null!==h&&(Ot(u=t("<a>",{class:d.sPageButton+" "+c,"aria-controls":e.sTableId,"aria-label":f[v],"data-dt-idx":g,tabindex:e.iTabIndex,id:0===o&&"string"==typeof v?e.sTableId+"_"+v:null}).html(h).appendTo(i),{action:v},_),g++)}};try{u=t(s).find(i.activeElement).data("dt-idx")}catch(t){}m(t(s).empty(),a),u!==n&&t(s).find("[data-dt-idx="+u+"]").focus()}}}),t.extend(Yt.ext.type.detect,[function(t,e){var i=e.oLanguage.sDecimal;return ie(t,i)?"num"+i:null},function(t){if(t&&!(t instanceof Date)&&!Gt.test(t))return null;var e=Date.parse(t);return null!==e&&!isNaN(e)||Qt(t)?"date":null},function(t,e){var i=e.oLanguage.sDecimal;return ie(t,i,!0)?"num-fmt"+i:null},function(t,e){var i=e.oLanguage.sDecimal;return ne(t,i)?"html-num"+i:null},function(t,e){var i=e.oLanguage.sDecimal;return ne(t,i,!0)?"html-num-fmt"+i:null},function(t){return Qt(t)||"string"==typeof t&&-1!==t.indexOf("<")?"html":null}]),t.extend(Yt.ext.type.search,{html:function(t){return Qt(t)?t:"string"==typeof t?t.replace(Kt," ").replace(Xt,""):""},string:function(t){return Qt(t)?t:"string"==typeof t?t.replace(Kt," "):t}});var Ee=function(t,e,i,n){return 0===t||t&&"-"!==t?(e&&(t=ee(t,e)),t.replace&&(i&&(t=t.replace(i,"")),n&&(t=t.replace(n,""))),1*t):-1/0};t.extend(Bt.type.order,{"date-pre":function(t){return Date.parse(t)||-1/0},"html-pre":function(t){return Qt(t)?"":t.replace?t.replace(/<.*?>/g,"").toLowerCase():t+""},"string-pre":function(t){return Qt(t)?"":"string"==typeof t?t.toLowerCase():t.toString?t.toString():""},"string-asc":function(t,e){return t<e?-1:t>e?1:0},"string-desc":function(t,e){return t<e?1:t>e?-1:0}}),Wt(""),t.extend(!0,Yt.ext.renderer,{header:{_:function(e,i,n,s){t(e.nTable).on("order.dt.DT",function(t,o,a,r){e===o&&(t=n.idx,i.removeClass(n.sSortingClass+" "+s.sSortAsc+" "+s.sSortDesc).addClass("asc"==r[t]?s.sSortAsc:"desc"==r[t]?s.sSortDesc:n.sSortingClass))})},jqueryui:function(e,i,n,s){t("<div/>").addClass(s.sSortJUIWrapper).append(i.contents()).append(t("<span/>").addClass(s.sSortIcon+" "+n.sSortingClassJUI)).appendTo(i),t(e.nTable).on("order.dt.DT",function(t,o,a,r){e===o&&(t=n.idx,i.removeClass(s.sSortAsc+" "+s.sSortDesc).addClass("asc"==r[t]?s.sSortAsc:"desc"==r[t]?s.sSortDesc:n.sSortingClass),i.find("span."+s.sSortIcon).removeClass(s.sSortJUIAsc+" "+s.sSortJUIDesc+" "+s.sSortJUI+" "+s.sSortJUIAscAllowed+" "+s.sSortJUIDescAllowed).addClass("asc"==r[t]?s.sSortJUIAsc:"desc"==r[t]?s.sSortJUIDesc:n.sSortingClassJUI))})}}});var Ae=function(t){return"string"==typeof t?t.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"):t};return Yt.render={number:function(t,e,i,n,s){return{display:function(o){if("number"!=typeof o&&"string"!=typeof o)return o;var a=0>o?"-":"",r=parseFloat(o);return isNaN(r)?Ae(o):(r=r.toFixed(i),o=Math.abs(r),r=parseInt(o,10),o=i?e+(o-r).toFixed(i).substring(2):"",a+(n||"")+r.toString().replace(/\B(?=(\d{3})+(?!\d))/g,t)+o+(s||""))}}},text:function(){return{display:Ae}}},t.extend(Yt.ext.internal,{_fnExternApiFunc:zt,_fnBuildAjax:z,_fnAjaxUpdate:B,_fnAjaxParameters:$,_fnAjaxUpdateDraw:q,_fnAjaxDataSrc:U,_fnAddColumn:u,_fnColumnOptions:d,_fnAdjustColumnSizing:p,_fnVisibleToColumnIndex:f,_fnColumnIndexToVisible:g,_fnVisbleColumns:m,_fnGetColumns:v,_fnColumnTypes:_,_fnApplyColumnDefs:b,_fnHungarianMap:s,_fnCamelToHungarian:o,_fnLanguageCompat:a,_fnBrowserDetect:h,_fnAddData:y,_fnAddTr:w,_fnNodeToDataIndex:function(t,e){return e._DT_RowIndex!==n?e._DT_RowIndex:null},_fnNodeToColumnIndex:function(e,i,n){return t.inArray(n,e.aoData[i].anCells)},_fnGetCellData:x,_fnSetCellData:C,_fnSplitObjNotation:D,_fnGetObjectDataFn:T,_fnSetObjectDataFn:S,_fnGetDataMaster:k,_fnClearTable:I,_fnDeleteIndex:E,_fnInvalidate:A,_fnGetRowElements:P,_fnCreateTr:M,_fnBuildHead:N,_fnDrawHead:H,_fnDraw:R,_fnReDraw:L,_fnAddOptionsHtml:F,_fnDetectHeader:j,_fnGetUniqueThs:W,_fnFeatureHtmlFilter:Y,_fnFilterComplete:V,_fnFilterCustom:K,_fnFilterColumn:X,_fnFilter:G,_fnFilterCreateSearch:J,_fnEscapeRegex:de,_fnFilterData:Z,_fnFeatureHtmlInfo:et,_fnUpdateInfo:it,_fnInfoMacros:nt,_fnInitialise:st,_fnInitComplete:ot,_fnLengthChange:at,_fnFeatureHtmlLength:rt,_fnFeatureHtmlPaginate:lt,_fnPageChange:ht,_fnFeatureHtmlProcessing:ct,_fnProcessingDisplay:ut,_fnFeatureHtmlTable:dt,_fnScrollDraw:pt,_fnApplyToChildren:ft,_fnCalculateColumnWidths:gt,_fnThrottle:me,_fnConvertToWidth:mt,_fnGetWidestNode:vt,_fnGetMaxLenString:_t,_fnStringToCss:bt,_fnSortFlatten:yt,_fnSort:wt,_fnSortAria:xt,_fnSortListener:Ct,_fnSortAttachListener:Dt,_fnSortingClasses:Tt,_fnSortData:St,_fnSaveState:kt,_fnLoadState:It,_fnSettingsFromNode:Et,_fnLog:At,_fnMap:Pt,_fnBindAction:Ot,_fnCallbackReg:Nt,_fnCallbackFire:Ht,_fnLengthOverflow:Rt,_fnRenderer:Lt,_fnDataSource:Ft,_fnRowAttributes:O,_fnCalculateEnd:function(){}}),t.fn.dataTable=Yt,Yt.$=t,t.fn.dataTableSettings=Yt.settings,t.fn.dataTableExt=Yt.ext,t.fn.DataTable=function(e){return t(this).dataTable(e).api()},t.each(Yt,function(e,i){t.fn.DataTable[e]=i}),t.fn.dataTable}),function(){var t,e,i,n,s=function(t,e){return function(){return t.apply(e,arguments)}},o={}.hasOwnProperty;(n=function(){function t(){this.options_index=0,this.parsed=[]}return t.prototype.add_node=function(t){return"OPTGROUP"===t.nodeName.toUpperCase()?this.add_group(t):this.add_option(t)},t.prototype.add_group=function(t){var e,i,n,s,o,a;for(e=this.parsed.length,this.parsed.push({array_index:e,group:!0,label:t.label,title:t.title?t.title:void 0,children:0,disabled:t.disabled,classes:t.className}),a=[],i=0,n=(o=t.childNodes).length;i<n;i++)s=o[i],a.push(this.add_option(s,e,t.disabled));return a},t.prototype.add_option=function(t,e,i){if("OPTION"===t.nodeName.toUpperCase())return""!==t.text?(null!=e&&(this.parsed[e].children+=1),this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,value:t.value,text:t.text,html:t.innerHTML,title:t.title?t.title:void 0,selected:t.selected,disabled:!0===i?i:t.disabled,group_array_index:e,group_label:null!=e?this.parsed[e].label:null,classes:t.className,style:t.style.cssText})):this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,empty:!0}),this.options_index+=1},t}()).select_to_array=function(t){var e,i,s,o,a;for(o=new n,i=0,s=(a=t.childNodes).length;i<s;i++)e=a[i],o.add_node(e);return o.parsed},e=function(){function t(e,i){this.form_field=e,this.options=null!=i?i:{},this.label_click_handler=s(this.label_click_handler,this),t.browser_is_supported()&&(this.is_multiple=this.form_field.multiple,this.set_default_text(),this.set_default_values(),this.setup(),this.set_up_html(),this.register_observers(),this.on_ready())}return t.prototype.set_default_values=function(){return this.click_test_action=(t=this,function(e){return t.test_active_click(e)}),this.activate_action=function(t){return function(e){return t.activate_field(e)}}(this),this.active_field=!1,this.mouse_on_container=!1,this.results_showing=!1,this.result_highlighted=null,this.is_rtl=this.options.rtl||/\bchosen-rtl\b/.test(this.form_field.className),this.allow_single_deselect=null!=this.options.allow_single_deselect&&null!=this.form_field.options[0]&&""===this.form_field.options[0].text&&this.options.allow_single_deselect,this.disable_search_threshold=this.options.disable_search_threshold||0,this.disable_search=this.options.disable_search||!1,this.enable_split_word_search=null==this.options.enable_split_word_search||this.options.enable_split_word_search,this.group_search=null==this.options.group_search||this.options.group_search,this.search_contains=this.options.search_contains||!1,this.single_backstroke_delete=null==this.options.single_backstroke_delete||this.options.single_backstroke_delete,this.max_selected_options=this.options.max_selected_options||1/0,this.inherit_select_classes=this.options.inherit_select_classes||!1,this.display_selected_options=null==this.options.display_selected_options||this.options.display_selected_options,this.display_disabled_options=null==this.options.display_disabled_options||this.options.display_disabled_options,this.include_group_label_in_selected=this.options.include_group_label_in_selected||!1,this.max_shown_results=this.options.max_shown_results||Number.POSITIVE_INFINITY,this.case_sensitive_search=this.options.case_sensitive_search||!1,this.hide_results_on_select=null==this.options.hide_results_on_select||this.options.hide_results_on_select;var t},t.prototype.set_default_text=function(){return this.form_field.getAttribute("data-placeholder")?this.default_text=this.form_field.getAttribute("data-placeholder"):this.is_multiple?this.default_text=this.options.placeholder_text_multiple||this.options.placeholder_text||t.default_multiple_text:this.default_text=this.options.placeholder_text_single||this.options.placeholder_text||t.default_single_text,this.default_text=this.escape_html(this.default_text),this.results_none_found=this.form_field.getAttribute("data-no_results_text")||this.options.no_results_text||t.default_no_result_text},t.prototype.choice_label=function(t){return this.include_group_label_in_selected&&null!=t.group_label?"<b class='group-name'>"+t.group_label+"</b>"+t.html:t.html},t.prototype.mouse_enter=function(){return this.mouse_on_container=!0},t.prototype.mouse_leave=function(){return this.mouse_on_container=!1},t.prototype.input_focus=function(t){if(this.is_multiple){if(!this.active_field)return setTimeout(function(t){return function(){return t.container_mousedown()}}(this),50)}else if(!this.active_field)return this.activate_field()},t.prototype.input_blur=function(t){if(!this.mouse_on_container)return this.active_field=!1,setTimeout(function(t){return function(){return t.blur_test()}}(this),100)},t.prototype.label_click_handler=function(t){return this.is_multiple?this.container_mousedown(t):this.activate_field()},t.prototype.results_option_build=function(t){var e,i,n,s,o,a,r;for(e="",r=0,s=0,o=(a=this.results_data).length;s<o&&("",""!==(n=(i=a[s]).group?this.result_add_group(i):this.result_add_option(i))&&(r++,e+=n),(null!=t?t.first:void 0)&&(i.selected&&this.is_multiple?this.choice_build(i):i.selected&&!this.is_multiple&&this.single_set_selected_text(this.choice_label(i))),!(r>=this.max_shown_results));s++);return e},t.prototype.result_add_option=function(t){var e,i;return t.search_match&&this.include_option_in_results(t)?(e=[],t.disabled||t.selected&&this.is_multiple||e.push("active-result"),!t.disabled||t.selected&&this.is_multiple||e.push("disabled-result"),t.selected&&e.push("result-selected"),null!=t.group_array_index&&e.push("group-option"),""!==t.classes&&e.push(t.classes),(i=document.createElement("li")).className=e.join(" "),i.style.cssText=t.style,i.setAttribute("data-option-array-index",t.array_index),i.innerHTML=t.highlighted_html||t.html,t.title&&(i.title=t.title),this.outerHTML(i)):""},t.prototype.result_add_group=function(t){var e,i;return(t.search_match||t.group_match)&&t.active_options>0?((e=[]).push("group-result"),t.classes&&e.push(t.classes),(i=document.createElement("li")).className=e.join(" "),i.innerHTML=t.highlighted_html||this.escape_html(t.label),t.title&&(i.title=t.title),this.outerHTML(i)):""},t.prototype.results_update_field=function(){if(this.set_default_text(),this.is_multiple||this.results_reset_cleanup(),this.result_clear_highlight(),this.results_build(),this.results_showing)return this.winnow_results()},t.prototype.reset_single_select_options=function(){var t,e,i,n,s;for(s=[],t=0,e=(i=this.results_data).length;t<e;t++)(n=i[t]).selected?s.push(n.selected=!1):s.push(void 0);return s},t.prototype.results_toggle=function(){return this.results_showing?this.results_hide():this.results_show()},t.prototype.results_search=function(t){return this.results_showing?this.winnow_results():this.results_show()},t.prototype.winnow_results=function(){var t,e,i,n,s,o,a,r,l,h,c,u,d,p,f;for(this.no_results_clear(),h=0,t=(a=this.get_search_text()).replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),l=this.get_search_regex(t),i=0,n=(r=this.results_data).length;i<n;i++)(s=r[i]).search_match=!1,c=null,u=null,s.highlighted_html="",this.include_option_in_results(s)&&(s.group&&(s.group_match=!1,s.active_options=0),null!=s.group_array_index&&this.results_data[s.group_array_index]&&(0===(c=this.results_data[s.group_array_index]).active_options&&c.search_match&&(h+=1),c.active_options+=1),f=s.group?s.label:s.text,s.group&&!this.group_search||(u=this.search_string_match(f,l),s.search_match=null!=u,s.search_match&&!s.group&&(h+=1),s.search_match?(a.length&&(d=u.index,o=f.slice(0,d),e=f.slice(d,d+a.length),p=f.slice(d+a.length),s.highlighted_html=this.escape_html(o)+"<em>"+this.escape_html(e)+"</em>"+this.escape_html(p)),null!=c&&(c.group_match=!0)):null!=s.group_array_index&&this.results_data[s.group_array_index].search_match&&(s.search_match=!0)));return this.result_clear_highlight(),h<1&&a.length?(this.update_results_content(""),this.no_results(a)):(this.update_results_content(this.results_option_build()),this.winnow_results_set_highlight())},t.prototype.get_search_regex=function(t){var e,i;return i=this.search_contains?t:"(^|\\s|\\b)"+t+"[^\\s]*",this.enable_split_word_search||this.search_contains||(i="^"+i),e=this.case_sensitive_search?"":"i",new RegExp(i,e)},t.prototype.search_string_match=function(t,e){var i;return i=e.exec(t),!this.search_contains&&(null!=i?i[1]:void 0)&&(i.index+=1),i},t.prototype.choices_count=function(){var t,e,i;if(null!=this.selected_option_count)return this.selected_option_count;for(this.selected_option_count=0,t=0,e=(i=this.form_field.options).length;t<e;t++)i[t].selected&&(this.selected_option_count+=1);return this.selected_option_count},t.prototype.choices_click=function(t){if(t.preventDefault(),this.activate_field(),!this.results_showing&&!this.is_disabled)return this.results_show()},t.prototype.keydown_checker=function(t){var e,i;switch(i=null!=(e=t.which)?e:t.keyCode,this.search_field_scale(),8!==i&&this.pending_backstroke&&this.clear_backstroke(),i){case 8:this.backstroke_length=this.get_search_field_value().length;break;case 9:this.results_showing&&!this.is_multiple&&this.result_select(t),this.mouse_on_container=!1;break;case 13:case 27:this.results_showing&&t.preventDefault();break;case 32:this.disable_search&&t.preventDefault();break;case 38:t.preventDefault(),this.keyup_arrow();break;case 40:t.preventDefault(),this.keydown_arrow()}},t.prototype.keyup_checker=function(t){var e,i;switch(i=null!=(e=t.which)?e:t.keyCode,this.search_field_scale(),i){case 8:this.is_multiple&&this.backstroke_length<1&&this.choices_count()>0?this.keydown_backstroke():this.pending_backstroke||(this.result_clear_highlight(),this.results_search());break;case 13:t.preventDefault(),this.results_showing&&this.result_select(t);break;case 27:this.results_showing&&this.results_hide();break;case 9:case 16:case 17:case 18:case 38:case 40:case 91:break;default:this.results_search()}},t.prototype.clipboard_event_checker=function(t){if(!this.is_disabled)return setTimeout(function(t){return function(){return t.results_search()}}(this),50)},t.prototype.container_width=function(){return null!=this.options.width?this.options.width:this.form_field.offsetWidth+"px"},t.prototype.include_option_in_results=function(t){return!(this.is_multiple&&!this.display_selected_options&&t.selected||!this.display_disabled_options&&t.disabled||t.empty)},t.prototype.search_results_touchstart=function(t){return this.touch_started=!0,this.search_results_mouseover(t)},t.prototype.search_results_touchmove=function(t){return this.touch_started=!1,this.search_results_mouseout(t)},t.prototype.search_results_touchend=function(t){if(this.touch_started)return this.search_results_mouseup(t)},t.prototype.outerHTML=function(t){var e;return t.outerHTML?t.outerHTML:((e=document.createElement("div")).appendChild(t),e.innerHTML)},t.prototype.get_single_html=function(){return'<a class="chosen-single chosen-default">\n  <input class="chosen-search-input" type="text" autocomplete="off" />\n  <span>'+this.default_text+'</span>\n  <div><b></b></div>\n</a>\n<div class="chosen-drop">\n  <div class="chosen-search">\n  </div>\n  <ul class="chosen-results"></ul>\n</div>'},t.prototype.get_multi_html=function(){return'<ul class="chosen-choices">\n  <li class="search-field">\n    <input class="chosen-search-input" type="text" autocomplete="off" value="'+this.default_text+'" />\n  </li>\n</ul>\n<div class="chosen-drop">\n  <ul class="chosen-results"></ul>\n</div>'},t.prototype.get_no_results_html=function(t){return'<li class="no-results">\n  '+this.results_none_found+" <span>"+this.escape_html(t)+"</span>\n</li>"},t.browser_is_supported=function(){return"Microsoft Internet Explorer"===window.navigator.appName?document.documentMode>=8:!(/iP(od|hone)/i.test(window.navigator.userAgent)||/IEMobile/i.test(window.navigator.userAgent)||/Windows Phone/i.test(window.navigator.userAgent)||/BlackBerry/i.test(window.navigator.userAgent)||/BB10/i.test(window.navigator.userAgent)||/Android.*Mobile/i.test(window.navigator.userAgent))},t.default_multiple_text="Select Some Options",t.default_single_text="Select an Option",t.default_no_result_text="No results match",t}(),(t=jQuery).fn.extend({chosen:function(n){return e.browser_is_supported()?this.each(function(e){var s,o;o=(s=t(this)).data("chosen"),"destroy"!==n?o instanceof i||s.data("chosen",new i(this,n)):o instanceof i&&o.destroy()}):this}}),i=function(i){function s(){return s.__super__.constructor.apply(this,arguments)}return function(t,e){function i(){this.constructor=t}for(var n in e)o.call(e,n)&&(t[n]=e[n]);i.prototype=e.prototype,t.prototype=new i,t.__super__=e.prototype}(s,e),s.prototype.setup=function(){return this.form_field_jq=t(this.form_field),this.current_selectedIndex=this.form_field.selectedIndex},s.prototype.set_up_html=function(){var e,i;return(e=["chosen-container"]).push("chosen-container-"+(this.is_multiple?"multi":"single")),this.inherit_select_classes&&this.form_field.className&&e.push(this.form_field.className),this.is_rtl&&e.push("chosen-rtl"),i={class:e.join(" "),title:this.form_field.title},this.form_field.id.length&&(i.id=this.form_field.id.replace(/[^\w]/g,"_")+"_chosen"),this.container=t("<div />",i),this.container.width(this.container_width()),this.is_multiple?this.container.html(this.get_multi_html()):this.container.html(this.get_single_html()),this.form_field_jq.hide().after(this.container),this.dropdown=this.container.find("div.chosen-drop").first(),this.search_field=this.container.find("input").first(),this.search_results=this.container.find("ul.chosen-results").first(),this.search_field_scale(),this.search_no_results=this.container.find("li.no-results").first(),this.is_multiple?(this.search_choices=this.container.find("ul.chosen-choices").first(),this.search_container=this.container.find("li.search-field").first()):(this.search_container=this.container.find("div.chosen-search").first(),this.selected_item=this.container.find(".chosen-single").first()),this.results_build(),this.set_tab_index(),this.set_label_behavior()},s.prototype.on_ready=function(){return this.form_field_jq.trigger("chosen:ready",{chosen:this})},s.prototype.register_observers=function(){return this.container.on("touchstart.chosen",(t=this,function(e){t.container_mousedown(e)})),this.container.on("touchend.chosen",function(t){return function(e){t.container_mouseup(e)}}(this)),this.container.on("mousedown.chosen",function(t){return function(e){t.container_mousedown(e)}}(this)),this.container.on("mouseup.chosen",function(t){return function(e){t.container_mouseup(e)}}(this)),this.container.on("mouseenter.chosen",function(t){return function(e){t.mouse_enter(e)}}(this)),this.container.on("mouseleave.chosen",function(t){return function(e){t.mouse_leave(e)}}(this)),this.search_results.on("mouseup.chosen",function(t){return function(e){t.search_results_mouseup(e)}}(this)),this.search_results.on("mouseover.chosen",function(t){return function(e){t.search_results_mouseover(e)}}(this)),this.search_results.on("mouseout.chosen",function(t){return function(e){t.search_results_mouseout(e)}}(this)),this.search_results.on("mousewheel.chosen DOMMouseScroll.chosen",function(t){return function(e){t.search_results_mousewheel(e)}}(this)),this.search_results.on("touchstart.chosen",function(t){return function(e){t.search_results_touchstart(e)}}(this)),this.search_results.on("touchmove.chosen",function(t){return function(e){t.search_results_touchmove(e)}}(this)),this.search_results.on("touchend.chosen",function(t){return function(e){t.search_results_touchend(e)}}(this)),this.form_field_jq.on("chosen:updated.chosen",function(t){return function(e){t.results_update_field(e)}}(this)),this.form_field_jq.on("chosen:activate.chosen",function(t){return function(e){t.activate_field(e)}}(this)),this.form_field_jq.on("chosen:open.chosen",function(t){return function(e){t.container_mousedown(e)}}(this)),this.form_field_jq.on("chosen:close.chosen",function(t){return function(e){t.close_field(e)}}(this)),this.search_field.on("blur.chosen",function(t){return function(e){t.input_blur(e)}}(this)),this.search_field.on("keyup.chosen",function(t){return function(e){t.keyup_checker(e)}}(this)),this.search_field.on("keydown.chosen",function(t){return function(e){t.keydown_checker(e)}}(this)),this.search_field.on("focus.chosen",function(t){return function(e){t.input_focus(e)}}(this)),this.search_field.on("cut.chosen",function(t){return function(e){t.clipboard_event_checker(e)}}(this)),this.search_field.on("paste.chosen",function(t){return function(e){t.clipboard_event_checker(e)}}(this)),this.is_multiple?this.search_choices.on("click.chosen",function(t){return function(e){t.choices_click(e)}}(this)):this.container.on("click.chosen",function(t){t.preventDefault()});var t},s.prototype.destroy=function(){return t(this.container[0].ownerDocument).off("click.chosen",this.click_test_action),this.form_field_label.length>0&&this.form_field_label.off("click.chosen"),this.search_field[0].tabIndex&&(this.form_field_jq[0].tabIndex=this.search_field[0].tabIndex),this.container.remove(),this.form_field_jq.removeData("chosen"),this.form_field_jq.show()},s.prototype.search_field_disabled=function(){return this.is_disabled=this.form_field.disabled||this.form_field_jq.parents("fieldset").is(":disabled"),this.container.toggleClass("chosen-disabled",this.is_disabled),this.search_field[0].disabled=this.is_disabled,this.is_multiple||this.selected_item.off("focus.chosen",this.activate_field),this.is_disabled?this.close_field():this.is_multiple?void 0:this.selected_item.on("focus.chosen",this.activate_field)},s.prototype.container_mousedown=function(e){var i;if(!this.is_disabled)return!e||"mousedown"!==(i=e.type)&&"touchstart"!==i||this.results_showing||e.preventDefault(),null!=e&&t(e.target).hasClass("search-choice-close")?void 0:(this.active_field?this.is_multiple||!e||t(e.target)[0]!==this.selected_item[0]&&!t(e.target).parents("a.chosen-single").length||(e.preventDefault(),this.results_toggle()):(this.is_multiple&&this.search_field.val(""),t(this.container[0].ownerDocument).on("click.chosen",this.click_test_action),this.results_show()),this.activate_field())},s.prototype.container_mouseup=function(t){if("ABBR"===t.target.nodeName&&!this.is_disabled)return this.results_reset(t)},s.prototype.search_results_mousewheel=function(t){var e;if(t.originalEvent&&(e=t.originalEvent.deltaY||-t.originalEvent.wheelDelta||t.originalEvent.detail),null!=e)return t.preventDefault(),"DOMMouseScroll"===t.type&&(e*=40),this.search_results.scrollTop(e+this.search_results.scrollTop())},s.prototype.blur_test=function(t){if(!this.active_field&&this.container.hasClass("chosen-container-active"))return this.close_field()},s.prototype.close_field=function(){return t(this.container[0].ownerDocument).off("click.chosen",this.click_test_action),this.active_field=!1,this.results_hide(),this.container.removeClass("chosen-container-active"),this.clear_backstroke(),this.show_search_field_default(),this.search_field_scale(),this.search_field.blur()},s.prototype.activate_field=function(){if(!this.is_disabled)return this.container.addClass("chosen-container-active"),this.active_field=!0,this.search_field.val(this.search_field.val()),this.search_field.focus()},s.prototype.test_active_click=function(e){var i;return(i=t(e.target).closest(".chosen-container")).length&&this.container[0]===i[0]?this.active_field=!0:this.close_field()},s.prototype.results_build=function(){return this.parsing=!0,this.selected_option_count=null,this.results_data=n.select_to_array(this.form_field),this.is_multiple?this.search_choices.find("li.search-choice").remove():this.is_multiple||(this.single_set_selected_text(),this.disable_search||this.form_field.options.length<=this.disable_search_threshold?(this.search_field[0].readOnly=!0,this.container.addClass("chosen-container-single-nosearch")):(this.search_field[0].readOnly=!1,this.container.removeClass("chosen-container-single-nosearch"))),this.update_results_content(this.results_option_build({first:!0})),this.search_field_disabled(),this.show_search_field_default(),this.search_field_scale(),this.parsing=!1},s.prototype.result_do_highlight=function(t){var e,i,n,s,o;if(t.length){if(this.result_clear_highlight(),this.result_highlight=t,this.result_highlight.addClass("highlighted"),s=(n=parseInt(this.search_results.css("maxHeight"),10))+(o=this.search_results.scrollTop()),(e=(i=this.result_highlight.position().top+this.search_results.scrollTop())+this.result_highlight.outerHeight())>=s)return this.search_results.scrollTop(e-n>0?e-n:0);if(i<o)return this.search_results.scrollTop(i)}},s.prototype.result_clear_highlight=function(){return this.result_highlight&&this.result_highlight.removeClass("highlighted"),this.result_highlight=null},s.prototype.results_show=function(){return this.is_multiple&&this.max_selected_options<=this.choices_count()?(this.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1):(this.is_multiple||this.search_container.append(this.search_field),this.container.addClass("chosen-with-drop"),this.results_showing=!0,this.search_field.focus(),this.search_field.val(this.get_search_field_value()),this.winnow_results(),this.form_field_jq.trigger("chosen:showing_dropdown",{chosen:this}))},s.prototype.update_results_content=function(t){return this.search_results.html(t)},s.prototype.results_hide=function(){return this.results_showing&&(this.result_clear_highlight(),this.is_multiple||(this.selected_item.prepend(this.search_field),this.search_field.focus()),this.container.removeClass("chosen-with-drop"),this.form_field_jq.trigger("chosen:hiding_dropdown",{chosen:this})),this.results_showing=!1},s.prototype.set_tab_index=function(t){var e;if(this.form_field.tabIndex)return e=this.form_field.tabIndex,this.form_field.tabIndex=-1,this.search_field[0].tabIndex=e},s.prototype.set_label_behavior=function(){if(this.form_field_label=this.form_field_jq.parents("label"),!this.form_field_label.length&&this.form_field.id.length&&(this.form_field_label=t("label[for='"+this.form_field.id+"']")),this.form_field_label.length>0)return this.form_field_label.on("click.chosen",this.label_click_handler)},s.prototype.show_search_field_default=function(){return this.is_multiple&&this.choices_count()<1&&!this.active_field?(this.search_field.val(this.default_text),this.search_field.addClass("default")):(this.search_field.val(""),this.search_field.removeClass("default"))},s.prototype.search_results_mouseup=function(e){var i;if((i=t(e.target).hasClass("active-result")?t(e.target):t(e.target).parents(".active-result").first()).length)return this.result_highlight=i,this.result_select(e),this.search_field.focus()},s.prototype.search_results_mouseover=function(e){var i;if(i=t(e.target).hasClass("active-result")?t(e.target):t(e.target).parents(".active-result").first())return this.result_do_highlight(i)},s.prototype.search_results_mouseout=function(e){if(t(e.target).hasClass("active-result")||t(e.target).parents(".active-result").first())return this.result_clear_highlight()},s.prototype.choice_build=function(e){var i,n;return i=t("<li />",{class:"search-choice"}).html("<span>"+this.choice_label(e)+"</span>"),e.disabled?i.addClass("search-choice-disabled"):((n=t("<a />",{class:"search-choice-close","data-option-array-index":e.array_index})).on("click.chosen",function(t){return function(e){return t.choice_destroy_link_click(e)}}(this)),i.append(n)),this.search_container.before(i)},s.prototype.choice_destroy_link_click=function(e){if(e.preventDefault(),e.stopPropagation(),!this.is_disabled)return this.choice_destroy(t(e.target))},s.prototype.choice_destroy=function(t){if(this.result_deselect(t[0].getAttribute("data-option-array-index")))return this.active_field?this.search_field.focus():this.show_search_field_default(),this.is_multiple&&this.choices_count()>0&&this.get_search_field_value().length<1&&this.results_hide(),t.parents("li").first().remove(),this.search_field_scale()},s.prototype.results_reset=function(){if(this.reset_single_select_options(),this.form_field.options[0].selected=!0,this.single_set_selected_text(),this.show_search_field_default(),this.results_reset_cleanup(),this.trigger_form_field_change(),this.active_field)return this.results_hide()},s.prototype.results_reset_cleanup=function(){return this.current_selectedIndex=this.form_field.selectedIndex,this.selected_item.find("abbr").remove()},s.prototype.result_select=function(t){var e,i;if(this.result_highlight)return e=this.result_highlight,this.result_clear_highlight(),this.is_multiple&&this.max_selected_options<=this.choices_count()?(this.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1):(this.is_multiple?e.removeClass("active-result"):this.reset_single_select_options(),e.addClass("result-selected"),(i=this.results_data[e[0].getAttribute("data-option-array-index")]).selected=!0,this.form_field.options[i.options_index].selected=!0,this.selected_option_count=null,this.search_field.val(""),this.is_multiple?this.choice_build(i):this.single_set_selected_text(this.choice_label(i)),this.is_multiple&&(!this.hide_results_on_select||t.metaKey||t.ctrlKey)?this.winnow_results():(this.results_hide(),this.show_search_field_default()),(this.is_multiple||this.form_field.selectedIndex!==this.current_selectedIndex)&&this.trigger_form_field_change({selected:this.form_field.options[i.options_index].value}),this.current_selectedIndex=this.form_field.selectedIndex,t.preventDefault(),this.search_field_scale())},s.prototype.single_set_selected_text=function(t){return null==t&&(t=this.default_text),t===this.default_text?this.selected_item.addClass("chosen-default"):(this.single_deselect_control_build(),this.selected_item.removeClass("chosen-default")),this.selected_item.find("span").html(t)},s.prototype.result_deselect=function(t){var e;return e=this.results_data[t],!this.form_field.options[e.options_index].disabled&&(e.selected=!1,this.form_field.options[e.options_index].selected=!1,this.selected_option_count=null,this.result_clear_highlight(),this.results_showing&&this.winnow_results(),this.trigger_form_field_change({deselected:this.form_field.options[e.options_index].value}),this.search_field_scale(),!0)},s.prototype.single_deselect_control_build=function(){if(this.allow_single_deselect)return this.selected_item.find("abbr").length||this.selected_item.find("span").first().after('<abbr class="search-choice-close"></abbr>'),this.selected_item.addClass("chosen-single-with-deselect")},s.prototype.get_search_field_value=function(){return this.search_field.val()},s.prototype.get_search_text=function(){return t.trim(this.get_search_field_value())},s.prototype.escape_html=function(e){return t("<div/>").text(e).html()},s.prototype.winnow_results_set_highlight=function(){var t,e;if(null!=(t=(e=this.is_multiple?[]:this.search_results.find(".result-selected.active-result")).length?e.first():this.search_results.find(".active-result").first()))return this.result_do_highlight(t)},s.prototype.no_results=function(t){var e;return e=this.get_no_results_html(t),this.search_results.append(e),this.form_field_jq.trigger("chosen:no_results",{chosen:this})},s.prototype.no_results_clear=function(){return this.search_results.find(".no-results").remove()},s.prototype.keydown_arrow=function(){var t;return this.results_showing&&this.result_highlight?(t=this.result_highlight.nextAll("li.active-result").first())?this.result_do_highlight(t):void 0:this.results_show()},s.prototype.keyup_arrow=function(){var t;return this.results_showing||this.is_multiple?this.result_highlight?(t=this.result_highlight.prevAll("li.active-result")).length?this.result_do_highlight(t.first()):(this.choices_count()>0&&this.results_hide(),this.result_clear_highlight()):void 0:this.results_show()},s.prototype.keydown_backstroke=function(){var t;return this.pending_backstroke?(this.choice_destroy(this.pending_backstroke.find("a").first()),this.clear_backstroke()):(t=this.search_container.siblings("li.search-choice").last()).length&&!t.hasClass("search-choice-disabled")?(this.pending_backstroke=t,this.single_backstroke_delete?this.keydown_backstroke():this.pending_backstroke.addClass("search-choice-focus")):void 0},s.prototype.clear_backstroke=function(){return this.pending_backstroke&&this.pending_backstroke.removeClass("search-choice-focus"),this.pending_backstroke=null},s.prototype.search_field_scale=function(){var e,i,n,s,o,a,r;if(this.is_multiple){for(o={position:"absolute",left:"-1000px",top:"-1000px",display:"none",whiteSpace:"pre"},i=0,n=(a=["fontSize","fontStyle","fontWeight","fontFamily","lineHeight","textTransform","letterSpacing"]).length;i<n;i++)o[s=a[i]]=this.search_field.css(s);return(e=t("<div />").css(o)).text(this.get_search_field_value()),t("body").append(e),r=e.width()+25,e.remove(),this.container.is(":visible")&&(r=Math.min(this.container.outerWidth()-10,r)),this.search_field.width(r)}},s.prototype.trigger_form_field_change=function(t){return this.form_field_jq.trigger("input",t),this.form_field_jq.trigger("change",t)},s}()}.call(this);

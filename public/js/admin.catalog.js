function editProductDetail(e,t){window.open("product_catalog_admin.php?action=editProductDetail&product_id="+e+"&page_id="+t,"popup","width=855,height=1200,scrollbars=yes").focus()}function editProductSpecField(e,t){window.open("product_catalog_admin.php?action=editProductSpecField&field_id="+e+"&page_id="+t,"popup","width=855,height=1200,scrollbars=yes").focus()}function deleteProductsConfirmation(){return confirm("Are you sure you want to delete the selected product(s)?")}function DeleteProductSpecField(e,t){confirm("Are you sure you want to delete this field and all its associated options?")&&(location.href="product_catalog_admin.php?action=DeleteProductSpecField&field_id="+e+"&page_id="+t)}var saved_order=0;function orderSave(e){saved_order=parseInt(e.value)}function orderChanged(e){var t=parseInt(e.value);(isNaN(t)&&(t=0,e.value="0"),t!=saved_order)&&reconcileOrder(e,t,t<saved_order?1:-1)}function reconcileOrder(e,t,r){var o=null,d=null==e?"":e.name,n=new Array;for(i=0;i<document.tform.elements.length;i++)if("text"==document.tform.elements[i].type){var a=document.tform.elements[i];"order"==a.id.substring(0,5)&&(parseInt(a.value)==t&&a.name!=d&&(o=a),n.push(a))}if(null!=o)o.value=t+r,reconcileOrder(o,t+r,r);else for(var l=1;n.length>0;){var c=-1,u=Number.MAX_VALUE;for(i=0;i<n.length;i++)parseInt(n[i].value)<u&&(c=i,u=parseInt(n[i].value));n[c].value=l,n.splice(c,1),l++}}

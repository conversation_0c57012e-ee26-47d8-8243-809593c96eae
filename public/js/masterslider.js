window.averta={},function(t){function i(t){var i=(document.body||document.documentElement).style,e=t;if("string"==typeof i[e])return!0;v=["Moz","Webkit","Khtml","O","ms"],e=e.charAt(0).toUpperCase()+e.substr(1);for(var s=0;s<v.length;s++)if("string"==typeof i[v[s]+e])return!0;return!1}function e(){return i("transform")}window.package=function(t){window[t]||(window[t]={})};Function.prototype.extend=function(t){"function"==typeof t.prototype.constructor?(function(t,i){for(var e in i)t[e]=i[e]}(this.prototype,t.prototype),this.prototype.constructor=this):(this.prototype.extend(t),this.prototype.constructor=this)};var s={Moz:"-moz-",Webkit:"-webkit-",Khtml:"-khtml-",O:"-o-",ms:"-ms-",Icab:"-icab-"};window._mobile=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),window._touch="ontouchstart"in document,t(document).ready(function(){window._jcsspfx=function(){if("result"in arguments.callee)return arguments.callee.result;var t=/^(Moz|Webkit|Khtml|O|ms|Icab)(?=[A-Z])/,i=document.getElementsByTagName("script")[0];for(var e in i.style)if(t.test(e))return arguments.callee.result=e.match(t)[0];return arguments.callee.result="WebkitOpacity"in i.style?"Webkit":"KhtmlOpacity"in i.style?"Khtml":""}(),window._csspfx=s[window._jcsspfx],window._cssanim=i("transition"),window._css3d=function(){if(!e())return!1;var t,i=document.createElement("i"),s={WebkitTransform:"-webkit-transform",OTransform:"-o-transform",MSTransform:"-ms-transform",msTransform:"-ms-transform",MozTransform:"-moz-transform",Transform:"transform",transform:"transform"};for(var n in i.style.display="block",document.body.insertBefore(i,null),s)void 0!==i.style[n]&&(i.style[n]="translate3d(1px,1px,1px)",t=window.getComputedStyle(i).getPropertyValue(s[n]));return document.body.removeChild(i),null!=t&&t.length>0&&"none"!==t}(),window._css2d=e()}),window.parseQueryString=function(t){var i={};return t.replace(new RegExp("([^?=&]+)(=([^&]*))?","g"),function(t,e,s,n){i[e]=n}),i};(window.requestAnimationFrame||(window.requestAnimationFrame=window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){window.setTimeout(t,50/3)}),window.getComputedStyle||(window.getComputedStyle=function(t){return this.el=t,this.getPropertyValue=function(i){var e=/(\-([a-z]){1})/g;return"float"==i&&(i="styleFloat"),e.test(i)&&(i=i.replace(e,function(){return arguments[2].toUpperCase()})),t.currentStyle[i]?t.currentStyle[i]:null},t.currentStyle}),Array.prototype.indexOf||(Array.prototype.indexOf=function(t){var i=this.length>>>0,e=Number(arguments[1])||0;for(0>(e=0>e?Math.ceil(e):Math.floor(e))&&(e+=i);i>e;e++)if(e in this&&this[e]===t)return e;return-1}),jQuery)&&(t.jqLoadFix=function(){if(this.complete){var i=this;setTimeout(function(){t(i).load()},1)}},jQuery.uaMatch=jQuery.uaMatch||function(t){t=t.toLowerCase();var i=/(chrome)[ \/]([\w.]+)/.exec(t)||/(webkit)[ \/]([\w.]+)/.exec(t)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(t)||/(msie) ([\w.]+)/.exec(t)||t.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(t)||[];return{browser:i[1]||"",version:i[2]||"0"}},matched=jQuery.uaMatch(navigator.userAgent),browser={},matched.browser&&(browser[matched.browser]=!0,browser.version=matched.version),browser.chrome?browser.webkit=!0:browser.webkit&&(browser.safari=!0),!!navigator.userAgent.match(/Trident\/7\./)&&(browser.msie="true",delete browser.mozilla),jQuery.browser=browser,t.fn.preloadImg=function(i,e){return this.each(function(){var s=t(this),n=this,o=new Image;o.onload=function(t){null==t&&(t={}),s.attr("src",i),t.width=o.width,t.height=o.height,s.data("width",o.width),s.data("height",o.height),setTimeout(function(){e.call(n,t)},50),o=null},o.src=i}),this})}(jQuery),function(){"use strict";averta.EventDispatcher=function(){this.listeners={}},averta.EventDispatcher.extend=function(t){var i=new averta.EventDispatcher;for(var e in i)"constructor"!=e&&(t[e]=averta.EventDispatcher.prototype[e])},averta.EventDispatcher.prototype={constructor:averta.EventDispatcher,addEventListener:function(t,i,e){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push({listener:i,ref:e})},removeEventListener:function(t,i,e){if(this.listeners[t]){for(var s=0;s<this.listeners[t].length;++s)i===this.listeners[t][s].listener&&e===this.listeners[t][s].ref&&this.listeners[t].splice(s--,1);0===this.listeners[t].length&&(this.listeners[t]=null)}},dispatchEvent:function(t){if(t.target=this,this.listeners[t.type])for(var i=0,e=this.listeners[t.type].length;e>i;++i)this.listeners[t.type][i].listener.call(this.listeners[t.type][i].ref,t)}}}(),function(t){"use strict";var i="ontouchstart"in document,e=window.navigator.pointerEnabled,s=!e&&window.navigator.msPointerEnabled,n=e||s,o=(e?"pointerdown ":"")+(s?"MSPointerDown ":"")+(i?"touchstart ":"")+"mousedown",a=(e?"pointermove ":"")+(s?"MSPointerMove ":"")+(i?"touchmove ":"")+"mousemove",r=(e?"pointerup ":"")+(s?"MSPointerUp ":"")+(i?"touchend ":"")+"mouseup",h=(e?"pointercancel ":"")+(s?"MSPointerCancel ":"")+"touchcancel";averta.TouchSwipe=function(t){this.$element=t,this.enabled=!0,t.bind(o,{target:this},this.__touchStart),t[0].swipe=this,this.onSwipe=null,this.swipeType="horizontal",this.noSwipeSelector="input, textarea, button, .no-swipe, .ms-no-swipe",this.lastStatus={}};var l=averta.TouchSwipe.prototype;l.getDirection=function(t,i){switch(this.swipeType){case"horizontal":return t<=this.start_x?"left":"right";case"vertical":return i<=this.start_y?"up":"down";case"all":return Math.abs(t-this.start_x)>Math.abs(i-this.start_y)?t<=this.start_x?"left":"right":i<=this.start_y?"up":"down"}},l.priventDefultEvent=function(t,i){var e=Math.abs(t-this.start_x)>Math.abs(i-this.start_y);return"horizontal"===this.swipeType&&e||"vertical"===this.swipeType&&!e},l.createStatusObject=function(t){var i,e,s={};return i=this.lastStatus.distanceX||0,e=this.lastStatus.distanceY||0,s.distanceX=t.pageX-this.start_x,s.distanceY=t.pageY-this.start_y,s.moveX=s.distanceX-i,s.moveY=s.distanceY-e,s.distance=parseInt(Math.sqrt(Math.pow(s.distanceX,2)+Math.pow(s.distanceY,2))),s.duration=(new Date).getTime()-this.start_time,s.direction=this.getDirection(t.pageX,t.pageY),s},l.__reset=function(t,e){this.reset=!1,this.lastStatus={},this.start_time=(new Date).getTime(),this.start_x=i?t.touches[0].pageX:n?t.pageX:e.pageX,this.start_y=i?t.touches[0].pageY:n?t.pageY:e.pageY},l.__touchStart=function(e){var s=e.data.target,o=e;if(s.enabled&&!(t(e.target).closest(s.noSwipeSelector,s.$element).length>0)){if(e=e.originalEvent,n&&t(this).css("-ms-touch-action","horizontal"===s.swipeType?"pan-y":"pan-x"),!s.onSwipe)return void t.error("Swipe listener is undefined");if(!s.touchStarted){s.start_x=i?e.touches[0].pageX:n?e.pageX:o.pageX,s.start_y=i?e.touches[0].pageY:n?e.pageY:o.pageY,s.start_time=(new Date).getTime(),t(document).bind(r,{target:s},s.__touchEnd).bind(a,{target:s},s.__touchMove).bind(h,{target:s},s.__touchCancel);var l=i?e.touches[0]:n?e:o,d=s.createStatusObject(l);d.phase="start",s.onSwipe.call(null,d),i||o.preventDefault(),s.lastStatus=d,s.touchStarted=!0}}},l.__touchMove=function(t){var e=t.data.target,s=t;if(t=t.originalEvent,e.touchStarted){clearTimeout(e.timo),e.timo=setTimeout(function(){e.__reset(t,s)},60);var o=i?t.touches[0]:n?t:s,a=e.createStatusObject(o);e.priventDefultEvent(o.pageX,o.pageY)&&s.preventDefault(),a.phase="move",e.lastStatus=a,e.onSwipe.call(null,a)}},l.__touchEnd=function(e){var s=e.data.target,n=e;e=e.originalEvent,clearTimeout(s.timo);var o=(i&&e.touches[0],s.lastStatus);i||n.preventDefault(),o.phase="end",s.touchStarted=!1,s.priventEvt=null,t(document).unbind(r,s.__touchEnd).unbind(a,s.__touchMove).unbind(h,s.__touchCancel),o.speed=o.distance/o.duration,s.onSwipe.call(null,o)},l.__touchCancel=function(t){t.data.target.__touchEnd(t)},l.enable=function(){this.enabled||(this.enabled=!0)},l.disable=function(){this.enabled&&(this.enabled=!1)}}(jQuery),function(){"use strict";averta.Ticker=function(){};var t=averta.Ticker,i=[],e=0,s=!0;t.add=function(s,n){return i.push([s,n]),1===i.length&&t.start(),e=i.length},t.remove=function(s,n){for(var o=0,a=i.length;a>o;++o)i[o]&&i[o][0]===s&&i[o][1]===n&&i.splice(o,1);0===(e=i.length)&&t.stop()},t.start=function(){s&&(s=!1,n())},t.stop=function(){s=!0};var n=function(){if(!t.__stopped){for(var s,o=0;o!==e;o++)(s=i[o])[0].call(s[1]);requestAnimationFrame(n)}}}(),function(){"use strict";Date.now||(Date.now=function(){return(new Date).getTime()}),averta.Timer=function(t,i){this.delay=t,this.currentCount=0,this.paused=!1,this.onTimer=null,this.refrence=null,i&&this.start()},averta.Timer.prototype={constructor:averta.Timer,start:function(){this.paused=!1,this.lastTime=Date.now(),averta.Ticker.add(this.update,this)},stop:function(){this.paused=!0,averta.Ticker.remove(this.update,this)},reset:function(){this.currentCount=0,this.paused=!0,this.lastTime=Date.now()},update:function(){this.paused||Date.now()-this.lastTime<this.delay||(this.currentCount++,this.lastTime=Date.now(),this.onTimer&&this.onTimer.call(this.refrence,this.getTime()))},getTime:function(){return this.delay*this.currentCount}}}(),function(){"use strict";window.CSSTween=function(t,i,e,s){this.$element=t,this.duration=i||1e3,this.delay=e||0,this.ease=s||"linear"};var t=CSSTween.prototype;t.to=function(t,i){return this.to_cb=t,this.to_cb_target=i,this},t.from=function(t,i){return this.fr_cb=t,this.fr_cb_target=i,this},t.onComplete=function(t,i){return this.oc_fb=t,this.oc_fb_target=i,this},t.chain=function(t){return this.chained_tween=t,this},t.reset=function(){clearTimeout(this.start_to),clearTimeout(this.end_to)},t.start=function(){var t=this.$element[0];clearTimeout(this.start_to),clearTimeout(this.end_to),this.fresh=!0,this.fr_cb&&(t.style[window._jcsspfx+"TransitionDuration"]="0ms",this.fr_cb.call(this.fr_cb_target));var i=this;return this.onTransComplete=function(){i.fresh&&(i.reset(),t.style[window._jcsspfx+"TransitionDuration"]="",t.style[window._jcsspfx+"TransitionProperty"]="",t.style[window._jcsspfx+"TransitionTimingFunction"]="",t.style[window._jcsspfx+"TransitionDelay"]="",i.fresh=!1,i.chained_tween&&i.chained_tween.start(),i.oc_fb&&i.oc_fb.call(i.oc_fb_target))},this.start_to=setTimeout(function(){i.$element&&(t.style[window._jcsspfx+"TransitionDuration"]=i.duration+"ms",t.style[window._jcsspfx+"TransitionProperty"]=i.transProperty||"all",t.style[window._jcsspfx+"TransitionDelay"]=i.delay>0?i.delay+"ms":"",t.style[window._jcsspfx+"TransitionTimingFunction"]=i.ease,i.to_cb&&i.to_cb.call(i.to_cb_target),i.end_to=setTimeout(function(){i.onTransComplete()},i.duration+(i.delay||0)))},100),this}}(),function(){"use strict";function t(t,e){if(void 0!==e.x||void 0!==e.y)if(i){var s=window._jcsspfx+"Transform";void 0!==e.x&&(e[s]=(e[s]||"")+" translateX("+e.x+"px)",delete e.x),void 0!==e.y&&(e[s]=(e[s]||"")+" translateY("+e.y+"px)",delete e.y)}else{if(void 0!==e.x)e["auto"!==t.css("right")?"right":"left"]=e.x+"px",delete e.x;if(void 0!==e.y)e["auto"!==t.css("bottom")?"bottom":"top"]=e.y+"px",delete e.y}return e}var i=null;window.CTween={},CTween.setPos=function(i,e){i.css(t(i,e))},CTween.animate=function(e,s,n,o){if(null==i&&(i=window._cssanim),o=o||{},t(e,n),i){var a=new CSSTween(e,s,o.delay,EaseDic[o.ease]);return o.transProperty&&(a.transProperty=o.transProperty),a.to(function(){e.css(n)}),o.complete&&a.onComplete(o.complete,o.target),a.start(),a.stop=a.reset,a}var r;return o.delay&&e.delay(o.delay),o.complete&&(r=function(){o.complete.call(o.target)}),e.stop(!0).animate(n,s,o.ease||"linear",r),e},CTween.fadeOut=function(t,i,e){var s={};!0===e?s.complete=function(){t.remove()}:2===e&&(s.complete=function(){t.css("display","none")}),CTween.animate(t,i||1e3,{opacity:0},s)},CTween.fadeIn=function(t,i,e){!1!==e&&t.css("opacity",0).css("display",""),CTween.animate(t,i||1e3,{opacity:1})}}(),window.EaseDic={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",easeInCubic:"cubic-bezier(.55,.055,.675,.19)",easeOutCubic:"cubic-bezier(.215,.61,.355,1)",easeInOutCubic:"cubic-bezier(.645,.045,.355,1)",easeInCirc:"cubic-bezier(.6,.04,.98,.335)",easeOutCirc:"cubic-bezier(.075,.82,.165,1)",easeInOutCirc:"cubic-bezier(.785,.135,.15,.86)",easeInExpo:"cubic-bezier(.95,.05,.795,.035)",easeOutExpo:"cubic-bezier(.19,1,.22,1)",easeInOutExpo:"cubic-bezier(1,0,0,1)",easeInQuad:"cubic-bezier(.55,.085,.68,.53)",easeOutQuad:"cubic-bezier(.25,.46,.45,.94)",easeInOutQuad:"cubic-bezier(.455,.03,.515,.955)",easeInQuart:"cubic-bezier(.895,.03,.685,.22)",easeOutQuart:"cubic-bezier(.165,.84,.44,1)",easeInOutQuart:"cubic-bezier(.77,0,.175,1)",easeInQuint:"cubic-bezier(.755,.05,.855,.06)",easeOutQuint:"cubic-bezier(.23,1,.32,1)",easeInOutQuint:"cubic-bezier(.86,0,.07,1)",easeInSine:"cubic-bezier(.47,0,.745,.715)",easeOutSine:"cubic-bezier(.39,.575,.565,1)",easeInOutSine:"cubic-bezier(.445,.05,.55,.95)",easeInBack:"cubic-bezier(.6,-.28,.735,.045)",easeOutBack:"cubic-bezier(.175, .885,.32,1.275)",easeInOutBack:"cubic-bezier(.68,-.55,.265,1.55)"},function(){"use strict";window.MSAligner=function(t,i,e){this.$container=i,this.$img=e,this.type=t||"stretch",this.widthOnly=!1,this.heightOnly=!1};var t=MSAligner.prototype;t.init=function(t,i){switch(this.baseWidth=t,this.baseHeight=i,this.imgRatio=t/i,this.imgRatio2=i/t,this.type){case"tile":this.$container.css("background-image","url("+this.$img.attr("src")+")"),this.$img.remove();break;case"center":this.$container.css("background-image","url("+this.$img.attr("src")+")"),this.$container.css({backgroundPosition:"center center",backgroundRepeat:"no-repeat"}),this.$img.remove();break;case"stretch":this.$img.css({width:"100%",height:"100%"});break;case"fill":case"fit":this.needAlign=!0,this.align()}},t.align=function(){if(this.needAlign){var t=this.$container.width(),i=this.$container.height(),e=t/i;"fill"==this.type?this.imgRatio<e?(this.$img.width(t),this.$img.height(t*this.imgRatio2)):(this.$img.height(i),this.$img.width(i*this.imgRatio)):"fit"==this.type&&(this.imgRatio<e?(this.$img.height(i),this.$img.width(i*this.imgRatio)):(this.$img.width(t),this.$img.height(t*this.imgRatio2))),this.setMargin()}},t.setMargin=function(){var t=this.$container.width(),i=this.$container.height();this.$img.css("margin-top",(i-this.$img[0].offsetHeight)/2+"px"),this.$img.css("margin-left",(t-this.$img[0].offsetWidth)/2+"px")}}(),function(){"use strict";var t={bouncing:!0,snapping:!1,snapsize:null,friction:.05,outFriction:.05,outAcceleration:.09,minValidDist:.3,snappingMinSpeed:2,paging:!1,endless:!1,maxSpeed:160},i=function(i,e,s){if(null===e||null===i)throw new Error("Max and Min values are required.");for(var n in this.options=s||{},t)n in this.options||(this.options[n]=t[n]);this._max_value=e,this._min_value=i,this.value=i,this.end_loc=i,this.current_snap=this.getSnapNum(i),this.__extrStep=0,this.__extraMove=0,this.__animID=-1},e=i.prototype;e.changeTo=function(t,i,e,s,n){if(this.stopped=!1,this._internalStop(),t=this._checkLimits(t),e=Math.abs(e||0),this.options.snapping&&(s=s||this.getSnapNum(t),!1!==n&&this._callsnapChange(s),this.current_snap=s),i){this.animating=!0;var o=this,a=++o.__animID,r=t-o.value,h=0,l=t,d=1-o.options.friction,c=d+(e-20)*d*1.3/o.options.maxSpeed,p=function(){if(a===o.__animID){var i=t-o.value;if(!(Math.abs(i)>o.options.minValidDist&&o.animating))return o.animating&&(o.value=t,o._callrenderer()),o.animating=!1,a!==o.__animID&&(o.__animID=-1),void o._callonComplete("anim");window.requestAnimationFrame(p),o.value=l-r*Math.exp(-++h*c),o._callrenderer()}};p()}else this.value=t,this._callrenderer()},e.drag=function(t){this.start_drag&&(this.drag_start_loc=this.value,this.start_drag=!1),this.animating=!1,this._deceleration=!1,this.value-=t,!this.options.endless&&(this.value>this._max_value||this.value<0)?this.options.bouncing?(this.__isout=!0,this.value+=.6*t):this.value=this.value>this._max_value?this._max_value:0:!this.options.endless&&this.options.bouncing&&(this.__isout=!1),this._callrenderer()},e.push=function(t){if(this.stopped=!1,this.options.snapping&&Math.abs(t)<=this.options.snappingMinSpeed)this.cancel();else{if(this.__speed=t,this.__startSpeed=t,this.end_loc=this._calculateEnd(),this.options.snapping){var i=this.getSnapNum(this.value),e=this.getSnapNum(this.end_loc);if(this.options.paging)return i=this.getSnapNum(this.drag_start_loc),this.__isout=!1,void(t>0?this.gotoSnap(i+1,!0,t):this.gotoSnap(i-1,!0,t));if(i===e)return void this.cancel();this._callsnapChange(e),this.current_snap=e}this.animating=!1,this.__needsSnap=this.options.endless||this.end_loc>this._min_value&&this.end_loc<this._max_value,this.options.snapping&&this.__needsSnap&&(this.__extraMove=this._calculateExtraMove(this.end_loc)),this._startDecelaration()}},e.bounce=function(t){this.animating||(this.stopped=!1,this.animating=!1,this.__speed=t,this.__startSpeed=t,this.end_loc=this._calculateEnd(),this._startDecelaration())},e.stop=function(){this.stopped=!0,this._internalStop()},e.cancel=function(){this.start_drag=!0,this.__isout?(this.__speed=4e-4,this._startDecelaration()):this.options.snapping&&this.gotoSnap(this.getSnapNum(this.value),!0)},e.renderCallback=function(t,i){this.__renderHook={fun:t,ref:i}},e.snappingCallback=function(t,i){this.__snapHook={fun:t,ref:i}},e.snapCompleteCallback=function(t,i){this.__compHook={fun:t,ref:i}},e.getSnapNum=function(t){return Math.floor((t+this.options.snapsize/2)/this.options.snapsize)},e.nextSnap=function(){this._internalStop();var t=this.getSnapNum(this.value);!this.options.endless&&(t+1)*this.options.snapsize>this._max_value?(this.__speed=8,this.__needsSnap=!1,this._startDecelaration()):this.gotoSnap(t+1,!0)},e.prevSnap=function(){this._internalStop();var t=this.getSnapNum(this.value);!this.options.endless&&(t-1)*this.options.snapsize<this._min_value?(this.__speed=-8,this.__needsSnap=!1,this._startDecelaration()):this.gotoSnap(t-1,!0)},e.gotoSnap=function(t,i,e){this.changeTo(t*this.options.snapsize,i,e,t)},e.destroy=function(){this._internalStop(),this.__renderHook=null,this.__snapHook=null,this.__compHook=null},e._internalStop=function(){this.start_drag=!0,this.animating=!1,this._deceleration=!1,this.__extrStep=0},e._calculateExtraMove=function(t){var i=t%this.options.snapsize;return i<this.options.snapsize/2?-i:this.options.snapsize-i},e._calculateEnd=function(t){for(var i=this.__speed,e=this.value,s=0;Math.abs(i)>this.options.minValidDist;)e+=i,i*=this.options.friction,s++;return t?s:e},e._checkLimits=function(t){return this.options.endless?t:t<this._min_value?this._min_value:t>this._max_value?this._max_value:t},e._callrenderer=function(){this.__renderHook&&this.__renderHook.fun.call(this.__renderHook.ref,this,this.value)},e._callsnapChange=function(t){this.__snapHook&&t!==this.current_snap&&this.__snapHook.fun.call(this.__snapHook.ref,this,t,t-this.current_snap)},e._callonComplete=function(t){this.__compHook&&!this.stopped&&this.__compHook.fun.call(this.__compHook.ref,this,this.current_snap,t)},e._computeDeceleration=function(){if(this.options.snapping&&this.__needsSnap){var t=(this.__startSpeed-this.__speed)/this.__startSpeed*this.__extraMove;this.value+=this.__speed+t-this.__extrStep,this.__extrStep=t}else this.value+=this.__speed;if(this.__speed*=this.options.friction,this.options.endless||this.options.bouncing||(this.value<=this._min_value?(this.value=this._min_value,this.__speed=0):this.value>=this._max_value&&(this.value=this._max_value,this.__speed=0)),this._callrenderer(),!this.options.endless&&this.options.bouncing){var i=0;this.value<this._min_value?i=this._min_value-this.value:this.value>this._max_value&&(i=this._max_value-this.value),this.__isout=Math.abs(i)>=this.options.minValidDist,this.__isout&&(this.__speed*i<=0?this.__speed+=i*this.options.outFriction:this.__speed=i*this.options.outAcceleration)}},e._startDecelaration=function(){if(!this._deceleration){this._deceleration=!0;var t=this,i=function(){t._deceleration&&(t._computeDeceleration(),Math.abs(t.__speed)>t.options.minValidDist||t.__isout?window.requestAnimationFrame(i):(t._deceleration=!1,t.__isout=!1,t.value=t.__needsSnap&&t.options.snapping&&!t.options.paging?t._checkLimits(t.end_loc+t.__extraMove):Math.round(t.value),t._callrenderer(),t._callonComplete("decel")))};i()}},window.Controller=i}(),function(t){window.MSLayerEffects={};var i,e={opacity:0};MSLayerEffects.setup=function(){if(!i){i=!0;var s=MSLayerEffects,n=window._jcsspfx+"Transform",o=window._jcsspfx+"TransformOrigin",a=t.browser.opera;_2d=window._css2d&&window._cssanim&&!a,s.defaultValues={left:0,top:0,opacity:1,right:0,bottom:0},s.defaultValues[n]="",s.rf=1,s.presetEffParams={random:"30|300",long:300,short:30,false:!1,true:!0,tl:"top left",bl:"bottom left",tr:"top right",br:"bottom right",rt:"top right",lb:"bottom left",lt:"top left",rb:"bottom right",t:"top",b:"bottom",r:"right",l:"left",c:"center"},s.fade=function(){return e},s.left=_2d?function(t,i){var e=!1===i?{}:{opacity:0};return e[n]="translateX("+-t*s.rf+"px)",e}:function(t,i){var e=!1===i?{}:{opacity:0};return e.left=-t*s.rf+"px",e},s.right=_2d?function(t,i){var e=!1===i?{}:{opacity:0};return e[n]="translateX("+t*s.rf+"px)",e}:function(t,i){var e=!1===i?{}:{opacity:0};return e.left=t*s.rf+"px",e},s.top=_2d?function(t,i){var e=!1===i?{}:{opacity:0};return e[n]="translateY("+-t*s.rf+"px)",e}:function(t,i){var e=!1===i?{}:{opacity:0};return e.top=-t*s.rf+"px",e},s.bottom=_2d?function(t,i){var e=!1===i?{}:{opacity:0};return e[n]="translateY("+t*s.rf+"px)",e}:function(t,i){var e=!1===i?{}:{opacity:0};return e.top=t*s.rf+"px",e},s.from=_2d?function(t,i,e){var o=!1===e?{}:{opacity:0};return o[n]="translateX("+t*s.rf+"px) translateY("+i*s.rf+"px)",o}:function(t,i,e){var n=!1===e?{}:{opacity:0};return n.top=i*s.rf+"px",n.left=t*s.rf+"px",n},s.rotate=_2d?function(t,i){var e={opacity:0};return e[n]=" rotate("+t+"deg)",i&&(e[o]=i),e}:function(){return e},s.rotateleft=_2d?function(t,i,e,a){var r=s.left(i,a);return r[n]+=" rotate("+t+"deg)",e&&(r[o]=e),r}:function(t,i,e,n){return s.left(i,n)},s.rotateright=_2d?function(t,i,e,a){var r=s.right(i,a);return r[n]+=" rotate("+t+"deg)",e&&(r[o]=e),r}:function(t,i,e,n){return s.right(i,n)},s.rotatetop=_2d?function(t,i,e,a){var r=s.top(i,a);return r[n]+=" rotate("+t+"deg)",e&&(r[o]=e),r}:function(t,i,e,n){return s.top(i,n)},s.rotatebottom=_2d?function(t,i,e,a){var r=s.bottom(i,a);return r[n]+=" rotate("+t+"deg)",e&&(r[o]=e),r}:function(t,i,e,n){return s.bottom(i,n)},s.rotatefrom=_2d?function(t,i,e,a,r){var h=s.from(i,e,r);return h[n]+=" rotate("+t+"deg)",a&&(h[o]=a),h}:function(t,i,e,n,o){return s.from(i,e,o)},s.skewleft=_2d?function(t,i,e){var o=s.left(i,e);return o[n]+=" skewX("+t+"deg)",o}:function(t,i,e){return s.left(i,e)},s.skewright=_2d?function(t,i,e){var o=s.right(i,e);return o[n]+=" skewX("+-t+"deg)",o}:function(t,i,e){return s.right(i,e)},s.skewtop=_2d?function(t,i,e){var o=s.top(i,e);return o[n]+=" skewY("+t+"deg)",o}:function(t,i,e){return s.top(i,e)},s.skewbottom=_2d?function(t,i,e){var o=s.bottom(i,e);return o[n]+=" skewY("+-t+"deg)",o}:function(t,i,e){return s.bottom(i,e)},s.scale=_2d?function(t,i,e,s){var a=!1===s?{}:{opacity:0};return a[n]=" scaleX("+t+") scaleY("+i+")",e&&(a[o]=e),a}:function(t,i,e,s){return!1===s?{}:{opacity:0}},s.scaleleft=_2d?function(t,i,e,a,r){var h=s.left(e,r);return h[n]=" scaleX("+t+") scaleY("+i+")",a&&(h[o]=a),h}:function(t,i,e,n,o){return s.left(e,o)},s.scaleright=_2d?function(t,i,e,a,r){var h=s.right(e,r);return h[n]=" scaleX("+t+") scaleY("+i+")",a&&(h[o]=a),h}:function(t,i,e,n,o){return s.right(e,o)},s.scaletop=_2d?function(t,i,e,a,r){var h=s.top(e,r);return h[n]=" scaleX("+t+") scaleY("+i+")",a&&(h[o]=a),h}:function(t,i,e,n,o){return s.top(e,o)},s.scalebottom=_2d?function(t,i,e,a,r){var h=s.bottom(e,r);return h[n]=" scaleX("+t+") scaleY("+i+")",a&&(h[o]=a),h}:function(t,i,e,n,o){return s.bottom(e,o)},s.scalefrom=_2d?function(t,i,e,a,r,h){var l=s.from(e,a,h);return l[n]+=" scaleX("+t+") scaleY("+i+")",r&&(l[o]=r),l}:function(t,i,e,n,o,a){return s.from(e,n,a)},s.rotatescale=_2d?function(t,i,e,a,r){var h=s.scale(i,e,a,r);return h[n]+=" rotate("+t+"deg)",a&&(h[o]=a),h}:function(t,i,e,n,o){return s.scale(i,e,n,o)},s.front=window._css3d?function(t,i){var e=!1===i?{}:{opacity:0};return e[n]="perspective(2000px) translate3d(0 , 0 ,"+t+"px ) rotate(0.001deg)",e}:function(){return e},s.back=window._css3d?function(t,i){var e=!1===i?{}:{opacity:0};return e[n]="perspective(2000px) translate3d(0 , 0 ,"+-t+"px ) rotate(0.001deg)",e}:function(){return e},s.rotatefront=window._css3d?function(t,i,e,s){var a=!1===s?{}:{opacity:0};return a[n]="perspective(2000px) translate3d(0 , 0 ,"+i+"px ) rotate("+(t||.001)+"deg)",e&&(a[o]=e),a}:function(){return e},s.rotateback=window._css3d?function(t,i,e,s){var a=!1===s?{}:{opacity:0};return a[n]="perspective(2000px) translate3d(0 , 0 ,"+-i+"px ) rotate("+(t||.001)+"deg)",e&&(a[o]=e),a}:function(){return e},s.rotate3dleft=window._css3d?function(t,i,e,a,r,h){var l=s.left(a,h);return l[n]+=(t?" rotateX("+t+"deg)":" ")+(i?" rotateY("+i+"deg)":"")+(e?" rotateZ("+e+"deg)":""),r&&(l[o]=r),l}:function(t,i,e,n,o,a){return s.left(n,a)},s.rotate3dright=window._css3d?function(t,i,e,a,r,h){var l=s.right(a,h);return l[n]+=(t?" rotateX("+t+"deg)":" ")+(i?" rotateY("+i+"deg)":"")+(e?" rotateZ("+e+"deg)":""),r&&(l[o]=r),l}:function(t,i,e,n,o,a){return s.right(n,a)},s.rotate3dtop=window._css3d?function(t,i,e,a,r,h){var l=s.top(a,h);return l[n]+=(t?" rotateX("+t+"deg)":" ")+(i?" rotateY("+i+"deg)":"")+(e?" rotateZ("+e+"deg)":""),r&&(l[o]=r),l}:function(t,i,e,n,o,a){return s.top(n,a)},s.rotate3dbottom=window._css3d?function(t,i,e,a,r,h){var l=s.bottom(a,h);return l[n]+=(t?" rotateX("+t+"deg)":" ")+(i?" rotateY("+i+"deg)":"")+(e?" rotateZ("+e+"deg)":""),r&&(l[o]=r),l}:function(t,i,e,n,o,a){return s.bottom(n,a)},s.rotate3dfront=window._css3d?function(t,i,e,a,r,h){var l=s.front(a,h);return l[n]+=(t?" rotateX("+t+"deg)":" ")+(i?" rotateY("+i+"deg)":"")+(e?" rotateZ("+e+"deg)":""),r&&(l[o]=r),l}:function(t,i,e,n,o,a){return s.front(n,a)},s.rotate3dback=window._css3d?function(t,i,e,a,r,h){var l=s.back(a,h);return l[n]+=(t?" rotateX("+t+"deg)":" ")+(i?" rotateY("+i+"deg)":"")+(e?" rotateZ("+e+"deg)":""),r&&(l[o]=r),l}:function(t,i,e,n,o,a){return s.back(n,a)},s.t=window._css3d?function(t,i,e,a,r,h,l,d,c,p,u,m,f,v,_){var w=!1===t?{}:{opacity:0},g="perspective(2000px) ";"n"!==i&&(g+="translateX("+i*s.rf+"px) "),"n"!==e&&(g+="translateY("+e*s.rf+"px) "),"n"!==a&&(g+="translateZ("+a*s.rf+"px) "),"n"!==r&&(g+="rotate("+r+"deg) "),"n"!==h&&(g+="rotateX("+h+"deg) "),"n"!==l&&(g+="rotateY("+l+"deg) "),"n"!==d&&(g+="rotateZ("+d+"deg) "),"n"!==u&&(g+="skewX("+u+"deg) "),"n"!==m&&(g+="skewY("+m+"deg) "),"n"!==c&&(g+="scaleX("+c+") "),"n"!==p&&(g+="scaleY("+p+")"),w[n]=g;var S="";return S+="n"!==f?f+"% ":"50% ",S+="n"!==v?v+"% ":"50% ",S+="n"!==_?_+"px":"",w[o]=S,w}:function(t,i,e,n,o){o=!1===t?{}:{opacity:0};return"n"!==i&&(o.left=i*s.rf+"px"),"n"!==e&&(o.top=e*s.rf+"px"),o}}}}(jQuery),function(t){window.MSLayerElement=function(){this.$cont=t("<div></div>").addClass("layer-cont"),this.start_anim={name:"fade",duration:1e3,ease:"linear",delay:0},this.end_anim={duration:1e3,ease:"linear"},this.type="text",this.resizable=!0,this.minWidth=-1,this.isVisible=!0,this.__cssConfig=["margin-top","padding-top","margin-bottom","padding-left","margin-right","padding-right","margin-left","padding-bottom","font-size","line-height","width","left","right","top","bottom"],this.baseStyle={}};var i=MSLayerElement.prototype;i.__playAnimation=function(t,i){var e={};t.ease&&(e.ease=t.ease),e.transProperty=window._csspfx+"transform,opacity",this.show_tween=CTween.animate(this.$element,t.duration,i,e)},i._randomParam=function(t){var i=Number(t.slice(0,t.indexOf("|"))),e=Number(t.slice(t.indexOf("|")+1));return i+Math.random()*(e-i)},i._parseEff=function(t){var i=[];if(-1!==t.indexOf("(")){var e,s=t.slice(0,t.indexOf("(")).toLowerCase();i=t.slice(t.indexOf("(")+1,-1).replace(/\"|\'|\s/g,"").split(","),t=s;for(var n=0,o=i.length;o>n;++n)(e=i[n])in MSLayerEffects.presetEffParams&&(e=MSLayerEffects.presetEffParams[e]),i[n]=e}return{eff_name:t,eff_params:i}},i._parseEffParams=function(t){for(var i=[],e=0,s=t.length;s>e;++e){var n=t[e];"string"==typeof n&&-1!==n.indexOf("|")&&(n=this._randomParam(n)),i[e]=n}return i},i._checkPosKey=function(t,i){return"left"===t&&!(t in this.baseStyle)&&"right"in this.baseStyle?(i.right=-parseInt(i.left)+"px",delete i.left,!0):"top"===t&&!(t in this.baseStyle)&&"bottom"in this.baseStyle&&(i.bottom=-parseInt(i.top)+"px",delete i.top,!0)},i.setStartAnim=function(i){t.extend(this.start_anim,i),t.extend(this.start_anim,this._parseEff(this.start_anim.name)),this.$element.css("visibility","hidden")},i.setEndAnim=function(i){t.extend(this.end_anim,i)},i.create=function(){if(this.$element.css("display","none").removeAttr("data-delay").removeAttr("data-effect").removeAttr("data-duration").removeAttr("data-type"),void 0!==this.$element.data("resize")&&(this.resizable=this.$element.data("resize"),this.$element.removeAttr("data-resize")),void 0!==this.$element.data("fixed")&&(this.fixed=this.$element.data("fixed"),this.$element.removeAttr("data-fixed")),void 0!==this.$element.data("widthlimit")&&(this.minWidth=this.$element.data("widthlimit"),this.$element.removeAttr("data-widthlimit")),this.end_anim.name||(this.end_anim.name=this.start_anim.name),this.end_anim.time&&(this.autoHide=!0),void 0!==this.$element.data("action")){var i=this.slide.slider.slideController;this.$element.on("click",function(e){i.runAction(t(this).data("action")),e.preventDefault()}).addClass("ms-action-layer")}t.extend(this.end_anim,this._parseEff(this.end_anim.name)),this.slider=this.slide.slider;var e=this.layerOrigin=this.$element.data("origin");if(e){var s=e.charAt(0),n=e.charAt(1),o=this.$element.data("offset-x"),a=this.$element.data("offset-y");switch(void 0!==a?this.$element.removeAttr("data-offset-y"):a=0,s){case"t":this.$element[0].style.top=a+"px";break;case"b":this.$element[0].style.bottom=a+"px";break;case"m":this.$element[0].style.top=a+"px",this.middleAlign=!0}switch(void 0!==o?this.$element.removeAttr("data-offset-x"):o=0,n){case"l":this.$element[0].style.left=o+"px";break;case"r":this.$element[0].style.right=o+"px";break;case"c":this.$element[0].style.left=o+"px",this.centerAlign=!0}this.$element.removeAttr("data-origin")}this.parallax=this.$element.data("parallax"),null!=this.parallax&&(this.parallax/=100,this.$parallaxElement=t("<div></div>").addClass("ms-parallax-layer"),this.link?(this.link.wrap(this.$parallaxElement),this.$parallaxElement=this.link.parent()):(this.$element.wrap(this.$parallaxElement),this.$parallaxElement=this.$element.parent()),this._lastParaX=0,this._lastParaY=0,this._paraX=0,this._paraY=0,this.alignedToBot=this.layerOrigin&&-1!==this.layerOrigin.indexOf("b"),this.alignedToBot&&this.$parallaxElement.css("bottom",0),this.parallaxRender=window._css3d?this.parallaxCSS3DRenderer:window._css2d?this.parallaxCSS2DRenderer:this.parallax2DRenderer,"swipe"!==this.slider.options.parallaxMode&&averta.Ticker.add(this.parallaxRender,this))},i.moveParallax=function(t,i,e){this._paraX=t,this._paraY=i,e&&(this._lastParaX=t,this._lastParaY=i,this.parallaxRender())},i.parallaxCalc=function(){var t=this._paraX-this._lastParaX,i=this._paraY-this._lastParaY;this._lastParaX+=t/12,this._lastParaY+=i/12,Math.abs(t)<.019&&(this._lastParaX=this._paraX),Math.abs(i)<.019&&(this._lastParaY=this._paraY)},i.parallaxCSS3DRenderer=function(){this.parallaxCalc(),this.$parallaxElement[0].style[window._jcsspfx+"Transform"]="translateX("+this._lastParaX*this.parallax+"px) translateY("+this._lastParaY*this.parallax+"px) translateZ(0)"},i.parallaxCSS2DRenderer=function(){this.parallaxCalc(),this.$parallaxElement[0].style[window._jcsspfx+"Transform"]="translateX("+this._lastParaX*this.parallax+"px) translateY("+this._lastParaY*this.parallax+"px)"},i.parallax2DRenderer=function(){this.parallaxCalc(),this.alignedToBot?this.$parallaxElement[0].style.bottom=this._lastParaY*this.parallax+"px":this.$parallaxElement[0].style.top=this._lastParaY*this.parallax+"px",this.$parallaxElement[0].style.left=this._lastParaX*this.parallax+"px"},i.init=function(){var t;this.initialized=!0,this.$element.css("visibility","");for(var i=0,e=this.__cssConfig.length;e>i;i++){var s=this.__cssConfig[i];"text"===this.type&&"width"===s?t=this.$element[0].style.width:(t=this.$element.css(s),"width"!==s&&"height"!==s||"0px"!==t||(t=this.$element.data(s)+"px")),"auto"!=t&&""!=t&&"normal"!=t&&(this.baseStyle[s]=parseInt(t))}this.middleAlign&&(this.baseHeight=this.$element.outerHeight(!1)),this.centerAlign&&(this.baseWidth=this.$element.outerWidth(!1))},i.locate=function(){if(this.slide.ready){var t,i,e=this.slide.$layers,s=parseFloat(e.css("width")),n=parseFloat(e.css("height"));for(var o in"none"===this.$element.css("display")&&this.isVisible&&this.$element.css("display","block").css("visibility","hidden"),t=this.resizeFactor=s/this.slide.slider.options.width,this.baseStyle)i="top"===o||"left"===o||"bottom"===o||"right"===o,t=this.fixed&&i?1:this.resizeFactor,(this.resizable||i)&&("top"===o&&this.middleAlign?(this.$element[0].style.top="0px",this.baseHeight=this.$element.outerHeight(!1),this.$element[0].style.top=this.baseStyle.top*t+(n-this.baseHeight)/2+"px"):"left"===o&&this.centerAlign?(this.$element[0].style.left="0px",this.baseWidth=this.$element.outerWidth(!1),this.$element[0].style.left=this.baseStyle.left*t+(s-this.baseWidth)/2+"px"):this.$element.css(o,this.baseStyle[o]*t+"px"));this.visible(this.minWidth<s)}},i.start=function(){if(!this.isShowing){this.isShowing=!0;var t,i,e=this.slide.$layers;MSLayerEffects.rf=this.resizeFactor;var s=MSLayerEffects[this.start_anim.eff_name].apply(null,this._parseEffParams(this.start_anim.eff_params)),n={};for(t in s)this._checkPosKey(t,s)||(null!=MSLayerEffects.defaultValues[t]&&(n[t]=MSLayerEffects.defaultValues[t]),t in this.baseStyle&&(i=this.baseStyle[t],this.middleAlign&&"top"===t&&(i+=(parseInt(e.height())-this.$element.outerHeight(!1))/2),this.centerAlign&&"left"===t&&(i+=(parseInt(e.width())-this.$element.outerWidth(!1))/2),s[t]=i+parseFloat(s[t])+"px",n[t]=i+"px"),this.$element.css(t,s[t]));var o=this;clearTimeout(this.to),this.to=setTimeout(function(){o.$element.css("visibility",""),o.__playAnimation(o.start_anim,n)},o.start_anim.delay||.01),this.cl_to=setTimeout(function(){o.show_cl=!0},(this.start_anim.delay||.01)+this.start_anim.duration),this.autoHide&&(clearTimeout(this.hto),this.hto=setTimeout(function(){o.hide()},o.end_anim.time))}},i.hide=function(){this.isShowing=!1;var t=MSLayerEffects[this.end_anim.eff_name].apply(null,this._parseEffParams(this.end_anim.eff_params));for(key in t)this._checkPosKey(key,t)||(key===window._jcsspfx+"TransformOrigin"&&this.$element.css(key,t[key]),key in this.baseStyle&&(t[key]=this.baseStyle[key]+parseFloat(t[key])+"px"));this.__playAnimation(this.end_anim,t),clearTimeout(this.to),clearTimeout(this.hto),clearTimeout(this.cl_to)},i.reset=function(){this.isShowing=!1,this.$element[0].style.display="none",this.$element.css("opacity","100"),this.$element[0].style.transitionDuration="0ms",this.show_tween&&this.show_tween.stop(!0),clearTimeout(this.to),clearTimeout(this.hto)},i.destroy=function(){this.reset(),this.$element.remove(),this.$cont.remove()},i.visible=function(t){this.isVisible!=t&&(this.isVisible=t,this.$element.css("display",t?"":"none"))}}(jQuery),function(t){window.MSImageLayerElement=function(){MSLayerElement.call(this),this.needPreload=!0,this.__cssConfig=["width","height","margin-top","padding-top","margin-bottom","padding-left","margin-right","padding-right","margin-left","padding-bottom","left","right","top","bottom"],this.type="image"},MSImageLayerElement.extend(MSLayerElement);var i=MSImageLayerElement.prototype,e=MSLayerElement.prototype;i.create=function(){if(this.link){var i=this.$element.parent();i.append(this.link),this.link.append(this.$element),this.link.removeClass("ms-layer"),this.$element.addClass("ms-layer"),i=null}if(e.create.call(this),null!=this.$element.data("src"))this.img_src=this.$element.data("src"),this.$element.removeAttr("data-src");else{var s=this;this.$element.on("load",function(){s.slide.preloadCount--,0===s.slide.preloadCount&&s.slide.___onlayersReady()}).each(t.jqLoadFix)}t.browser.msie&&this.$element.on("dragstart",function(t){t.preventDefault()})},i.loadImage=function(){var t=this;this.$element.preloadImg(this.img_src,function(){t.slide.preloadCount--,0===t.slide.preloadCount&&t.slide.___onlayersReady()})}}(jQuery),function(t){window.MSVideoLayerElement=function(){MSLayerElement.call(this),this.__cssConfig.push("height"),this.type="video"},MSVideoLayerElement.extend(MSLayerElement);var i=MSVideoLayerElement.prototype,e=MSLayerElement.prototype;i.__playVideo=function(){this.img&&CTween.fadeOut(this.img,500,2),CTween.fadeOut(this.video_btn,500,2),this.video_frame.attr("src","about:blank").css("display","block"),-1==this.video_url.indexOf("?")&&(this.video_url+="?"),this.video_frame.attr("src",this.video_url+"&autoplay=1")},i.start=function(){e.start.call(this),this.$element.data("autoplay")&&this.__playVideo()},i.reset=function(){return e.reset.call(this),(this.needPreload||this.$element.data("btn"))&&(this.video_btn.css("opacity",1).css("display","block"),this.video_frame.attr("src","about:blank").css("display","none")),this.needPreload?void this.img.css("opacity",1).css("display","block"):void this.video_frame.attr("src",this.video_url)},i.create=function(){e.create.call(this),this.video_frame=this.$element.find("iframe").css({width:"100%",height:"100%"}),this.video_url=this.video_frame.attr("src");var i=0!=this.$element.has("img").length;if(i||this.$element.data("btn")){this.video_frame.attr("src","about:blank").css("display","none");var s=this;if(this.video_btn=t("<div></div>").appendTo(this.$element).addClass("ms-video-btn").click(function(){s.__playVideo()}),i){if(this.needPreload=!0,this.img=this.$element.find("img:first").addClass("ms-video-img"),void 0!==this.img.data("src"))this.img_src=this.img.data("src"),this.img.removeAttr("data-src");else{s=this;this.img.attr("src",this.img_src).on("load",function(){s.slide.preloadCount--,0==s.slide.preloadCount&&s.slide.___onlayersReady()}).each(t.jqLoadFix)}t.browser.msie&&this.img.on("dragstart",function(t){t.preventDefault()})}}},i.loadImage=function(){var t=this;this.img.preloadImg(this.img_src,function(){t.slide.preloadCount--,0==t.slide.preloadCount&&t.slide.___onlayersReady()})}}(jQuery),function(t){"use strict";window.MSHotspotLayer=function(){MSLayerElement.call(this),this.__cssConfig=["margin-top","padding-top","margin-bottom","padding-left","margin-right","padding-right","margin-left","padding-bottom","left","right","top","bottom"],this.ease="Expo",this.hide_start=!0,this.type="hotspot"},MSHotspotLayer.extend(MSLayerElement);var i=MSHotspotLayer.prototype,e=MSLayerElement.prototype;i._showTT=function(){this.show_cl&&(clearTimeout(this.hto),this._tween&&this._tween.stop(!0),this.hide_start&&(this.align=this._orgAlign,this._locateTT(),this.tt.css({display:"block"}),this._tween=CTween.animate(this.tt,900,this.to,{ease:"easeOut"+this.ease}),this.hide_start=!1))},i._hideTT=function(){if(this.show_cl){this._tween&&this._tween.stop(!0);var t=this;clearTimeout(this.hto),this.hto=setTimeout(function(){t.hide_start=!0,t._tween=CTween.animate(t.tt,900,t.from,{ease:"easeOut"+t.ease,complete:function(){t.tt.css("display","none")}})},200)}},i._updateClassName=function(t){this._lastClass&&this.tt.removeClass(this._lastClass),this.tt.addClass(t),this._lastClass=t},i._alignPolicy=function(){var t=(this.tt.outerHeight(!1),Math.max(this.tt.outerWidth(!1),parseInt(this.tt.css("max-width")))),i=window.innerWidth;switch(window.innerHeight,this.align){case"top":if(this.base_t<0)return"bottom";break;case"right":if(this.base_l+t>i||this.base_t<0)return"bottom";break;case"left":if(this.base_l<0||this.base_t<0)return"bottom"}return null},i._locateTT=function(){var t=this.$element.offset(),i=this.slide.slider.$element.offset();this.pos_x=t.left-i.left-this.slide.slider.$element.scrollLeft(),this.pos_y=t.top-i.top-this.slide.slider.$element.scrollTop(),this.from={opacity:0},this.to={opacity:1},this._updateClassName("ms-tooltip-"+this.align),this.tt_arrow.css("margin-left","");switch(this.align){case"top":var e=Math.min(this.tt.outerWidth(!1),parseInt(this.tt.css("max-width")));this.base_t=this.pos_y-this.tt.outerHeight(!1)-15-15,this.base_l=this.pos_x-e/2,this.base_l+e>window.innerWidth&&(this.tt_arrow.css("margin-left",-7.5+this.base_l+e-window.innerWidth+"px"),this.base_l=window.innerWidth-e),this.base_l<0&&(this.base_l=0,this.tt_arrow.css("margin-left",-7.5+this.pos_x-this.tt.outerWidth(!1)/2+"px")),window._css3d?(this.from[window._jcsspfx+"Transform"]="translateY(-50px)",this.to[window._jcsspfx+"Transform"]=""):(this.from.top=this.base_t-50+"px",this.to.top=this.base_t+"px");break;case"bottom":e=Math.min(this.tt.outerWidth(!1),parseInt(this.tt.css("max-width")));this.base_t=this.pos_y+15+15,this.base_l=this.pos_x-e/2,this.base_l+e>window.innerWidth&&(this.tt_arrow.css("margin-left",-7.5+this.base_l+e-window.innerWidth+"px"),this.base_l=window.innerWidth-e),this.base_l<0&&(this.base_l=0,this.tt_arrow.css("margin-left",-7.5+this.pos_x-this.tt.outerWidth(!1)/2+"px")),window._css3d?(this.from[window._jcsspfx+"Transform"]="translateY(50px)",this.to[window._jcsspfx+"Transform"]=""):(this.from.top=this.base_t+50+"px",this.to.top=this.base_t+"px");break;case"right":this.base_l=this.pos_x+15+15,this.base_t=this.pos_y-this.tt.outerHeight(!1)/2,window._css3d?(this.from[window._jcsspfx+"Transform"]="translateX(50px)",this.to[window._jcsspfx+"Transform"]=""):(this.from.left=this.base_l+50+"px",this.to.left=this.base_l+"px");break;case"left":this.base_l=this.pos_x-15-this.tt.outerWidth(!1)-15,this.base_t=this.pos_y-this.tt.outerHeight(!1)/2,window._css3d?(this.from[window._jcsspfx+"Transform"]="translateX(-50px)",this.to[window._jcsspfx+"Transform"]=""):(this.from.left=this.base_l-50+"px",this.to.left=this.base_l+"px")}var s=this._alignPolicy();return null!==s?(this.align=s,void this._locateTT()):(this.tt.css("top",parseInt(this.base_t)+"px").css("left",parseInt(this.base_l)+"px"),void this.tt.css(this.from))},i.start=function(){e.start.call(this),this.tt.appendTo(this.slide.slider.$element),this.tt.css("display","none")},i.reset=function(){e.reset.call(this),this.tt.detach()},i.create=function(){var i=this;e.create.call(this),this._orgAlign=this.align=void 0!==this.$element.data("align")?this.$element.data("align"):"top",this.data=this.$element.html(),this.$element.html("").on("mouseenter",function(){i._showTT()}).on("mouseleave",function(){i._hideTT()}),this.point=t('<div><div class="ms-point-center"></div><div class="ms-point-border"></div></div>').addClass("ms-tooltip-point").appendTo(this.$element);var s=this.$element.data("link"),n=this.$element.data("target");s&&this.point.on("click",function(){window.open(s,n||"_self")}),this.tt=t("<div></div>").addClass("ms-tooltip").css("display","hidden").css("opacity",0),void 0!==this.$element.data("width")&&this.tt.css("width",this.$element.data("width")).css("max-width",this.$element.data("width")),this.tt_arrow=t("<div></div>").addClass("ms-tooltip-arrow").appendTo(this.tt),this._updateClassName("ms-tooltip-"+this.align),this.ttcont=t("<div></div>").addClass("ms-tooltip-cont").html(this.data).appendTo(this.tt),!0===this.$element.data("stay-hover")&&this.tt.on("mouseenter",function(){i.hide_start||(clearTimeout(i.hto),i._tween.stop(!0),i._showTT())}).on("mouseleave",function(){i._hideTT()})}}(jQuery),function(){window.MSButtonLayer=function(){MSLayerElement.call(this),this.type="button"},MSButtonLayer.extend(MSLayerElement);var t=MSButtonLayer.prototype,i=MSLayerElement.prototype,e=["top","left","bottom","right"];t.create=function(){i.create.call(this),this.$element.wrap('<div class="ms-btn-container"></div>').css("position","relative"),this.$container=this.$element.parent()},t.locate=function(){i.locate.call(this);for(var t,s,n=0;4>n;n++)(t=e[n])in this.baseStyle&&(s=this.$element.css(t),this.$element.css(t,""),this.$container.css(t,s));this.$container.width(this.$element.outerWidth(!0)).height(this.$element.outerHeight(!0))}}(jQuery),window.MSSliderEvent=function(t){this.type=t},MSSliderEvent.CHANGE_START="ms_changestart",MSSliderEvent.CHANGE_END="ms_changeend",MSSliderEvent.WAITING="ms_waiting",MSSliderEvent.AUTOPLAY_CHANGE="ms_autoplaychange",MSSliderEvent.VIDEO_PLAY="ms_videoPlay",MSSliderEvent.VIDEO_CLOSE="ms_videoclose",MSSliderEvent.INIT="ms_init",MSSliderEvent.HARD_UPDATE="ms_hard_update",MSSliderEvent.RESIZE="ms_resize",MSSliderEvent.RESERVED_SPACE_CHANGE="ms_rsc",MSSliderEvent.DESTROY="ms_destroy",function(t){"use strict";window.MSSlide=function(){this.$element=null,this.$loading=t("<div></div>").addClass("ms-slide-loading"),this.layers=[],this.view=null,this.index=-1,this.__width=0,this.__height=0,this.preloadCount=0,this.fillMode="fill",this.selected=!1,this.pselected=!1,this.autoAppend=!0,this.isSleeping=!0,this.moz=t.browser.mozilla};var i=MSSlide.prototype;i.onSwipeStart=function(){this.link&&(this.linkdis=!0),this.video&&(this.videodis=!0)},i.onSwipeMove=function(t){var i=Math.max(Math.abs(t.data.distanceX),Math.abs(t.data.distanceY));this.swipeMoved=i>4},i.onSwipeCancel=function(){return this.swipeMoved?void(this.swipeMoved=!1):(this.link&&(this.linkdis=!1),void(this.video&&(this.videodis=!1)))},i.addLayer=function(i){this.hasLayers||(this.$layers=t("<div></div>").addClass("ms-slide-layers")),this.hasLayers=!0,this.$layers.append(i.$element),this.layers.push(i),i.slide=this,i.create(),i.parallax&&(this.hasParallaxLayer=!0),i.needPreload&&this.preloadCount++},i.___onlayersReady=function(){this.ready=!0,this.slider.api._startTimer(),(this.selected||this.pselected&&this.slider.options.instantStartLayers)&&(this.showLayers(),this.vinit&&(this.bgvideo.play(),this.autoPauseBgVid||(this.bgvideo.currentTime=0))),this.isSleeping||this.setup(),CTween.fadeOut(this.$loading,300,!0),(0===this.slider.options.preload||"all"===this.slider.options.preload)&&this.index<this.view.slideList.length-1?this.view.slideList[this.index+1].loadImages():"all"===this.slider.options.preload&&this.index===this.view.slideList.length-1&&this.slider._removeLoading()},i.startLayers=function(){for(var t=0,i=this.layers.length;i>t;++t)this.layers[t].start()},i.initLayers=function(t){if(!(this.init&&!t||this.slider.init_safemode)){this.init=!0;for(var i=0,e=this.layers.length;e>i;++i)this.layers[i].init()}},i.locateLayers=function(){for(var t=0,i=this.layers.length;i>t;++t)this.layers[t].locate()},i.resetLayers=function(){this.$layers.css("display","none"),this.$layers.css("opacity",1);for(var t=0,i=this.layers.length;i>t;++t)this.layers[t].reset()},i.hideLayers=function(){if(0===this.preloadCount)for(var t=0,i=this.layers.length;i>t;++t)this.layers[t].hide()},i.showLayers=function(){this.hasLayers&&(this.lht&&(this.lht.reset?this.lht.reset():this.lht.stop(!0)),this.resetLayers(),this.$layers.css("opacity",1).css("display","block"),0===this.preloadCount&&(this.initLayers(),this.locateLayers(),this.startLayers()))},i.applyParallax=function(t,i,e){for(var s=0,n=this.layers.length;s!==n;++s)null!=this.layers[s].parallax&&this.layers[s].moveParallax(t,i,e)},i.enableParallaxEffect=function(){this.hasParallaxLayer&&("swipe"===this.slider.options.parallaxMode?this.view.addEventListener(MSViewEvents.SCROLL,this.swipeParallaxMove,this):this.$element.on("mousemove",{that:this},this.mouseParallaxMove).on("mouseleave",{that:this},this.resetParalax))},i.disableParallaxEffect=function(){this.hasParallaxLayer&&("swipe"===this.slider.options.parallaxMode?this.view.removeEventListener(MSViewEvents.SCROLL,this.swipeParallaxMove,this):this.$element.off("mousemove",this.mouseParallaxMove).off("mouseleave",this.resetParalax))},i.resetParalax=function(t){t.data.that.applyParallax(0,0)},i.mouseParallaxMove=function(t){var i=t.data.that,e=i.$element.offset(),s=i.slider;if("mouse:y-only"!==s.options.parallaxMode)var n=t.pageX-e.left-i.__width/2;else n=0;if("mouse:x-only"!==s.options.parallaxMode)var o=t.pageY-e.top-i.__height/2;else o=0;i.applyParallax(-n,-o)},i.swipeParallaxMove=function(){var t=this.position-this.view.__contPos;this.applyParallax(t,0,!0)},i.setBG=function(i){this.hasBG=!0;var e=this;this.$imgcont=t("<div></div>").addClass("ms-slide-bgcont"),this.$element.append(this.$loading).append(this.$imgcont),this.$bg_img=t(i).css("visibility","hidden"),this.$imgcont.append(this.$bg_img),this.bgAligner=new MSAligner(e.fillMode,e.$imgcont,e.$bg_img),this.bgAligner.widthOnly=this.slider.options.autoHeight,e.slider.options.autoHeight&&(e.pselected||e.selected)&&e.slider.setHeight(e.slider.options.height),void 0!==this.$bg_img.data("src")?(this.bg_src=this.$bg_img.data("src"),this.$bg_img.removeAttr("data-src")):this.$bg_img.one("load",function(t){e._onBGLoad(t)}).each(t.jqLoadFix),this.preloadCount++},i._onBGLoad=function(i){this.bgNatrualWidth=i.width,this.bgNatrualHeight=i.height,this.bgLoaded=!0,t.browser.msie&&this.$bg_img.on("dragstart",function(t){t.preventDefault()}),this.preloadCount--,0===this.preloadCount&&this.___onlayersReady()},i.loadImages=function(){if(!this.ls){if(this.ls=!0,0===this.preloadCount&&this.___onlayersReady(),this.bgvideo&&this.bgvideo.load(),this.hasBG&&this.bg_src){var t=this;this.$bg_img.preloadImg(this.bg_src,function(i){t._onBGLoad(i)})}for(var i=0,e=this.layers.length;e>i;++i)this.layers[i].needPreload&&this.layers[i].loadImage()}},i.setBGVideo=function(i){if(i[0].play){if(window._mobile)return void i.remove();this.bgvideo=i[0];var e=this;i.addClass("ms-slide-bgvideo"),!1!==i.data("loop")&&this.bgvideo.addEventListener("ended",function(){e.bgvideo.play()}),!1!==i.data("mute")&&(this.bgvideo.muted=!0),!0===i.data("autopause")&&(this.autoPauseBgVid=!0),this.bgvideo_fillmode=i.data("fill-mode")||"fill","none"!==this.bgvideo_fillmode&&(this.bgVideoAligner=new MSAligner(this.bgvideo_fillmode,this.$element,i),this.bgvideo.addEventListener("loadedmetadata",function(){e.vinit||(e.vinit=!0,e.video_aspect=e.bgVideoAligner.baseHeight/e.bgVideoAligner.baseWidth,e.bgVideoAligner.init(e.bgvideo.videoWidth,e.bgvideo.videoHeight),e._alignBGVideo(),CTween.fadeIn(t(e.bgvideo),200),e.selected&&e.bgvideo.play())})),i.css("opacity",0),this.$bgvideocont=t("<div></div>").addClass("ms-slide-bgvideocont").append(i),this.hasBG?this.$imgcont.before(this.$bgvideocont):this.$bgvideocont.appendTo(this.$element)}},i._alignBGVideo=function(){this.bgvideo_fillmode&&"none"!==this.bgvideo_fillmode&&this.bgVideoAligner.align()},i.setSize=function(t,i,e){this.__width=t,this.slider.options.autoHeight&&(this.bgLoaded?(this.ratio=this.__width/this.bgWidth,i=Math.floor(this.ratio*this.bgHeight),this.$imgcont.height(i)):(this.ratio=t/this.slider.options.width,i=this.slider.options.height*this.ratio)),this.__height=i,this.$element.width(t).height(i),this.hasBG&&this.bgLoaded&&this.bgAligner.align(),this._alignBGVideo(),e&&this.selected&&this.initLayers(e),this.selected&&this.locateLayers(),this.hasLayers&&(this.slider.options.autoHeight&&(this.$layers[0].style.height=this.getHeight()+"px"),"center"==this.slider.options.layersMode&&(this.$layers[0].style.left=Math.max(0,(this.__width-this.slider.options.width)/2)+"px"))},i.getHeight=function(){return this.hasBG&&this.bgLoaded?this.bgHeight*this.ratio:Math.max(this.$element[0].clientHeight,this.slider.options.height*this.ratio)},i.__playVideo=function(){this.vplayed||this.videodis||(this.vplayed=!0,this.slider.api.paused||(this.slider.api.pause(),this.roc=!0),this.vcbtn.css("display",""),CTween.fadeOut(this.vpbtn,500,!1),CTween.fadeIn(this.vcbtn,500),CTween.fadeIn(this.vframe,500),this.vframe.css("display","block").attr("src",this.video+"&autoplay=1"),this.view.$element.addClass("ms-def-cursor"),this.view.swipeControl&&this.view.swipeControl.disable(),this.slider.slideController.dispatchEvent(new MSSliderEvent(MSSliderEvent.VIDEO_PLAY)))},i.__closeVideo=function(){if(this.vplayed){this.vplayed=!1,this.roc&&this.slider.api.resume();var t=this;CTween.fadeIn(this.vpbtn,500),CTween.animate(this.vcbtn,500,{opacity:0},{complete:function(){t.vcbtn.css("display","none")}}),CTween.animate(this.vframe,500,{opacity:0},{complete:function(){t.vframe.attr("src","about:blank").css("display","none")}}),this.view.swipeControl&&this.view.swipeControl.enable(),this.view.$element.removeClass("ms-def-cursor"),this.slider.slideController.dispatchEvent(new MSSliderEvent(MSSliderEvent.VIDEO_CLOSE))}},i.create=function(){var i=this;this.hasLayers&&(this.$element.append(this.$layers),"center"==this.slider.options.layersMode&&this.$layers.css("max-width",this.slider.options.width+"px")),this.link&&this.link.addClass("ms-slide-link").html("").click(function(t){i.linkdis&&t.preventDefault()}),this.video&&(-1===this.video.indexOf("?")&&(this.video+="?"),this.vframe=t("<iframe></iframe>").addClass("ms-slide-video").css({width:"100%",height:"100%",display:"none"}).attr("src","about:blank").attr("allowfullscreen","true").appendTo(this.$element),this.vpbtn=t("<div></div>").addClass("ms-slide-vpbtn").click(function(){i.__playVideo()}).appendTo(this.$element),this.vcbtn=t("<div></div>").addClass("ms-slide-vcbtn").click(function(){i.__closeVideo()}).appendTo(this.$element).css("display","none"),window._touch&&this.vcbtn.removeClass("ms-slide-vcbtn").addClass("ms-slide-vcbtn-mobile").append('<div class="ms-vcbtn-txt">Close video</div>').appendTo(this.view.$element.parent())),!this.slider.options.autoHeight&&this.hasBG&&(this.$imgcont.css("height","100%"),("center"===this.fillMode||"stretch"===this.fillMode)&&(this.fillMode="fill")),this.slider.options.autoHeight&&this.$element.addClass("ms-slide-auto-height"),this.sleep(!0)},i.destroy=function(){for(var t=0,i=this.layers.length;i>t;++t)this.layers[t].$element.stop(!0).remove();this.$element.remove(),this.$element=null},i.setup=function(){!this.initBG&&this.bgLoaded&&(this.initBG=!0,this.$bg_img.css("visibility",""),this.bgWidth=this.bgNatrualWidth||this.$bg_img.width(),this.bgHeight=this.bgNatrualHeight||this.$bg_img.height(),CTween.fadeIn(this.$imgcont,300),this.slider.options.autoHeight&&this.$imgcont.height(this.bgHeight*this.ratio),this.bgAligner.init(this.bgWidth,this.bgHeight),this.setSize(this.__width,this.__height),this.slider.options.autoHeight&&(this.pselected||this.selected)&&this.slider.setHeight(this.getHeight()))},i.prepareToSelect=function(){this.pselected||this.selected||(this.pselected=!0,(this.link||this.video)&&(this.view.addEventListener(MSViewEvents.SWIPE_START,this.onSwipeStart,this),this.view.addEventListener(MSViewEvents.SWIPE_MOVE,this.onSwipeMove,this),this.view.addEventListener(MSViewEvents.SWIPE_CANCEL,this.onSwipeCancel,this),this.linkdis=!1,this.swipeMoved=!1),this.loadImages(),0===this.preloadCount&&(this.bgvideo&&this.bgvideo.play(),this.slider.options.instantStartLayers&&this.showLayers()),this.enableParallaxEffect(),this.moz&&this.$element.css("margin-top",""))},i.select=function(){this.selected||(this.selected=!0,this.pselected=!1,this.$element.addClass("ms-sl-selected"),this.hasLayers&&(this.slider.options.autoHeight&&(this.$layers[0].style.height=this.getHeight()+"px"),this.slider.options.instantStartLayers||this.showLayers()),0===this.preloadCount&&this.bgvideo&&this.bgvideo.play(),this.videoAutoPlay&&(this.videodis=!1,this.vpbtn.trigger("click")))},i.unselect=function(){if(this.pselected=!1,this.moz&&this.$element.css("margin-top","0.1px"),(this.link||this.video)&&(this.view.removeEventListener(MSViewEvents.SWIPE_START,this.onSwipeStart,this),this.view.removeEventListener(MSViewEvents.SWIPE_MOVE,this.onSwipeMove,this),this.view.removeEventListener(MSViewEvents.SWIPE_CANCEL,this.onSwipeCancel,this)),this.bgvideo&&(this.bgvideo.pause(),!this.autoPauseBgVid&&this.vinit&&(this.bgvideo.currentTime=0)),this.hasLayers&&(this.selected||this.slider.options.instantStartLayers)){var t=this;t.lht=CTween.animate(this.$layers,500,{opacity:0},{complete:function(){t.resetLayers()}}),this.disableParallaxEffect()}this.selected&&(this.selected=!1,this.$element.removeClass("ms-sl-selected"),this.video&&this.vplayed&&(this.__closeVideo(),this.roc=!1))},i.sleep=function(t){(!this.isSleeping||t)&&(this.isSleeping=!0,this.autoAppend&&this.$element.detach())},i.wakeup=function(){this.isSleeping&&(this.isSleeping=!1,this.autoAppend&&this.view.$slideCont.append(this.$element),this.moz&&this.$element.css("margin-top","0.1px"),this.setup(),this.hasBG&&this.bgAligner.align())}}(jQuery),function(t){"use strict";var i={};window.MSSlideController=function(t){this._delayProgress=0,this._timer=new averta.Timer(100),this._timer.onTimer=this.onTimer,this._timer.refrence=this,this.currentSlide=null,this.slider=t,this.so=t.options,averta.EventDispatcher.call(this)},MSSlideController.registerView=function(t,e){if(t in i)throw new Error(t+", is already registered.");i[t]=e},MSSlideController.SliderControlList={},MSSlideController.registerControl=function(t,i){if(t in MSSlideController.SliderControlList)throw new Error(t+", is already registered.");MSSlideController.SliderControlList[t]=i};var e=MSSlideController.prototype;e.setupView=function(){var e=this;this.resize_listener=function(){e.__resize()};var s={spacing:this.so.space,mouseSwipe:this.so.mouse,loop:this.so.loop,autoHeight:this.so.autoHeight,swipe:this.so.swipe,speed:this.so.speed,dir:this.so.dir,viewNum:this.so.inView,critMargin:this.so.critMargin};this.so.viewOptions&&t.extend(s,this.so.viewOptions),this.so.autoHeight&&(this.so.heightLimit=!1);var n=i[this.slider.options.view]||MSBasicView;if(!n._3dreq||window._css3d&&!t.browser.msie||(n=n._fallback||MSBasicView),this.view=new n(s),this.so.overPause){e=this;this.slider.$element.mouseenter(function(){e.is_over=!0,e._stopTimer()}).mouseleave(function(){e.is_over=!1,e._startTimer()})}},e.onChangeStart=function(){this.change_started=!0,this.currentSlide&&this.currentSlide.unselect(),this.currentSlide=this.view.currentSlide,this.currentSlide.prepareToSelect(),this.so.endPause&&this.currentSlide.index===this.slider.slides.length-1&&(this.pause(),this.skipTimer()),this.so.autoHeight&&this.slider.setHeight(this.currentSlide.getHeight()),this.so.deepLink&&this.__updateWindowHash(),this.dispatchEvent(new MSSliderEvent(MSSliderEvent.CHANGE_START))},e.onChangeEnd=function(){if(this.change_started=!1,this._startTimer(),this.currentSlide.select(),this.so.preload>1){var t,i,e=this.so.preload-1;for(i=1;e>=i;++i){if((t=this.view.index+i)>=this.view.slideList.length){if(!this.so.loop){i=e;continue}t-=this.view.slideList.length}this.view.slideList[t].loadImages()}for(e>this.view.slideList.length/2&&(e=Math.floor(this.view.slideList.length/2)),i=1;e>=i;++i){if(0>(t=this.view.index-i)){if(!this.so.loop){i=e;continue}t=this.view.slideList.length+t}this.view.slideList[t].loadImages()}}this.dispatchEvent(new MSSliderEvent(MSSliderEvent.CHANGE_END))},e.onSwipeStart=function(){this.skipTimer()},e.skipTimer=function(){this._timer.reset(),this._delayProgress=0,this.dispatchEvent(new MSSliderEvent(MSSliderEvent.WAITING))},e.onTimer=function(){this._timer.getTime()>=1e3*this.view.currentSlide.delay&&(this.skipTimer(),this.view.next(),this.hideCalled=!1),this._delayProgress=this._timer.getTime()/(10*this.view.currentSlide.delay),this.so.hideLayers&&!this.hideCalled&&1e3*this.view.currentSlide.delay-this._timer.getTime()<=300&&(this.view.currentSlide.hideLayers(),this.hideCalled=!0),this.dispatchEvent(new MSSliderEvent(MSSliderEvent.WAITING))},e._stopTimer=function(){this._timer&&this._timer.stop()},e._startTimer=function(){this.paused||this.is_over||!this.currentSlide||!this.currentSlide.ready||this.change_started||this._timer.start()},e.__appendSlides=function(){for(var t,i,e=0,s=this.view.slideList.length-1;s>e;++e)(t=this.view.slideList[e]).detached||(t.$element.detach(),t.detached=!0);for(this.view.appendSlide(this.view.slideList[this.view.index]),s=3,e=1;s>=e;++e){if((i=this.view.index+e)>=this.view.slideList.length){if(!this.so.loop){e=s;continue}i-=this.view.slideList.length}(t=this.view.slideList[i]).detached=!1,this.view.appendSlide(t)}for(s>this.view.slideList.length/2&&(s=Math.floor(this.view.slideList.length/2)),e=1;s>=e;++e){if(0>(i=this.view.index-e)){if(!this.so.loop){e=s;continue}i=this.view.slideList.length+i}(t=this.view.slideList[i]).detached=!1,this.view.appendSlide(t)}},e.__resize=function(t){this.created&&(this.width=this.slider.$element[0].clientWidth||this.so.width,this.so.fullwidth||(this.width=Math.min(this.width,this.so.width)),this.so.fullheight?(this.so.heightLimit=!1,this.so.autoHeight=!1,this.height=this.slider.$element[0].clientHeight):this.height=this.width/this.slider.aspect,this.so.autoHeight?(this.currentSlide.setSize(this.width,null,t),this.view.setSize(this.width,this.currentSlide.getHeight(),t)):this.view.setSize(this.width,this.so.heightLimit?Math.min(this.height,this.so.height):this.height,t),this.slider.$controlsCont&&this.so.centerControls&&this.so.fullwidth&&this.view.$element.css("left",Math.min(0,-(this.slider.$element[0].clientWidth-this.so.width)/2)+"px"),this.dispatchEvent(new MSSliderEvent(MSSliderEvent.RESIZE)))},e.__dispatchInit=function(){this.dispatchEvent(new MSSliderEvent(MSSliderEvent.INIT))},e.__updateWindowHash=function(){var t=window.location.hash,i=this.so.deepLink,e=this.so.deepLinkType,s="path"===e?"/":"=",n="path"===e?"/":"&",o=i+s+(this.view.index+1),a=new RegExp(i+s+"[0-9]+","g");window.location.hash=""===t?n+o:a.test(t)?t.replace(a,o):t+n+o},e.__curentSlideInHash=function(){var t=window.location.hash,i=this.so.deepLink,e=this.so.deepLinkType,s=new RegExp(i+("path"===e?"/":"=")+"[0-9]+","g");if(s.test(t)){var n=Number(t.match(s)[0].match(/[0-9]+/g).pop());if(!isNaN(n))return n-1}return-1},e.__onHashChanged=function(){var t=this.__curentSlideInHash();-1!==t&&this.gotoSlide(t)},e.setup=function(){this.created=!0,this.paused=!this.so.autoplay,this.view.addEventListener(MSViewEvents.CHANGE_START,this.onChangeStart,this),this.view.addEventListener(MSViewEvents.CHANGE_END,this.onChangeEnd,this),this.view.addEventListener(MSViewEvents.SWIPE_START,this.onSwipeStart,this),this.currentSlide=this.view.slideList[this.so.start-1],this.__resize();var i=this.__curentSlideInHash(),e=-1!==i?i:this.so.start-1;if(this.view.create(e),0===this.so.preload&&this.view.slideList[0].loadImages(),this.scroller=this.view.controller,this.so.wheel){var s=this,n=(new Date).getTime();this.wheellistener=function(i){var e=window.event||i.orginalEvent||i;e.preventDefault();var o=(new Date).getTime();if(!(400>o-n)){n=o;var a=Math.abs(e.detail||e.wheelDelta);t.browser.mozilla&&(a*=100);return e.detail<0||e.wheelDelta>0?a>=15&&s.previous(!0):a>=15&&s.next(!0),!1}},t.browser.mozilla?this.slider.$element[0].addEventListener("DOMMouseScroll",this.wheellistener):this.slider.$element.bind("mousewheel",this.wheellistener)}0===this.slider.$element[0].clientWidth&&(this.slider.init_safemode=!0),this.__resize();s=this;this.so.deepLink&&t(window).on("hashchange",function(){s.__onHashChanged()})},e.index=function(){return this.view.index},e.count=function(){return this.view.slidesCount},e.next=function(t){this.skipTimer(),this.view.next(t)},e.previous=function(t){this.skipTimer(),this.view.previous(t)},e.gotoSlide=function(t){t=Math.min(t,this.count()-1),this.skipTimer(),this.view.gotoSlide(t)},e.destroy=function(t){this.dispatchEvent(new MSSliderEvent(MSSliderEvent.DESTROY)),this.slider.destroy(t)},e._destroy=function(){this._timer.reset(),this._timer=null,t(window).unbind("resize",this.resize_listener),this.view.destroy(),this.view=null,this.so.wheel&&(t.browser.mozilla?this.slider.$element[0].removeEventListener("DOMMouseScroll",this.wheellistener):this.slider.$element.unbind("mousewheel",this.wheellistener),this.wheellistener=null),this.so=null},e.runAction=function(t){var i=[];if(-1!==t.indexOf("(")){var e=t.slice(0,t.indexOf("("));i=t.slice(t.indexOf("(")+1,-1).replace(/\"|\'|\s/g,"").split(","),t=e}t in this?this[t].apply(this,i):console},e.scrollToEnd=function(i){var e=this.slider.$element;null==i&&(i=1.4),t("html, body").animate({scrollTop:e.offset().top+e.outerHeight(!1)},1e3*i,"easeInOutQuad")},e.update=function(t){this.slider.init_safemode&&t&&(this.slider.init_safemode=!1),this.__resize(t),t&&this.dispatchEvent(new MSSliderEvent(MSSliderEvent.HARD_UPDATE))},e.locate=function(){this.__resize()},e.resume=function(){this.paused&&(this.paused=!1,this._startTimer())},e.pause=function(){this.paused||(this.paused=!0,this._stopTimer())},e.currentTime=function(){return this._delayProgress},averta.EventDispatcher.extend(e)}(jQuery),function(t){"use strict";var i={image:MSImageLayerElement,text:MSLayerElement,video:MSVideoLayerElement,hotspot:MSHotspotLayer,button:MSButtonLayer};window.MasterSlider=function(){this.options={autoplay:!1,loop:!1,mouse:!0,swipe:!0,grabCursor:!0,space:0,fillMode:"fill",start:1,view:"basic",width:300,height:150,inView:15,critMargin:1,heightLimit:!0,smoothHeight:!0,autoHeight:!1,fullwidth:!1,fullheight:!1,autofill:!1,layersMode:"center",hideLayers:!1,endPause:!1,centerControls:!0,overPause:!0,shuffle:!1,speed:17,dir:"h",preload:0,wheel:!1,layout:"boxed",fullscreenMargin:0,instantStartLayers:!1,parallaxMode:"mouse",rtl:!1,deepLink:null,deepLinkType:"path",disablePlugins:[]},this.slides=[],this.activePlugins=[],this.$element=null,this.lastMargin=0,this.leftSpace=0,this.topSpace=0,this.rightSpace=0,this.bottomSpace=0,this._holdOn=0;var i=this;this.resize_listener=function(){i._resize()},t(window).bind("resize",this.resize_listener)},MasterSlider.author="Averta Ltd. (www.averta.net)",MasterSlider.version="2.9.8",MasterSlider.releaseDate="Mar 2015",MasterSlider._plugins=[];var e=MasterSlider;e.registerPlugin=function(t){-1===e._plugins.indexOf(t)&&e._plugins.push(t)};var s=MasterSlider.prototype;s.__setupSlides=function(){var i,e=this,s=0;this.$element.children(".ms-slide").each(function(){var n=t(this);(i=new MSSlide).$element=n,i.slider=e,i.delay=void 0!==n.data("delay")?n.data("delay"):3,i.fillMode=void 0!==n.data("fill-mode")?n.data("fill-mode"):e.options.fillMode,i.index=s++;var o=n.children("img:not(.ms-layer)");o.length>0&&i.setBG(o[0]);var a=n.children("video");if(a.length>0&&i.setBGVideo(a),e.controls)for(var r=0,h=e.controls.length;h>r;++r)e.controls[r].slideAction(i);n.children("a").each(function(){var e=t(this);"video"===this.getAttribute("data-type")?(i.video=this.getAttribute("href"),i.videoAutoPlay=e.data("autoplay"),e.remove()):e.hasClass("ms-layer")||(i.link=t(this))}),e.__createSlideLayers(i,n.find(".ms-layer")),e.slides.push(i),e.slideController.view.addSlide(i)})},s.__createSlideLayers=function(e,s){0!=s.length&&s.each(function(s,n){var o,a=t(this);"A"===n.nodeName&&"image"===a.find(">img").data("type")&&(a=(o=t(this)).find("img"));var r=new(i[a.data("type")||"text"]);r.$element=a,r.link=o;var h={},l={};void 0!==a.data("effect")&&(h.name=a.data("effect")),void 0!==a.data("ease")&&(h.ease=a.data("ease")),void 0!==a.data("duration")&&(h.duration=a.data("duration")),void 0!==a.data("delay")&&(h.delay=a.data("delay")),a.data("hide-effect")&&(l.name=a.data("hide-effect")),a.data("hide-ease")&&(l.ease=a.data("hide-ease")),void 0!==a.data("hide-duration")&&(l.duration=a.data("hide-duration")),void 0!==a.data("hide-time")&&(l.time=a.data("hide-time")),r.setStartAnim(h),r.setEndAnim(l),e.addLayer(r)})},s._removeLoading=function(){t(window).unbind("resize",this.resize_listener),this.$element.removeClass("before-init").css("visibility","visible").css("height","").css("opacity",0),CTween.fadeIn(this.$element),this.$loading.remove(),this.slideController&&this.slideController.__resize()},s._resize=function(){if(this.$loading){var t=this.$loading[0].clientWidth/this.aspect;t=this.options.heightLimit?Math.min(t,this.options.height):t,this.$loading.height(t),this.$element.height(t)}},s._shuffleSlides=function(){for(var t,i=this.$element.children(".ms-slide"),e=0,s=i.length;s>e;++e)e!=(t=Math.floor(Math.random()*(s-1)))&&(this.$element[0].insertBefore(i[e],i[t]),i=this.$element.children(".ms-slide"))},s._setupSliderLayout=function(){this._updateSideMargins(),this.lastMargin=this.leftSpace;var i=this.options.layout;"boxed"!==i&&"partialview"!==i&&(this.options.fullwidth=!0),("fullscreen"===i||"autofill"===i)&&(this.options.fullheight=!0),"partialview"===i&&this.$element.addClass("ms-layout-partialview"),("fullscreen"===i||"fullwidth"===i)&&(t(window).bind("resize",{that:this},this._updateLayout),this._updateLayout()),t(window).bind("resize",this.slideController.resize_listener)},s._updateLayout=function(i){var e=i?i.data.that:this,s=e.options.layout,n=e.$element;"fullscreen"===s&&(document.body.style.overflow="hidden",n.height(t(window).height()-e.options.fullscreenMargin-e.topSpace-e.bottomSpace),document.body.style.overflow=""),n.width(t("body").width()-e.leftSpace-e.rightSpace);var o=-n.offset().left+e.leftSpace+e.lastMargin;n.css("margin-left",o),e.lastMargin=o},s._init=function(){if(!(this._holdOn>0)){if(this.initialized=!0,"all"!==this.options.preload&&this._removeLoading(),this.options.shuffle&&this._shuffleSlides(),MSLayerEffects.setup(),this.slideController.setupView(),this.view=this.slideController.view,this.$controlsCont=t("<div></div>").addClass("ms-inner-controls-cont"),this.options.centerControls&&this.$controlsCont.css("max-width",this.options.width+"px"),this.$controlsCont.prepend(this.view.$element),this.$msContainer=t("<div></div>").addClass("ms-container").prependTo(this.$element).append(this.$controlsCont),this.controls)for(var i=0,e=this.controls.length;e>i;++i)this.controls[i].setup();if(this._setupSliderLayout(),this.__setupSlides(),this.slideController.setup(),this.controls)for(i=0,e=this.controls.length;e>i;++i)this.controls[i].create();if(this.options.autoHeight&&this.slideController.view.$element.height(this.slideController.currentSlide.getHeight()),this.options.swipe&&!window._touch&&this.options.grabCursor&&this.options.mouse){var s=this.view.$element;s.mousedown(function(){s.removeClass("ms-grab-cursor"),s.addClass("ms-grabbing-cursor"),t.browser.msie&&window.ms_grabbing_curosr&&(s[0].style.cursor="url("+window.ms_grabbing_curosr+"), move")}).addClass("ms-grab-cursor"),t(document).mouseup(function(){s.removeClass("ms-grabbing-cursor"),s.addClass("ms-grab-cursor"),t.browser.msie&&window.ms_grab_curosr&&(s[0].style.cursor="url("+window.ms_grab_curosr+"), move")})}this.slideController.__dispatchInit()}},s.setHeight=function(t){this.options.smoothHeight?(this.htween&&(this.htween.reset?this.htween.reset():this.htween.stop(!0)),this.htween=CTween.animate(this.slideController.view.$element,500,{height:t},{ease:"easeOutQuart"})):this.slideController.view.$element.height(t)},s.reserveSpace=function(t,i){var e=t+"Space",s=this[e];return this[e]+=i,this._updateSideMargins(),s},s._updateSideMargins=function(){this.$element.css("margin",this.topSpace+"px "+this.rightSpace+"px "+this.bottomSpace+"px "+this.leftSpace+"px")},s._realignControls=function(){this.rightSpace=this.leftSpace=this.topSpace=this.bottomSpace=0,this._updateSideMargins(),this.api.dispatchEvent(new MSSliderEvent(MSSliderEvent.RESERVED_SPACE_CHANGE))},s.control=function(t,i){if(t in MSSlideController.SliderControlList){this.controls||(this.controls=[]);var e=new MSSlideController.SliderControlList[t](i);return e.slider=this,this.controls.push(e),this}},s.holdOn=function(){this._holdOn++},s.release=function(){this._holdOn--,this._init()},s.setup=function(i,s){if(this.$element="string"==typeof i?t("#"+i):i.eq(0),this.setupMarkup=this.$element.html(),0!==this.$element.length){this.$element.addClass("master-slider").addClass("before-init"),t.browser.msie?this.$element.addClass("ms-ie").addClass("ms-ie"+t.browser.version.slice(0,t.browser.version.indexOf("."))):t.browser.webkit?this.$element.addClass("ms-wk"):t.browser.mozilla&&this.$element.addClass("ms-moz"),navigator.userAgent.toLowerCase().indexOf("android")>-1&&this.$element.addClass("ms-android");var n=this;t.extend(this.options,s),this.aspect=this.options.width/this.options.height,this.$loading=t("<div></div>").addClass("ms-loading-container").insertBefore(this.$element).append(t("<div></div>").addClass("ms-loading")),this.$loading.parent().css("position","relative"),this.options.autofill&&(this.options.fullwidth=!0,this.options.fullheight=!0),this.options.fullheight&&this.$element.addClass("ms-fullheight"),this._resize(),this.slideController=new MSSlideController(this),this.api=this.slideController;for(var o=0,a=e._plugins.length;o!==a;o++){var r=e._plugins[o];-1===this.options.disablePlugins.indexOf(r.name)&&this.activePlugins.push(new r(this))}return t(document).ready(function(){n._init()}),this}},s.destroy=function(i){for(var e=0,s=this.activePlugins.length;e!==s;e++)this.activePlugins[e].destroy();if(this.controls)for(e=0,s=this.controls.length;e!==s;e++)this.controls[e].destroy();this.slideController&&this.slideController._destroy(),this.$loading&&this.$loading.remove(),i?this.$element.html(this.setupMarkup).css("visibility","hidden"):this.$element.remove();var n=this.options.layout;("fullscreen"===n||"fullwidth"===n)&&t(window).unbind("resize",this._updateLayout),this.view=null,this.slides=null,this.options=null,this.slideController=null,this.api=null,this.resize_listener=null,this.activePlugins=null}}(jQuery),function(t,i,e,s){function n(i,e){this.element=i,this.$element=t(i),this.settings=t.extend({},a,e),this._defaults=a,this._name=o,this.init()}var o="masterslider",a={controls:{}};t.extend(n.prototype,{init:function(){var t=this;for(var i in this._slider=new MasterSlider,this.settings.controls)this._slider.control(i,this.settings.controls[i]);this._slider.setup(this.$element,this.settings);var e=this._slider.api.dispatchEvent;this._slider.api.dispatchEvent=function(i){t.$element.trigger(i.type),e.call(this,i)}},api:function(){return this._slider.api},slider:function(){return this._slider}}),t.fn[o]=function(i){var e,s=arguments,a="plugin_"+o;return void 0===i||"object"==typeof i?this.each(function(){t.data(this,a)||t.data(this,a,new n(this,i))}):"string"==typeof i&&"_"!==i[0]&&"init"!==i?(this.each(function(){var o=t.data(this,a);o instanceof n&&"function"==typeof o[i]&&(e=o[i].apply(o,Array.prototype.slice.call(s,1))),o instanceof n&&"function"==typeof o._slider.api[i]&&(e=o._slider.api[i].apply(o._slider.api,Array.prototype.slice.call(s,1))),"destroy"===i&&t.data(this,a,null)}),void 0!==e?e:this):void 0}}(jQuery,window,document),window.MSViewEvents=function(t,i){this.type=t,this.data=i},MSViewEvents.SWIPE_START="swipeStart",MSViewEvents.SWIPE_END="swipeEnd",MSViewEvents.SWIPE_MOVE="swipeMove",MSViewEvents.SWIPE_CANCEL="swipeCancel",MSViewEvents.SCROLL="scroll",MSViewEvents.CHANGE_START="slideChangeStart",MSViewEvents.CHANGE_END="slideChangeEnd",function(t){"use strict";window.MSBasicView=function(i){this.options={loop:!1,dir:"h",autoHeight:!1,spacing:5,mouseSwipe:!0,swipe:!0,speed:17,minSlideSpeed:2,viewNum:20,critMargin:1},t.extend(this.options,i),this.dir=this.options.dir,this.loop=this.options.loop,this.spacing=this.options.spacing,this.__width=0,this.__height=0,this.__cssProb="h"===this.dir?"left":"top",this.__offset="h"===this.dir?"offsetLeft":"offsetTop",this.__dimension="h"===this.dir?"__width":"__height",this.__translate_end=window._css3d?" translateZ(0px)":"",this.$slideCont=t("<div></div>").addClass("ms-slide-container"),this.$element=t("<div></div>").addClass("ms-view").addClass("ms-basic-view").append(this.$slideCont),this.currentSlide=null,this.index=-1,this.slidesCount=0,this.slides=[],this.slideList=[],this.viewSlidesList=[],this.css3=window._cssanim,this.start_buffer=0,this.firstslide_snap=0,this.slideChanged=!1,this.controller=new Controller(0,0,{snapping:!0,snapsize:100,paging:!0,snappingMinSpeed:this.options.minSlideSpeed,friction:(100-.5*this.options.speed)/100,endless:this.loop}),this.controller.renderCallback("h"===this.dir?this._horizUpdate:this._vertiUpdate,this),this.controller.snappingCallback(this.__snapUpdate,this),this.controller.snapCompleteCallback(this.__snapCompelet,this),averta.EventDispatcher.call(this)};var i=MSBasicView.prototype;i.__snapCompelet=function(){this.slideChanged&&(this.slideChanged=!1,this.__locateSlides(),this.start_buffer=0,this.dispatchEvent(new MSViewEvents(MSViewEvents.CHANGE_END)))},i.__snapUpdate=function(i,e,s){if(this.loop){var n=this.index+s;this.updateLoop(n),n>=this.slidesCount&&(n-=this.slidesCount),0>n&&(n=this.slidesCount+n),this.index=n}else{if(0>e||e>=this.slidesCount)return;this.index=e}this._checkCritMargins(),t.browser.mozilla&&(this.slideList[this.index].$element[0].style.marginTop="0.1px",this.currentSlide&&(this.currentSlide.$element[0].style.marginTop=""));var o=this.slideList[this.index];o!==this.currentSlide&&(this.currentSlide=o,this.__updateSlidesZindex(),this.slideChanged=!0,this.dispatchEvent(new MSViewEvents(MSViewEvents.CHANGE_START)))},i._checkCritMargins=function(){if(!this.normalMode){var t=Math.floor(this.options.viewNum/2),i=this.viewSlidesList.indexOf(this.slideList[this.index]),e=this[this.__dimension]+this.spacing,s=this.options.critMargin;return this.loop?void((s>=i||i>=this.viewSlidesList.length-s)&&(e*=i-t,this.__locateSlides(!1,e+this.start_buffer),this.start_buffer+=e)):void((s>i&&this.index>=s||i>=this.viewSlidesList.length-s&&this.index<this.slidesCount-s)&&this.__locateSlides(!1))}},i._vertiUpdate=function(t,i){return this.__contPos=i,this.dispatchEvent(new MSViewEvents(MSViewEvents.SCROLL)),this.css3?void(this.$slideCont[0].style[window._jcsspfx+"Transform"]="translateY("+-i+"px)"+this.__translate_end):void(this.$slideCont[0].style.top=-i+"px")},i._horizUpdate=function(t,i){return this.__contPos=i,this.dispatchEvent(new MSViewEvents(MSViewEvents.SCROLL)),this.css3?void(this.$slideCont[0].style[window._jcsspfx+"Transform"]="translateX("+-i+"px)"+this.__translate_end):void(this.$slideCont[0].style.left=-i+"px")},i.__updateViewList=function(){if(this.normalMode)this.viewSlidesList=this.slides;else{var t=this.viewSlidesList.slice();this.viewSlidesList=[];var i,e=0,s=Math.floor(this.options.viewNum/2);if(this.loop)for(;e!==this.options.viewNum;e++)this.viewSlidesList.push(this.slides[this.currentSlideLoc-s+e]);else{for(e=0;e!==s&&this.index-e!=-1;e++)this.viewSlidesList.unshift(this.slideList[this.index-e]);for(e=1;e!==s&&this.index+e!==this.slidesCount;e++)this.viewSlidesList.push(this.slideList[this.index+e])}for(e=0,i=t.length;e!==i;e++)-1===this.viewSlidesList.indexOf(t[e])&&t[e].sleep();t=null,this.currentSlide&&this.__updateSlidesZindex()}},i.__locateSlides=function(t,i){this.__updateViewList(),i=this.loop?i||0:this.slides.indexOf(this.viewSlidesList[0])*(this[this.__dimension]+this.spacing);for(var e,s=this.viewSlidesList.length,n=0;n!==s;n++){var o=i+n*(this[this.__dimension]+this.spacing);(e=this.viewSlidesList[n]).wakeup(),e.position=o,e.$element[0].style[this.__cssProb]=o+"px"}!1!==t&&this.controller.changeTo(this.slideList[this.index].position,!1,null,null,!1)},i.__createLoopList=function(){var t=[],i=0,e=this.slidesCount/2,s=this.slidesCount%2==0?e-1:Math.floor(e),n=this.slidesCount%2==0?e:Math.floor(e);for(this.currentSlideLoc=s,i=1;s>=i;++i)t.unshift(this.slideList[this.index-i<0?this.slidesCount-i+this.index:this.index-i]);for(t.push(this.slideList[this.index]),i=1;n>=i;++i)t.push(this.slideList[this.index+i>=this.slidesCount?this.index+i-this.slidesCount:this.index+i]);return t},i.__getSteps=function(t,i){var e=t>i?this.slidesCount-t+i:i-t,s=Math.abs(this.slidesCount-e);return s>e?e:-s},i.__pushEnd=function(){var t=this.slides.shift(),i=this.slides[this.slidesCount-2];if(this.slides.push(t),this.normalMode){var e=i.$element[0][this.__offset]+this.spacing+this[this.__dimension];t.$element[0].style[this.__cssProb]=e+"px",t.position=e}},i.__pushStart=function(){var t=this.slides.pop(),i=this.slides[0];if(this.slides.unshift(t),this.normalMode){var e=i.$element[0][this.__offset]-this.spacing-this[this.__dimension];t.$element[0].style[this.__cssProb]=e+"px",t.position=e}},i.__updateSlidesZindex=function(){if(this.autoUpdateZIndex){var t=this.viewSlidesList.length;if(Math.floor(t/2),this.loop)for(var i=this.viewSlidesList.indexOf(this.currentSlide),e=0;e!==t;e++)this.viewSlidesList[e],this.viewSlidesList[e].$element.css("z-index",i>=e?e+1:t-e);else{var s=this.currentSlide.index-this.viewSlidesList[0].index;for(e=0;e!==t;e++)this.viewSlidesList[e].$element.css("z-index",s>=e?e+1:t-e);this.currentSlide.$element.css("z-index",t)}}},i.addSlide=function(t){t.view=this,this.slides.push(t),this.slideList.push(t),this.slidesCount++},i.appendSlide=function(t){this.$slideCont.append(t.$element)},i.updateLoop=function(t){if(this.loop)for(var i=this.__getSteps(this.index,t),e=0,s=Math.abs(i);s>e;++e)0>i?this.__pushStart():this.__pushEnd()},i.gotoSlide=function(t,i){this.updateLoop(t),this.index=t;var e=this.slideList[t];this._checkCritMargins(),this.controller.changeTo(e.position,!i,null,null,!1),e!==this.currentSlide&&(this.slideChanged=!0,this.currentSlide=e,this.__updateSlidesZindex(),this.dispatchEvent(new MSViewEvents(MSViewEvents.CHANGE_START)),i&&this.dispatchEvent(new MSViewEvents(MSViewEvents.CHANGE_END)))},i.next=function(t){return t&&!this.loop&&this.index+1>=this.slidesCount?void this.controller.bounce(10):void this.gotoSlide(this.index+1>=this.slidesCount?0:this.index+1)},i.previous=function(t){return t&&!this.loop&&this.index-1<0?void this.controller.bounce(-10):void this.gotoSlide(this.index-1<0?this.slidesCount-1:this.index-1)},i.setupSwipe=function(){this.swipeControl=new averta.TouchSwipe(this.$element),this.swipeControl.swipeType="h"===this.dir?"horizontal":"vertical";var t=this;this.swipeControl.onSwipe="h"===this.dir?function(i){t.horizSwipeMove(i)}:function(i){t.vertSwipeMove(i)}},i.vertSwipeMove=function(t){var i=t.phase;if("start"===i)this.controller.stop(),this.dispatchEvent(new MSViewEvents(MSViewEvents.SWIPE_START,t));else if("move"===i&&(!this.loop||Math.abs(this.currentSlide.position-this.controller.value+t.moveY)<this.cont_size/2))this.controller.drag(t.moveY),this.dispatchEvent(new MSViewEvents(MSViewEvents.SWIPE_MOVE,t));else if("end"===i||"cancel"===i){var e=t.distanceY/t.duration*50/3;Math.abs(e)>.1?(this.controller.push(-e),e>this.controller.options.snappingMinSpeed&&this.dispatchEvent(new MSViewEvents(MSViewEvents.SWIPE_END,t))):(this.controller.cancel(),this.dispatchEvent(new MSViewEvents(MSViewEvents.SWIPE_CANCEL,t)))}},i.horizSwipeMove=function(t){var i=t.phase;if("start"===i)this.controller.stop(),this.dispatchEvent(new MSViewEvents(MSViewEvents.SWIPE_START,t));else if("move"===i&&(!this.loop||Math.abs(this.currentSlide.position-this.controller.value+t.moveX)<this.cont_size/2))this.controller.drag(t.moveX),this.dispatchEvent(new MSViewEvents(MSViewEvents.SWIPE_MOVE,t));else if("end"===i||"cancel"===i){var e=t.distanceX/t.duration*50/3;Math.abs(e)>.1?(this.controller.push(-e),e>this.controller.options.snappingMinSpeed&&this.dispatchEvent(new MSViewEvents(MSViewEvents.SWIPE_END,t))):(this.controller.cancel(),this.dispatchEvent(new MSViewEvents(MSViewEvents.SWIPE_CANCEL,t)))}},i.setSize=function(t,i,e){if(this.lastWidth!==t||i!==this.lastHeight||e){this.$element.width(t).height(i);for(var s=0;s<this.slidesCount;++s)this.slides[s].setSize(t,i,e);this.__width=t,this.__height=i,this.__created&&(this.__locateSlides(),this.cont_size=(this.slidesCount-1)*(this[this.__dimension]+this.spacing),this.loop||(this.controller._max_value=this.cont_size),this.controller.options.snapsize=this[this.__dimension]+this.spacing,this.controller.changeTo(this.currentSlide.position,!1,null,null,!1),this.controller.cancel(),this.lastWidth=t,this.lastHeight=i)}},i.create=function(t){this.__created=!0,this.index=Math.min(t||0,this.slidesCount-1),this.lastSnap=this.index,this.loop&&(this.slides=this.__createLoopList()),this.normalMode=this.slidesCount<=this.options.viewNum;for(var i=0;i<this.slidesCount;++i)this.slides[i].create();this.__locateSlides(),this.controller.options.snapsize=this[this.__dimension]+this.spacing,this.loop||(this.controller._max_value=(this.slidesCount-1)*(this[this.__dimension]+this.spacing)),this.gotoSlide(this.index,!0),this.options.swipe&&(window._touch||this.options.mouseSwipe)&&this.setupSwipe()},i.destroy=function(){if(this.__created){for(var t=0;t<this.slidesCount;++t)this.slides[t].destroy();this.slides=null,this.slideList=null,this.$element.remove(),this.controller.destroy(),this.controller=null}},averta.EventDispatcher.extend(i),MSSlideController.registerView("basic",MSBasicView)}(jQuery),function(){"use strict";window.MSWaveView=function(t){MSBasicView.call(this,t),this.$element.removeClass("ms-basic-view").addClass("ms-wave-view"),this.$slideCont.css(window._csspfx+"transform-style","preserve-3d"),this.autoUpdateZIndex=!0},MSWaveView.extend(MSBasicView),MSWaveView._3dreq=!0,MSWaveView._fallback=MSBasicView;var t=MSWaveView.prototype,i=MSBasicView.prototype;t._horizUpdate=function(t,e){i._horizUpdate.call(this,t,e);for(var s,n,o=-e,a=0;a<this.slidesCount;++a)n=-o-(s=this.slideList[a]).position,this.__updateSlidesHoriz(s,n)},t._vertiUpdate=function(t,e){i._vertiUpdate.call(this,t,e);for(var s,n,o=-e,a=0;a<this.slidesCount;++a)n=-o-(s=this.slideList[a]).position,this.__updateSlidesVertic(s,n)},t.__updateSlidesHoriz=function(t,i){var e=Math.abs(100*i/this.__width);t.$element.css(window._csspfx+"transform","translateZ("+3*-e+"px) rotateY(0.01deg)")},t.__updateSlidesVertic=function(t,i){this.__updateSlidesHoriz(t,i)},MSSlideController.registerView("wave",MSWaveView)}(jQuery),function(){window.MSFadeBasicView=function(t){MSWaveView.call(this,t),this.$element.removeClass("ms-wave-view").addClass("ms-fade-basic-view")},MSFadeBasicView.extend(MSWaveView);var t=MSFadeBasicView.prototype;MSFadeBasicView.prototype,t.__updateSlidesHoriz=function(t,i){var e=Math.abs(.6*i/this.__width);e=1-Math.min(e,.6),t.$element.css("opacity",e)},t.__updateSlidesVertic=function(t,i){this.__updateSlidesHoriz(t,i)},MSSlideController.registerView("fadeBasic",MSFadeBasicView),MSWaveView._fallback=MSFadeBasicView}(),function(){window.MSFadeWaveView=function(t){MSWaveView.call(this,t),this.$element.removeClass("ms-wave-view").addClass("ms-fade-wave-view")},MSFadeWaveView.extend(MSWaveView),MSFadeWaveView._3dreq=!0,MSFadeWaveView._fallback=MSFadeBasicView;var t=MSFadeWaveView.prototype;MSWaveView.prototype,t.__updateSlidesHoriz=function(t,i){var e=Math.abs(100*i/this.__width);e=Math.min(e,100),t.$element.css("opacity",1-e/300),t.$element[0].style[window._jcsspfx+"Transform"]="scale("+(1-e/800)+") rotateY(0.01deg) "},t.__updateSlidesVertic=function(t,i){this.__updateSlidesHoriz(t,i)},MSSlideController.registerView("fadeWave",MSFadeWaveView)}(),function(){"use strict";window.MSFlowView=function(t){MSWaveView.call(this,t),this.$element.removeClass("ms-wave-view").addClass("ms-flow-view")},MSFlowView.extend(MSWaveView),MSFlowView._3dreq=!0,MSFlowView._fallback=MSFadeBasicView;var t=MSFlowView.prototype;MSWaveView.prototype,t.__updateSlidesHoriz=function(t,i){var e=Math.abs(100*i/this.__width),s=Math.min(.3*e,30)*(0>i?-1:1),n=1.2*e;t.$element[0].style[window._jcsspfx+"Transform"]="translateZ("+5*-n+"px) rotateY("+s+"deg) "},t.__updateSlidesVertic=function(t,i){var e=Math.abs(100*i/this.__width),s=Math.min(.3*e,30)*(0>i?-1:1),n=1.2*e;t.$element[0].style[window._jcsspfx+"Transform"]="translateZ("+5*-n+"px) rotateX("+-s+"deg) "},MSSlideController.registerView("flow",MSFlowView)}(jQuery),function(){window.MSFadeFlowView=function(t){MSWaveView.call(this,t),this.$element.removeClass("ms-wave-view").addClass("ms-fade-flow-view")},MSFadeFlowView.extend(MSWaveView),MSFadeFlowView._3dreq=!0;var t=MSFadeFlowView.prototype;MSWaveView.prototype,t.__calculate=function(t){var i=Math.min(Math.abs(100*t/this.__width),100);return{value:i,rvalue:Math.min(.5*i,50)*(0>t?-1:1)}},t.__updateSlidesHoriz=function(t,i){var e=this.__calculate(i);t.$element.css("opacity",1-e.value/300),t.$element[0].style[window._jcsspfx+"Transform"]="translateZ("+-e.value+"px) rotateY("+e.rvalue+"deg) "},t.__updateSlidesVertic=function(t,i){var e=this.__calculate(i);t.$element.css("opacity",1-e.value/300),t.$element[0].style[window._jcsspfx+"Transform"]="translateZ("+-e.value+"px) rotateX("+-e.rvalue+"deg) "},MSSlideController.registerView("fadeFlow",MSFadeFlowView)}(),function(t){"use strict";window.MSMaskView=function(t){MSBasicView.call(this,t),this.$element.removeClass("ms-basic-view").addClass("ms-mask-view")},MSMaskView.extend(MSBasicView);var i=MSMaskView.prototype,e=MSBasicView.prototype;i.addSlide=function(i){i.view=this,i.$frame=t("<div></div>").addClass("ms-mask-frame").append(i.$element),i.$element[0].style.position="relative",i.autoAppend=!1,this.slides.push(i),this.slideList.push(i),this.slidesCount++},i.setSize=function(t,i){for(var s=this.slides[0].slider,n=0;n<this.slidesCount;++n)this.slides[n].$frame[0].style.width=t+"px",s.options.autoHeight||(this.slides[n].$frame[0].style.height=i+"px");e.setSize.call(this,t,i)},i._horizUpdate=function(t,i){e._horizUpdate.call(this,t,i);var s=0;if(this.css3)for(s=0;s<this.slidesCount;++s)this.slideList[s].$element[0].style[window._jcsspfx+"Transform"]="translateX("+(i-this.slideList[s].position)+"px)"+this.__translate_end;else for(s=0;s<this.slidesCount;++s)this.slideList[s].$element[0].style.left=i-this.slideList[s].position+"px"},i._vertiUpdate=function(t,i){e._vertiUpdate.call(this,t,i);var s=0;if(this.css3)for(s=0;s<this.slidesCount;++s)this.slideList[s].$element[0].style[window._jcsspfx+"Transform"]="translateY("+(i-this.slideList[s].position)+"px)"+this.__translate_end;else for(s=0;s<this.slidesCount;++s)this.slideList[s].$element[0].style.top=i-this.slideList[s].position+"px"},i.__pushEnd=function(){var t=this.slides.shift(),i=this.slides[this.slidesCount-2];if(this.slides.push(t),this.normalMode){var e=i.$frame[0][this.__offset]+this.spacing+this[this.__dimension];t.$frame[0].style[this.__cssProb]=e+"px",t.position=e}},i.__pushStart=function(){var t=this.slides.pop(),i=this.slides[0];if(this.slides.unshift(t),this.normalMode){var e=i.$frame[0][this.__offset]-this.spacing-this[this.__dimension];t.$frame[0].style[this.__cssProb]=e+"px",t.position=e}},i.__updateViewList=function(){if(this.normalMode)this.viewSlidesList=this.slides;else{var t=this.viewSlidesList.slice();this.viewSlidesList=[];var i,e=0,s=Math.floor(this.options.viewNum/2);if(this.loop)for(;e!==this.options.viewNum;e++)this.viewSlidesList.push(this.slides[this.currentSlideLoc-s+e]);else{for(e=0;e!==s&&this.index-e!=-1;e++)this.viewSlidesList.unshift(this.slideList[this.index-e]);for(e=1;e!==s&&this.index+e!==this.slidesCount;e++)this.viewSlidesList.push(this.slideList[this.index+e])}for(e=0,i=t.length;e!==i;e++)-1===this.viewSlidesList.indexOf(t[e])&&(t[e].sleep(),t[e].$frame.detach());t=null}},i.__locateSlides=function(t,i){this.__updateViewList(),i=this.loop?i||0:this.slides.indexOf(this.viewSlidesList[0])*(this[this.__dimension]+this.spacing);for(var e,s=this.viewSlidesList.length,n=0;n!==s;n++){var o=i+n*(this[this.__dimension]+this.spacing);if(e=this.viewSlidesList[n],this.$slideCont.append(e.$frame),e.wakeup(!1),e.position=o,e.selected&&e.bgvideo)try{e.bgvideo.play()}catch(t){}e.$frame[0].style[this.__cssProb]=o+"px"}!1!==t&&this.controller.changeTo(this.slideList[this.index].position,!1,null,null,!1)},MSSlideController.registerView("mask",MSMaskView)}(jQuery),function(){"use strict";window.MSParallaxMaskView=function(t){MSMaskView.call(this,t),this.$element.removeClass("ms-basic-view").addClass("ms-parallax-mask-view")},MSParallaxMaskView.extend(MSMaskView),MSParallaxMaskView.parallaxAmount=.5;var t=MSParallaxMaskView.prototype,i=MSBasicView.prototype;t._horizUpdate=function(t,e){i._horizUpdate.call(this,t,e);var s=0;if(this.css3)for(s=0;s<this.slidesCount;++s)this.slideList[s].$element[0].style[window._jcsspfx+"Transform"]="translateX("+(e-this.slideList[s].position)*MSParallaxMaskView.parallaxAmount+"px)"+this.__translate_end;else for(s=0;s<this.slidesCount;++s)this.slideList[s].$element[0].style.left=(e-this.slideList[s].position)*MSParallaxMaskView.parallaxAmount+"px"},t._vertiUpdate=function(t,e){i._vertiUpdate.call(this,t,e);var s=0;if(this.css3)for(s=0;s<this.slidesCount;++s)this.slideList[s].$element[0].style[window._jcsspfx+"Transform"]="translateY("+(e-this.slideList[s].position)*MSParallaxMaskView.parallaxAmount+"px)"+this.__translate_end;else for(s=0;s<this.slidesCount;++s)this.slideList[s].$element[0].style.top=(e-this.slideList[s].position)*MSParallaxMaskView.parallaxAmount+"px"},MSSlideController.registerView("parallaxMask",MSParallaxMaskView)}(jQuery),function(){"use strict";window.MSFadeView=function(t){MSBasicView.call(this,t),this.$element.removeClass("ms-basic-view").addClass("ms-fade-view"),this.controller.renderCallback(this.__update,this)},MSFadeView.extend(MSBasicView);var t=MSFadeView.prototype,i=MSBasicView.prototype;t.__update=function(t,i){for(var e,s,n=-i,o=0;o<this.slidesCount;++o)s=-n-(e=this.slideList[o]).position,this.__updateSlides(e,s)},t.__updateSlides=function(t,i){var e=Math.abs(i/this[this.__dimension]);0>=1-e?t.$element.fadeTo(0,0).css("visibility","hidden"):t.$element.fadeTo(0,1-e).css("visibility","")},t.__locateSlides=function(t,i){this.__updateViewList(),i=this.loop?i||0:this.slides.indexOf(this.viewSlidesList[0])*(this[this.__dimension]+this.spacing);for(var e,s=this.viewSlidesList.length,n=0;n!==s;n++){var o=i+n*this[this.__dimension];(e=this.viewSlidesList[n]).wakeup(),e.position=o}!1!==t&&this.controller.changeTo(this.slideList[this.index].position,!1,null,null,!1)},t.__pushEnd=function(){var t=this.slides.shift(),i=this.slides[this.slidesCount-2];this.slides.push(t),t.position=i.position+this[this.__dimension]},t.__pushStart=function(){var t=this.slides.pop(),i=this.slides[0];this.slides.unshift(t),t.position=i.position-this[this.__dimension]},t.create=function(t){i.create.call(this,t),this.spacing=0,this.controller.options.minValidDist=10},MSSlideController.registerView("fade",MSFadeView)}(jQuery),function(){"use strict";window.MSScaleView=function(t){MSBasicView.call(this,t),this.$element.removeClass("ms-basic-view").addClass("ms-scale-view"),this.controller.renderCallback(this.__update,this)},MSScaleView.extend(MSFadeView);var t=MSScaleView.prototype,i=MSFadeView.prototype;t.__updateSlides=function(t,i){var e=Math.abs(i/this[this.__dimension]),s=t.$element[0];0>=1-e?(s.style.opacity=0,s.style.visibility="hidden",s.style[window._jcsspfx+"Transform"]=""):(s.style.opacity=1-e,s.style.visibility="",s.style[window._jcsspfx+"Transform"]="perspective(2000px) translateZ("+e*(0>i?-.5:.5)*300+"px)")},t.create=function(t){i.create.call(this,t),this.controller.options.minValidDist=.03},MSSlideController.registerView("scale",MSScaleView)}(jQuery),function(){"use strict";window.MSFocusView=function(t){MSWaveView.call(this,t),this.$element.removeClass("ms-wave-view").addClass("ms-focus-view"),this.options.centerSpace=this.options.centerSpace||1},MSFocusView.extend(MSWaveView),MSFocusView._3dreq=!0,MSFocusView._fallback=MSFadeBasicView;var t=MSFocusView.prototype;MSWaveView.prototype,t.__calcview=function(t,i){return i/2*t/(t+2e3)*(t+2e3)/2e3},t.__updateSlidesHoriz=function(t,i){var e=Math.abs(100*i/this.__width);e=15*-Math.min(e,100),t.$element.css(window._csspfx+"transform","translateZ("+(e+1)+"px) rotateY(0.01deg) translateX("+(0>i?1:-1)*-this.__calcview(e,this.__width)*this.options.centerSpace+"px)")},t.__updateSlidesVertic=function(t,i){var e=Math.abs(100*i/this.__width);e=15*-Math.min(e,100),t.$element.css(window._csspfx+"transform","translateZ("+(e+1)+"px) rotateY(0.01deg) translateY("+(0>i?1:-1)*-this.__calcview(e,this.__width)*this.options.centerSpace+"px)")},MSSlideController.registerView("focus",MSFocusView)}(),function(){window.MSPartialWaveView=function(t){MSWaveView.call(this,t),this.$element.removeClass("ms-wave-view").addClass("ms-partial-wave-view")},MSPartialWaveView.extend(MSWaveView),MSPartialWaveView._3dreq=!0,MSPartialWaveView._fallback=MSFadeBasicView;var t=MSPartialWaveView.prototype;MSWaveView.prototype,t.__updateSlidesHoriz=function(t,i){var e=Math.abs(100*i/this.__width);t.hasBG&&t.$bg_img.css("opacity",(100-Math.abs(120*i/this.__width/3))/100),t.$element.css(window._csspfx+"transform","translateZ("+3*-e+"px) rotateY(0.01deg) translateX("+.75*i+"px)")},t.__updateSlidesVertic=function(t,i){var e=Math.abs(100*i/this.__width);t.hasBG&&t.$bg_img.css("opacity",(100-Math.abs(120*i/this.__width/3))/100),t.$element.css(window._csspfx+"transform","translateZ("+3*-e+"px) rotateY(0.01deg) translateY("+.75*i+"px)")},MSSlideController.registerView("partialWave",MSPartialWaveView)}(),function(t){"use strict";var i=function(){this.options={prefix:"ms-",autohide:!0,overVideo:!0}},e=i.prototype;e.slideAction=function(){},e.setup=function(){this.cont=this.options.insertTo?t(this.options.insertTo):this.slider.$controlsCont,this.options.overVideo||this._hideOnvideoStarts()},e.checkHideUnder=function(){this.options.hideUnder&&(this.needsRealign=!this.options.insetTo&&("left"===this.options.align||"right"===this.options.align)&&!1===this.options.inset,t(window).bind("resize",{that:this},this.onResize),this.onResize())},e.onResize=function(t){var i=t&&t.data.that||this,e=window.innerWidth;e<=i.options.hideUnder&&!i.detached?(i.hide(!0),i.detached=!0,i.onDetach()):e>=i.options.hideUnder&&i.detached&&(i.detached=!1,i.visible(),i.onAppend())},e.create=function(){this.options.autohide&&!window._touch&&(this.hide(!0),this.slider.$controlsCont.mouseenter(t.proxy(this._onMouseEnter,this)).mouseleave(t.proxy(this._onMouseLeave,this)).mousedown(t.proxy(this._onMouseDown,this)),this.$element&&this.$element.mouseenter(t.proxy(this._onMouseEnter,this)).mouseleave(t.proxy(this._onMouseLeave,this)).mousedown(t.proxy(this._onMouseDown,this)),t(document).mouseup(t.proxy(this._onMouseUp,this))),this.options.align&&this.$element.addClass("ms-align-"+this.options.align)},e._onMouseEnter=function(){this._disableAH||this.mdown||this.visible(),this.mleave=!1},e._onMouseLeave=function(){this.mdown||this.hide(),this.mleave=!0},e._onMouseDown=function(){this.mdown=!0},e._onMouseUp=function(){this.mdown&&this.mleave&&this.hide(),this.mdown=!1},e.onAppend=function(){this.needsRealign&&this.slider._realignControls()},e.onDetach=function(){this.needsRealign&&this.slider._realignControls()},e._hideOnvideoStarts=function(){var t=this;this.slider.api.addEventListener(MSSliderEvent.VIDEO_PLAY,function(){t._disableAH=!0,t.hide()}),this.slider.api.addEventListener(MSSliderEvent.VIDEO_CLOSE,function(){t._disableAH=!1,t.visible()})},e.hide=function(t){if(t)this.$element.css("opacity",0),this.$element.css("display","none");else{clearTimeout(this.hideTo);var i=this.$element;this.hideTo=setTimeout(function(){CTween.fadeOut(i,400,!1)},20)}this.$element.addClass("ms-ctrl-hide")},e.visible=function(){this.detached||(clearTimeout(this.hideTo),this.$element.css("display",""),CTween.fadeIn(this.$element,400,!1),this.$element.removeClass("ms-ctrl-hide"))},e.destroy=function(){this.options&&this.options.hideUnder&&t(window).unbind("resize",this.onResize)},window.BaseControl=i}(jQuery),function(t){"use strict";var i=function(i){BaseControl.call(this),t.extend(this.options,i)};i.extend(BaseControl);var e=i.prototype,s=BaseControl.prototype;e.setup=function(){var i=this;this.$next=t("<div></div>").addClass(this.options.prefix+"nav-next").bind("click",function(){i.slider.api.next(!0)}),this.$prev=t("<div></div>").addClass(this.options.prefix+"nav-prev").bind("click",function(){i.slider.api.previous(!0)}),s.setup.call(this),this.cont.append(this.$next),this.cont.append(this.$prev),this.checkHideUnder()},e.hide=function(t){return t?(this.$prev.css("opacity",0).css("display","none"),void this.$next.css("opacity",0).css("display","none")):(CTween.fadeOut(this.$prev,400,!1),CTween.fadeOut(this.$next,400,!1),this.$prev.addClass("ms-ctrl-hide"),void this.$next.addClass("ms-ctrl-hide"))},e.visible=function(){this.detached||(CTween.fadeIn(this.$prev,400),CTween.fadeIn(this.$next,400),this.$prev.removeClass("ms-ctrl-hide").css("display",""),this.$next.removeClass("ms-ctrl-hide").css("display",""))},e.destroy=function(){s.destroy(),this.$next.remove(),this.$prev.remove()},window.MSArrows=i,MSSlideController.registerControl("arrows",i)}(jQuery),function(t){"use strict";var i=function(i){BaseControl.call(this),this.options.dir="h",this.options.wheel="v"===i.dir,this.options.arrows=!0,this.options.speed=17,this.options.align=null,this.options.inset=!1,this.options.margin=10,this.options.space=10,this.options.width=100,this.options.height=100,this.options.type="thumbs",t.extend(this.options,i),this.thumbs=[],this.index_count=0,this.__dimen="h"===this.options.dir?"width":"height",this.__alignsize="h"===this.options.dir?"height":"width",this.__jdimen="h"===this.options.dir?"outerWidth":"outerHeight",this.__pos="h"===this.options.dir?"left":"top",this.click_enable=!0};i.extend(BaseControl);var e=i.prototype,s=BaseControl.prototype;e.setup=function(){if(this.$element=t("<div></div>").addClass(this.options.prefix+"thumb-list"),"tabs"===this.options.type&&this.$element.addClass(this.options.prefix+"tabs"),this.$element.addClass("ms-dir-"+this.options.dir),s.setup.call(this),this.$element.appendTo(this.slider.$controlsCont===this.cont?this.slider.$element:this.cont),this.$thumbscont=t("<div></div>").addClass("ms-thumbs-cont").appendTo(this.$element),this.options.arrows){var i=this;this.$fwd=t("<div></div>").addClass("ms-thumblist-fwd").appendTo(this.$element).click(function(){i.controller.push(-15)}),this.$bwd=t("<div></div>").addClass("ms-thumblist-bwd").appendTo(this.$element).click(function(){i.controller.push(15)})}if(!this.options.insetTo&&this.options.align){var e=this.options.align;this.options.inset?this.$element.css(e,this.options.margin):"top"===e?this.$element.detach().prependTo(this.slider.$element).css({"margin-bottom":this.options.margin,position:"relative"}):"bottom"===e?this.$element.css({"margin-top":this.options.margin,position:"relative"}):(this.slider.api.addEventListener(MSSliderEvent.RESERVED_SPACE_CHANGE,this.align,this),this.align()),"v"===this.options.dir?this.$element.width(this.options.width):this.$element.height(this.options.height)}this.checkHideUnder()},e.align=function(){if(!this.detached){var t=this.options.align,i=this.slider.reserveSpace(t,this.options[this.__alignsize]+2*this.options.margin);this.$element.css(t,-i-this.options[this.__alignsize]-this.options.margin)}},e.slideAction=function(i){var e=i.$element.find(".ms-thumb"),s=this,n=t("<div></div>").addClass("ms-thumb-frame").append(e).append(t('<div class="ms-thumb-ol"></div>')).bind("click",function(){s.changeSlide(n)});if(this.options.align&&n.width(this.options.width-("v"===this.options.dir?12:0)).height(this.options.height).css("margin-"+("v"===this.options.dir?"bottom":"right"),this.options.space),n[0].index=this.index_count++,this.$thumbscont.append(n),this.options.fillMode&&e.is("img")){var o=new window.MSAligner(this.options.fillMode,n,e);e[0].aligner=o,e.one("load",function(){var i=t(this);i[0].aligner.init(i.width(),i.height()),i[0].aligner.align()}).each(t.jqLoadFix)}t.browser.msie&&e.on("dragstart",function(t){t.preventDefault()}),this.thumbs.push(n)},e.create=function(){s.create.call(this),this.__translate_end=window._css3d?" translateZ(0px)":"",this.controller=new Controller(0,0,{snappingMinSpeed:2,friction:(100-.5*this.options.speed)/100}),this.controller.renderCallback("h"===this.options.dir?this._hMove:this._vMove,this);var i=this;this.resize_listener=function(){i.__resize()},t(window).bind("resize",this.resize_listener),this.thumbSize=this.thumbs[0][this.__jdimen](!0),this.setupSwipe(),this.__resize();i=this;this.options.wheel&&(this.wheellistener=function(t){var e=window.event||t.orginalEvent||t,s=Math.max(-1,Math.min(1,e.wheelDelta||-e.detail));return i.controller.push(10*-s),!1},t.browser.mozilla?this.$element[0].addEventListener("DOMMouseScroll",this.wheellistener):this.$element.bind("mousewheel",this.wheellistener)),this.slider.api.addEventListener(MSSliderEvent.CHANGE_START,this.update,this),this.slider.api.addEventListener(MSSliderEvent.HARD_UPDATE,this.realignThumbs,this),this.cindex=this.slider.api.index(),this.select(this.thumbs[this.cindex])},e._hMove=function(t,i){return this.__contPos=i,window._cssanim?void(this.$thumbscont[0].style[window._jcsspfx+"Transform"]="translateX("+-i+"px)"+this.__translate_end):void(this.$thumbscont[0].style.left=-i+"px")},e._vMove=function(t,i){return this.__contPos=i,window._cssanim?void(this.$thumbscont[0].style[window._jcsspfx+"Transform"]="translateY("+-i+"px)"+this.__translate_end):void(this.$thumbscont[0].style.top=-i+"px")},e.setupSwipe=function(){this.swipeControl=new averta.TouchSwipe(this.$element),this.swipeControl.swipeType="h"===this.options.dir?"horizontal":"vertical";var t=this;this.swipeControl.onSwipe="h"===this.options.dir?function(i){t.horizSwipeMove(i)}:function(i){t.vertSwipeMove(i)}},e.vertSwipeMove=function(t){if(!this.dTouch){var i=t.phase;if("start"===i)this.controller.stop();else if("move"===i)this.controller.drag(t.moveY);else if("end"===i||"cancel"===i){Math.abs(t.distanceY/t.duration*50/3)>.1?this.controller.push(-t.distanceY/t.duration*50/3):(this.click_enable=!0,this.controller.cancel())}}},e.horizSwipeMove=function(t){if(!this.dTouch){var i=t.phase;if("start"===i)this.controller.stop(),this.click_enable=!1;else if("move"===i)this.controller.drag(t.moveX);else if("end"===i||"cancel"===i){Math.abs(t.distanceX/t.duration*50/3)>.1?this.controller.push(-t.distanceX/t.duration*50/3):(this.click_enable=!0,this.controller.cancel())}}},e.update=function(){var t=this.slider.api.index();this.cindex!==t&&(null!=this.cindex&&this.unselect(this.thumbs[this.cindex]),this.cindex=t,this.select(this.thumbs[this.cindex]),this.dTouch||this.updateThumbscroll())},e.realignThumbs=function(){this.$element.find(".ms-thumb").each(function(t,i){i.aligner.align()})},e.updateThumbscroll=function(){var t=this.thumbSize*this.cindex;if(NaN==this.controller.value&&(this.controller.value=0),t-this.controller.value<0)this.controller.gotoSnap(this.cindex,!0);else if(t+this.thumbSize-this.controller.value>this.$element[this.__dimen]()){var i=this.cindex-Math.floor(this.$element[this.__dimen]()/this.thumbSize)+1;this.controller.gotoSnap(i,!0)}else;},e.changeSlide=function(t){this.click_enable&&this.cindex!==t[0].index&&this.slider.api.gotoSlide(t[0].index)},e.unselect=function(t){t.removeClass("ms-thumb-frame-selected")},e.select=function(t){t.addClass("ms-thumb-frame-selected")},e.__resize=function(){var t=this.$element[this.__dimen]();if(this.ls!==t){this.ls=t,this.thumbSize=this.thumbs[0][this.__jdimen](!0);var i=this.slider.api.count()*this.thumbSize;this.$thumbscont[0].style[this.__dimen]=i+"px",t>=i?(this.dTouch=!0,this.controller.stop(),this.$thumbscont[0].style[this.__pos]=.5*(t-i)+"px",this.$thumbscont[0].style[window._jcsspfx+"Transform"]=""):(this.dTouch=!1,this.click_enable=!0,this.$thumbscont[0].style[this.__pos]="",this.controller._max_value=i-t,this.controller.options.snapsize=this.thumbSize,this.updateThumbscroll())}},e.destroy=function(){s.destroy(),this.options.wheel&&(t.browser.mozilla?this.$element[0].removeEventListener("DOMMouseScroll",this.wheellistener):this.$element.unbind("mousewheel",this.wheellistener),this.wheellistener=null),t(window).unbind("resize",this.resize_listener),this.$element.remove(),this.slider.api.removeEventListener(MSSliderEvent.RESERVED_SPACE_CHANGE,this.align,this),this.slider.api.removeEventListener(MSSliderEvent.CHANGE_START,this.update,this)},window.MSThumblist=i,MSSlideController.registerControl("thumblist",i)}(jQuery),function(t){"use strict";var i=function(i){BaseControl.call(this),this.options.dir="h",this.options.inset=!0,this.options.margin=10,this.options.space=10,t.extend(this.options,i),this.bullets=[]};i.extend(BaseControl);var e=i.prototype,s=BaseControl.prototype;e.setup=function(){if(s.setup.call(this),this.$element=t("<div></div>").addClass(this.options.prefix+"bullets").addClass("ms-dir-"+this.options.dir).appendTo(this.cont),this.$bullet_cont=t("<div></div>").addClass("ms-bullets-count").appendTo(this.$element),!this.options.insetTo&&this.options.align){var i=this.options.align;this.options.inset&&this.$element.css(i,this.options.margin)}this.checkHideUnder()},e.create=function(){s.create.call(this);var i=this;this.slider.api.addEventListener(MSSliderEvent.CHANGE_START,this.update,this),this.cindex=this.slider.api.index();for(var e=0;e<this.slider.api.count();++e){var n=t("<div></div>").addClass("ms-bullet");n[0].index=e,n.on("click",function(){i.changeSlide(this.index)}),this.$bullet_cont.append(n),this.bullets.push(n),"h"===this.options.dir?n.css("margin",this.options.space/2):n.css("margin",this.options.space)}"h"===this.options.dir?this.$element.width(n.outerWidth(!0)*this.slider.api.count()):this.$element.css("margin-top",-this.$element.outerHeight(!0)/2),this.select(this.bullets[this.cindex])},e.update=function(){var t=this.slider.api.index();this.cindex!==t&&(null!=this.cindex&&this.unselect(this.bullets[this.cindex]),this.cindex=t,this.select(this.bullets[this.cindex]))},e.changeSlide=function(t){this.cindex!==t&&this.slider.api.gotoSlide(t)},e.unselect=function(t){t.removeClass("ms-bullet-selected")},e.select=function(t){t.addClass("ms-bullet-selected")},e.destroy=function(){s.destroy(),this.slider.api.removeEventListener(MSSliderEvent.CHANGE_START,this.update,this),this.$element.remove()},window.MSBulltes=i,MSSlideController.registerControl("bullets",i)}(jQuery),function(t){"use strict";var i=function(i){BaseControl.call(this),this.options.dir="h",this.options.autohide=!0,this.options.width=4,this.options.color="#3D3D3D",this.options.margin=10,t.extend(this.options,i),this.__dimen="h"===this.options.dir?"width":"height",this.__jdimen="h"===this.options.dir?"outerWidth":"outerHeight",this.__pos="h"===this.options.dir?"left":"top",this.__translate_end=window._css3d?" translateZ(0px)":"",this.__translate_start="h"===this.options.dir?" translateX(":"translateY("};i.extend(BaseControl);var e=i.prototype,s=BaseControl.prototype;e.setup=function(){if(this.$element=t("<div></div>").addClass(this.options.prefix+"sbar").addClass("ms-dir-"+this.options.dir),s.setup.call(this),this.$element.appendTo(this.slider.$controlsCont===this.cont?this.slider.$element:this.cont),this.$bar=t("<div></div>").addClass(this.options.prefix+"bar").appendTo(this.$element),this.slider.options.loop&&(this.disable=!0,this.$element.remove()),"v"===this.options.dir?this.$bar.width(this.options.width):this.$bar.height(this.options.width),this.$bar.css("background-color",this.options.color),!this.options.insetTo&&this.options.align){this.$element.css("v"===this.options.dir?{right:"auto",left:"auto"}:{top:"auto",bottom:"auto"});var i=this.options.align;this.options.inset?this.$element.css(i,this.options.margin):"top"===i?this.$element.prependTo(this.slider.$element).css({"margin-bottom":this.options.margin,position:"relative"}):"bottom"===i?this.$element.css({"margin-top":this.options.margin,position:"relative"}):(this.slider.api.addEventListener(MSSliderEvent.RESERVED_SPACE_CHANGE,this.align,this),this.align())}this.checkHideUnder()},e.align=function(){if(!this.detached){var t=this.options.align,i=this.slider.reserveSpace(t,2*this.options.margin+this.options.width);this.$element.css(t,-i-this.options.margin-this.options.width)}},e.create=function(){this.disable||(this.scroller=this.slider.api.scroller,this.slider.api.view.addEventListener(MSViewEvents.SCROLL,this._update,this),this.slider.api.addEventListener(MSSliderEvent.RESIZE,this._resize,this),this._resize(),this.options.autohide&&this.$bar.css("opacity","0"))},e._resize=function(){this.vdimen=this.$element[this.__dimen](),this.bar_dimen=this.slider.api.view["__"+this.__dimen]*this.vdimen/this.scroller._max_value,this.$bar[this.__dimen](this.bar_dimen)},e._update=function(){var t=this.scroller.value*(this.vdimen-this.bar_dimen)/this.scroller._max_value;if(this.lvalue!==t){if(this.lvalue=t,this.options.autohide){clearTimeout(this.hto),this.$bar.css("opacity","1");var i=this;this.hto=setTimeout(function(){i.$bar.css("opacity","0")},150)}return 0>t?void(this.$bar[0].style[this.__dimen]=this.bar_dimen+t+"px"):(t>this.vdimen-this.bar_dimen&&(this.$bar[0].style[this.__dimen]=this.vdimen-t+"px"),window._cssanim?void(this.$bar[0].style[window._jcsspfx+"Transform"]=this.__translate_start+t+"px)"+this.__translate_end):void(this.$bar[0].style[this.__pos]=t+"px"))}},e.destroy=function(){s.destroy(),this.slider.api.view.removeEventListener(MSViewEvents.SCROLL,this._update,this),this.slider.api.removeEventListener(MSSliderEvent.RESIZE,this._resize,this),this.slider.api.removeEventListener(MSSliderEvent.RESERVED_SPACE_CHANGE,this.align,this),this.$element.remove()},window.MSScrollbar=i,MSSlideController.registerControl("scrollbar",i)}(jQuery),function(t){"use strict";var i=function(i){BaseControl.call(this),this.options.autohide=!1,this.options.width=4,this.options.color="#FFFFFF",this.options.inset=!0,this.options.margin=0,t.extend(this.options,i)};i.extend(BaseControl);var e=i.prototype,s=BaseControl.prototype;e.setup=function(){if(s.setup.call(this),this.$element=t("<div></div>").addClass(this.options.prefix+"timerbar"),s.setup.call(this),this.$element.appendTo(this.slider.$controlsCont===this.cont?this.slider.$element:this.cont),this.$bar=t("<div></div>").addClass("ms-time-bar").appendTo(this.$element),"v"===this.options.dir?(this.$bar.width(this.options.width),this.$element.width(this.options.width)):(this.$bar.height(this.options.width),this.$element.height(this.options.width)),this.$bar.css("background-color",this.options.color),!this.options.insetTo&&this.options.align){this.$element.css({top:"auto",bottom:"auto"});var i=this.options.align;this.options.inset?this.$element.css(i,this.options.margin):"top"===i?this.$element.prependTo(this.slider.$element).css({"margin-bottom":this.options.margin,position:"relative"}):"bottom"===i?this.$element.css({"margin-top":this.options.margin,position:"relative"}):(this.slider.api.addEventListener(MSSliderEvent.RESERVED_SPACE_CHANGE,this.align,this),this.align())}this.checkHideUnder()},e.align=function(){if(!this.detached){var t=this.options.align,i=this.slider.reserveSpace(t,2*this.options.margin+this.options.width);this.$element.css(t,-i-this.options.margin-this.options.width)}},e.create=function(){s.create.call(this),this.slider.api.addEventListener(MSSliderEvent.WAITING,this._update,this),this._update()},e._update=function(){this.$bar[0].style.width=this.slider.api._delayProgress+"%"},e.destroy=function(){s.destroy(),this.slider.api.removeEventListener(MSSliderEvent.RESERVED_SPACE_CHANGE,this.align,this),this.slider.api.removeEventListener(MSSliderEvent.WAITING,this._update,this),this.$element.remove()},window.MSTimerbar=i,MSSlideController.registerControl("timebar",i)}(jQuery),function(t){"use strict";var i=function(i){BaseControl.call(this),this.options.color="#A2A2A2",this.options.stroke=10,this.options.radius=4,this.options.autohide=!1,t.extend(this.options,i)};i.extend(BaseControl);var e=i.prototype,s=BaseControl.prototype;e.setup=function(){return s.setup.call(this),this.$element=t("<div></div>").addClass(this.options.prefix+"ctimer").appendTo(this.cont),this.$canvas=t("<canvas></canvas>").addClass("ms-ctimer-canvas").appendTo(this.$element),this.$bar=t("<div></div>").addClass("ms-ctimer-bullet").appendTo(this.$element),this.$canvas[0].getContext?(this.ctx=this.$canvas[0].getContext("2d"),this.prog=0,this.__w=2*(this.options.radius+this.options.stroke/2),this.$canvas[0].width=this.__w,this.$canvas[0].height=this.__w,void this.checkHideUnder()):(this.destroy(),void(this.disable=!0))},e.create=function(){if(!this.disable){s.create.call(this),this.slider.api.addEventListener(MSSliderEvent.WAITING,this._update,this);var t=this;this.$element.click(function(){t.slider.api.paused?t.slider.api.resume():t.slider.api.pause()}),this._update()}},e._update=function(){var i=this;t(this).stop(!0).animate({prog:.01*this.slider.api._delayProgress},{duration:200,step:function(){i._draw()}})},e._draw=function(){this.ctx.clearRect(0,0,this.__w,this.__w),this.ctx.beginPath(),this.ctx.arc(.5*this.__w,.5*this.__w,this.options.radius,1.5*Math.PI,1.5*Math.PI+2*Math.PI*this.prog,!1),this.ctx.strokeStyle=this.options.color,this.ctx.lineWidth=this.options.stroke,this.ctx.stroke()},e.destroy=function(){s.destroy(),this.disable||(t(this).stop(!0),this.slider.api.removeEventListener(MSSliderEvent.WAITING,this._update,this),this.$element.remove())},window.MSCircleTimer=i,MSSlideController.registerControl("circletimer",i)}(jQuery),function(t){"use strict";window.MSLightbox=function(i){BaseControl.call(this,i),this.options.autohide=!1,t.extend(this.options,i),this.data_list=[]},MSLightbox.fadeDuratation=400,MSLightbox.extend(BaseControl);var i=MSLightbox.prototype,e=BaseControl.prototype;i.setup=function(){e.setup.call(this),this.$element=t("<div></div>").addClass(this.options.prefix+"lightbox-btn").appendTo(this.cont),this.checkHideUnder()},i.slideAction=function(i){t("<div></div>").addClass(this.options.prefix+"lightbox-btn").appendTo(i.$element).append(t(i.$element.find(".ms-lightbox")))},i.create=function(){e.create.call(this)},MSSlideController.registerControl("lightbox",MSLightbox)}(jQuery),function(t){"use strict";window.MSSlideInfo=function(i){BaseControl.call(this,i),this.options.autohide=!1,this.options.align=null,this.options.inset=!1,this.options.margin=10,this.options.size=100,this.options.dir="h",t.extend(this.options,i),this.data_list=[]},MSSlideInfo.fadeDuratation=400,MSSlideInfo.extend(BaseControl);var i=MSSlideInfo.prototype,e=BaseControl.prototype;i.setup=function(){if(this.$element=t("<div></div>").addClass(this.options.prefix+"slide-info").addClass("ms-dir-"+this.options.dir),e.setup.call(this),this.$element.appendTo(this.slider.$controlsCont===this.cont?this.slider.$element:this.cont),!this.options.insetTo&&this.options.align){var i=this.options.align;this.options.inset?this.$element.css(i,this.options.margin):"top"===i?this.$element.prependTo(this.slider.$element).css({"margin-bottom":this.options.margin,position:"relative"}):"bottom"===i?this.$element.css({"margin-top":this.options.margin,position:"relative"}):(this.slider.api.addEventListener(MSSliderEvent.RESERVED_SPACE_CHANGE,this.align,this),this.align()),"v"===this.options.dir?this.$element.width(this.options.size):this.$element.css("min-height",this.options.size)}this.checkHideUnder()},i.align=function(){if(!this.detached){var t=this.options.align,i=this.slider.reserveSpace(t,this.options.size+2*this.options.margin);this.$element.css(t,-i-this.options.size-this.options.margin)}},i.slideAction=function(i){var e=t(i.$element.find(".ms-info"));e.detach(),this.data_list[i.index]=e},i.create=function(){e.create.call(this),this.slider.api.addEventListener(MSSliderEvent.CHANGE_START,this.update,this),this.cindex=this.slider.api.index(),this.switchEle(this.data_list[this.cindex])},i.update=function(){var t=this.slider.api.index();this.switchEle(this.data_list[t]),this.cindex=t},i.switchEle=function(t){this.current_ele&&(this.current_ele[0].tween&&this.current_ele[0].tween.stop(!0),this.current_ele[0].tween=CTween.animate(this.current_ele,MSSlideInfo.fadeDuratation,{opacity:0},{complete:function(){this.detach(),this[0].tween=null,t.css("position","relative")},target:this.current_ele}),t.css("position","absolute")),this.__show(t)},i.__show=function(t){t.appendTo(this.$element).css("opacity","0"),this.current_ele&&t.height(Math.max(t.height(),this.current_ele.height())),clearTimeout(this.tou),this.tou=setTimeout(function(){CTween.fadeIn(t,MSSlideInfo.fadeDuratation),t.css("height","")},MSSlideInfo.fadeDuratation),t[0].tween&&t[0].tween.stop(!0),this.current_ele=t},i.destroy=function(){e.destroy(),clearTimeout(this.tou),this.current_ele&&this.current_ele[0].tween&&this.current_ele[0].tween.stop("true"),this.$element.remove(),this.slider.api.removeEventListener(MSSliderEvent.RESERVED_SPACE_CHANGE,this.align,this),this.slider.api.removeEventListener(MSSliderEvent.CHANGE_START,this.update,this)},MSSlideController.registerControl("slideinfo",MSSlideInfo)}(jQuery),function(t){window.MSGallery=function(i,e){this.id=i,this.slider=e,this.telement=t("#"+i),this.botcont=t("<div></div>").addClass("ms-gallery-botcont").appendTo(this.telement),this.thumbcont=t("<div></div>").addClass("ms-gal-thumbcont hide-thumbs").appendTo(this.botcont),this.playbtn=t("<div></div>").addClass("ms-gal-playbtn").appendTo(this.botcont),this.thumbtoggle=t("<div></div>").addClass("ms-gal-thumbtoggle").appendTo(this.botcont),e.control("thumblist",{insertTo:this.thumbcont,autohide:!1,dir:"h"}),e.control("slidenum",{insertTo:this.botcont,autohide:!1}),e.control("slideinfo",{insertTo:this.botcont,autohide:!1}),e.control("timebar",{insertTo:this.botcont,autohide:!1}),e.control("bullets",{insertTo:this.botcont,autohide:!1})};var i=MSGallery.prototype;i._init=function(){var t=this;this.slider.api.paused||this.playbtn.addClass("btn-pause"),this.playbtn.click(function(){t.slider.api.paused?(t.slider.api.resume(),t.playbtn.addClass("btn-pause")):(t.slider.api.pause(),t.playbtn.removeClass("btn-pause"))}),this.thumbtoggle.click(function(){t.vthumbs?(t.thumbtoggle.removeClass("btn-hide"),t.vthumbs=!1,t.thumbcont.addClass("hide-thumbs")):(t.thumbtoggle.addClass("btn-hide"),t.thumbcont.removeClass("hide-thumbs"),t.vthumbs=!0)})},i.setup=function(){var i=this;t(document).ready(function(){i._init()})}}(jQuery),function(t){var i=function(t,i,e,s,n,o){return"_o"===n&&o?o.url_o:"https://farm"+t+".staticflickr.com/"+i+"/"+e+"_"+s+n+".jpg"};window.MSFlickrV2=function(i,e){var s={count:10,type:"photoset",thumbSize:"q",imgSize:"c"};if(this.slider=i,this.slider.holdOn(),e.key){t.extend(s,e),this.options=s;var n,o,a,r=this;"photoset"===this.options.type?t.getJSON((n=this.options.key,o=this.options.id,a=this.options.count,"https://api.flickr.com/services/rest/?method=flickr.photosets.getPhotos&api_key="+n+"&photoset_id="+o+"&per_page="+a+"&extras=url_o,description,date_taken,owner_name,views&format=json&jsoncallback=?"),function(t){r._photosData(t)}):t.getJSON(function(t,i,e){return"https://api.flickr.com/services/rest/?&method=flickr.people.getPublicPhotos&api_key="+t+"&user_id="+i+"&per_page="+e+"&extras=url_o,description,date_taken,owner_name,views&format=json&jsoncallback=?"}(this.options.key,this.options.id,this.options.count),function(t){r.options.type="photos",r._photosData(t)}),""!==this.options.imgSize&&"-"!==this.options.imgSize&&(this.options.imgSize="_"+this.options.imgSize),this.options.thumbSize="_"+this.options.thumbSize,this.slideTemplate=this.slider.$element.find(".ms-slide")[0].outerHTML,this.slider.$element.find(".ms-slide").remove()}else this.errMsg("Flickr API Key required. Please add it in settings.")};var e=MSFlickrV2.prototype;e._photosData=function(i){if("fail"!==i.stat){var e=this;this.options.author||this.options.desc,t.each(i[this.options.type].photo,function(i,n){var o=e.slideTemplate.replace(/{{[\w-]+}}/g,function(t){return t=t.replace(/{{|}}/g,""),s[t]?s[t](n,e):"["+t+"]"});t(o).appendTo(e.slider.$element)}),e._initSlider()}else this.errMsg("Flickr API ERROR#"+i.code+": "+i.message)},e.errMsg=function(i){this.slider.$element.css("display","block"),this.errEle||(this.errEle=t('<div style="font-family:Arial; color:red; font-size:12px; position:absolute; top:10px; left:10px"></div>').appendTo(this.slider.$loading)),this.errEle.html(i)},e._initSlider=function(){this.slider.release()};var s={image:function(t,e){return i(t.farm,t.server,t.id,t.secret,e.options.imgSize,t)},thumb:function(t,e){return i(t.farm,t.server,t.id,t.secret,e.options.thumbSize)},title:function(t){return t.title},"owner-name":function(t){return t.ownername},"date-taken":function(t){return t.datetaken},views:function(t){return t.views},description:function(t){return t.description._content}}}(jQuery),function(t){window.MSFacebookGallery=function(i,e){var s={count:10,type:"photostream",thumbSize:"320",imgSize:"orginal",https:!1};this.slider=i,this.slider.preventInit=!0,t.extend(s,e),this.options=s,this.graph=this.options.https?"https://graph.facebook.com":"http://graph.facebook.com";var n=this;"photostream"===this.options.type?t.getJSON(this.graph+"/"+this.options.username+"/photos/uploaded/?fields=source,name,link,images,from&limit="+this.options.count,function(t){n._photosData(t)}):t.getJSON(this.graph+"/"+this.options.albumId+"/photos?fields=source,name,link,images,from&limit="+this.options.count,function(t){n._photosData(t)}),this.slideTemplate=this.slider.$element.find(".ms-slide")[0].outerHTML,this.slider.$element.find(".ms-slide").remove()};var i=MSFacebookGallery.prototype;i._photosData=function(i){if(i.error)this.errMsg("Facebook API ERROR#"+i.error.code+"("+i.error.type+"): "+i.error.message);else{for(var e=this,n=(this.options.author||this.options.desc,0),o=i.data.length;n!==o;n++){var a=e.slideTemplate.replace(/{{[\w-]+}}/g,function(t){return t=t.replace(/{{|}}/g,""),s[t]?s[t](i.data[n],e):"{{"+t+"}}"});t(a).appendTo(e.slider.$element)}e._initSlider()}},i.errMsg=function(i){this.slider.$element.css("display","block"),this.errEle||(this.errEle=t('<div style="font-family:Arial; color:red; font-size:12px; position:absolute; top:10px; left:10px"></div>').appendTo(this.slider.$loading)),this.errEle.html(i)},i._initSlider=function(){this.slider.preventInit=!1,this.slider._init()};var e=function(t,i){if("orginal"===i)return t[0].source;for(var e=0,s=t.length;e!==s;e++)if(-1!==t[e].source.indexOf(i+"x"+i))return t[e].source;return t[s-3].source},s={image:function(t,i){return e(t.images,i.options.imgSize)},thumb:function(t,i){return e(t.images,i.options.thumbSize)},name:function(t){return t.name},"owner-name":function(t){return t.from.name},link:function(t){return t.link}}}(jQuery),function(t){"use strict";window.MSScrollParallax=function(t,i,e,s){this.fade=s,this.slider=t,this.parallax=i/100,this.bgparallax=e/100,t.api.addEventListener(MSSliderEvent.INIT,this.init,this),t.api.addEventListener(MSSliderEvent.DESTROY,this.destory,this),t.api.addEventListener(MSSliderEvent.CHANGE_END,this.resetLayers,this),t.api.addEventListener(MSSliderEvent.CHANGE_START,this.updateCurrentSlide,this)},window.MSScrollParallax.setup=function(t,i,e,s){return window._mobile?void 0:(null==i&&(i=50),null==e&&(e=40),new MSScrollParallax(t,i,e,s))};var i=window.MSScrollParallax.prototype;i.init=function(){this.slider.$element.addClass("ms-scroll-parallax"),this.sliderOffset=this.slider.$element.offset().top,this.updateCurrentSlide();for(var i,e=this.slider.api.view.slideList,s=0,n=e.length;s!==n;s++)(i=e[s]).$layers&&(i.$layers.wrap('<div class="ms-scroll-parallax-cont"></div>'),i.$scrollParallaxCont=i.$layers.parent());t(window).on("scroll",{that:this},this.moveParallax).trigger("scroll")},i.resetLayers=function(){if(this.lastSlide){var t=this.lastSlide.$scrollParallaxCont;window._css2d?(t&&(t[0].style[window._jcsspfx+"Transform"]=""),this.lastSlide.hasBG&&(this.lastSlide.$imgcont[0].style[window._jcsspfx+"Transform"]="")):(t&&(t[0].style.top=""),this.lastSlide.hasBG&&(this.lastSlide.$imgcont[0].style.top="0px"))}},i.updateCurrentSlide=function(){this.lastSlide=this.currentSlide,this.currentSlide=this.slider.api.currentSlide,this.moveParallax({data:{that:this}})},i.moveParallax=function(i){var e=i.data.that,s=e.slider,n=e.sliderOffset,o=t(window).scrollTop(),a=e.currentSlide.$scrollParallaxCont,r=n-o;0>=r?(a&&(window._css3d?a[0].style[window._jcsspfx+"Transform"]="translateY("+-r*e.parallax+"px) translateZ(0.4px)":window._css2d?a[0].style[window._jcsspfx+"Transform"]="translateY("+-r*e.parallax+"px)":a[0].style.top=-r*e.parallax+"px"),e.updateSlidesBG(-r*e.bgparallax+"px",!0),a&&e.fade&&a.css("opacity",1-Math.min(1,-r/s.api.height))):(a&&(window._css2d?a[0].style[window._jcsspfx+"Transform"]="":a[0].style.top=""),e.updateSlidesBG("0px",!1),a&&e.fade&&a.css("opacity",1))},i.updateSlidesBG=function(i,e){for(var s=this.slider.api.view.slideList,n=!e||t.browser.msie||t.browser.opera?"":"fixed",o=0,a=s.length;o!==a;o++)s[o].hasBG&&(s[o].$imgcont[0].style.position=n,s[o].$imgcont[0].style.top=i),s[o].$bgvideocont&&(s[o].$bgvideocont[0].style.position=n,s[o].$bgvideocont[0].style.top=i)},i.destory=function(){slider.api.removeEventListener(MSSliderEvent.INIT,this.init,this),slider.api.removeEventListener(MSSliderEvent.DESTROY,this.destory,this),slider.api.removeEventListener(MSSliderEvent.CHANGE_END,this.resetLayers,this),slider.api.removeEventListener(MSSliderEvent.CHANGE_START,this.updateCurrentSlide,this),t(window).off("scroll",this.moveParallax)}}(jQuery),function(t,i,e){var s=0;if(e.MasterSlider){var n=function(t){this.slider=t,this.PId=s++,this.slider.options.keyboard&&t.api.addEventListener(MSSliderEvent.INIT,this.init,this)};n.name="MSKeyboardNav";var o=n.prototype;o.init=function(){var e=this.slider.api;t(i).on("keydown.kbnav"+this.PId,function(t){var i=t.which;37===i||40===i?e.previous(!0):(38===i||39===i)&&e.next(!0)})},o.destroy=function(){t(i).off("keydown.kbnav"+this.PId),this.slider.api.removeEventListener(MSSliderEvent.INIT,this.init,this)},MasterSlider.registerPlugin(n)}}(jQuery,document,window),function(t,i,e){var s=t(e),n=t(i);if(e.MasterSlider){var o=function(i){this.slider=i,this.$slider=i.$element,this.slider.options.startOnAppear&&(i.holdOn(),n.ready(t.proxy(this.init,this)))};o.name="MSStartOnAppear";var a=o.prototype;a.init=function(){this.slider.api,s.on("scroll",t.proxy(this._onScroll,this)).trigger("scroll")},a._onScroll=function(){s.scrollTop()+s.height()>=this.$slider.offset().top&&(this.slider.release(),s.off("scroll",this._onScroll))},a.destroy=function(){},MasterSlider.registerPlugin(o)}}(jQuery,document,window);var slider=new MasterSlider;$("#container").width()<460?($(".ms-slide img").each(function(){$(this).attr("src",$(this).attr("src").replace("/sliders","/sliders/mobile"))}),$("#homepage_chip img").attr("src",$("#homepage_chip img").attr("src").replace("homepage_chip","category_chip"))):(slider.control("arrows",{autohide:!0}),slider.control("bullets",{autohide:!1})),slider.setup("masterslider",{width:1235,height:494,space:0,start:1,grabCursor:!0,swipe:!0,mouse:!0,keyboard:!1,fullwidth:!0,wheel:!1,autoplay:!0,instantStartLayers:!1,loop:!0,shuffle:!0,preload:0,heightLimit:!0,autoHeight:!0,smoothHeight:!0,endPause:!1,overPause:!0,fillMode:"fill",centerControls:!0,startOnAppear:!1,layersMode:"center",hideLayers:!1,fullscreenMargin:0,speed:20,dir:"h",view:"basic"});

!function(){var b=void 0,f=!0,j=null,l=!1,s;function m(){return function(){}}function n(t){return function(){return this[t]}}function q(t){return function(){return t}}function t(e,i,o){if("string"==typeof e){if(0===e.indexOf("#")&&(e=e.slice(1)),t.Aa[e])return i&&t.log.warn('Player "'+e+'" is already initialised. Options will not be applied.'),o&&t.Aa[e].I(o),t.Aa[e];e=t.m(e)}if(!e||!e.nodeName)throw new TypeError("The element or ID supplied is not valid. (videojs)");return e.player||new t.Player(e,i,o)}document.createElement("video"),document.createElement("audio"),document.createElement("track");var videojs=window.videojs=t;function v(e,i,o,n){t.wc.forEach(o,function(t){e(i,t,n)})}t.ic="4.12",t.vd="https:"==document.location.protocol?"https://":"http://",t.VERSION="4.12.5",t.options={techOrder:["html5","flash"],html5:{},flash:{},width:300,height:150,defaultVolume:0,playbackRates:[],inactivityTimeout:2e3,children:{mediaLoader:{},posterImage:{},loadingSpinner:{},textTrackDisplay:{},bigPlayButton:{},controlBar:{},errorDisplay:{},textTrackSettings:{}},language:document.getElementsByTagName("html")[0].getAttribute("lang")||navigator.languages&&navigator.languages[0]||navigator.If||navigator.language||"en",languages:{},notSupportedMessage:"No compatible source was found for this video."},"GENERATED_CDN_VSN"!==t.ic&&(videojs.options.flash.swf=t.vd+"vjs.zencdn.net/"+t.ic+"/video-js.swf"),t.Jd=function(e,i){return t.options.languages[e]=t.options.languages[e]!==b?t.$.ya(t.options.languages[e],i):i,t.options.languages},t.Aa={},"function"==typeof define&&define.amd?define("videojs",[],function(){return videojs}):"object"==typeof exports&&"object"==typeof module&&(module.exports=videojs),t.Ea=t.CoreObject=m(),t.Ea.extend=function(e){var i,o;for(var n in i=(e=e||{}).init||e.l||this.prototype.init||this.prototype.l||m(),((o=function(){i.apply(this,arguments)}).prototype=t.i.create(this.prototype)).constructor=o,o.extend=t.Ea.extend,o.create=t.Ea.create,e)e.hasOwnProperty(n)&&(o.prototype[n]=e[n]);return o},t.Ea.create=function(){var e=t.i.create(this.prototype);return this.apply(e,arguments),e},t.b=function(e,i,o){if(t.i.isArray(i))return v(t.b,e,i,o);var n=t.getData(e);n.G||(n.G={}),n.G[i]||(n.G[i]=[]),o.s||(o.s=t.s++),n.G[i].push(o),n.ca||(n.disabled=l,n.ca=function(i){if(!n.disabled&&(i=t.Pb(i),o=n.G[i.type]))for(var o,s=0,r=(o=o.slice(0)).length;s<r&&!i.Rc();s++)o[s].call(e,i)}),1==n.G[i].length&&(e.addEventListener?e.addEventListener(i,n.ca,l):e.attachEvent&&e.attachEvent("on"+i,n.ca))},t.n=function(e,i,o){if(t.Mc(e)){var n=t.getData(e);if(n.G){if(t.i.isArray(i))return v(t.n,e,i,o);if(i){var s=n.G[i];if(s){if(o){if(o.s)for(n=0;n<s.length;n++)s[n].s===o.s&&s.splice(n--,1)}else n.G[i]=[];t.Ac(e,i)}}else for(s in n.G)i=s,n.G[i]=[],t.Ac(e,i)}}},t.Ac=function(e,i){var o=t.getData(e);0===o.G[i].length&&(delete o.G[i],e.removeEventListener?e.removeEventListener(i,o.ca,l):e.detachEvent&&e.detachEvent("on"+i,o.ca)),t.ib(o.G)&&(delete o.G,delete o.ca,delete o.disabled),t.ib(o)&&t.cd(e)},t.Pb=function(t){function e(){return f}function i(){return l}if(!t||!t.Vb){var o=t||window.event;for(var n in t={},o)"layerX"!==n&&"layerY"!==n&&"keyLocation"!==n&&("returnValue"==n&&o.preventDefault||(t[n]=o[n]));if(t.target||(t.target=t.srcElement||document),t.relatedTarget=t.fromElement===t.target?t.toElement:t.fromElement,t.preventDefault=function(){o.preventDefault&&o.preventDefault(),t.returnValue=l,t.ie=e,t.defaultPrevented=f},t.ie=i,t.defaultPrevented=l,t.stopPropagation=function(){o.stopPropagation&&o.stopPropagation(),t.cancelBubble=f,t.Vb=e},t.Vb=i,t.stopImmediatePropagation=function(){o.stopImmediatePropagation&&o.stopImmediatePropagation(),t.Rc=e,t.stopPropagation()},t.Rc=i,t.clientX!=j){n=document.documentElement;var s=document.body;t.pageX=t.clientX+(n&&n.scrollLeft||s&&s.scrollLeft||0)-(n&&n.clientLeft||s&&s.clientLeft||0),t.pageY=t.clientY+(n&&n.scrollTop||s&&s.scrollTop||0)-(n&&n.clientTop||s&&s.clientTop||0)}t.which=t.charCode||t.keyCode,t.button!=j&&(t.button=1&t.button?0:4&t.button?1:2&t.button?2:0)}return t},t.o=function(e,i){var o=t.Mc(e)?t.getData(e):{},n=e.parentNode||e.ownerDocument;return"string"==typeof i&&(i={type:i,target:e}),i=t.Pb(i),o.ca&&o.ca.call(e,i),n&&!i.Vb()&&i.bubbles!==l?t.o(n,i):n||i.defaultPrevented||(o=t.getData(i.target),!i.target[i.type])||(o.disabled=f,"function"==typeof i.target[i.type]&&i.target[i.type](),o.disabled=l),!i.defaultPrevented},t.N=function(e,i,o){function n(){t.n(e,i,n),o.apply(this,arguments)}if(t.i.isArray(i))return v(t.N,e,i,o);n.s=o.s=o.s||t.s++,t.b(e,i,n)};var w=Object.prototype.hasOwnProperty;t.e=function(e,i){var o;return i=i||{},o=document.createElement(e||"div"),t.i.da(i,function(t,e){-1!==t.indexOf("aria-")||"role"==t?o.setAttribute(t,e):o[t]=e}),o},t.ua=function(t){return t.charAt(0).toUpperCase()+t.slice(1)},t.i={},t.i.create=Object.create||function(t){function e(){}return e.prototype=t,new e},t.i.da=function(t,e,i){for(var o in t)w.call(t,o)&&e.call(i||this,o,t[o])},t.i.D=function(t,e){if(!e)return t;for(var i in e)w.call(e,i)&&(t[i]=e[i]);return t},t.i.Rd=function(e,i){var o,n,s;for(o in e=t.i.copy(e),i)w.call(i,o)&&(n=e[o],s=i[o],e[o]=t.i.jb(n)&&t.i.jb(s)?t.i.Rd(n,s):i[o]);return e},t.i.copy=function(e){return t.i.D({},e)},t.i.jb=function(t){return!!t&&"object"==typeof t&&"[object Object]"===t.toString()&&t.constructor===Object},t.i.isArray=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},t.ke=function(t){return t!=t},t.bind=function(e,i,o){function n(){return i.apply(e,arguments)}return i.s||(i.s=t.s++),n.s=o?o+"_"+i.s:i.s,n},t.ta={},t.s=1,t.expando="vdata"+(new Date).getTime(),t.getData=function(e){var i=e[t.expando];return i||(i=e[t.expando]=t.s++),t.ta[i]||(t.ta[i]={}),t.ta[i]},t.Mc=function(e){return!(!(e=e[t.expando])||t.ib(t.ta[e]))},t.cd=function(e){var i=e[t.expando];if(i){delete t.ta[i];try{delete e[t.expando]}catch(i){e.removeAttribute?e.removeAttribute(t.expando):e[t.expando]=j}}},t.ib=function(t){for(var e in t)if(t[e]!==j)return l;return f},t.Oa=function(t,e){return-1!==(" "+t.className+" ").indexOf(" "+e+" ")},t.p=function(e,i){t.Oa(e,i)||(e.className=""===e.className?i:e.className+" "+i)},t.r=function(e,i){var o,n;if(t.Oa(e,i)){for(n=(o=e.className.split(" ")).length-1;0<=n;n--)o[n]===i&&o.splice(n,1);e.className=o.join(" ")}},t.A=t.e("video");var x=document.createElement("track");x.Wb="captions",x.hd="en",x.label="English",t.A.appendChild(x),t.P=navigator.userAgent,t.Cd=/iPhone/i.test(t.P),t.Bd=/iPad/i.test(t.P),t.Dd=/iPod/i.test(t.P),t.Ad=t.Cd||t.Bd||t.Dd;var aa=t,y,z=t.P.match(/OS (\d+)_/i);y=z&&z[1]?z[1]:b,aa.kf=y,t.zd=/Android/i.test(t.P);var ba=t,B,C=t.P.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i),D,E,J,ha,K,L,O;function F(e,i){var o,n;o=Array.prototype.slice.call(i),n=m(),n=window.console||{log:n,warn:n,error:n},e?o.unshift(e.toUpperCase()+":"):e="log",t.log.history.push(o),o.unshift("VIDEOJS:"),n[e].apply?n[e].apply(n,o):n[e](o.join(" "))}function G(t){t.r("vjs-lock-showing")}function ca(e,i,o,n){return o!==b?((o===j||t.ke(o))&&(o=0),e.c.style[i]=-1!==(""+o).indexOf("%")||-1!==(""+o).indexOf("px")?o:"auto"===o?"":o+"px",n||e.o("resize"),e):e.c?-1!==(n=(o=e.c.style[i]).indexOf("px"))?parseInt(o.slice(0,n),10):parseInt(e.c["offset"+t.ua(i)],10):0}function da(e){var i,o,n,s,r,a;i=0,o=j,e.b("touchstart",function(e){1===e.touches.length&&(o=t.i.copy(e.touches[0]),i=(new Date).getTime(),n=f)}),e.b("touchmove",function(t){1<t.touches.length?n=l:o&&(r=t.touches[0].pageX-o.pageX,a=t.touches[0].pageY-o.pageY,10<Math.sqrt(r*r+a*a)&&(n=l))}),s=function(){n=l},e.b("touchleave",s),e.b("touchcancel",s),e.b("touchend",function(t){o=j,n===f&&(200>(new Date).getTime()-i&&(t.preventDefault(),this.o("tap")))})}function ea(e,i){var o,n,s,r;return o=e.c,n=t.Yd(o),r=s=o.offsetWidth,o=e.handle,e.options().vertical?(r=n.top,n=i.changedTouches?i.changedTouches[0].pageY:i.pageY,o&&(r+=(o=o.m().offsetHeight)/2,s-=o),Math.max(0,Math.min(1,(r-n+s)/s))):(s=n.left,n=i.changedTouches?i.changedTouches[0].pageX:i.pageX,o&&(s+=(o=o.m().offsetWidth)/2,r-=o),Math.max(0,Math.min(1,(n-s)/r)))}function fa(e,i){e.ba(i),i.b("click",t.bind(e,function(){G(this)}))}function ga(t){t.Ha=f,t.xa.p("vjs-lock-showing"),t.c.setAttribute("aria-pressed",f),t.H&&0<t.H.length&&t.H[0].m().focus()}function H(t){t.Ha=l,G(t.xa),t.c.setAttribute("aria-pressed",l)}C?(D=C[1]&&parseFloat(C[1]),E=C[2]&&parseFloat(C[2]),B=D&&E?parseFloat(C[1]+"."+C[2]):D||j):B=j,ba.hc=B,t.Ed=t.zd&&/webkit/i.test(t.P)&&2.3>t.hc,t.jc=/Firefox/i.test(t.P),t.lf=/Chrome/i.test(t.P),t.oa=/MSIE\s8\.0/.test(t.P),t.Eb=!!("ontouchstart"in window||window.xd&&document instanceof window.xd),t.wd="backgroundSize"in t.A.style,t.ed=function(e,i){t.i.da(i,function(t,i){i===j||void 0===i||i===l?e.removeAttribute(t):e.setAttribute(t,i===f?"":i)})},t.Na=function(t){var e,i,o,n;if(e={},t&&t.attributes&&0<t.attributes.length)for(var s=(i=t.attributes).length-1;0<=s;s--)o=i[s].name,n=i[s].value,"boolean"!=typeof t[o]&&-1===",autoplay,controls,loop,muted,default,".indexOf(","+o+",")||(n=n!==j?f:l),e[o]=n;return e},t.vf=function(t,e){var i="";return document.defaultView&&document.defaultView.getComputedStyle?i=document.defaultView.getComputedStyle(t,"").getPropertyValue(e):t.currentStyle&&(i=t["client"+e.substr(0,1).toUpperCase()+e.substr(1)]+"px"),i},t.Ub=function(t,e){e.firstChild?e.insertBefore(t,e.firstChild):e.appendChild(t)},t.cb={},t.m=function(t){return 0===t.indexOf("#")&&(t=t.slice(1)),document.getElementById(t)},t.Ma=function(t,e){e=e||t;var i=Math.floor(t%60),o=Math.floor(t/60%60),n=Math.floor(t/3600),s=Math.floor(e/60%60),r=Math.floor(e/3600);return(isNaN(t)||1/0===t)&&(n=o=i="-"),(n=0<n||0<r?n+":":"")+(((n||10<=s)&&10>o?"0"+o:o)+":")+(10>i?"0"+i:i)},t.Ld=function(){document.body.focus(),document.onselectstart=q(l)},t.af=function(){document.onselectstart=q(f)},t.trim=function(t){return(t+"").replace(/^\s+|\s+$/g,"")},t.round=function(t,e){return e||(e=0),Math.round(t*Math.pow(10,e))/Math.pow(10,e)},t.Lb=function(t,e){return{length:1,start:function(){return t},end:function(){return e}}},t.Me=function(e){try{var i=window.localStorage||l;i&&(i.volume=e)}catch(e){22==e.code||1014==e.code?t.log("LocalStorage Full (VideoJS)",e):18==e.code?t.log("LocalStorage not allowed (VideoJS)",e):t.log("LocalStorage Error (VideoJS)",e)}},t.$d=function(e){return e.match(/^https?:\/\//)||(e=t.e("div",{innerHTML:'<a href="'+e+'">x</a>'}).firstChild.href),e},t.Ee=function(e){var i,o,n,s;s="protocol hostname port pathname search hash host".split(" "),(n=""===(o=t.e("a",{href:e})).host&&"file:"!==o.protocol)&&((i=t.e("div")).innerHTML='<a href="'+e+'"></a>',o=i.firstChild,i.setAttribute("style","display:none; position:absolute;"),document.body.appendChild(i)),e={};for(var r=0;r<s.length;r++)e[s[r]]=o[s[r]];return"http:"===e.protocol&&(e.host=e.host.replace(/:80$/,"")),"https:"===e.protocol&&(e.host=e.host.replace(/:443$/,"")),n&&document.body.removeChild(i),e},t.log=function(){F(j,arguments)},t.log.history=[],t.log.error=function(){F("error",arguments)},t.log.warn=function(){F("warn",arguments)},t.Yd=function(e){var i,o;return e.getBoundingClientRect&&e.parentNode&&(i=e.getBoundingClientRect()),i?(e=document.documentElement,o=document.body,{left:t.round(i.left+(window.pageXOffset||o.scrollLeft)-(e.clientLeft||o.clientLeft||0)),top:t.round(i.top+(window.pageYOffset||o.scrollTop)-(e.clientTop||o.clientTop||0))}):{left:0,top:0}},t.wc={},t.wc.forEach=function(e,i,o){if(t.i.isArray(e)&&i instanceof Function)for(var n=0,s=e.length;n<s;++n)i.call(o||t,e[n],n,e);return e},t.ff=function(e,i){var o,n,s,r,a,c,l;"string"==typeof e&&(e={uri:e}),videojs.$.ya({method:"GET",timeout:45e3},e),i=i||m(),c=function(){window.clearTimeout(a),i(j,n,n.response||n.responseText)},l=function(t){window.clearTimeout(a),t&&"string"!=typeof t||(t=Error(t)),i(t,n)},void 0===(o=window.XMLHttpRequest)&&(o=function(){try{return new window.ActiveXObject("Msxml2.XMLHTTP.6.0")}catch(t){}try{return new window.ActiveXObject("Msxml2.XMLHTTP.3.0")}catch(t){}try{return new window.ActiveXObject("Msxml2.XMLHTTP")}catch(t){}throw Error("This browser does not support XMLHttpRequest.")}),(n=new o).uri=e.uri,o=t.Ee(e.uri),s=window.location,o.protocol+o.host===s.protocol+s.host||!window.XDomainRequest||"withCredentials"in n?(r="file:"==o.protocol||"file:"==s.protocol,n.onreadystatechange=function(){if(4===n.readyState){if(n.Ye)return l("timeout");200===n.status||r&&0===n.status?c():l()}},e.timeout&&(a=window.setTimeout(function(){4!==n.readyState&&(n.Ye=f,n.abort())},e.timeout))):((n=new window.XDomainRequest).onload=c,n.onerror=l,n.onprogress=m(),n.ontimeout=m());try{n.open(e.method||"GET",e.uri,f)}catch(t){return void l(t)}e.withCredentials&&(n.withCredentials=f),e.responseType&&(n.responseType=e.responseType);try{n.send()}catch(t){l(t)}},t.$={},t.$.ya=function(e,i){var o,n,s;for(o in e=t.i.copy(e),i)i.hasOwnProperty(o)&&(n=e[o],s=i[o],e[o]=t.i.jb(n)&&t.i.jb(s)?t.$.ya(n,s):i[o]);return e},t.z=m(),s=t.z.prototype,s.bb={},s.b=function(e,i){var o=this.addEventListener;this.addEventListener=Function.prototype,t.b(this,e,i),this.addEventListener=o},s.addEventListener=t.z.prototype.b,s.n=function(e,i){t.n(this,e,i)},s.removeEventListener=t.z.prototype.n,s.N=function(e,i){t.N(this,e,i)},s.o=function(e){var i=e.type||e;"string"==typeof e&&(e={type:i}),e=t.Pb(e),this.bb[i]&&this["on"+i]&&this["on"+i](e),t.o(this,e)},s.dispatchEvent=t.z.prototype.o,t.a=t.Ea.extend({l:function(e,i,o){var n,s;(this.d=e,this.q=t.i.copy(this.q),i=this.options(i),this.Pa=i.id||i.el&&i.el.id,this.Pa||(this.Pa=(e.id&&e.id()||"no_player")+"_component_"+t.s++),this.te=i.name||j,this.c=i.el||this.e(),this.R=[],this.fb={},this.gb={},this.Oc(),this.I(o),i.dd!==l)&&(this.k().reportUserActivity&&(n=t.bind(this.k(),this.k().reportUserActivity),this.b("touchstart",function(){n(),this.clearInterval(s),s=this.setInterval(n,250)}),e=function(){n(),this.clearInterval(s)},this.b("touchmove",n),this.b("touchend",e),this.b("touchcancel",e)))}}),s=t.a.prototype,s.dispose=function(){if(this.o({type:"dispose",bubbles:l}),this.R)for(var e=this.R.length-1;0<=e;e--)this.R[e].dispose&&this.R[e].dispose();this.gb=this.fb=this.R=j,this.n(),this.c.parentNode&&this.c.parentNode.removeChild(this.c),t.cd(this.c),this.c=j},s.d=f,s.k=n("d"),s.options=function(e){return e===b?this.q:this.q=t.$.ya(this.q,e)},s.e=function(e,i){return t.e(e,i)},s.v=function(t){var e=this.d.language(),i=this.d.languages();return i&&i[e]&&i[e][t]?i[e][t]:t},s.m=n("c"),s.va=function(){return this.B||this.c},s.id=n("Pa"),s.name=n("te"),s.children=n("R"),s.ae=function(t){return this.fb[t]},s.ea=function(t){return this.gb[t]},s.ba=function(e,i){var o,n;return"string"==typeof e?(n=e,o=(i=i||{}).componentClass||t.ua(n),i.name=n,o=new window.videojs[o](this.d||this,i)):o=e,this.R.push(o),"function"==typeof o.id&&(this.fb[o.id()]=o),(n=n||o.name&&o.name())&&(this.gb[n]=o),"function"==typeof o.el&&o.el()&&this.va().appendChild(o.el()),o},s.removeChild=function(t){if("string"==typeof t&&(t=this.ea(t)),t&&this.R){for(var e=l,i=this.R.length-1;0<=i;i--)if(this.R[i]===t){e=f,this.R.splice(i,1);break}e&&(this.fb[t.id()]=j,this.gb[t.name()]=j,(e=t.m())&&e.parentNode===this.va()&&this.va().removeChild(t.m()))}},s.Oc=function(){var e,i,o,n,s,r;if(o=(i=(e=this).options()).children)if(r=function(t,o){i[t]!==b&&(o=i[t]),o!==l&&(e[t]=e.ba(t,o))},t.i.isArray(o))for(var a=0;a<o.length;a++)"string"==typeof(n=o[a])?(s=n,n={}):s=n.name,r(s,n);else t.i.da(o,r)},s.V=q(""),s.b=function(e,i,o){var n,s,r;return"string"==typeof e||t.i.isArray(e)?t.b(this.c,e,t.bind(this,i)):(n=t.bind(this,o),r=this,(s=function(){r.n(e,i,n)}).s=n.s,this.b("dispose",s),(o=function(){r.n("dispose",s)}).s=n.s,e.nodeName?(t.b(e,i,n),t.b(e,"dispose",o)):"function"==typeof e.b&&(e.b(i,n),e.b("dispose",o))),this},s.n=function(e,i,o){return!e||"string"==typeof e||t.i.isArray(e)?t.n(this.c,e,i):(o=t.bind(this,o),this.n("dispose",o),e.nodeName?(t.n(e,i,o),t.n(e,"dispose",o)):(e.n(i,o),e.n("dispose",o))),this},s.N=function(e,i,o){var n,s,r;return"string"==typeof e||t.i.isArray(e)?t.N(this.c,e,t.bind(this,i)):(n=t.bind(this,o),s=this,(r=function(){s.n(e,i,r),n.apply(this,arguments)}).s=n.s,this.b(e,i,r)),this},s.o=function(e){return t.o(this.c,e),this},s.I=function(t){return t&&(this.wa?t.call(this):(this.nb===b&&(this.nb=[]),this.nb.push(t))),this},s.Wa=function(){this.wa=f;var t=this.nb;if(t&&0<t.length){for(var e=0,i=t.length;e<i;e++)t[e].call(this);this.nb=[],this.o("ready")}},s.Oa=function(e){return t.Oa(this.c,e)},s.p=function(e){return t.p(this.c,e),this},s.r=function(e){return t.r(this.c,e),this},s.show=function(){return this.r("vjs-hidden"),this},s.Y=function(){return this.p("vjs-hidden"),this},s.width=function(t,e){return ca(this,"width",t,e)},s.height=function(t,e){return ca(this,"height",t,e)},s.Td=function(t,e){return this.width(t,f).height(e)},s.setTimeout=function(e,i){function o(){this.clearTimeout(n)}e=t.bind(this,e);var n=setTimeout(e,i);return o.s="vjs-timeout-"+n,this.b("dispose",o),n},s.clearTimeout=function(t){function e(){}return clearTimeout(t),e.s="vjs-timeout-"+t,this.n("dispose",e),t},s.setInterval=function(e,i){function o(){this.clearInterval(n)}e=t.bind(this,e);var n=setInterval(e,i);return o.s="vjs-interval-"+n,this.b("dispose",o),n},s.clearInterval=function(t){function e(){}return clearInterval(t),e.s="vjs-interval-"+t,this.n("dispose",e),t},t.w=t.a.extend({l:function(e,i){t.a.call(this,e,i),da(this),this.b("tap",this.u),this.b("click",this.u),this.b("focus",this.lb),this.b("blur",this.kb)}}),s=t.w.prototype,s.e=function(e,i){var o;return i=t.i.D({className:this.V(),role:"button","aria-live":"polite",tabIndex:0},i),o=t.a.prototype.e.call(this,e,i),i.innerHTML||(this.B=t.e("div",{className:"vjs-control-content"}),this.Jb=t.e("span",{className:"vjs-control-text",innerHTML:this.v(this.sa)||"Need Text"}),this.B.appendChild(this.Jb),o.appendChild(this.B)),o},s.V=function(){return"vjs-control "+t.a.prototype.V.call(this)},s.u=m(),s.lb=function(){t.b(document,"keydown",t.bind(this,this.ja))},s.ja=function(t){32!=t.which&&13!=t.which||(t.preventDefault(),this.u())},s.kb=function(){t.n(document,"keydown",t.bind(this,this.ja))},t.U=t.a.extend({l:function(e,i){t.a.call(this,e,i),this.Kd=this.ea(this.q.barName),this.handle=this.ea(this.q.handleName),this.b("mousedown",this.mb),this.b("touchstart",this.mb),this.b("focus",this.lb),this.b("blur",this.kb),this.b("click",this.u),this.b(e,"controlsvisible",this.update),this.b(e,this.Yc,this.update)}}),s=t.U.prototype,s.e=function(e,i){return(i=i||{}).className+=" vjs-slider",i=t.i.D({role:"slider","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100,tabIndex:0},i),t.a.prototype.e.call(this,e,i)},s.mb=function(e){e.preventDefault(),t.Ld(),this.p("vjs-sliding"),this.b(document,"mousemove",this.ka),this.b(document,"mouseup",this.za),this.b(document,"touchmove",this.ka),this.b(document,"touchend",this.za),this.ka(e)},s.ka=m(),s.za=function(){t.af(),this.r("vjs-sliding"),this.n(document,"mousemove",this.ka),this.n(document,"mouseup",this.za),this.n(document,"touchmove",this.ka),this.n(document,"touchend",this.za),this.update()},s.update=function(){if(this.c){var e,i=this.Sb(),o=this.handle,n=this.Kd;if(("number"!=typeof i||i!=i||0>i||1/0===i)&&(i=0),e=i,o){e=this.c.offsetWidth;var s=o.m().offsetWidth;e=(i*=1-(e=s?s/e:0))+e/2,o.m().style.left=t.round(100*i,2)+"%"}n&&(n.m().style.width=t.round(100*e,2)+"%")}},s.lb=function(){this.b(document,"keydown",this.ja)},s.ja=function(t){37==t.which||40==t.which?(t.preventDefault(),this.jd()):38!=t.which&&39!=t.which||(t.preventDefault(),this.kd())},s.kb=function(){this.n(document,"keydown",this.ja)},s.u=function(t){t.stopImmediatePropagation(),t.preventDefault()},t.ga=t.a.extend(),t.ga.prototype.defaultValue=0,t.ga.prototype.e=function(e,i){return(i=i||{}).className+=" vjs-slider-handle",i=t.i.D({innerHTML:'<span class="vjs-control-text">'+this.defaultValue+"</span>"},i),t.a.prototype.e.call(this,"div",i)},t.pa=t.a.extend(),t.pa.prototype.e=function(){var e=this.options().Cc||"ul";return this.B=t.e(e,{className:"vjs-menu-content"}),(e=t.a.prototype.e.call(this,"div",{append:this.B,className:"vjs-menu"})).appendChild(this.B),t.b(e,"click",function(t){t.preventDefault(),t.stopImmediatePropagation()}),e},t.M=t.w.extend({l:function(e,i){t.w.call(this,e,i),this.selected(i.selected)}}),t.M.prototype.e=function(e,i){return t.w.prototype.e.call(this,"li",t.i.D({className:"vjs-menu-item",innerHTML:this.v(this.q.label)},i))},t.M.prototype.u=function(){this.selected(f)},t.M.prototype.selected=function(t){t?(this.p("vjs-selected"),this.c.setAttribute("aria-selected",f)):(this.r("vjs-selected"),this.c.setAttribute("aria-selected",l))},t.O=t.w.extend({l:function(e,i){t.w.call(this,e,i),this.update(),this.b("keydown",this.ja),this.c.setAttribute("aria-haspopup",f),this.c.setAttribute("role","button")}}),s=t.O.prototype,s.update=function(){var t=this.Ja();this.xa&&this.removeChild(this.xa),this.xa=t,this.ba(t),this.H&&0===this.H.length?this.Y():this.H&&1<this.H.length&&this.show()},s.Ha=l,s.Ja=function(){var e=new t.pa(this.d);if(this.options().title&&e.va().appendChild(t.e("li",{className:"vjs-menu-title",innerHTML:t.ua(this.options().title),We:-1})),this.H=this.createItems())for(var i=0;i<this.H.length;i++)fa(e,this.H[i]);return e},s.Ia=m(),s.V=function(){return this.className+" vjs-menu-button "+t.w.prototype.V.call(this)},s.lb=m(),s.kb=m(),s.u=function(){this.N("mouseout",t.bind(this,function(){G(this.xa),this.c.blur()})),this.Ha?H(this):ga(this)},s.ja=function(t){32==t.which||13==t.which?(this.Ha?H(this):ga(this),t.preventDefault()):27==t.which&&(this.Ha&&H(this),t.preventDefault())},t.J=function(e){"number"==typeof e?this.code=e:"string"==typeof e?this.message=e:"object"==typeof e&&t.i.D(this,e),this.message||(this.message=t.J.Sd[this.code]||"")},t.J.prototype.code=0,t.J.prototype.message="",t.J.prototype.status=j,t.J.hb="MEDIA_ERR_CUSTOM MEDIA_ERR_ABORTED MEDIA_ERR_NETWORK MEDIA_ERR_DECODE MEDIA_ERR_SRC_NOT_SUPPORTED MEDIA_ERR_ENCRYPTED".split(" "),t.J.Sd={1:"You aborted the video playback",2:"A network error caused the video download to fail part-way.",3:"The video playback was aborted due to a corruption problem or because the video used features your browser did not support.",4:"The video could not be loaded, either because the server or network failed or because the format is not supported.",5:"The video is encrypted and we do not have the keys to decrypt it."};for(var I=0;I<t.J.hb.length;I++)t.J[t.J.hb[I]]=I,t.J.prototype[t.J.hb[I]]=I;for(J=["requestFullscreen exitFullscreen fullscreenElement fullscreenEnabled fullscreenchange fullscreenerror".split(" "),"webkitRequestFullscreen webkitExitFullscreen webkitFullscreenElement webkitFullscreenEnabled webkitfullscreenchange webkitfullscreenerror".split(" "),"webkitRequestFullScreen webkitCancelFullScreen webkitCurrentFullScreenElement webkitCancelFullScreen webkitfullscreenchange webkitfullscreenerror".split(" "),"mozRequestFullScreen mozCancelFullScreen mozFullScreenElement mozFullScreenEnabled mozfullscreenchange mozfullscreenerror".split(" "),"msRequestFullscreen msExitFullscreen msFullscreenElement msFullscreenEnabled MSFullscreenChange MSFullscreenError".split(" ")],ha=J[0],L=0;L<J.length;L++)if(J[L][1]in document){K=J[L];break}if(K)for(t.cb.Rb={},L=0;L<K.length;L++)t.cb.Rb[ha[L]]=K[L];function ia(e){var i,o,n,s,r={sources:[],tracks:[]};if((o=(i=t.Na(e))["data-setup"])!==j&&t.i.D(i,t.JSON.parse(o||"{}")),t.i.D(r,i),e.hasChildNodes())for(n=0,s=(e=e.childNodes).length;n<s;n++)"source"===(o=(i=e[n]).nodeName.toLowerCase())?r.sources.push(t.Na(i)):"track"===o&&r.tracks.push(t.Na(i));return r}function ka(e,i,o){e.h&&(e.wa=l,e.h.dispose(),e.h=l),"Html5"!==i&&e.L&&(t.f.Mb(e.L),e.L=j),e.Ua=i,e.wa=l;var n=t.i.D({source:o,parentEl:e.c},e.q[i.toLowerCase()]);o&&(e.Gc=o.type,o.src==e.K.src&&0<e.K.currentTime&&(n.startTime=e.K.currentTime),e.K.src=o.src),e.h=new window.videojs[i](e,n),e.h.I(function(){this.d.Wa()})}function la(t,e){e!==b&&t.Nc!==e&&((t.Nc=e)?(t.p("vjs-has-started"),t.o("firstplay")):t.r("vjs-has-started"))}function N(e,i,o){if(e.h&&!e.h.wa)e.h.I(function(){this[i](o)});else try{e.h[i](o)}catch(e){throw t.log(e),e}}function M(e,i){if(e.h&&e.h.wa)try{return e.h[i]()}catch(o){throw e.h[i]===b?t.log("Video.js: "+i+" method not defined for "+e.Ua+" playback technology.",o):"TypeError"==o.name?(t.log("Video.js: "+i+" unavailable on "+e.Ua+" playback technology element.",o),e.h.wa=l):t.log(o),o}}function ma(t,e){var i=t.selectSource(e);i?i.h===t.Ua?t.src(i.source):ka(t,i.h,i.source):(t.setTimeout(function(){this.error({code:4,message:this.v(this.options().notSupportedMessage)})},0),t.Wa())}function ja(t,e){return e!==b?(t.Pc=!!e,t):t.Pc}function na(t){return t.k().h&&t.k().h.featuresPlaybackRate&&t.k().options().playbackRates&&0<t.k().options().playbackRates.length}t.Player=t.a.extend({l:function(e,i,o){var n,s,r,a,c;this.L=e,e.id=e.id||"vjs_video_"+t.s++,this.Xe=e&&t.Na(e),i=t.i.D(ia(e),i),this.Tc=i.language||t.options.language,this.ne=i.languages||t.options.languages,this.K={},this.Zc=i.poster||"",this.Kb=!!i.controls,e.controls=l,i.dd=l,ja(this,"audio"===this.L.nodeName.toLowerCase()),t.a.call(this,this,i,o),this.controls()?this.p("vjs-controls-enabled"):this.p("vjs-controls-disabled"),ja(this)&&this.p("vjs-audio"),t.Aa[this.Pa]=this,i.plugins&&t.i.da(i.plugins,function(t,e){this[t](e)},this),n=t.bind(this,this.reportUserActivity),this.b("mousedown",function(){n(),this.clearInterval(s),s=this.setInterval(n,250)}),this.b("mousemove",function(t){t.screenX==a&&t.screenY==c||(a=t.screenX,c=t.screenY,n())}),this.b("mouseup",function(){n(),this.clearInterval(s)}),this.b("keydown",n),this.b("keyup",n),this.setInterval(function(){if(this.Da){this.Da=l,this.userActive(f),this.clearTimeout(r);var t=this.options().inactivityTimeout;0<t&&(r=this.setTimeout(function(){this.Da||this.userActive(l)},t))}},250)}}),s=t.Player.prototype,s.language=function(t){return t===b?this.Tc:(this.Tc=t,this)},s.languages=n("ne"),s.q=t.options,s.dispose=function(){this.o("dispose"),this.n("dispose"),t.Aa[this.Pa]=j,this.L&&this.L.player&&(this.L.player=j),this.c&&this.c.player&&(this.c.player=j),this.h&&this.h.dispose(),t.a.prototype.dispose.call(this)},s.e=function(){var e,i=this.c=t.a.prototype.e.call(this,"div"),o=this.L;return o.removeAttribute("width"),o.removeAttribute("height"),e=t.Na(o),t.i.da(e,function(t){"class"==t?i.className=e[t]:i.setAttribute(t,e[t])}),o.id+="_html5_api",o.className="vjs-tech",o.player=i.player=this,this.p("vjs-paused"),this.width(this.q.width,f),this.height(this.q.height,f),o.ge=o.networkState,o.parentNode&&o.parentNode.insertBefore(i,o),t.Ub(o,i),this.c=i,this.b("loadstart",this.xe),this.b("waiting",this.De),this.b(["canplay","canplaythrough","playing","ended"],this.Ce),this.b("seeking",this.Ae),this.b("seeked",this.ze),this.b("ended",this.ue),this.b("play",this.$b),this.b("firstplay",this.ve),this.b("pause",this.Zb),this.b("progress",this.ye),this.b("durationchange",this.Wc),this.b("fullscreenchange",this.we),i},s.xe=function(){this.r("vjs-ended"),this.error(j),this.paused()?la(this,l):this.o("firstplay")},s.Nc=l,s.$b=function(){this.r("vjs-ended"),this.r("vjs-paused"),this.p("vjs-playing"),la(this,f)},s.De=function(){this.p("vjs-waiting")},s.Ce=function(){this.r("vjs-waiting")},s.Ae=function(){this.p("vjs-seeking")},s.ze=function(){this.r("vjs-seeking")},s.ve=function(){this.q.starttime&&this.currentTime(this.q.starttime),this.p("vjs-has-started")},s.Zb=function(){this.r("vjs-playing"),this.p("vjs-paused")},s.ye=function(){1==this.bufferedPercent()&&this.o("loadedalldata")},s.ue=function(){this.p("vjs-ended"),this.q.loop?(this.currentTime(0),this.play()):this.paused()||this.pause()},s.Wc=function(){var t=M(this,"duration");t&&(0>t&&(t=1/0),this.duration(t),1/0===t?this.p("vjs-live"):this.r("vjs-live"))},s.we=function(){this.isFullscreen()?this.p("vjs-fullscreen"):this.r("vjs-fullscreen")},s.play=function(){return N(this,"play"),this},s.pause=function(){return N(this,"pause"),this},s.paused=function(){return M(this,"paused")===l?l:f},s.currentTime=function(t){return t!==b?(N(this,"setCurrentTime",t),this):this.K.currentTime=M(this,"currentTime")||0},s.duration=function(t){return t!==b?(this.K.duration=parseFloat(t),this):(this.K.duration===b&&this.Wc(),this.K.duration||0)},s.remainingTime=function(){return this.duration()-this.currentTime()},s.buffered=function(){var e=M(this,"buffered");return e&&e.length||(e=t.Lb(0,0)),e},s.bufferedPercent=function(){var t,e,i=this.duration(),o=this.buffered(),n=0;if(!i)return 0;for(var s=0;s<o.length;s++)t=o.start(s),(e=o.end(s))>i&&(e=i),n+=e-t;return n/i},s.volume=function(e){return e!==b?(e=Math.max(0,Math.min(1,parseFloat(e))),this.K.volume=e,N(this,"setVolume",e),t.Me(e),this):(e=parseFloat(M(this,"volume")),isNaN(e)?1:e)},s.muted=function(t){return t!==b?(N(this,"setMuted",t),this):M(this,"muted")||l},s.Ta=function(){return M(this,"supportsFullScreen")||l},s.Qc=l,s.isFullscreen=function(t){return t!==b?(this.Qc=!!t,this):this.Qc},s.isFullScreen=function(e){return t.log.warn('player.isFullScreen() has been deprecated, use player.isFullscreen() with a lowercase "s")'),this.isFullscreen(e)},s.requestFullscreen=function(){var e=t.cb.Rb;return this.isFullscreen(f),e?(t.b(document,e.fullscreenchange,t.bind(this,function(i){this.isFullscreen(document[e.fullscreenElement]),this.isFullscreen()===l&&t.n(document,e.fullscreenchange,arguments.callee),this.o("fullscreenchange")})),this.c[e.requestFullscreen]()):this.h.Ta()?N(this,"enterFullScreen"):(this.Jc(),this.o("fullscreenchange")),this},s.requestFullScreen=function(){return t.log.warn('player.requestFullScreen() has been deprecated, use player.requestFullscreen() with a lowercase "s")'),this.requestFullscreen()},s.exitFullscreen=function(){var e=t.cb.Rb;return this.isFullscreen(l),e?document[e.exitFullscreen]():this.h.Ta()?N(this,"exitFullScreen"):(this.Nb(),this.o("fullscreenchange")),this},s.cancelFullScreen=function(){return t.log.warn("player.cancelFullScreen() has been deprecated, use player.exitFullscreen()"),this.exitFullscreen()},s.Jc=function(){this.je=f,this.Ud=document.documentElement.style.overflow,t.b(document,"keydown",t.bind(this,this.Kc)),document.documentElement.style.overflow="hidden",t.p(document.body,"vjs-full-window"),this.o("enterFullWindow")},s.Kc=function(t){27===t.keyCode&&(this.isFullscreen()===f?this.exitFullscreen():this.Nb())},s.Nb=function(){this.je=l,t.n(document,"keydown",this.Kc),document.documentElement.style.overflow=this.Ud,t.r(document.body,"vjs-full-window"),this.o("exitFullWindow")},s.selectSource=function(e){for(var i=0,o=this.q.techOrder;i<o.length;i++){var n=t.ua(o[i]),s=window.videojs[n];if(s){if(s.isSupported())for(var r=0,a=e;r<a.length;r++){var c=a[r];if(s.canPlaySource(c))return{source:c,h:n}}}else t.log.error('The "'+n+'" tech is undefined. Skipped browser support check for that tech.')}return l},s.src=function(e){return e===b?M(this,"src"):(t.i.isArray(e)?ma(this,e):"string"==typeof e?this.src({src:e}):e instanceof Object&&(e.type&&!window.videojs[this.Ua].canPlaySource(e)?ma(this,[e]):(this.K.src=e.src,this.Gc=e.type||"",this.I(function(){window.videojs[this.Ua].prototype.hasOwnProperty("setSource")?N(this,"setSource",e):N(this,"src",e.src),"auto"==this.q.preload&&this.load(),this.q.autoplay&&this.play()}))),this)},s.load=function(){return N(this,"load"),this},s.currentSrc=function(){return M(this,"currentSrc")||this.K.src||""},s.Qd=function(){return this.Gc||""},s.Qa=function(t){return t!==b?(N(this,"setPreload",t),this.q.preload=t,this):M(this,"preload")},s.autoplay=function(t){return t!==b?(N(this,"setAutoplay",t),this.q.autoplay=t,this):M(this,"autoplay")},s.loop=function(t){return t!==b?(N(this,"setLoop",t),this.q.loop=t,this):M(this,"loop")},s.poster=function(t){return t===b?this.Zc:(t||(t=""),this.Zc=t,N(this,"setPoster",t),this.o("posterchange"),this)},s.controls=function(t){return t!==b?(t=!!t,this.Kb!==t&&((this.Kb=t)?(this.r("vjs-controls-disabled"),this.p("vjs-controls-enabled"),this.o("controlsenabled")):(this.r("vjs-controls-enabled"),this.p("vjs-controls-disabled"),this.o("controlsdisabled"))),this):this.Kb},t.Player.prototype.ec,s=t.Player.prototype,s.usingNativeControls=function(t){return t!==b?(t=!!t,this.ec!==t&&((this.ec=t)?(this.p("vjs-using-native-controls"),this.o("usingnativecontrols")):(this.r("vjs-using-native-controls"),this.o("usingcustomcontrols"))),this):this.ec},s.ia=j,s.error=function(e){return e===b?this.ia:e===j?(this.ia=e,this.r("vjs-error"),this):(this.ia=e instanceof t.J?e:new t.J(e),this.o("error"),this.p("vjs-error"),t.log.error("(CODE:"+this.ia.code+" "+t.J.hb[this.ia.code]+")",this.ia.message,this.ia),this)},s.ended=function(){return M(this,"ended")},s.seeking=function(){return M(this,"seeking")},s.Da=f,s.reportUserActivity=function(){this.Da=f},s.dc=f,s.userActive=function(t){return t!==b?((t=!!t)!==this.dc&&((this.dc=t)?(this.Da=f,this.r("vjs-user-inactive"),this.p("vjs-user-active"),this.o("useractive")):(this.Da=l,this.h&&this.h.N("mousemove",function(t){t.stopPropagation(),t.preventDefault()}),this.r("vjs-user-active"),this.p("vjs-user-inactive"),this.o("userinactive"))),this):this.dc},s.playbackRate=function(t){return t!==b?(N(this,"setPlaybackRate",t),this):this.h&&this.h.featuresPlaybackRate?M(this,"playbackRate"):1},s.Pc=l,s.networkState=function(){return M(this,"networkState")},s.readyState=function(){return M(this,"readyState")},s.textTracks=function(){return this.h&&this.h.textTracks()},s.Z=function(){return this.h&&this.h.remoteTextTracks()},s.addTextTrack=function(t,e,i){return this.h&&this.h.addTextTrack(t,e,i)},s.ha=function(t){return this.h&&this.h.addRemoteTextTrack(t)},s.Ba=function(t){this.h&&this.h.removeRemoteTextTrack(t)},t.ub=t.a.extend(),t.ub.prototype.q={wf:"play",children:{playToggle:{},currentTimeDisplay:{},timeDivider:{},durationDisplay:{},remainingTimeDisplay:{},liveDisplay:{},progressControl:{},fullscreenToggle:{},volumeControl:{},muteToggle:{},playbackRateMenuButton:{},subtitlesButton:{},captionsButton:{},chaptersButton:{}}},t.ub.prototype.e=function(){return t.e("div",{className:"vjs-control-bar"})},t.kc=t.a.extend({l:function(e,i){t.a.call(this,e,i)}}),t.kc.prototype.e=function(){var e=t.a.prototype.e.call(this,"div",{className:"vjs-live-controls vjs-control"});return this.B=t.e("div",{className:"vjs-live-display",innerHTML:'<span class="vjs-control-text">'+this.v("Stream Type")+"</span>"+this.v("LIVE"),"aria-live":"off"}),e.appendChild(this.B),e},t.nc=t.w.extend({l:function(e,i){t.w.call(this,e,i),this.b(e,"play",this.$b),this.b(e,"pause",this.Zb)}}),s=t.nc.prototype,s.sa="Play",s.V=function(){return"vjs-play-control "+t.w.prototype.V.call(this)},s.u=function(){this.d.paused()?this.d.play():this.d.pause()},s.$b=function(){this.r("vjs-paused"),this.p("vjs-playing"),this.c.children[0].children[0].innerHTML=this.v("Pause")},s.Zb=function(){this.r("vjs-playing"),this.p("vjs-paused"),this.c.children[0].children[0].innerHTML=this.v("Play")},t.vb=t.a.extend({l:function(e,i){t.a.call(this,e,i),this.b(e,"timeupdate",this.ma)}}),t.vb.prototype.e=function(){var e=t.a.prototype.e.call(this,"div",{className:"vjs-current-time vjs-time-controls vjs-control"});return this.B=t.e("div",{className:"vjs-current-time-display",innerHTML:'<span class="vjs-control-text">Current Time </span>0:00',"aria-live":"off"}),e.appendChild(this.B),e},t.vb.prototype.ma=function(){var e=this.d.ob?this.d.K.currentTime:this.d.currentTime();this.B.innerHTML='<span class="vjs-control-text">'+this.v("Current Time")+"</span> "+t.Ma(e,this.d.duration())},t.wb=t.a.extend({l:function(e,i){t.a.call(this,e,i),this.b(e,"timeupdate",this.ma)}}),t.wb.prototype.e=function(){var e=t.a.prototype.e.call(this,"div",{className:"vjs-duration vjs-time-controls vjs-control"});return this.B=t.e("div",{className:"vjs-duration-display",innerHTML:'<span class="vjs-control-text">'+this.v("Duration Time")+"</span> 0:00","aria-live":"off"}),e.appendChild(this.B),e},t.wb.prototype.ma=function(){var e=this.d.duration();e&&(this.B.innerHTML='<span class="vjs-control-text">'+this.v("Duration Time")+"</span> "+t.Ma(e))},t.tc=t.a.extend({l:function(e,i){t.a.call(this,e,i)}}),t.tc.prototype.e=function(){return t.a.prototype.e.call(this,"div",{className:"vjs-time-divider",innerHTML:"<div><span>/</span></div>"})},t.Db=t.a.extend({l:function(e,i){t.a.call(this,e,i),this.b(e,"timeupdate",this.ma)}}),t.Db.prototype.e=function(){var e=t.a.prototype.e.call(this,"div",{className:"vjs-remaining-time vjs-time-controls vjs-control"});return this.B=t.e("div",{className:"vjs-remaining-time-display",innerHTML:'<span class="vjs-control-text">'+this.v("Remaining Time")+"</span> -0:00","aria-live":"off"}),e.appendChild(this.B),e},t.Db.prototype.ma=function(){this.d.duration()&&(this.B.innerHTML='<span class="vjs-control-text">'+this.v("Remaining Time")+"</span> -"+t.Ma(this.d.remainingTime()))},t.Za=t.w.extend({l:function(e,i){t.w.call(this,e,i)}}),t.Za.prototype.sa="Fullscreen",t.Za.prototype.V=function(){return"vjs-fullscreen-control "+t.w.prototype.V.call(this)},t.Za.prototype.u=function(){this.d.isFullscreen()?(this.d.exitFullscreen(),this.Jb.innerHTML=this.v("Fullscreen")):(this.d.requestFullscreen(),this.Jb.innerHTML=this.v("Non-Fullscreen"))},t.Cb=t.a.extend({l:function(e,i){t.a.call(this,e,i)}}),t.Cb.prototype.q={children:{seekBar:{}}},t.Cb.prototype.e=function(){return t.a.prototype.e.call(this,"div",{className:"vjs-progress-control vjs-control"})},t.qc=t.U.extend({l:function(e,i){t.U.call(this,e,i),this.b(e,"timeupdate",this.Ca),e.I(t.bind(this,this.Ca))}}),s=t.qc.prototype,s.q={children:{loadProgressBar:{},playProgressBar:{},seekHandle:{}},barName:"playProgressBar",handleName:"seekHandle"},s.Yc="timeupdate",s.e=function(){return t.U.prototype.e.call(this,"div",{className:"vjs-progress-holder","aria-label":"video progress bar"})},s.Ca=function(){var e=this.d.ob?this.d.K.currentTime:this.d.currentTime();this.c.setAttribute("aria-valuenow",t.round(100*this.Sb(),2)),this.c.setAttribute("aria-valuetext",t.Ma(e,this.d.duration()))},s.Sb=function(){return this.d.currentTime()/this.d.duration()},s.mb=function(e){t.U.prototype.mb.call(this,e),this.d.ob=f,this.d.p("vjs-scrubbing"),this.df=!this.d.paused(),this.d.pause()},s.ka=function(t){(t=ea(this,t)*this.d.duration())==this.d.duration()&&(t-=.1),this.d.currentTime(t)},s.za=function(e){t.U.prototype.za.call(this,e),this.d.ob=l,this.d.r("vjs-scrubbing"),this.df&&this.d.play()},s.kd=function(){this.d.currentTime(this.d.currentTime()+5)},s.jd=function(){this.d.currentTime(this.d.currentTime()-5)},t.zb=t.a.extend({l:function(e,i){t.a.call(this,e,i),this.b(e,"progress",this.update)}}),t.zb.prototype.e=function(){return t.a.prototype.e.call(this,"div",{className:"vjs-load-progress",innerHTML:'<span class="vjs-control-text"><span>'+this.v("Loaded")+"</span>: 0%</span>"})},t.zb.prototype.update=function(){var e,i,o,n,s=this.d.buffered();e=this.d.duration();var r,a=this.d;for(r=a.buffered(),a=a.duration(),(r=r.end(r.length-1))>a&&(r=a),a=this.c.children,this.c.style.width=100*(r/e||0)+"%",e=0;e<s.length;e++)i=s.start(e),o=s.end(e),(n=a[e])||(n=this.c.appendChild(t.e())),n.style.left=100*(i/r||0)+"%",n.style.width=100*((o-i)/r||0)+"%";for(e=a.length;e>s.length;e--)this.c.removeChild(a[e-1])},t.mc=t.a.extend({l:function(e,i){t.a.call(this,e,i)}}),t.mc.prototype.e=function(){return t.a.prototype.e.call(this,"div",{className:"vjs-play-progress",innerHTML:'<span class="vjs-control-text"><span>'+this.v("Progress")+"</span>: 0%</span>"})},t.$a=t.ga.extend({l:function(e,i){t.ga.call(this,e,i),this.b(e,"timeupdate",this.ma)}}),t.$a.prototype.defaultValue="00:00",t.$a.prototype.e=function(){return t.ga.prototype.e.call(this,"div",{className:"vjs-seek-handle","aria-live":"off"})},t.$a.prototype.ma=function(){var e=this.d.ob?this.d.K.currentTime:this.d.currentTime();this.c.innerHTML='<span class="vjs-control-text">'+t.Ma(e,this.d.duration())+"</span>"},t.Gb=t.a.extend({l:function(e,i){t.a.call(this,e,i),e.h&&e.h.featuresVolumeControl===l&&this.p("vjs-hidden"),this.b(e,"loadstart",function(){e.h.featuresVolumeControl===l?this.p("vjs-hidden"):this.r("vjs-hidden")})}}),t.Gb.prototype.q={children:{volumeBar:{}}},t.Gb.prototype.e=function(){return t.a.prototype.e.call(this,"div",{className:"vjs-volume-control vjs-control"})},t.Fb=t.U.extend({l:function(e,i){t.U.call(this,e,i),this.b(e,"volumechange",this.Ca),e.I(t.bind(this,this.Ca))}}),s=t.Fb.prototype,s.Ca=function(){this.c.setAttribute("aria-valuenow",t.round(100*this.d.volume(),2)),this.c.setAttribute("aria-valuetext",t.round(100*this.d.volume(),2)+"%")},s.q={children:{volumeLevel:{},volumeHandle:{}},barName:"volumeLevel",handleName:"volumeHandle"},s.Yc="volumechange",s.e=function(){return t.U.prototype.e.call(this,"div",{className:"vjs-volume-bar","aria-label":"volume level"})},s.ka=function(t){this.d.muted()&&this.d.muted(l),this.d.volume(ea(this,t))},s.Sb=function(){return this.d.muted()?0:this.d.volume()},s.kd=function(){this.d.volume(this.d.volume()+.1)},s.jd=function(){this.d.volume(this.d.volume()-.1)},t.uc=t.a.extend({l:function(e,i){t.a.call(this,e,i)}}),t.uc.prototype.e=function(){return t.a.prototype.e.call(this,"div",{className:"vjs-volume-level",innerHTML:'<span class="vjs-control-text"></span>'})},t.Hb=t.ga.extend(),t.Hb.prototype.defaultValue="00:00",t.Hb.prototype.e=function(){return t.ga.prototype.e.call(this,"div",{className:"vjs-volume-handle"})},t.qa=t.w.extend({l:function(e,i){t.w.call(this,e,i),this.b(e,"volumechange",this.update),e.h&&e.h.featuresVolumeControl===l&&this.p("vjs-hidden"),this.b(e,"loadstart",function(){e.h.featuresVolumeControl===l?this.p("vjs-hidden"):this.r("vjs-hidden")})}}),t.qa.prototype.e=function(){return t.w.prototype.e.call(this,"div",{className:"vjs-mute-control vjs-control",innerHTML:'<div><span class="vjs-control-text">'+this.v("Mute")+"</span></div>"})},t.qa.prototype.u=function(){this.d.muted(this.d.muted()?l:f)},t.qa.prototype.update=function(){var e=this.d.volume(),i=3;for(0===e||this.d.muted()?i=0:.33>e?i=1:.67>e&&(i=2),this.d.muted()?this.c.children[0].children[0].innerHTML!=this.v("Unmute")&&(this.c.children[0].children[0].innerHTML=this.v("Unmute")):this.c.children[0].children[0].innerHTML!=this.v("Mute")&&(this.c.children[0].children[0].innerHTML=this.v("Mute")),e=0;4>e;e++)t.r(this.c,"vjs-vol-"+e);t.p(this.c,"vjs-vol-"+i)},t.Fa=t.O.extend({l:function(e,i){t.O.call(this,e,i),this.b(e,"volumechange",this.ef),e.h&&e.h.featuresVolumeControl===l&&this.p("vjs-hidden"),this.b(e,"loadstart",function(){e.h.featuresVolumeControl===l?this.p("vjs-hidden"):this.r("vjs-hidden")}),this.p("vjs-menu-button")}}),t.Fa.prototype.Ja=function(){var e=new t.pa(this.d,{Cc:"div"}),i=new t.Fb(this.d,this.q.volumeBar);return i.b("focus",function(){e.p("vjs-lock-showing")}),i.b("blur",function(){G(e)}),e.ba(i),e},t.Fa.prototype.u=function(){t.qa.prototype.u.call(this),t.O.prototype.u.call(this)},t.Fa.prototype.e=function(){return t.w.prototype.e.call(this,"div",{className:"vjs-volume-menu-button vjs-menu-button vjs-control",innerHTML:'<div><span class="vjs-control-text">'+this.v("Mute")+"</span></div>"})},t.Fa.prototype.ef=t.qa.prototype.update,t.oc=t.O.extend({l:function(e,i){t.O.call(this,e,i),this.sd(),this.rd(),this.b(e,"loadstart",this.sd),this.b(e,"ratechange",this.rd)}}),s=t.oc.prototype,s.sa="Playback Rate",s.className="vjs-playback-rate",s.e=function(){var e=t.O.prototype.e.call(this);return this.Sc=t.e("div",{className:"vjs-playback-rate-value",innerHTML:1}),e.appendChild(this.Sc),e},s.Ja=function(){var e=new t.pa(this.k()),i=this.k().options().playbackRates;if(i)for(var o=i.length-1;0<=o;o--)e.ba(new t.Bb(this.k(),{rate:i[o]+"x"}));return e},s.Ca=function(){this.m().setAttribute("aria-valuenow",this.k().playbackRate())},s.u=function(){for(var t=this.k().playbackRate(),e=this.k().options().playbackRates,i=e[0],o=0;o<e.length;o++)if(e[o]>t){i=e[o];break}this.k().playbackRate(i)},s.sd=function(){na(this)?this.r("vjs-hidden"):this.p("vjs-hidden")},s.rd=function(){na(this)&&(this.Sc.innerHTML=this.k().playbackRate()+"x")},t.Bb=t.M.extend({Cc:"button",l:function(e,i){var o=this.label=i.rate,n=this.$c=parseFloat(o,10);i.label=o,i.selected=1===n,t.M.call(this,e,i),this.b(e,"ratechange",this.update)}}),t.Bb.prototype.u=function(){t.M.prototype.u.call(this),this.k().playbackRate(this.$c)},t.Bb.prototype.update=function(){this.selected(this.k().playbackRate()==this.$c)},t.pc=t.w.extend({l:function(e,i){t.w.call(this,e,i),this.update(),e.b("posterchange",t.bind(this,this.update))}}),s=t.pc.prototype,s.dispose=function(){this.k().n("posterchange",this.update),t.w.prototype.dispose.call(this)},s.e=function(){var e=t.e("div",{className:"vjs-poster",tabIndex:-1});return t.wd||(this.Ob=t.e("img"),e.appendChild(this.Ob)),e},s.update=function(){var t=this.k().poster();this.la(t),t?this.show():this.Y()},s.la=function(t){var e;this.Ob?this.Ob.src=t:(e="",t&&(e='url("'+t+'")'),this.c.style.backgroundImage=e)},s.u=function(){this.d.play()},t.lc=t.a.extend({l:function(e,i){t.a.call(this,e,i)}}),t.lc.prototype.e=function(){return t.a.prototype.e.call(this,"div",{className:"vjs-loading-spinner"})},t.sb=t.w.extend(),t.sb.prototype.e=function(){return t.w.prototype.e.call(this,"div",{className:"vjs-big-play-button",innerHTML:'<span aria-hidden="true"></span>',"aria-label":"play video"})},t.sb.prototype.u=function(){this.d.play()},t.xb=t.a.extend({l:function(e,i){t.a.call(this,e,i),this.update(),this.b(e,"error",this.update)}}),t.xb.prototype.e=function(){var e=t.a.prototype.e.call(this,"div",{className:"vjs-error-display"});return this.B=t.e("div"),e.appendChild(this.B),e},t.xb.prototype.update=function(){this.k().error()&&(this.B.innerHTML=this.v(this.k().error().message))},t.j=t.a.extend({l:function(e,i,o){(i=i||{}).dd=l,t.a.call(this,e,i,o),this.featuresProgressEvents||this.re(),this.featuresTimeupdateEvents||this.se(),this.fe(),this.featuresNativeTextTracks||this.Vd(),this.he()}}),s=t.j.prototype,s.fe=function(){var t,e;t=this.k(),e=function(){t.controls()&&!t.usingNativeControls()&&this.Id()},this.I(e),this.b(t,"controlsenabled",e),this.b(t,"controlsdisabled",this.He),this.I(function(){this.networkState&&0<this.networkState()&&this.k().o("loadstart")})},s.Id=function(){var t;this.b("mousedown",this.u),this.b("touchstart",function(){t=this.d.userActive()}),this.b("touchmove",function(){t&&this.k().reportUserActivity()}),this.b("touchend",function(t){t.preventDefault()}),da(this),this.b("tap",this.Be)},s.He=function(){this.n("tap"),this.n("touchstart"),this.n("touchmove"),this.n("touchleave"),this.n("touchcancel"),this.n("touchend"),this.n("click"),this.n("mousedown")},s.u=function(t){0===t.button&&this.k().controls()&&(this.k().paused()?this.k().play():this.k().pause())},s.Be=function(){this.k().userActive(!this.k().userActive())},s.re=function(){this.Uc=f,this.$e()},s.qe=function(){this.Uc=l,this.ld()},s.$e=function(){this.Ge=this.setInterval(function(){var t=this.k().bufferedPercent();this.Md!=t&&this.k().o("progress"),this.Md=t,1===t&&this.ld()},500)},s.ld=function(){this.clearInterval(this.Ge)},s.se=function(){var t=this.d;this.Yb=f,this.b(t,"play",this.pd),this.b(t,"pause",this.rb),this.N("timeupdate",function(){this.featuresTimeupdateEvents=f,this.Vc()})},s.Vc=function(){var t=this.d;this.Yb=l,this.rb(),this.n(t,"play",this.pd),this.n(t,"pause",this.rb)},s.pd=function(){this.Fc&&this.rb(),this.Fc=this.setInterval(function(){this.k().o("timeupdate")},250)},s.rb=function(){this.clearInterval(this.Fc),this.k().o("timeupdate")},s.dispose=function(){this.Uc&&this.qe(),this.Yb&&this.Vc(),t.a.prototype.dispose.call(this)},s.bc=function(){this.Yb&&this.k().o("timeupdate")},s.he=function(){function e(){var t=o.ea("textTrackDisplay");t&&t.C()}var i,o=this.d;(i=this.textTracks())&&(i.addEventListener("removetrack",e),i.addEventListener("addtrack",e),this.b("dispose",t.bind(this,function(){i.removeEventListener("removetrack",e),i.removeEventListener("addtrack",e)})))},s.Vd=function(){var e,i,o,n=this.d;window.WebVTT||((o=document.createElement("script")).src=n.options()["vtt.js"]||"../node_modules/vtt.js/dist/vtt.js",n.m().appendChild(o),window.WebVTT=f),(i=this.textTracks())&&(e=function(){var e,i,o;for((o=n.ea("textTrackDisplay")).C(),e=0;e<this.length;e++)(i=this[e]).removeEventListener("cuechange",t.bind(o,o.C)),"showing"===i.mode&&i.addEventListener("cuechange",t.bind(o,o.C))},i.addEventListener("change",e),this.b("dispose",t.bind(this,function(){i.removeEventListener("change",e)})))},s.textTracks=function(){return this.d.od=this.d.od||new t.F,this.d.od},s.Z=function(){return this.d.ad=this.d.ad||new t.F,this.d.ad},O=function(e,i,o,n,s){var r=e.textTracks();return(s=s||{}).kind=i,o&&(s.label=o),n&&(s.language=n),s.player=e.d,P(r,e=new t.t(s)),e},t.j.prototype.addTextTrack=function(t,e,i){if(!t)throw Error("TextTrack kind is required but was not provided");return O(this,t,e,i)},t.j.prototype.ha=function(t){return t=O(this,t.kind,t.label,t.language,t),P(this.Z(),t),{T:t}},t.j.prototype.Ba=function(t){Q(this.textTracks(),t),Q(this.Z(),t)},t.j.prototype.fd=m(),t.j.prototype.featuresVolumeControl=f,t.j.prototype.featuresFullscreenResize=l,t.j.prototype.featuresPlaybackRate=l,t.j.prototype.featuresProgressEvents=l,t.j.prototype.featuresTimeupdateEvents=l,t.j.prototype.featuresNativeTextTracks=l,t.j.gc=function(e){e.Ra=function(t,i){var o=e.gd;o||(o=e.gd=[]),i===b&&(i=o.length),o.splice(i,0,t)},e.pb=function(t){for(var i=e.gd||[],o=0;o<i.length;o++)if(i[o].eb(t))return i[o];return j},e.zc=function(t){var i=e.pb(t);return i?i.eb(t):""},e.prototype.Sa=function(i){var o=e.pb(i);return o||(e.S?o=e.S:t.log.error("No source hander found for the current source.")),this.Ka(),this.n("dispose",this.Ka),this.Ec=i,this.cc=o.Tb(i,this),this.b("dispose",this.Ka),this},e.prototype.Ka=function(){this.cc&&this.cc.dispose&&this.cc.dispose()}},t.media={},t.f=t.j.extend({l:function(e,i,o){var n,s,r,a,c,u;for(i.nativeCaptions!==l&&i.nativeTextTracks!==l||(this.featuresNativeTextTracks=l),t.j.call(this,e,i,o),o=t.f.yb.length-1;0<=o;o--)this.b(t.f.yb[o],this.Wd);if((i=i.source)&&(this.c.currentSrc!==i.src||e.L&&3===e.L.ge)&&this.Sa(i),this.c.hasChildNodes()){for(n=(o=this.c.childNodes).length,i=[];n--;)"track"===(s=o[n]).nodeName.toLowerCase()&&(this.featuresNativeTextTracks?P(this.Z(),s.track):i.push(s));for(o=0;o<i.length;o++)this.c.removeChild(i[o])}(this.featuresNativeTextTracks&&this.b("loadstart",t.bind(this,this.ee)),t.Eb&&e.options().nativeControlsForTouch===f)&&(r=this,i=(a=this.k()).controls(),r.c.controls=!!i,c=function(){r.c.controls=f},u=function(){r.c.controls=l},a.b("controlsenabled",c),a.b("controlsdisabled",u),i=function(){a.n("controlsenabled",c),a.n("controlsdisabled",u)},r.b("dispose",i),a.b("usingcustomcontrols",i),a.usingNativeControls(f));e.I(function(){this.L&&this.q.autoplay&&this.paused()&&(delete this.L.poster,this.play())}),this.Wa()}}),s=t.f.prototype,s.dispose=function(){t.f.Mb(this.c),t.j.prototype.dispose.call(this)},s.e=function(){var e,i,o,n=this.d,s=n.L;if(!s||this.movingMediaElementInDOM===l){if(s?(o=s.cloneNode(l),t.f.Mb(s),s=o,n.L=j):(s=t.e("video"),o=videojs.$.ya({},n.Xe),(!t.Eb||n.options().nativeControlsForTouch!==f)&&delete o.controls,t.ed(s,t.i.D(o,{id:n.id()+"_html5_api",class:"vjs-tech"}))),s.player=n,n.q.qd)for(o=0;o<n.q.qd.length;o++)e=n.q.qd[o],(i=document.createElement("track")).Wb=e.Wb,i.label=e.label,i.hd=e.hd,i.src=e.src,"default"in e&&i.setAttribute("default","default"),s.appendChild(i);t.Ub(s,n.m())}for(o=(e=["autoplay","preload","loop","muted"]).length-1;0<=o;o--){i=e[o];var r={};void 0!==n.q[i]&&(r[i]=n.q[i]),t.ed(s,r)}return s},s.ee=function(){for(var t,e=this.c.querySelectorAll("track"),i=e.length,o={captions:1,subtitles:1};i--;)(t=e[i].T)&&t.kind in o&&!e[i].default&&(t.mode="disabled")},s.Wd=function(t){"error"==t.type&&this.error()?this.k().error(this.error().code):(t.bubbles=l,this.k().o(t))},s.play=function(){this.c.play()},s.pause=function(){this.c.pause()},s.paused=function(){return this.c.paused},s.currentTime=function(){return this.c.currentTime},s.bc=function(e){try{this.c.currentTime=e}catch(e){t.log(e,"Video is not ready. (Video.js)")}},s.duration=function(){return this.c.duration||0},s.buffered=function(){return this.c.buffered},s.volume=function(){return this.c.volume},s.Se=function(t){this.c.volume=t},s.muted=function(){return this.c.muted},s.Oe=function(t){this.c.muted=t},s.width=function(){return this.c.offsetWidth},s.height=function(){return this.c.offsetHeight},s.Ta=function(){return"function"!=typeof this.c.webkitEnterFullScreen||!/Android/.test(t.P)&&/Chrome|Mac OS X 10.5/.test(t.P)?l:f},s.Ic=function(){var t=this.c;"webkitDisplayingFullscreen"in t&&this.N("webkitbeginfullscreen",function(){this.d.isFullscreen(f),this.N("webkitendfullscreen",function(){this.d.isFullscreen(l),this.d.o("fullscreenchange")}),this.d.o("fullscreenchange")}),t.paused&&t.networkState<=t.jf?(this.c.play(),this.setTimeout(function(){t.pause(),t.webkitEnterFullScreen()},0)):t.webkitEnterFullScreen()},s.Xd=function(){this.c.webkitExitFullScreen()},s.src=function(t){if(t===b)return this.c.src;this.la(t)},s.la=function(t){this.c.src=t},s.load=function(){this.c.load()},s.currentSrc=function(){return this.c.currentSrc},s.poster=function(){return this.c.poster},s.fd=function(t){this.c.poster=t},s.Qa=function(){return this.c.Qa},s.Qe=function(t){this.c.Qa=t},s.autoplay=function(){return this.c.autoplay},s.Le=function(t){this.c.autoplay=t},s.controls=function(){return this.c.controls},s.loop=function(){return this.c.loop},s.Ne=function(t){this.c.loop=t},s.error=function(){return this.c.error},s.seeking=function(){return this.c.seeking},s.ended=function(){return this.c.ended},s.playbackRate=function(){return this.c.playbackRate},s.Pe=function(t){this.c.playbackRate=t},s.networkState=function(){return this.c.networkState},s.readyState=function(){return this.c.readyState},s.textTracks=function(){return this.featuresNativeTextTracks?this.c.textTracks:t.j.prototype.textTracks.call(this)},s.addTextTrack=function(e,i,o){return this.featuresNativeTextTracks?this.c.addTextTrack(e,i,o):t.j.prototype.addTextTrack.call(this,e,i,o)},s.ha=function(e){if(!this.featuresNativeTextTracks)return t.j.prototype.ha.call(this,e);var i=document.createElement("track");return(e=e||{}).kind&&(i.kind=e.kind),e.label&&(i.label=e.label),(e.language||e.srclang)&&(i.srclang=e.language||e.srclang),e.default&&(i.default=e.default),e.id&&(i.id=e.id),e.src&&(i.src=e.src),this.m().appendChild(i),i.track.mode="metadata"===i.T.kind?"hidden":"disabled",i.onload=function(){var t=i.track;2<=i.readyState&&("metadata"===t.kind&&"hidden"!==t.mode?t.mode="hidden":"metadata"!==t.kind&&"disabled"!==t.mode&&(t.mode="disabled"),i.onload=j)},P(this.Z(),i.T),i},s.Ba=function(e){if(!this.featuresNativeTextTracks)return t.j.prototype.Ba.call(this,e);var i,o;for(Q(this.Z(),e),i=this.m().querySelectorAll("track"),o=0;o<i.length;o++)if(i[o]===e||i[o].track===e){i[o].parentNode.removeChild(i[o]);break}},t.f.isSupported=function(){try{t.A.volume=.5}catch(t){return l}return!!t.A.canPlayType},t.j.gc(t.f),t.f.S={},t.f.S.eb=function(e){function i(e){try{return t.A.canPlayType(e)}catch(t){return""}}return e.type?i(e.type):e.src?i("video/"+(e=(e=e.src.match(/\.([^.\/\?]+)(\?[^\/]+)?$/i))&&e[1])):""},t.f.S.Tb=function(t,e){e.la(t.src)},t.f.S.dispose=m(),t.f.Ra(t.f.S),t.f.Od=function(){var e=t.A.volume;return t.A.volume=e/2+.1,e!==t.A.volume},t.f.Nd=function(){var e=t.A.playbackRate;return t.A.playbackRate=e/2+.1,e!==t.A.playbackRate},t.f.Ve=function(){var e;return(e=!!t.A.textTracks)&&0<t.A.textTracks.length&&(e="number"!=typeof t.A.textTracks[0].mode),e&&t.jc&&(e=l),e},t.f.prototype.featuresVolumeControl=t.f.Od(),t.f.prototype.featuresPlaybackRate=t.f.Nd(),t.f.prototype.movingMediaElementInDOM=!t.Ad,t.f.prototype.featuresFullscreenResize=f,t.f.prototype.featuresProgressEvents=f,t.f.prototype.featuresNativeTextTracks=t.f.Ve();var S,oa=/^application\/(?:x-|vnd\.apple\.)mpegurl/i,pa=/^video\/mp4/i;function qa(){var t=T[U],e=t.charAt(0).toUpperCase()+t.slice(1);ra["set"+e]=function(e){return this.c.vjs_setProperty(t,e)}}function sa(t){ra[t]=function(){return this.c.vjs_getProperty(t)}}t.f.Xc=function(){4<=t.hc&&(S||(S=t.A.constructor.prototype.canPlayType),t.A.constructor.prototype.canPlayType=function(t){return t&&oa.test(t)?"maybe":S.call(this,t)}),t.Ed&&(S||(S=t.A.constructor.prototype.canPlayType),t.A.constructor.prototype.canPlayType=function(t){return t&&pa.test(t)?"maybe":S.call(this,t)})},t.f.bf=function(){var e=t.A.constructor.prototype.canPlayType;return t.A.constructor.prototype.canPlayType=S,S=j,e},t.f.Xc(),t.f.yb="loadstart suspend abort error emptied stalled loadedmetadata loadeddata canplay canplaythrough playing waiting seeking seeked ended durationchange timeupdate progress play pause ratechange volumechange".split(" "),t.f.Mb=function(t){if(t){for(t.player=j,t.parentNode&&t.parentNode.removeChild(t);t.hasChildNodes();)t.removeChild(t.firstChild);if(t.removeAttribute("src"),"function"==typeof t.load)try{t.load()}catch(t){}}},t.g=t.j.extend({l:function(e,i,o){t.j.call(this,e,i,o);var n=i.source;o=i.parentEl;var s=this.c=t.e("div",{id:e.id()+"_temp_flash"}),r=e.id()+"_flash_api",a=e.q,c=(a=t.i.D({readyFunction:"videojs.Flash.onReady",eventProxyFunction:"videojs.Flash.onEvent",errorEventProxyFunction:"videojs.Flash.onError",autoplay:a.autoplay,preload:a.Qa,loop:a.loop,muted:a.muted},i.flashVars),t.i.D({wmode:"opaque",bgcolor:"#000000"},i.params));r=t.i.D({id:r,name:r,class:"vjs-tech"},i.attributes);n&&this.I(function(){this.Sa(n)}),t.Ub(s,o),i.startTime&&this.I(function(){this.load(),this.play(),this.currentTime(i.startTime)}),t.jc&&this.I(function(){this.b("mousemove",function(){this.k().o({type:"mousemove",bubbles:l})})}),e.b("stageclick",e.reportUserActivity),this.c=t.g.Hc(i.swf,s,a,c,r)}}),s=t.g.prototype,s.dispose=function(){t.j.prototype.dispose.call(this)},s.play=function(){this.c.vjs_play()},s.pause=function(){this.c.vjs_pause()},s.src=function(t){return t===b?this.currentSrc():this.la(t)},s.la=function(e){if(e=t.$d(e),this.c.vjs_src(e),this.d.autoplay()){var i=this;this.setTimeout(function(){i.play()},0)}},t.g.prototype.setCurrentTime=function(e){this.oe=e,this.c.vjs_setProperty("currentTime",e),t.j.prototype.bc.call(this)},t.g.prototype.currentTime=function(){return this.seeking()?this.oe||0:this.c.vjs_getProperty("currentTime")},t.g.prototype.currentSrc=function(){return this.Ec?this.Ec.src:this.c.vjs_getProperty("currentSrc")},t.g.prototype.load=function(){this.c.vjs_load()},t.g.prototype.poster=function(){this.c.vjs_getProperty("poster")},t.g.prototype.setPoster=m(),t.g.prototype.buffered=function(){return t.Lb(0,this.c.vjs_getProperty("buffered"))},t.g.prototype.Ta=q(l),t.g.prototype.Ic=q(l);var ra=t.g.prototype,T="rtmpConnection rtmpStream preload defaultPlaybackRate playbackRate autoplay loop mediaGroup controller controls volume muted defaultMuted".split(" "),ta="error networkState readyState seeking initialTime duration startOffsetTime paused played seekable ended videoTracks audioTracks videoWidth videoHeight".split(" "),U,va,V,ua;for(U=0;U<T.length;U++)sa(T[U]),qa();for(U=0;U<ta.length;U++)sa(ta[U]);for(var wa in t.g.isSupported=function(){return 10<=t.g.version()[0]},t.j.gc(t.g),t.g.S={},t.g.S.eb=function(e){return e.type&&e.type.replace(/;.*/,"").toLowerCase()in t.g.Zd?"maybe":""},t.g.S.Tb=function(t,e){e.la(t.src)},t.g.S.dispose=m(),t.g.Ra(t.g.S),t.g.Zd={"video/flv":"FLV","video/x-flv":"FLV","video/mp4":"MP4","video/m4v":"MP4"},t.g.onReady=function(e){var i;(i=(e=t.m(e))&&e.parentNode&&e.parentNode.player)&&(e.player=i,t.g.checkReady(i.h))},t.g.checkReady=function(e){e.m()&&(e.m().vjs_getProperty?e.Wa():this.setTimeout(function(){t.g.checkReady(e)},50))},t.g.onEvent=function(e,i){t.m(e).player.o(i)},t.g.onError=function(e,i){var o=t.m(e).player,n="FLASH: "+i;"srcnotfound"==i?o.error({code:4,message:n}):o.error(n)},t.g.version=function(){var t="0,0,0";try{t=new window.ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version").replace(/\D+/g,",").match(/^,?(.+),?$/)[1]}catch(e){try{navigator.mimeTypes["application/x-shockwave-flash"].enabledPlugin&&(t=(navigator.plugins["Shockwave Flash 2.0"]||navigator.plugins["Shockwave Flash"]).description.replace(/\D+/g,",").match(/^,?(.+),?$/)[1])}catch(t){}}return t.split(",")},t.g.Hc=function(e,i,o,n,s){e=t.g.ce(e,o,n,s),e=t.e("div",{innerHTML:e}).childNodes[0],o=i.parentNode,i.parentNode.replaceChild(e,i);var r=o.childNodes[0];return setTimeout(function(){r.style.display="block"},1e3),e},t.g.ce=function(e,i,o,n){var s="",r="",a="";return i&&t.i.da(i,function(t,e){s+=t+"="+e+"&amp;"}),o=t.i.D({movie:e,flashvars:s,allowScriptAccess:"always",allowNetworking:"all"},o),t.i.da(o,function(t,e){r+='<param name="'+t+'" value="'+e+'" />'}),n=t.i.D({data:e,width:"100%",height:"100%"},n),t.i.da(n,function(t,e){a+=t+'="'+e+'" '}),'<object type="application/x-shockwave-flash" '+a+">"+r+"</object>"},t.g.Ue={"rtmp/mp4":"MP4","rtmp/flv":"FLV"},t.g.Hf=function(t,e){return t+"&"+e},t.g.Te=function(t){var e={Bc:"",md:""};if(!t)return e;var i,o=t.indexOf("&");return-1!==o?i=o+1:0===(o=i=t.lastIndexOf("/")+1)&&(o=i=t.length),e.Bc=t.substring(0,o),e.md=t.substring(i,t.length),e},t.g.me=function(e){return e in t.g.Ue},t.g.Gd=/^rtmp[set]?:\/\//i,t.g.le=function(e){return t.g.Gd.test(e)},t.g.ac={},t.g.ac.eb=function(e){return t.g.me(e.type)||t.g.le(e.src)?"maybe":""},t.g.ac.Tb=function(e,i){var o=t.g.Te(e.src);i.setRtmpConnection(o.Bc),i.setRtmpStream(o.md)},t.g.Ra(t.g.ac),t.Fd=t.a.extend({l:function(e,i,o){if(t.a.call(this,e,i,o),e.q.sources&&0!==e.q.sources.length)e.src(e.q.sources);else for(i=0,o=e.q.techOrder;i<o.length;i++){var n=t.ua(o[i]),s=window.videojs[n];if(s&&s.isSupported()){ka(e,n);break}}}}),t.rc={disabled:"disabled",hidden:"hidden",showing:"showing"},t.Hd={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},t.t=function(e){var i,o,n,s,r,a,c,u,h,p,d;if(!(e=e||{}).player)throw Error("A player was not provided.");if(i=this,t.oa)for(d in i=document.createElement("custom"),t.t.prototype)i[d]=t.t.prototype[d];if(i.d=e.player,n=t.rc[e.mode]||"disabled",s=t.Hd[e.kind]||"subtitles",r=e.label||"",a=e.language||e.srclang||"",o=e.id||"vjs_text_track_"+t.s++,"metadata"!==s&&"chapters"!==s||(n="hidden"),i.X=[],i.Ga=[],c=new t.W(i.X),u=new t.W(i.Ga),p=l,h=t.bind(i,function(){this.activeCues,p&&(this.trigger("cuechange"),p=l)}),"disabled"!==n&&i.d.b("timeupdate",h),Object.defineProperty(i,"kind",{get:function(){return s},set:Function.prototype}),Object.defineProperty(i,"label",{get:function(){return r},set:Function.prototype}),Object.defineProperty(i,"language",{get:function(){return a},set:Function.prototype}),Object.defineProperty(i,"id",{get:function(){return o},set:Function.prototype}),Object.defineProperty(i,"mode",{get:function(){return n},set:function(e){t.rc[e]&&("showing"===(n=e)&&this.d.b("timeupdate",h),this.o("modechange"))}}),Object.defineProperty(i,"cues",{get:function(){return this.Xb?c:j},set:Function.prototype}),Object.defineProperty(i,"activeCues",{get:function(){var t,e,i,o,n;if(!this.Xb)return j;if(0===this.cues.length)return u;for(o=this.d.currentTime(),t=0,e=this.cues.length,i=[];t<e;t++)(n=this.cues[t]).startTime<=o&&n.endTime>=o?i.push(n):n.startTime===n.endTime&&n.startTime<=o&&n.startTime+.5>=o&&i.push(n);if(p=l,i.length!==this.Ga.length)p=f;else for(t=0;t<i.length;t++)-1===ua.call(this.Ga,i[t])&&(p=f);return this.Ga=i,u.qb(this.Ga),u},set:Function.prototype}),e.src?va(e.src,i):i.Xb=f,t.oa)return i},t.t.prototype=t.i.create(t.z.prototype),t.t.prototype.constructor=t.t,t.t.prototype.bb={cuechange:"cuechange"},t.t.prototype.vc=function(t){var e=this.d.textTracks(),i=0;if(e)for(;i<e.length;i++)e[i]!==this&&e[i].bd(t);this.X.push(t),this.cues.qb(this.X)},t.t.prototype.bd=function(t){for(var e=0,i=this.X.length,o=l;e<i;e++)this.X[e]===t&&(this.X.splice(e,1),o=f);o&&this.Dc.qb(this.X)},va=function(e,i){t.ff(e,t.bind(this,function(e,o,n){if(e)return t.log.error(e);i.Xb=f,V(n,i)}))},V=function(e,i){if("function"!=typeof window.WebVTT)window.setTimeout(function(){V(e,i)},25);else{var o=new window.WebVTT.Parser(window,window.vttjs,window.WebVTT.StringDecoder());o.oncue=function(t){i.vc(t)},o.onparsingerror=function(e){t.log.error(e)},o.parse(e),o.flush()}},ua=function(t,e){var i;if(this==j)throw new TypeError('"this" is null or not defined');var o=Object(this),n=o.length>>>0;if(0===n)return-1;if(i=+e||0,1/0===Math.abs(i)&&(i=0),i>=n)return-1;for(i=Math.max(0<=i?i:n-Math.abs(i),0);i<n;){if(i in o&&o[i]===t)return i;i++}return-1},t.F=function(e){var i,o=this,n=0;if(t.oa)for(i in o=document.createElement("custom"),t.F.prototype)o[i]=t.F.prototype[i];for(e=e||[],o.Va=[],Object.defineProperty(o,"length",{get:function(){return this.Va.length}});n<e.length;n++)P(o,e[n]);if(t.oa)return o},t.F.prototype=t.i.create(t.z.prototype),t.F.prototype.constructor=t.F,t.F.prototype.bb={change:"change",addtrack:"addtrack",removetrack:"removetrack"},t.F.prototype.bb)t.F.prototype["on"+wa]=j;function P(e,i){var o=e.Va.length;""+o in e||Object.defineProperty(e,o,{get:function(){return this.Va[o]}}),i.addEventListener("modechange",t.bind(e,function(){this.o("change")})),e.Va.push(i),e.o({type:"addtrack",T:i})}function Q(t,e){for(var i=0,o=t.length;i<o;i++)if(t[i]===e){t.Va.splice(i,1);break}t.o({type:"removetrack",T:e})}function W(t,e){return"rgba("+parseInt(t[1]+t[1],16)+","+parseInt(t[2]+t[2],16)+","+parseInt(t[3]+t[3],16)+","+e+")"}t.F.prototype.de=function(t){for(var e,i=0,o=this.length,n=j;i<o;i++)if((e=this[i]).id===t){n=e;break}return n},t.W=function(e){var i,o=this;if(t.oa)for(i in o=document.createElement("custom"),t.W.prototype)o[i]=t.W.prototype[i];if(t.W.prototype.qb.call(o,e),Object.defineProperty(o,"length",{get:n("pe")}),t.oa)return o},t.W.prototype.qb=function(t){var e=this.length||0,i=0,o=t.length;if(this.X=t,this.pe=t.length,t=function(t){""+t in this||Object.defineProperty(this,""+t,{get:function(){return this.X[t]}})},e<o)for(i=e;i<o;i++)t.call(this,i)},t.W.prototype.be=function(t){for(var e,i=0,o=this.length,n=j;i<o;i++)if((e=this[i]).id===t){n=e;break}return n},t.ra=t.a.extend({l:function(e,i,o){t.a.call(this,e,i,o),e.b("loadstart",t.bind(this,this.Ze)),e.I(t.bind(this,function(){var i,o,n;if(e.h&&e.h.featuresNativeTextTracks)this.Y();else for(e.b("fullscreenchange",t.bind(this,this.C)),o=e.q.tracks||[],i=0;i<o.length;i++)n=o[i],this.d.ha(n)}))}}),t.ra.prototype.Ze=function(){this.d.h&&this.d.h.featuresNativeTextTracks?this.Y():this.show()},t.ra.prototype.e=function(){return t.a.prototype.e.call(this,"div",{className:"vjs-text-track-display"})},t.ra.prototype.Pd=function(){"function"==typeof window.WebVTT&&window.WebVTT.processCues(window,[],this.c)};var xa={xf:"monospace",Df:"sans-serif",Ff:"serif",yf:'"Andale Mono", "Lucida Console", monospace',zf:'"Courier New", monospace',Bf:"sans-serif",Cf:"serif",of:'"Comic Sans MS", Impact, fantasy',Ef:'"Monotype Corsiva", cursive',Gf:'"Andale Mono", "Lucida Console", monospace, sans-serif'};function X(t){var e;return t.Ke?e=t.Ke[0]:t.options&&(e=t.options[t.options.selectedIndex]),e.value}function Y(t,e){var i;if(e){for(i=0;i<t.options.length&&t.options[i].value!==e;i++);t.selectedIndex=i}}if(t.ra.prototype.C=function(){var t,e=this.d.textTracks(),i=0;if(this.Pd(),e)for(;i<e.length;i++)"showing"===(t=e[i]).mode&&this.cf(t)},t.ra.prototype.cf=function(t){if("function"==typeof window.WebVTT&&t.activeCues){for(var e,i=0,o=this.d.textTrackSettings.Lc(),n=[];i<t.activeCues.length;i++)n.push(t.activeCues[i]);for(window.WebVTT.processCues(window,t.activeCues,this.c),i=n.length;i--;){if(t=n[i].pf,o.color&&(t.firstChild.style.color=o.color),o.nd)try{t.firstChild.style.color=W(o.color||"#fff",o.nd)}catch(t){}if(o.backgroundColor&&(t.firstChild.style.backgroundColor=o.backgroundColor),o.yc)try{t.firstChild.style.backgroundColor=W(o.backgroundColor||"#000",o.yc)}catch(t){}if(o.fc)if(o.ud)try{t.style.backgroundColor=W(o.fc,o.ud)}catch(t){}else t.style.backgroundColor=o.fc;o.La&&("dropshadow"===o.La?t.firstChild.style.textShadow="2px 2px 3px #222, 2px 2px 4px #222, 2px 2px 5px #222":"raised"===o.La?t.firstChild.style.textShadow="1px 1px #222, 2px 2px #222, 3px 3px #222":"depressed"===o.La?t.firstChild.style.textShadow="1px 1px #ccc, 0 1px #ccc, -1px -1px #222, 0 -1px #222":"uniform"===o.La&&(t.firstChild.style.textShadow="0 0 4px #222, 0 0 4px #222, 0 0 4px #222, 0 0 4px #222")),o.Qb&&1!==o.Qb&&(e=window.Af(t.style.fontSize),t.style.fontSize=e*o.Qb+"px",t.style.height="auto",t.style.top="auto",t.style.bottom="2px"),o.fontFamily&&"default"!==o.fontFamily&&("small-caps"===o.fontFamily?t.firstChild.style.fontVariant="small-caps":t.firstChild.style.fontFamily=xa[o.fontFamily])}}},t.aa=t.M.extend({l:function(e,i){var o,n,s=this.T=i.track,r=e.textTracks();r&&(o=t.bind(this,function(){var e,i,o,n="showing"===this.T.mode;if(this instanceof t.Ab)for(n=f,i=0,o=r.length;i<o;i++)if((e=r[i]).kind===this.T.kind&&"showing"===e.mode){n=l;break}this.selected(n)}),r.addEventListener("change",o),e.b("dispose",function(){r.removeEventListener("change",o)})),i.label=s.label||s.language||"Unknown",i.selected=s.default||"showing"===s.mode,t.M.call(this,e,i),r&&r.onchange===b&&this.b(["tap","click"],function(){if("object"!=typeof window.yd)try{n=new window.yd("change")}catch(t){}n||(n=document.createEvent("Event")).initEvent("change",f,f),r.dispatchEvent(n)})}}),t.aa.prototype.u=function(){var e,i=this.T.kind,o=this.d.textTracks(),n=0;if(t.M.prototype.u.call(this),o)for(;n<o.length;n++)(e=o[n]).kind===i&&(e.mode=e===this.T?"showing":"disabled")},t.Ab=t.aa.extend({l:function(e,i){i.track={kind:i.kind,player:e,label:i.kind+" off",default:l,mode:"disabled"},t.aa.call(this,e,i),this.selected(f)}}),t.tb=t.aa.extend({l:function(e,i){i.track={kind:i.kind,player:e,label:i.kind+" settings",default:l,mode:"disabled"},t.aa.call(this,e,i),this.p("vjs-texttrack-settings")}}),t.tb.prototype.u=function(){this.k().ea("textTrackSettings").show()},t.Q=t.O.extend({l:function(e,i){var o,n;t.O.call(this,e,i),o=this.d.textTracks(),1>=this.H.length&&this.Y(),o&&(n=t.bind(this,this.update),o.addEventListener("removetrack",n),o.addEventListener("addtrack",n),this.d.b("dispose",function(){o.removeEventListener("removetrack",n),o.removeEventListener("addtrack",n)}))}}),t.Q.prototype.Ia=function(){var e,i,o=[];if(this instanceof t.na&&(!this.k().h||!this.k().h.featuresNativeTextTracks)&&o.push(new t.tb(this.d,{kind:this.fa})),o.push(new t.Ab(this.d,{kind:this.fa})),!(i=this.d.textTracks()))return o;for(var n=0;n<i.length;n++)(e=i[n]).kind===this.fa&&o.push(new t.aa(this.d,{track:e}));return o},t.na=t.Q.extend({l:function(e,i,o){t.Q.call(this,e,i,o),this.c.setAttribute("aria-label","Captions Menu")}}),t.na.prototype.fa="captions",t.na.prototype.sa="Captions",t.na.prototype.className="vjs-captions-button",t.na.prototype.update=function(){var e=2;t.Q.prototype.update.call(this),this.k().h&&this.k().h.featuresNativeTextTracks&&(e=1),this.H&&this.H.length>e?this.show():this.Y()},t.ab=t.Q.extend({l:function(e,i,o){t.Q.call(this,e,i,o),this.c.setAttribute("aria-label","Subtitles Menu")}}),t.ab.prototype.fa="subtitles",t.ab.prototype.sa="Subtitles",t.ab.prototype.className="vjs-subtitles-button",t.Xa=t.Q.extend({l:function(e,i,o){t.Q.call(this,e,i,o),this.c.setAttribute("aria-label","Chapters Menu")}}),s=t.Xa.prototype,s.fa="chapters",s.sa="Chapters",s.className="vjs-chapters-button",s.Ia=function(){var e,i,o=[];if(!(i=this.d.textTracks()))return o;for(var n=0;n<i.length;n++)(e=i[n]).kind===this.fa&&o.push(new t.aa(this.d,{track:e}));return o},s.Ja=function(){for(var e,i,o=this.d.textTracks()||[],n=0,s=o.length,r=this.H=[];n<s;n++)if((e=o[n]).kind==this.fa){if(e.Dc){i=e;break}e.mode="hidden",window.setTimeout(t.bind(this,function(){this.Ja()}),100)}if((o=this.xa)===b&&(o=new t.pa(this.d)).va().appendChild(t.e("li",{className:"vjs-menu-title",innerHTML:t.ua(this.fa),We:-1})),i){var a;for(n=0,s=(e=i.cues).length;n<s;n++)a=e[n],a=new t.Ya(this.d,{track:i,cue:a}),r.push(a),o.ba(a);this.ba(o)}return 0<this.H.length&&this.show(),o},t.Ya=t.M.extend({l:function(e,i){var o=this.T=i.track,n=this.cue=i.cue,s=e.currentTime();i.label=n.text,i.selected=n.startTime<=s&&s<n.endTime,t.M.call(this,e,i),o.addEventListener("cuechange",t.bind(this,this.update))}}),t.Ya.prototype.u=function(){t.M.prototype.u.call(this),this.d.currentTime(this.cue.startTime),this.update(this.cue.startTime)},t.Ya.prototype.update=function(){var t=this.cue,e=this.d.currentTime();this.selected(t.startTime<=e&&e<t.endTime)},t.sc=t.a.extend({l:function(e,i){t.a.call(this,e,i),this.Y(),t.b(this.m().querySelector(".vjs-done-button"),"click",t.bind(this,function(){this.Je(),this.Y()})),t.b(this.m().querySelector(".vjs-default-button"),"click",t.bind(this,function(){this.m().querySelector(".vjs-fg-color > select").selectedIndex=0,this.m().querySelector(".vjs-bg-color > select").selectedIndex=0,this.m().querySelector(".window-color > select").selectedIndex=0,this.m().querySelector(".vjs-text-opacity > select").selectedIndex=0,this.m().querySelector(".vjs-bg-opacity > select").selectedIndex=0,this.m().querySelector(".vjs-window-opacity > select").selectedIndex=0,this.m().querySelector(".vjs-edge-style select").selectedIndex=0,this.m().querySelector(".vjs-font-family select").selectedIndex=0,this.m().querySelector(".vjs-font-percent select").selectedIndex=2,this.C()})),t.b(this.m().querySelector(".vjs-fg-color > select"),"change",t.bind(this,this.C)),t.b(this.m().querySelector(".vjs-bg-color > select"),"change",t.bind(this,this.C)),t.b(this.m().querySelector(".window-color > select"),"change",t.bind(this,this.C)),t.b(this.m().querySelector(".vjs-text-opacity > select"),"change",t.bind(this,this.C)),t.b(this.m().querySelector(".vjs-bg-opacity > select"),"change",t.bind(this,this.C)),t.b(this.m().querySelector(".vjs-window-opacity > select"),"change",t.bind(this,this.C)),t.b(this.m().querySelector(".vjs-font-percent select"),"change",t.bind(this,this.C)),t.b(this.m().querySelector(".vjs-edge-style select"),"change",t.bind(this,this.C)),t.b(this.m().querySelector(".vjs-font-family select"),"change",t.bind(this,this.C)),e.options().persistTextTrackSettings&&this.Ie()}}),s=t.sc.prototype,s.e=function(){return t.a.prototype.e.call(this,"div",{className:"vjs-caption-settings vjs-modal-overlay",innerHTML:'<div class="vjs-tracksettings"><div class="vjs-tracksettings-colors"><div class="vjs-fg-color vjs-tracksetting"><label class="vjs-label">Foreground</label><select><option value="">---</option><option value="#FFF">White</option><option value="#000">Black</option><option value="#F00">Red</option><option value="#0F0">Green</option><option value="#00F">Blue</option><option value="#FF0">Yellow</option><option value="#F0F">Magenta</option><option value="#0FF">Cyan</option></select><span class="vjs-text-opacity vjs-opacity"><select><option value="">---</option><option value="1">Opaque</option><option value="0.5">Semi-Opaque</option></select></span></div><div class="vjs-bg-color vjs-tracksetting"><label class="vjs-label">Background</label><select><option value="">---</option><option value="#FFF">White</option><option value="#000">Black</option><option value="#F00">Red</option><option value="#0F0">Green</option><option value="#00F">Blue</option><option value="#FF0">Yellow</option><option value="#F0F">Magenta</option><option value="#0FF">Cyan</option></select><span class="vjs-bg-opacity vjs-opacity"><select><option value="">---</option><option value="1">Opaque</option><option value="0.5">Semi-Transparent</option><option value="0">Transparent</option></select></span></div><div class="window-color vjs-tracksetting"><label class="vjs-label">Window</label><select><option value="">---</option><option value="#FFF">White</option><option value="#000">Black</option><option value="#F00">Red</option><option value="#0F0">Green</option><option value="#00F">Blue</option><option value="#FF0">Yellow</option><option value="#F0F">Magenta</option><option value="#0FF">Cyan</option></select><span class="vjs-window-opacity vjs-opacity"><select><option value="">---</option><option value="1">Opaque</option><option value="0.5">Semi-Transparent</option><option value="0">Transparent</option></select></span></div></div><div class="vjs-tracksettings-font"><div class="vjs-font-percent vjs-tracksetting"><label class="vjs-label">Font Size</label><select><option value="0.50">50%</option><option value="0.75">75%</option><option value="1.00" selected>100%</option><option value="1.25">125%</option><option value="1.50">150%</option><option value="1.75">175%</option><option value="2.00">200%</option><option value="3.00">300%</option><option value="4.00">400%</option></select></div><div class="vjs-edge-style vjs-tracksetting"><label class="vjs-label">Text Edge Style</label><select><option value="none">None</option><option value="raised">Raised</option><option value="depressed">Depressed</option><option value="uniform">Uniform</option><option value="dropshadow">Dropshadow</option></select></div><div class="vjs-font-family vjs-tracksetting"><label class="vjs-label">Font Family</label><select><option value="">Default</option><option value="monospaceSerif">Monospace Serif</option><option value="proportionalSerif">Proportional Serif</option><option value="monospaceSansSerif">Monospace Sans-Serif</option><option value="proportionalSansSerif">Proportional Sans-Serif</option><option value="casual">Casual</option><option value="script">Script</option><option value="small-caps">Small Caps</option></select></div></div></div><div class="vjs-tracksettings-controls"><button class="vjs-default-button">Defaults</button><button class="vjs-done-button">Done</button></div>'})},s.Lc=function(){var t,e,i,o,n,s,r,a,c;for(c in o=X((t=this.m()).querySelector(".vjs-edge-style select")),n=X(t.querySelector(".vjs-font-family select")),s=X(t.querySelector(".vjs-fg-color > select")),i=X(t.querySelector(".vjs-text-opacity > select")),r=X(t.querySelector(".vjs-bg-color > select")),e=X(t.querySelector(".vjs-bg-opacity > select")),a=X(t.querySelector(".window-color > select")),e={backgroundOpacity:e,textOpacity:i,windowOpacity:X(t.querySelector(".vjs-window-opacity > select")),edgeStyle:o,fontFamily:n,color:s,backgroundColor:r,windowColor:a,fontPercent:t=window.parseFloat(X(t.querySelector(".vjs-font-percent > select")))})(""===e[c]||"none"===e[c]||"fontPercent"===c&&1===e[c])&&delete e[c];return e},s.Re=function(t){var e=this.m();Y(e.querySelector(".vjs-edge-style select"),t.La),Y(e.querySelector(".vjs-font-family select"),t.fontFamily),Y(e.querySelector(".vjs-fg-color > select"),t.color),Y(e.querySelector(".vjs-text-opacity > select"),t.nd),Y(e.querySelector(".vjs-bg-color > select"),t.backgroundColor),Y(e.querySelector(".vjs-bg-opacity > select"),t.yc),Y(e.querySelector(".window-color > select"),t.fc),Y(e.querySelector(".vjs-window-opacity > select"),t.ud),(t=t.Qb)&&(t=t.toFixed(2)),Y(e.querySelector(".vjs-font-percent > select"),t)},s.Ie=function(){var t;try{t=JSON.parse(window.localStorage.getItem("vjs-text-track-settings"))}catch(t){}t&&this.Re(t)},s.Je=function(){var e;if(this.d.options().persistTextTrackSettings){e=this.Lc();try{t.ib(e)?window.localStorage.removeItem("vjs-text-track-settings"):window.localStorage.setItem("vjs-text-track-settings",JSON.stringify(e))}catch(t){}}},s.C=function(){var t=this.d.ea("textTrackDisplay");t&&t.C()},void 0!==window.JSON&&"function"==typeof window.JSON.parse)t.JSON=window.JSON;else{t.JSON={};var Z=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g;t.JSON.parse=function(a,c){function d(t,e){var i,o,n=t[e];if(n&&"object"==typeof n)for(i in n)Object.prototype.hasOwnProperty.call(n,i)&&((o=d(n,i))!==b?n[i]=o:delete n[i]);return c.call(t,e,n)}var e;if(a=String(a),Z.lastIndex=0,Z.test(a)&&(a=a.replace(Z,function(t){return"\\u"+("0000"+t.charCodeAt(0).toString(16)).slice(-4)})),/^[\],:{}\s]*$/.test(a.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return e=eval("("+a+")"),"function"==typeof c?d({"":e},""):e;throw new SyntaxError("JSON.parse(): invalid or malformed JSON data")}}t.xc=function(){var e,i,o,n;e=document.getElementsByTagName("video"),i=document.getElementsByTagName("audio");var s=[];if(e&&0<e.length)for(o=0,n=e.length;o<n;o++)s.push(e[o]);if(i&&0<i.length)for(o=0,n=i.length;o<n;o++)s.push(i[o]);if(s&&0<s.length)for(o=0,n=s.length;o<n;o++){if(!(i=s[o])||!i.getAttribute){t.Ib();break}i.player===b&&((e=i.getAttribute("data-setup"))!==j&&videojs(i))}else t.td||t.Ib()},t.Ib=function(){setTimeout(t.xc,1)},"complete"===document.readyState?t.td=f:t.N(window,"load",function(){t.td=f}),t.Ib(),t.Fe=function(e,i){t.Player.prototype[e]=i};var ya=this;function $(t,e){var i,o=t.split("."),n=ya;!(o[0]in n)&&n.execScript&&n.execScript("var "+o[0]);for(;o.length&&(i=o.shift());)o.length||e===b?n=n[i]?n[i]:n[i]={}:n[i]=e}$("videojs",t),$("_V_",t),$("videojs.options",t.options),$("videojs.players",t.Aa),$("videojs.TOUCH_ENABLED",t.Eb),$("videojs.cache",t.ta),$("videojs.Component",t.a),t.a.prototype.player=t.a.prototype.k,t.a.prototype.options=t.a.prototype.options,t.a.prototype.init=t.a.prototype.l,t.a.prototype.dispose=t.a.prototype.dispose,t.a.prototype.createEl=t.a.prototype.e,t.a.prototype.contentEl=t.a.prototype.va,t.a.prototype.el=t.a.prototype.m,t.a.prototype.addChild=t.a.prototype.ba,t.a.prototype.getChild=t.a.prototype.ea,t.a.prototype.getChildById=t.a.prototype.ae,t.a.prototype.children=t.a.prototype.children,t.a.prototype.initChildren=t.a.prototype.Oc,t.a.prototype.removeChild=t.a.prototype.removeChild,t.a.prototype.on=t.a.prototype.b,t.a.prototype.off=t.a.prototype.n,t.a.prototype.one=t.a.prototype.N,t.a.prototype.trigger=t.a.prototype.o,t.a.prototype.triggerReady=t.a.prototype.Wa,t.a.prototype.show=t.a.prototype.show,t.a.prototype.hide=t.a.prototype.Y,t.a.prototype.width=t.a.prototype.width,t.a.prototype.height=t.a.prototype.height,t.a.prototype.dimensions=t.a.prototype.Td,t.a.prototype.ready=t.a.prototype.I,t.a.prototype.addClass=t.a.prototype.p,t.a.prototype.removeClass=t.a.prototype.r,t.a.prototype.hasClass=t.a.prototype.Oa,t.a.prototype.buildCSSClass=t.a.prototype.V,t.a.prototype.localize=t.a.prototype.v,t.a.prototype.setInterval=t.a.prototype.setInterval,t.a.prototype.setTimeout=t.a.prototype.setTimeout,$("videojs.EventEmitter",t.z),t.z.prototype.on=t.z.prototype.b,t.z.prototype.addEventListener=t.z.prototype.addEventListener,t.z.prototype.off=t.z.prototype.n,t.z.prototype.removeEventListener=t.z.prototype.removeEventListener,t.z.prototype.one=t.z.prototype.N,t.z.prototype.trigger=t.z.prototype.o,t.z.prototype.dispatchEvent=t.z.prototype.dispatchEvent,t.Player.prototype.ended=t.Player.prototype.ended,t.Player.prototype.enterFullWindow=t.Player.prototype.Jc,t.Player.prototype.exitFullWindow=t.Player.prototype.Nb,t.Player.prototype.preload=t.Player.prototype.Qa,t.Player.prototype.remainingTime=t.Player.prototype.remainingTime,t.Player.prototype.supportsFullScreen=t.Player.prototype.Ta,t.Player.prototype.currentType=t.Player.prototype.Qd,t.Player.prototype.requestFullScreen=t.Player.prototype.requestFullScreen,t.Player.prototype.requestFullscreen=t.Player.prototype.requestFullscreen,t.Player.prototype.cancelFullScreen=t.Player.prototype.cancelFullScreen,t.Player.prototype.exitFullscreen=t.Player.prototype.exitFullscreen,t.Player.prototype.isFullScreen=t.Player.prototype.isFullScreen,t.Player.prototype.isFullscreen=t.Player.prototype.isFullscreen,t.Player.prototype.textTracks=t.Player.prototype.textTracks,t.Player.prototype.remoteTextTracks=t.Player.prototype.Z,t.Player.prototype.addTextTrack=t.Player.prototype.addTextTrack,t.Player.prototype.addRemoteTextTrack=t.Player.prototype.ha,t.Player.prototype.removeRemoteTextTrack=t.Player.prototype.Ba,$("videojs.MediaLoader",t.Fd),$("videojs.TextTrackDisplay",t.ra),$("videojs.ControlBar",t.ub),$("videojs.Button",t.w),$("videojs.PlayToggle",t.nc),$("videojs.FullscreenToggle",t.Za),$("videojs.BigPlayButton",t.sb),$("videojs.LoadingSpinner",t.lc),$("videojs.CurrentTimeDisplay",t.vb),$("videojs.DurationDisplay",t.wb),$("videojs.TimeDivider",t.tc),$("videojs.RemainingTimeDisplay",t.Db),$("videojs.LiveDisplay",t.kc),$("videojs.ErrorDisplay",t.xb),$("videojs.Slider",t.U),$("videojs.ProgressControl",t.Cb),$("videojs.SeekBar",t.qc),$("videojs.LoadProgressBar",t.zb),$("videojs.PlayProgressBar",t.mc),$("videojs.SeekHandle",t.$a),$("videojs.VolumeControl",t.Gb),$("videojs.VolumeBar",t.Fb),$("videojs.VolumeLevel",t.uc),$("videojs.VolumeMenuButton",t.Fa),$("videojs.VolumeHandle",t.Hb),$("videojs.MuteToggle",t.qa),$("videojs.PosterImage",t.pc),$("videojs.Menu",t.pa),$("videojs.MenuItem",t.M),$("videojs.MenuButton",t.O),$("videojs.PlaybackRateMenuButton",t.oc),$("videojs.ChaptersTrackMenuItem",t.Ya),$("videojs.TextTrackButton",t.Q),$("videojs.TextTrackMenuItem",t.aa),$("videojs.OffTextTrackMenuItem",t.Ab),$("videojs.CaptionSettingsMenuItem",t.tb),t.O.prototype.createItems=t.O.prototype.Ia,t.Q.prototype.createItems=t.Q.prototype.Ia,t.Xa.prototype.createItems=t.Xa.prototype.Ia,$("videojs.SubtitlesButton",t.ab),$("videojs.CaptionsButton",t.na),$("videojs.ChaptersButton",t.Xa),$("videojs.MediaTechController",t.j),t.j.withSourceHandlers=t.j.gc,t.j.prototype.featuresVolumeControl=t.j.prototype.uf,t.j.prototype.featuresFullscreenResize=t.j.prototype.qf,t.j.prototype.featuresPlaybackRate=t.j.prototype.rf,t.j.prototype.featuresProgressEvents=t.j.prototype.sf,t.j.prototype.featuresTimeupdateEvents=t.j.prototype.tf,t.j.prototype.setPoster=t.j.prototype.fd,t.j.prototype.textTracks=t.j.prototype.textTracks,t.j.prototype.remoteTextTracks=t.j.prototype.Z,t.j.prototype.addTextTrack=t.j.prototype.addTextTrack,t.j.prototype.addRemoteTextTrack=t.j.prototype.ha,t.j.prototype.removeRemoteTextTrack=t.j.prototype.Ba,$("videojs.Html5",t.f),t.f.Events=t.f.yb,t.f.isSupported=t.f.isSupported,t.f.canPlaySource=t.f.zc,t.f.patchCanPlayType=t.f.Xc,t.f.unpatchCanPlayType=t.f.bf,t.f.prototype.setCurrentTime=t.f.prototype.bc,t.f.prototype.setVolume=t.f.prototype.Se,t.f.prototype.setMuted=t.f.prototype.Oe,t.f.prototype.setPreload=t.f.prototype.Qe,t.f.prototype.setAutoplay=t.f.prototype.Le,t.f.prototype.setLoop=t.f.prototype.Ne,t.f.prototype.enterFullScreen=t.f.prototype.Ic,t.f.prototype.exitFullScreen=t.f.prototype.Xd,t.f.prototype.playbackRate=t.f.prototype.playbackRate,t.f.prototype.setPlaybackRate=t.f.prototype.Pe,t.f.registerSourceHandler=t.f.Ra,t.f.selectSourceHandler=t.f.pb,t.f.prototype.setSource=t.f.prototype.Sa,t.f.prototype.disposeSourceHandler=t.f.prototype.Ka,t.f.prototype.textTracks=t.f.prototype.textTracks,t.f.prototype.remoteTextTracks=t.f.prototype.Z,t.f.prototype.addTextTrack=t.f.prototype.addTextTrack,t.f.prototype.addRemoteTextTrack=t.f.prototype.ha,t.f.prototype.removeRemoteTextTrack=t.f.prototype.Ba,$("videojs.Flash",t.g),t.g.isSupported=t.g.isSupported,t.g.canPlaySource=t.g.zc,t.g.onReady=t.g.onReady,t.g.embed=t.g.Hc,t.g.version=t.g.version,t.g.prototype.setSource=t.g.prototype.Sa,t.g.registerSourceHandler=t.g.Ra,t.g.selectSourceHandler=t.g.pb,t.g.prototype.setSource=t.g.prototype.Sa,t.g.prototype.disposeSourceHandler=t.g.prototype.Ka,$("videojs.TextTrack",t.t),$("videojs.TextTrackList",t.F),$("videojs.TextTrackCueList",t.W),$("videojs.TextTrackSettings",t.sc),t.t.prototype.id=t.t.prototype.id,t.t.prototype.label=t.t.prototype.label,t.t.prototype.kind=t.t.prototype.Wb,t.t.prototype.mode=t.t.prototype.mode,t.t.prototype.cues=t.t.prototype.Dc,t.t.prototype.activeCues=t.t.prototype.nf,t.t.prototype.addCue=t.t.prototype.vc,t.t.prototype.removeCue=t.t.prototype.bd,t.F.prototype.getTrackById=t.F.prototype.de,t.W.prototype.getCueById=t.F.prototype.be,$("videojs.CaptionsTrack",t.gf),$("videojs.SubtitlesTrack",t.mf),$("videojs.ChaptersTrack",t.hf),$("videojs.autoSetup",t.xc),$("videojs.plugin",t.Fe),$("videojs.createTimeRange",t.Lb),$("videojs.util",t.$),t.$.mergeOptions=t.$.ya,t.addLanguage=t.Jd}(),function(t){var e=t.vttjs={},i=e.VTTCue,o=e.VTTRegion,n=t.VTTCue,s=t.VTTRegion;e.shim=function(){e.VTTCue=i,e.VTTRegion=o},e.restore=function(){e.VTTCue=n,e.VTTRegion=s}}(this),function(t,e){function i(t){return"string"==typeof t&&(!!a[t.toLowerCase()]&&t.toLowerCase())}function o(t){return"string"==typeof t&&(!!c[t.toLowerCase()]&&t.toLowerCase())}function n(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var o in i)t[o]=i[o]}return t}function s(t,e,s){var a=this,c=/MSIE\s8\.0/.test(navigator.userAgent),l={};c?a=document.createElement("custom"):l.enumerable=!0,a.hasBeenReset=!1;var u="",h=!1,p=t,d=e,f=s,v=null,y="",b=!0,m="auto",g="start",w=50,T="middle",j=50,k="middle";return Object.defineProperty(a,"id",n({},l,{get:function(){return u},set:function(t){u=""+t}})),Object.defineProperty(a,"pauseOnExit",n({},l,{get:function(){return h},set:function(t){h=!!t}})),Object.defineProperty(a,"startTime",n({},l,{get:function(){return p},set:function(t){if("number"!=typeof t)throw new TypeError("Start time must be set to a number.");p=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"endTime",n({},l,{get:function(){return d},set:function(t){if("number"!=typeof t)throw new TypeError("End time must be set to a number.");d=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"text",n({},l,{get:function(){return f},set:function(t){f=""+t,this.hasBeenReset=!0}})),Object.defineProperty(a,"region",n({},l,{get:function(){return v},set:function(t){v=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"vertical",n({},l,{get:function(){return y},set:function(t){var e=i(t);if(!1===e)throw new SyntaxError("An invalid or illegal string was specified.");y=e,this.hasBeenReset=!0}})),Object.defineProperty(a,"snapToLines",n({},l,{get:function(){return b},set:function(t){b=!!t,this.hasBeenReset=!0}})),Object.defineProperty(a,"line",n({},l,{get:function(){return m},set:function(t){if("number"!=typeof t&&t!==r)throw new SyntaxError("An invalid number or illegal string was specified.");m=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"lineAlign",n({},l,{get:function(){return g},set:function(t){var e=o(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");g=e,this.hasBeenReset=!0}})),Object.defineProperty(a,"position",n({},l,{get:function(){return w},set:function(t){if(0>t||t>100)throw new Error("Position must be between 0 and 100.");w=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"positionAlign",n({},l,{get:function(){return T},set:function(t){var e=o(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");T=e,this.hasBeenReset=!0}})),Object.defineProperty(a,"size",n({},l,{get:function(){return j},set:function(t){if(0>t||t>100)throw new Error("Size must be between 0 and 100.");j=t,this.hasBeenReset=!0}})),Object.defineProperty(a,"align",n({},l,{get:function(){return k},set:function(t){var e=o(t);if(!e)throw new SyntaxError("An invalid or illegal string was specified.");k=e,this.hasBeenReset=!0}})),a.displayState=void 0,c?a:void 0}var r="auto",a={"":!0,lr:!0,rl:!0},c={start:!0,middle:!0,end:!0,left:!0,right:!0};s.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)},t.VTTCue=t.VTTCue||s,e.VTTCue=s}(this,this.vttjs||{}),function(t,e){function i(t){return"string"==typeof t&&(!!s[t.toLowerCase()]&&t.toLowerCase())}function o(t){return"number"==typeof t&&t>=0&&100>=t}function n(){var t=100,e=3,n=0,s=100,r=0,a=100,c="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return t},set:function(e){if(!o(e))throw new Error("Width must be between 0 and 100.");t=e}},lines:{enumerable:!0,get:function(){return e},set:function(t){if("number"!=typeof t)throw new TypeError("Lines must be set to a number.");e=t}},regionAnchorY:{enumerable:!0,get:function(){return s},set:function(t){if(!o(t))throw new Error("RegionAnchorX must be between 0 and 100.");s=t}},regionAnchorX:{enumerable:!0,get:function(){return n},set:function(t){if(!o(t))throw new Error("RegionAnchorY must be between 0 and 100.");n=t}},viewportAnchorY:{enumerable:!0,get:function(){return a},set:function(t){if(!o(t))throw new Error("ViewportAnchorY must be between 0 and 100.");a=t}},viewportAnchorX:{enumerable:!0,get:function(){return r},set:function(t){if(!o(t))throw new Error("ViewportAnchorX must be between 0 and 100.");r=t}},scroll:{enumerable:!0,get:function(){return c},set:function(t){var e=i(t);if(!1===e)throw new SyntaxError("An invalid or illegal string was specified.");c=e}}})}var s={"":!0,up:!0};t.VTTRegion=t.VTTRegion||n,e.VTTRegion=n}(this,this.vttjs||{}),function(t){function e(t,e){this.name="ParsingError",this.code=t.code,this.message=e||t.message}function i(t){function e(t,e,i,o){return 3600*(0|t)+60*(0|e)+(0|i)+(0|o)/1e3}var i=t.match(/^(\d+):(\d{2})(:\d{2})?\.(\d{3})/);return i?i[3]?e(i[1],i[2],i[3].replace(":",""),i[4]):i[1]>59?e(i[1],i[2],0,i[4]):e(0,i[1],i[2],i[4]):null}function o(){this.values=p(null)}function n(t,e,i,o){var n=o?t.split(o):[t];for(var s in n)if("string"==typeof n[s]){var r=n[s].split(i);if(2===r.length)e(r[0],r[1])}}function s(t,s,r){function a(){var o=i(t);if(null===o)throw new e(e.Errors.BadTimeStamp,"Malformed timestamp: "+l);return t=t.replace(/^[^\sa-zA-Z-]+/,""),o}function c(){t=t.replace(/^\s+/,"")}var l=t;if(c(),s.startTime=a(),c(),"--\x3e"!==t.substr(0,3))throw new e(e.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '--\x3e'): "+l);t=t.substr(3),c(),s.endTime=a(),c(),function(t,e){var i=new o;n(t,function(t,e){switch(t){case"region":for(var o=r.length-1;o>=0;o--)if(r[o].id===e){i.set(t,r[o].region);break}break;case"vertical":i.alt(t,e,["rl","lr"]);break;case"line":var n=e.split(","),s=n[0];i.integer(t,s),i.percent(t,s)&&i.set("snapToLines",!1),i.alt(t,s,["auto"]),2===n.length&&i.alt("lineAlign",n[1],["start","middle","end"]);break;case"position":n=e.split(","),i.percent(t,n[0]),2===n.length&&i.alt("positionAlign",n[1],["start","middle","end"]);break;case"size":i.percent(t,e);break;case"align":i.alt(t,e,["start","middle","end","left","right"])}},/:/,/\s/),e.region=i.get("region",null),e.vertical=i.get("vertical",""),e.line=i.get("line","auto"),e.lineAlign=i.get("lineAlign","start"),e.snapToLines=i.get("snapToLines",!0),e.size=i.get("size",100),e.align=i.get("align","middle"),e.position=i.get("position",{start:0,left:0,middle:50,end:100,right:100},e.align),e.positionAlign=i.get("positionAlign",{start:"start",left:"start",middle:"middle",end:"end",right:"end"},e.align)}(t,s)}function r(t,e){function o(){if(!e)return null;var t=e.match(/^([^<]*)(<[^>]+>?)?/);return function(t){return e=e.substr(t.length),t}(t[1]?t[1]:t[2])}function n(t){return d[t]}function s(t){for(;m=t.match(/&(amp|lt|gt|lrm|rlm|nbsp);/);)t=t.replace(m[0],n);return t}function r(t,e){return!y[e.localName]||y[e.localName]===t.localName}function a(e,i){var o=f[e];if(!o)return null;var n=t.document.createElement(o);n.localName=o;var s=v[e];return s&&i&&(n[s]=i.trim()),n}for(var c,l=t.document.createElement("div"),u=l,h=[];null!==(c=o());)if("<"!==c[0])u.appendChild(t.document.createTextNode(s(c)));else{if("/"===c[1]){h.length&&h[h.length-1]===c.substr(2).replace(">","")&&(h.pop(),u=u.parentNode);continue}var p,b=i(c.substr(1,c.length-2));if(b){p=t.document.createProcessingInstruction("timestamp",b),u.appendChild(p);continue}var m=c.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!m)continue;if(!(p=a(m[1],m[3])))continue;if(!r(u,p))continue;m[2]&&(p.className=m[2].substr(1).replace("."," ")),h.push(m[1]),u.appendChild(p),u=p}return l}function a(){}function c(t,e,i){var o=/MSIE\s8\.0/.test(navigator.userAgent),n="rgba(255, 255, 255, 1)",s="rgba(0, 0, 0, 0.8)";o&&(n="rgb(255, 255, 255)",s="rgb(0, 0, 0)"),a.call(this),this.cue=e,this.cueDiv=r(t,e.text);var c={color:n,backgroundColor:s,position:"relative",left:0,right:0,top:0,bottom:0,display:"inline"};o||(c.writingMode=""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl",c.unicodeBidi="plaintext"),this.applyStyles(c,this.cueDiv),this.div=t.document.createElement("div"),c={textAlign:"middle"===e.align?"center":e.align,font:i.font,whiteSpace:"pre-line",position:"absolute"},o||(c.direction=function(t){function e(t,e){for(var i=e.childNodes.length-1;i>=0;i--)t.push(e.childNodes[i])}function i(t){if(!t||!t.length)return null;var o=t.pop(),n=o.textContent||o.innerText;if(n){var s=n.match(/^.*(\n|\r)/);return s?(t.length=0,s[0]):n}return"ruby"===o.tagName?i(t):o.childNodes?(e(t,o),i(t)):void 0}var o,n=[],s="";if(!t||!t.childNodes)return"ltr";for(e(n,t);s=i(n);)for(var r=0;r<s.length;r++){o=s.charCodeAt(r);for(var a=0;a<b.length;a++)if(b[a]===o)return"rtl"}return"ltr"}(this.cueDiv),c.writingMode=""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl".stylesunicodeBidi="plaintext"),this.applyStyles(c),this.div.appendChild(this.cueDiv);var l=0;switch(e.positionAlign){case"start":l=e.position;break;case"middle":l=e.position-e.size/2;break;case"end":l=e.position-e.size}this.applyStyles(""===e.vertical?{left:this.formatStyle(l,"%"),width:this.formatStyle(e.size,"%")}:{top:this.formatStyle(l,"%"),height:this.formatStyle(e.size,"%")}),this.move=function(t){this.applyStyles({top:this.formatStyle(t.top,"px"),bottom:this.formatStyle(t.bottom,"px"),left:this.formatStyle(t.left,"px"),right:this.formatStyle(t.right,"px"),height:this.formatStyle(t.height,"px"),width:this.formatStyle(t.width,"px")})}}function l(t){var e,i,o,n,s=/MSIE\s8\.0/.test(navigator.userAgent);if(t.div){i=t.div.offsetHeight,o=t.div.offsetWidth,n=t.div.offsetTop;var r=(r=t.div.childNodes)&&(r=r[0])&&r.getClientRects&&r.getClientRects();t=t.div.getBoundingClientRect(),e=r?Math.max(r[0]&&r[0].height||0,t.height/r.length):0}this.left=t.left,this.right=t.right,this.top=t.top||n,this.height=t.height||i,this.bottom=t.bottom||n+(t.height||i),this.width=t.width||o,this.lineHeight=void 0!==e?e:t.lineHeight,s&&!this.lineHeight&&(this.lineHeight=13)}function u(t,e,i,o){var n=new l(e),s=e.cue,r=function(t){if("number"==typeof t.line&&(t.snapToLines||t.line>=0&&t.line<=100))return t.line;if(!t.track||!t.track.textTrackList||!t.track.textTrackList.mediaElement)return-1;for(var e=t.track,i=e.textTrackList,o=0,n=0;n<i.length&&i[n]!==e;n++)"showing"===i[n].mode&&o++;return-1*++o}(s),a=[];if(s.snapToLines){var c;switch(s.vertical){case"":a=["+y","-y"],c="height";break;case"rl":a=["+x","-x"],c="width";break;case"lr":a=["-x","+x"],c="width"}var u=n.lineHeight,h=u*Math.round(r),p=i[c]+u,d=a[0];Math.abs(h)>p&&(h=0>h?-1:1,h*=Math.ceil(p/u)*u),0>r&&(h+=""===s.vertical?i.height:i.width,a=a.reverse()),n.move(d,h)}else{var f=n.lineHeight/i.height*100;switch(s.lineAlign){case"middle":r-=f/2;break;case"end":r-=f}switch(s.vertical){case"":e.applyStyles({top:e.formatStyle(r,"%")});break;case"rl":e.applyStyles({left:e.formatStyle(r,"%")});break;case"lr":e.applyStyles({right:e.formatStyle(r,"%")})}a=["+y","-x","+x","-y"],n=new l(e)}var v=function(t,e){for(var n,s=new l(t),r=1,a=0;a<e.length;a++){for(;t.overlapsOppositeAxis(i,e[a])||t.within(i)&&t.overlapsAny(o);)t.move(e[a]);if(t.within(i))return t;var c=t.intersectPercentage(i);r>c&&(n=new l(t),r=c),t=new l(s)}return n||s}(n,a);e.move(v.toCSSCompatValues(i))}function h(){}var p=Object.create||function(){function t(){}return function(e){if(1!==arguments.length)throw new Error("Object.create shim only accepts one parameter.");return t.prototype=e,new t}}();e.prototype=p(Error.prototype),e.prototype.constructor=e,e.Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},o.prototype={set:function(t,e){this.get(t)||""===e||(this.values[t]=e)},get:function(t,e,i){return i?this.has(t)?this.values[t]:e[i]:this.has(t)?this.values[t]:e},has:function(t){return t in this.values},alt:function(t,e,i){for(var o=0;o<i.length;++o)if(e===i[o]){this.set(t,e);break}},integer:function(t,e){/^-?\d+$/.test(e)&&this.set(t,parseInt(e,10))},percent:function(t,e){return!!(e.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&(e=parseFloat(e),e>=0&&100>=e))&&(this.set(t,e),!0)}};var d={"&amp;":"&","&lt;":"<","&gt;":">","&lrm;":"‎","&rlm;":"‏","&nbsp;":" "},f={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},v={v:"title",lang:"lang"},y={rt:"ruby"},b=[1470,1472,1475,1478,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1520,1521,1522,1523,1524,1544,1547,1549,1563,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1645,1646,1647,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1765,1766,1774,1775,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1807,1808,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1969,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2e3,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2036,2037,2042,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2074,2084,2088,2096,2097,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2142,2208,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,8207,64285,64287,64288,64289,64290,64291,64292,64293,64294,64295,64296,64298,64299,64300,64301,64302,64303,64304,64305,64306,64307,64308,64309,64310,64312,64313,64314,64315,64316,64318,64320,64321,64323,64324,64326,64327,64328,64329,64330,64331,64332,64333,64334,64335,64336,64337,64338,64339,64340,64341,64342,64343,64344,64345,64346,64347,64348,64349,64350,64351,64352,64353,64354,64355,64356,64357,64358,64359,64360,64361,64362,64363,64364,64365,64366,64367,64368,64369,64370,64371,64372,64373,64374,64375,64376,64377,64378,64379,64380,64381,64382,64383,64384,64385,64386,64387,64388,64389,64390,64391,64392,64393,64394,64395,64396,64397,64398,64399,64400,64401,64402,64403,64404,64405,64406,64407,64408,64409,64410,64411,64412,64413,64414,64415,64416,64417,64418,64419,64420,64421,64422,64423,64424,64425,64426,64427,64428,64429,64430,64431,64432,64433,64434,64435,64436,64437,64438,64439,64440,64441,64442,64443,64444,64445,64446,64447,64448,64449,64467,64468,64469,64470,64471,64472,64473,64474,64475,64476,64477,64478,64479,64480,64481,64482,64483,64484,64485,64486,64487,64488,64489,64490,64491,64492,64493,64494,64495,64496,64497,64498,64499,64500,64501,64502,64503,64504,64505,64506,64507,64508,64509,64510,64511,64512,64513,64514,64515,64516,64517,64518,64519,64520,64521,64522,64523,64524,64525,64526,64527,64528,64529,64530,64531,64532,64533,64534,64535,64536,64537,64538,64539,64540,64541,64542,64543,64544,64545,64546,64547,64548,64549,64550,64551,64552,64553,64554,64555,64556,64557,64558,64559,64560,64561,64562,64563,64564,64565,64566,64567,64568,64569,64570,64571,64572,64573,64574,64575,64576,64577,64578,64579,64580,64581,64582,64583,64584,64585,64586,64587,64588,64589,64590,64591,64592,64593,64594,64595,64596,64597,64598,64599,64600,64601,64602,64603,64604,64605,64606,64607,64608,64609,64610,64611,64612,64613,64614,64615,64616,64617,64618,64619,64620,64621,64622,64623,64624,64625,64626,64627,64628,64629,64630,64631,64632,64633,64634,64635,64636,64637,64638,64639,64640,64641,64642,64643,64644,64645,64646,64647,64648,64649,64650,64651,64652,64653,64654,64655,64656,64657,64658,64659,64660,64661,64662,64663,64664,64665,64666,64667,64668,64669,64670,64671,64672,64673,64674,64675,64676,64677,64678,64679,64680,64681,64682,64683,64684,64685,64686,64687,64688,64689,64690,64691,64692,64693,64694,64695,64696,64697,64698,64699,64700,64701,64702,64703,64704,64705,64706,64707,64708,64709,64710,64711,64712,64713,64714,64715,64716,64717,64718,64719,64720,64721,64722,64723,64724,64725,64726,64727,64728,64729,64730,64731,64732,64733,64734,64735,64736,64737,64738,64739,64740,64741,64742,64743,64744,64745,64746,64747,64748,64749,64750,64751,64752,64753,64754,64755,64756,64757,64758,64759,64760,64761,64762,64763,64764,64765,64766,64767,64768,64769,64770,64771,64772,64773,64774,64775,64776,64777,64778,64779,64780,64781,64782,64783,64784,64785,64786,64787,64788,64789,64790,64791,64792,64793,64794,64795,64796,64797,64798,64799,64800,64801,64802,64803,64804,64805,64806,64807,64808,64809,64810,64811,64812,64813,64814,64815,64816,64817,64818,64819,64820,64821,64822,64823,64824,64825,64826,64827,64828,64829,64848,64849,64850,64851,64852,64853,64854,64855,64856,64857,64858,64859,64860,64861,64862,64863,64864,64865,64866,64867,64868,64869,64870,64871,64872,64873,64874,64875,64876,64877,64878,64879,64880,64881,64882,64883,64884,64885,64886,64887,64888,64889,64890,64891,64892,64893,64894,64895,64896,64897,64898,64899,64900,64901,64902,64903,64904,64905,64906,64907,64908,64909,64910,64911,64914,64915,64916,64917,64918,64919,64920,64921,64922,64923,64924,64925,64926,64927,64928,64929,64930,64931,64932,64933,64934,64935,64936,64937,64938,64939,64940,64941,64942,64943,64944,64945,64946,64947,64948,64949,64950,64951,64952,64953,64954,64955,64956,64957,64958,64959,64960,64961,64962,64963,64964,64965,64966,64967,65008,65009,65010,65011,65012,65013,65014,65015,65016,65017,65018,65019,65020,65136,65137,65138,65139,65140,65142,65143,65144,65145,65146,65147,65148,65149,65150,65151,65152,65153,65154,65155,65156,65157,65158,65159,65160,65161,65162,65163,65164,65165,65166,65167,65168,65169,65170,65171,65172,65173,65174,65175,65176,65177,65178,65179,65180,65181,65182,65183,65184,65185,65186,65187,65188,65189,65190,65191,65192,65193,65194,65195,65196,65197,65198,65199,65200,65201,65202,65203,65204,65205,65206,65207,65208,65209,65210,65211,65212,65213,65214,65215,65216,65217,65218,65219,65220,65221,65222,65223,65224,65225,65226,65227,65228,65229,65230,65231,65232,65233,65234,65235,65236,65237,65238,65239,65240,65241,65242,65243,65244,65245,65246,65247,65248,65249,65250,65251,65252,65253,65254,65255,65256,65257,65258,65259,65260,65261,65262,65263,65264,65265,65266,65267,65268,65269,65270,65271,65272,65273,65274,65275,65276,67584,67585,67586,67587,67588,67589,67592,67594,67595,67596,67597,67598,67599,67600,67601,67602,67603,67604,67605,67606,67607,67608,67609,67610,67611,67612,67613,67614,67615,67616,67617,67618,67619,67620,67621,67622,67623,67624,67625,67626,67627,67628,67629,67630,67631,67632,67633,67634,67635,67636,67637,67639,67640,67644,67647,67648,67649,67650,67651,67652,67653,67654,67655,67656,67657,67658,67659,67660,67661,67662,67663,67664,67665,67666,67667,67668,67669,67671,67672,67673,67674,67675,67676,67677,67678,67679,67840,67841,67842,67843,67844,67845,67846,67847,67848,67849,67850,67851,67852,67853,67854,67855,67856,67857,67858,67859,67860,67861,67862,67863,67864,67865,67866,67867,67872,67873,67874,67875,67876,67877,67878,67879,67880,67881,67882,67883,67884,67885,67886,67887,67888,67889,67890,67891,67892,67893,67894,67895,67896,67897,67903,67968,67969,67970,67971,67972,67973,67974,67975,67976,67977,67978,67979,67980,67981,67982,67983,67984,67985,67986,67987,67988,67989,67990,67991,67992,67993,67994,67995,67996,67997,67998,67999,68e3,68001,68002,68003,68004,68005,68006,68007,68008,68009,68010,68011,68012,68013,68014,68015,68016,68017,68018,68019,68020,68021,68022,68023,68030,68031,68096,68112,68113,68114,68115,68117,68118,68119,68121,68122,68123,68124,68125,68126,68127,68128,68129,68130,68131,68132,68133,68134,68135,68136,68137,68138,68139,68140,68141,68142,68143,68144,68145,68146,68147,68160,68161,68162,68163,68164,68165,68166,68167,68176,68177,68178,68179,68180,68181,68182,68183,68184,68192,68193,68194,68195,68196,68197,68198,68199,68200,68201,68202,68203,68204,68205,68206,68207,68208,68209,68210,68211,68212,68213,68214,68215,68216,68217,68218,68219,68220,68221,68222,68223,68352,68353,68354,68355,68356,68357,68358,68359,68360,68361,68362,68363,68364,68365,68366,68367,68368,68369,68370,68371,68372,68373,68374,68375,68376,68377,68378,68379,68380,68381,68382,68383,68384,68385,68386,68387,68388,68389,68390,68391,68392,68393,68394,68395,68396,68397,68398,68399,68400,68401,68402,68403,68404,68405,68416,68417,68418,68419,68420,68421,68422,68423,68424,68425,68426,68427,68428,68429,68430,68431,68432,68433,68434,68435,68436,68437,68440,68441,68442,68443,68444,68445,68446,68447,68448,68449,68450,68451,68452,68453,68454,68455,68456,68457,68458,68459,68460,68461,68462,68463,68464,68465,68466,68472,68473,68474,68475,68476,68477,68478,68479,68608,68609,68610,68611,68612,68613,68614,68615,68616,68617,68618,68619,68620,68621,68622,68623,68624,68625,68626,68627,68628,68629,68630,68631,68632,68633,68634,68635,68636,68637,68638,68639,68640,68641,68642,68643,68644,68645,68646,68647,68648,68649,68650,68651,68652,68653,68654,68655,68656,68657,68658,68659,68660,68661,68662,68663,68664,68665,68666,68667,68668,68669,68670,68671,68672,68673,68674,68675,68676,68677,68678,68679,68680,126464,126465,126466,126467,126469,126470,126471,126472,126473,126474,126475,126476,126477,126478,126479,126480,126481,126482,126483,126484,126485,126486,126487,126488,126489,126490,126491,126492,126493,126494,126495,126497,126498,126500,126503,126505,126506,126507,126508,126509,126510,126511,126512,126513,126514,126516,126517,126518,126519,126521,126523,126530,126535,126537,126539,126541,126542,126543,126545,126546,126548,126551,126553,126555,126557,126559,126561,126562,126564,126567,126568,126569,126570,126572,126573,126574,126575,126576,126577,126578,126580,126581,126582,126583,126585,126586,126587,126588,126590,126592,126593,126594,126595,126596,126597,126598,126599,126600,126601,126603,126604,126605,126606,126607,126608,126609,126610,126611,126612,126613,126614,126615,126616,126617,126618,126619,126625,126626,126627,126629,126630,126631,126632,126633,126635,126636,126637,126638,126639,126640,126641,126642,126643,126644,126645,126646,126647,126648,126649,126650,126651,1114109];a.prototype.applyStyles=function(t,e){for(var i in e=e||this.div,t)t.hasOwnProperty(i)&&(e.style[i]=t[i])},a.prototype.formatStyle=function(t,e){return 0===t?0:t+e},c.prototype=p(a.prototype),c.prototype.constructor=c,l.prototype.move=function(t,e){switch(e=void 0!==e?e:this.lineHeight,t){case"+x":this.left+=e,this.right+=e;break;case"-x":this.left-=e,this.right-=e;break;case"+y":this.top+=e,this.bottom+=e;break;case"-y":this.top-=e,this.bottom-=e}},l.prototype.overlaps=function(t){return this.left<t.right&&this.right>t.left&&this.top<t.bottom&&this.bottom>t.top},l.prototype.overlapsAny=function(t){for(var e=0;e<t.length;e++)if(this.overlaps(t[e]))return!0;return!1},l.prototype.within=function(t){return this.top>=t.top&&this.bottom<=t.bottom&&this.left>=t.left&&this.right<=t.right},l.prototype.overlapsOppositeAxis=function(t,e){switch(e){case"+x":return this.left<t.left;case"-x":return this.right>t.right;case"+y":return this.top<t.top;case"-y":return this.bottom>t.bottom}},l.prototype.intersectPercentage=function(t){return Math.max(0,Math.min(this.right,t.right)-Math.max(this.left,t.left))*Math.max(0,Math.min(this.bottom,t.bottom)-Math.max(this.top,t.top))/(this.height*this.width)},l.prototype.toCSSCompatValues=function(t){return{top:this.top-t.top,bottom:t.bottom-this.bottom,left:this.left-t.left,right:t.right-this.right,height:this.height,width:this.width}},l.getSimpleBoxPosition=function(t){var e=t.div?t.div.offsetHeight:t.tagName?t.offsetHeight:0,i=t.div?t.div.offsetWidth:t.tagName?t.offsetWidth:0,o=t.div?t.div.offsetTop:t.tagName?t.offsetTop:0;return{left:(t=t.div?t.div.getBoundingClientRect():t.tagName?t.getBoundingClientRect():t).left,right:t.right,top:t.top||o,height:t.height||e,bottom:t.bottom||o+(t.height||e),width:t.width||i}},h.StringDecoder=function(){return{decode:function(t){if(!t)return"";if("string"!=typeof t)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(t))}}},h.convertCueToDOMTree=function(t,e){return t&&e?r(t,e):null};h.processCues=function(t,e,i){if(!t||!e||!i)return null;for(;i.firstChild;)i.removeChild(i.firstChild);var o=t.document.createElement("div");if(o.style.position="absolute",o.style.left="0",o.style.right="0",o.style.top="0",o.style.bottom="0",o.style.margin="1.5%",i.appendChild(o),function(t){for(var e=0;e<t.length;e++)if(t[e].hasBeenReset||!t[e].displayState)return!0;return!1}(e)){var n=[],s=l.getSimpleBoxPosition(o),r={font:Math.round(.05*s.height*100)/100+"px sans-serif"};!function(){for(var i,a,h=0;h<e.length;h++)a=e[h],i=new c(t,a,r),o.appendChild(i.div),u(0,i,s,n),a.displayState=i.div,n.push(l.getSimpleBoxPosition(i))}()}else for(var a=0;a<e.length;a++)o.appendChild(e[a].displayState)},h.Parser=function(t,e,i){i||(i=e,e={}),e||(e={}),this.window=t,this.vttjs=e,this.state="INITIAL",this.buffer="",this.decoder=i||new TextDecoder("utf8"),this.regionList=[]},h.Parser.prototype={reportOrThrowError:function(t){if(!(t instanceof e))throw t;this.onparsingerror&&this.onparsingerror(t)},parse:function(t){function i(){for(var t=a.buffer,e=0;e<t.length&&"\r"!==t[e]&&"\n"!==t[e];)++e;var i=t.substr(0,e);return"\r"===t[e]&&++e,"\n"===t[e]&&++e,a.buffer=t.substr(e),i}function r(t){n(t,function(t,e){switch(t){case"Region":!function(t){var e=new o;if(n(t,function(t,i){switch(t){case"id":e.set(t,i);break;case"width":e.percent(t,i);break;case"lines":e.integer(t,i);break;case"regionanchor":case"viewportanchor":var n=i.split(",");if(2!==n.length)break;var s=new o;if(s.percent("x",n[0]),s.percent("y",n[1]),!s.has("x")||!s.has("y"))break;e.set(t+"X",s.get("x")),e.set(t+"Y",s.get("y"));break;case"scroll":e.alt(t,i,["up"])}},/=/,/\s/),e.has("id")){var i=new(a.vttjs.VTTRegion||a.window.VTTRegion);i.width=e.get("width",100),i.lines=e.get("lines",3),i.regionAnchorX=e.get("regionanchorX",0),i.regionAnchorY=e.get("regionanchorY",100),i.viewportAnchorX=e.get("viewportanchorX",0),i.viewportAnchorY=e.get("viewportanchorY",100),i.scroll=e.get("scroll",""),a.onregion&&a.onregion(i),a.regionList.push({id:e.get("id"),region:i})}}(e)}},/:/)}var a=this;t&&(a.buffer+=a.decoder.decode(t,{stream:!0}));try{var c;if("INITIAL"===a.state){if(!/\r\n|\n/.test(a.buffer))return this;var l=(c=i()).match(/^WEBVTT([ \t].*)?$/);if(!l||!l[0])throw new e(e.Errors.BadSignature);a.state="HEADER"}for(var u=!1;a.buffer;){if(!/\r\n|\n/.test(a.buffer))return this;switch(u?u=!1:c=i(),a.state){case"HEADER":/:/.test(c)?r(c):c||(a.state="ID");continue;case"NOTE":c||(a.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(c)){a.state="NOTE";break}if(!c)continue;if(a.cue=new(a.vttjs.VTTCue||a.window.VTTCue)(0,0,""),a.state="CUE",-1===c.indexOf("--\x3e")){a.cue.id=c;continue}case"CUE":try{s(c,a.cue,a.regionList)}catch(t){a.reportOrThrowError(t),a.cue=null,a.state="BADCUE";continue}a.state="CUETEXT";continue;case"CUETEXT":var h=-1!==c.indexOf("--\x3e");if(!c||h&&(u=!0)){a.oncue&&a.oncue(a.cue),a.cue=null,a.state="ID";continue}a.cue.text&&(a.cue.text+="\n"),a.cue.text+=c;continue;case"BADCUE":c||(a.state="ID");continue}}}catch(t){a.reportOrThrowError(t),"CUETEXT"===a.state&&a.cue&&a.oncue&&a.oncue(a.cue),a.cue=null,a.state="INITIAL"===a.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){var t=this;try{if(t.buffer+=t.decoder.decode(),(t.cue||"HEADER"===t.state)&&(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state)throw new e(e.Errors.BadSignature)}catch(e){t.reportOrThrowError(e)}return t.onflush&&t.onflush(),this}},t.WebVTT=h}(this,this.vttjs),videojs.plugin("playlist",function(t){for(var e=this.el().id,i=document.querySelectorAll("#"+e+"-vjs-playlist .vjs-track"),o=i.length,n=this,s=(i[0],0),r=!0,a=t.onTrackSelected,c=0;o>c;c++)i[c].onclick=function(){l(this)};(void 0===t.continuous||1==t.continuous)&&n.on("ended",function(){++s>=o&&(s=0),i[s].click()});var l=function(e){var c=e.getAttribute("data-src");s=parseInt(e.getAttribute("data-index"))||s,n.src("youtube"==n.techName?[{type:type="video/youtube",src:c}]:"AUDIO"==n.el().firstChild.tagName||void 0!==t.mediaType&&"audio"==t.mediaType?[{type:"audio/mp4",src:c+".m4a"},{type:"audio/webm",src:c+".webm"},{type:"audio/ogg",src:c+".ogg"}]:[{type:"video/mp4",src:c+".mp4"},{type:"video/webm",src:c+".webm"}]),r&&n.play();for(var l=0;o>l;l++)i[l].classList.contains("currentTrack")&&(i[l].className=i[l].className.replace(/\bcurrentTrack\b/,"nonPlayingTrack"));e.className=e.className+" currentTrack","function"==typeof a&&a.apply(e)};return void 0!==t.setTrack&&(t.setTrack=parseInt(t.setTrack),i[t.setTrack],s=t.setTrack,r=!1,l(i[s]),r=!0),{tracks:i,trackCount:o,play:function(){return r},index:function(){return s},prev:function(){var t=s-1;(0>t||t>o)&&(t=0),l(i[t])},next:function(){var t=s+1;(0>t||t>o)&&(t=0),l(i[t])}}}),videojs("#video-playlist",{height:"auto",width:"auto",techOrder:["html5","flash"]}).ready(function(){function t(){var t=document.getElementById(e.el().id).parentElement.offsetWidth;e.width(t).height(t*(9/16))}var e=this;this.volume(.3),this.playlist({continuous:!0}),t(),window.onresize=t});

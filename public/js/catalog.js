function catalog_change(){var el,erase_flag=0,arr=new Array,options=new Array,options_flag=0,selected=new Array,images=new Array,add_arrays="";for(i=0;i<add_fields.length;i++){eval("el = document.tform."+add_fields[i]);for(var j=0;j<el.options.length;j++)el.options[j].selected&&(""!=add_arrays&&(add_arrays+=", "),add_arrays+=add_fields[i]+"["+j+"]")}for(""!=add_arrays&&(erase_flag=1,eval("selected = arr.concat("+add_arrays+")")),i=0;i<normal_fields.length;i++){var arrays="";eval("el = document.tform."+normal_fields[i]);for(var j=0;j<el.options.length;j++)1==erase_flag?el.options[j].selected=!1:el.options[j].selected&&(""!=arrays&&(arrays+=", "),arrays+=normal_fields[i]+"["+j+"]");""!=arrays&&(options_flag=1,eval("var theseoptions = arr.concat("+arrays+")"),theseoptions.sort(numsort),options=0==options.length?theseoptions:array_union(options,theseoptions))}if(1!=erase_flag){if(selected=new Array,options.length>0&&(selected[0]=options),0==selected.length)for(i=0;i<product_codes.length;i++)selected[i]=i;for(arrays="",i=0;i<selected.length;i++)i>0&&(arrays+=", "),arrays+="selected["+i+"]",0==options.length?0!=options_flag&&(selected[i]=new Array):selected[i]=array_union(selected[i],options);eval("selected = arr.concat("+arrays+")");var unique=selected.sort().reduce(function(e,r){return r!=e[0]&&e.unshift(r),e},[]);unique.sort(numsort),showproducts(unique)}else{el=document.tform.products;for(var j=0;j<el.options.length;j++)el.options[j].selected=!1;showproducts(selected)}}function showproducts(e){var r="",a=$("#results");if(a){matches=1==e.length?"1 match":e.length+" matches",r='<div align="right" class="matches">'+matches+" found</div>";for(var t=0;t<e.length;t++){if(imgstr="",""!=images[e[t]]){for(imgarray=images[e[t]].split(","),j=0;j<imgarray.length;j++)num=imgarray.length>1?" #"+(j+1):"",""!=imgstr&&(imgstr+=" | "),imgstr+="<a href=\"javascript:image_popup('"+imgarray[j]+"')\">Kit Image"+num+"</a>";imgstr+="<br>"}r+=desc[e[t]]+"<br><i>Product ID</i> - "+product_codes[e[t]]+"<br>"+imgstr+"<br>"}$(a).html(r)}}function numsort(e,r){return e-r}function array_union(e,r){for(var a=new Array,t=0,o=0,s=r.length,i=0;i<e.length;i++){for(;e[i]>r[t]&&t<s;)t++;t<s&&e[i]==r[t]&&(a[o++]=e[i])}return a}function clear_form(){for(i=0;i<normal_fields.length;i++){eval("el = document.tform."+normal_fields[i]);for(var j=0;j<el.options.length;j++)el.options[j].selected=!1}for(i=0;i<add_fields.length;i++){eval("el = document.tform."+add_fields[i]);for(var j=0;j<el.options.length;j++)el.options[j].selected=!1}}function image_popup(e){openShadowbox("assets/images/kit_config/"+e,"img","Kit Image")}

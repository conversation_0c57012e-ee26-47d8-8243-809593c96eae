function timelineBreakpointCheck(){window.matchMedia("(min-width:768px)").matches&&($("nav.solution-indicator a.indicator-toggle").removeClass("opened"),$("nav.solution-indicator .section-list").removeAttr("style"))}function desktopNavBreakpointCheck(){window.matchMedia("(min-width:992px)").matches||clearNav()}function mobileNavBreakpointCheck(){window.matchMedia("(min-width:992px)").matches&&($("header a.mobile-toggle").removeClass("opened"),$("nav.mobile").removeAttr("style"))}function clearNav(){$("header .desktop ul li:first-child a").removeClass("opened"),$("header .mega-menu").removeAttr("style"),$("header .links a.language").removeClass("opened"),$("header .languages").removeAttr("style"),$("header .links a.user").removeClass("opened"),$("header .user-options").removeAttr("style"),$("header .links a.bd-sites").removeClass("opened"),$("header .sites").removeAttr("style"),$("header form.search").removeAttr("style"),$("header nav.desktop.float-right").removeAttr("style")}function equalHeight(){window.matchMedia("(min-width:768px)").matches&&$("div.equals").each(function(e){var t=$(this).height();$(this).find("div.equal").css("height",t-20)})}function sourceOrder(){window.matchMedia("(min-width: 768px)").matches?$("#move").insertBefore("#location-1"):$("#move").insertBefore("#location-2")}function navHeightCheck(){$(window).height()<600?($("header .desktop ul li:first-child span.go-to-link").show(),$("header .desktop ul li:first-child span.go-to-link").click(function(e){e.preventDefault(),$("header .desktop ul li:first-child a").off(),window.location=$(this).parent().attr("href")})):$("header .desktop ul li:first-child span.go-to-link").hide()}function crumbsCheck(){var e=$(window).width();$("nav.crumbs > div > ul:first-child").width()+$("nav.crumbs > div > ul:last-child").width()+50<e?$("nav.crumbs").addClass("expand"):$("nav.crumbs").removeClass("expand")}!function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof module&&module.exports?module.exports=e(require("jquery")):e(jQuery)}(function(e){var t=-1,a=-1,n=function(e){return parseFloat(e)||0},i=function(t){var a=e(t),i=null,o=[];return a.each(function(){var t=e(this),a=t.offset().top-n(t.css("margin-top")),s=o.length>0?o[o.length-1]:null;null===s?o.push(t):Math.floor(Math.abs(i-a))<=1?o[o.length-1]=s.add(t):o.push(t),i=a}),o},o=function(t){var a={byRow:!0,property:"height",target:null,remove:!1};return"object"==typeof t?e.extend(a,t):("boolean"==typeof t?a.byRow=t:"remove"===t&&(a.remove=!0),a)},s=e.fn.matchHeight=function(t){var a=o(t);if(a.remove){var n=this;return this.css(a.property,""),e.each(s._groups,function(e,t){t.elements=t.elements.not(n)}),this}return this.length<=1&&!a.target?this:(s._groups.push({elements:this,options:a}),s._apply(this,a),this)};s.version="master",s._groups=[],s._throttle=80,s._maintainScroll=!1,s._beforeUpdate=null,s._afterUpdate=null,s._rows=i,s._parse=n,s._parseOptions=o,s._apply=function(t,a){var l=o(a),r=e(t),c=[r],d=e(window).scrollTop(),u=e("html").outerHeight(!0),h=r.parents().filter(":hidden");return h.each(function(){var t=e(this);t.data("style-cache",t.attr("style"))}),h.css("display","block"),l.byRow&&!l.target&&(r.each(function(){var t=e(this),a=t.css("display");"inline-block"!==a&&"flex"!==a&&"inline-flex"!==a&&(a="block"),t.data("style-cache",t.attr("style")),t.css({display:a,"padding-top":"0","padding-bottom":"0","margin-top":"0","margin-bottom":"0","border-top-width":"0","border-bottom-width":"0",height:"100px",overflow:"hidden"})}),c=i(r),r.each(function(){var t=e(this);t.attr("style",t.data("style-cache")||"")})),e.each(c,function(t,a){var i=e(a),o=0;if(l.target)o=l.target.outerHeight(!1);else{if(l.byRow&&i.length<=1)return void i.css(l.property,"");i.each(function(){var t=e(this),a=t.attr("style"),n=t.css("display");"inline-block"!==n&&"flex"!==n&&"inline-flex"!==n&&(n="block");var i={display:n};i[l.property]="",t.css(i),t.outerHeight(!1)>o&&(o=t.outerHeight(!1)),a?t.attr("style",a):t.css("display","")})}i.each(function(){var t=e(this),a=0;l.target&&t.is(l.target)||("border-box"!==t.css("box-sizing")&&(a+=n(t.css("border-top-width"))+n(t.css("border-bottom-width")),a+=n(t.css("padding-top"))+n(t.css("padding-bottom"))),t.css(l.property,o-a+"px"))})}),h.each(function(){var t=e(this);t.attr("style",t.data("style-cache")||null)}),s._maintainScroll&&e(window).scrollTop(d/u*e("html").outerHeight(!0)),this},s._applyDataApi=function(){var t={};e("[data-match-height], [data-mh]").each(function(){var a=e(this),n=a.attr("data-mh")||a.attr("data-match-height");t[n]=n in t?t[n].add(a):a}),e.each(t,function(){this.matchHeight(!0)})};var l=function(t){s._beforeUpdate&&s._beforeUpdate(t,s._groups),e.each(s._groups,function(){s._apply(this.elements,this.options)}),s._afterUpdate&&s._afterUpdate(t,s._groups)};s._update=function(n,i){if(i&&"resize"===i.type){var o=e(window).width();if(o===t)return;t=o}n?-1===a&&(a=setTimeout(function(){l(i),a=-1},s._throttle)):l(i)},e(s._applyDataApi);var r=e.fn.on?"on":"bind";e(window)[r]("load",function(e){s._update(!1,e)}),e(window)[r]("resize orientationchange",function(e){s._update(!0,e)})}),function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?e(require("jquery")):e(window.jQuery||window.Zepto)}(function(e){var t,a,n,i,o,s,l="Close",r="BeforeClose",c="MarkupParse",d="Open",u="Change",h="mfp",p="."+h,f="mfp-ready",m="mfp-removing",v="mfp-prevent-close",g=function(){},b=!!window.jQuery,_=e(window),w=function(e,a){t.ev.on(h+e+p,a)},C=function(t,a,n,i){var o=document.createElement("div");return o.className="mfp-"+t,n&&(o.innerHTML=n),i?a&&a.appendChild(o):(o=e(o),a&&o.appendTo(a)),o},x=function(a,n){t.ev.triggerHandler(h+a,n),t.st.callbacks&&(a=a.charAt(0).toLowerCase()+a.slice(1),t.st.callbacks[a]&&t.st.callbacks[a].apply(t,e.isArray(n)?n:[n]))},y=function(a){return a===s&&t.currTemplate.closeBtn||(t.currTemplate.closeBtn=e(t.st.closeMarkup.replace("%title%",t.st.tClose)),s=a),t.currTemplate.closeBtn},S=function(){e.magnificPopup.instance||((t=new g).init(),e.magnificPopup.instance=t)};g.prototype={constructor:g,init:function(){var a=navigator.appVersion;t.isLowIE=t.isIE8=document.all&&!document.addEventListener,t.isAndroid=/android/gi.test(a),t.isIOS=/iphone|ipad|ipod/gi.test(a),t.supportsTransition=function(){var e=document.createElement("p").style,t=["ms","O","Moz","Webkit"];if(void 0!==e.transition)return!0;for(;t.length;)if(t.pop()+"Transition"in e)return!0;return!1}(),t.probablyMobile=t.isAndroid||t.isIOS||/(Opera Mini)|Kindle|webOS|BlackBerry|(Opera Mobi)|(Windows Phone)|IEMobile/i.test(navigator.userAgent),n=e(document),t.popupsCache={}},open:function(a){var i;if(!1===a.isObj){t.items=a.items.toArray(),t.index=0;var s,l=a.items;for(i=0;i<l.length;i++)if((s=l[i]).parsed&&(s=s.el[0]),s===a.el[0]){t.index=i;break}}else t.items=e.isArray(a.items)?a.items:[a.items],t.index=a.index||0;if(!t.isOpen){t.types=[],o="",a.mainEl&&a.mainEl.length?t.ev=a.mainEl.eq(0):t.ev=n,a.key?(t.popupsCache[a.key]||(t.popupsCache[a.key]={}),t.currTemplate=t.popupsCache[a.key]):t.currTemplate={},t.st=e.extend(!0,{},e.magnificPopup.defaults,a),t.fixedContentPos="auto"===t.st.fixedContentPos?!t.probablyMobile:t.st.fixedContentPos,t.st.modal&&(t.st.closeOnContentClick=!1,t.st.closeOnBgClick=!1,t.st.showCloseBtn=!1,t.st.enableEscapeKey=!1),t.bgOverlay||(t.bgOverlay=C("bg").on("click"+p,function(){t.close()}),t.wrap=C("wrap").attr("tabindex",-1).on("click"+p,function(e){t._checkIfClose(e.target)&&t.close()}),t.container=C("container",t.wrap)),t.contentContainer=C("content"),t.st.preloader&&(t.preloader=C("preloader",t.container,t.st.tLoading));var r=e.magnificPopup.modules;for(i=0;i<r.length;i++){var u=r[i];u=u.charAt(0).toUpperCase()+u.slice(1),t["init"+u].call(t)}x("BeforeOpen"),t.st.showCloseBtn&&(t.st.closeBtnInside?(w(c,function(e,t,a,n){a.close_replaceWith=y(n.type)}),o+=" mfp-close-btn-in"):t.wrap.append(y())),t.st.alignTop&&(o+=" mfp-align-top"),t.fixedContentPos?t.wrap.css({overflow:t.st.overflowY,overflowX:"hidden",overflowY:t.st.overflowY}):t.wrap.css({top:_.scrollTop(),position:"absolute"}),(!1===t.st.fixedBgPos||"auto"===t.st.fixedBgPos&&!t.fixedContentPos)&&t.bgOverlay.css({height:n.height(),position:"absolute"}),t.st.enableEscapeKey&&n.on("keyup"+p,function(e){27===e.keyCode&&t.close()}),_.on("resize"+p,function(){t.updateSize()}),t.st.closeOnContentClick||(o+=" mfp-auto-cursor"),o&&t.wrap.addClass(o);var h=t.wH=_.height(),m={};if(t.fixedContentPos&&t._hasScrollBar(h)){var v=t._getScrollbarSize();v&&(m.marginRight=v)}t.fixedContentPos&&(t.isIE7?e("body, html").css("overflow","hidden"):m.overflow="hidden");var g=t.st.mainClass;return t.isIE7&&(g+=" mfp-ie7"),g&&t._addClassToMFP(g),t.updateItemHTML(),x("BuildControls"),e("html").css(m),t.bgOverlay.add(t.wrap).prependTo(t.st.prependTo||e(document.body)),t._lastFocusedEl=document.activeElement,setTimeout(function(){t.content?(t._addClassToMFP(f),t._setFocus()):t.bgOverlay.addClass(f),n.on("focusin"+p,t._onFocusIn)},16),t.isOpen=!0,t.updateSize(h),x(d),a}t.updateItemHTML()},close:function(){t.isOpen&&(x(r),t.isOpen=!1,t.st.removalDelay&&!t.isLowIE&&t.supportsTransition?(t._addClassToMFP(m),setTimeout(function(){t._close()},t.st.removalDelay)):t._close())},_close:function(){x(l);var a=m+" "+f+" ";if(t.bgOverlay.detach(),t.wrap.detach(),t.container.empty(),t.st.mainClass&&(a+=t.st.mainClass+" "),t._removeClassFromMFP(a),t.fixedContentPos){var i={marginRight:""};t.isIE7?e("body, html").css("overflow",""):i.overflow="",e("html").css(i)}n.off("keyup.mfp focusin"+p),t.ev.off(p),t.wrap.attr("class","mfp-wrap").removeAttr("style"),t.bgOverlay.attr("class","mfp-bg"),t.container.attr("class","mfp-container"),t.st.showCloseBtn&&(!t.st.closeBtnInside||!0===t.currTemplate[t.currItem.type])&&t.currTemplate.closeBtn&&t.currTemplate.closeBtn.detach(),t.st.autoFocusLast&&t._lastFocusedEl&&e(t._lastFocusedEl).focus(),t.currItem=null,t.content=null,t.currTemplate=null,t.prevHeight=0,x("AfterClose")},updateSize:function(e){if(t.isIOS){var a=document.documentElement.clientWidth/window.innerWidth,n=window.innerHeight*a;t.wrap.css("height",n),t.wH=n}else t.wH=e||_.height();t.fixedContentPos||t.wrap.css("height",t.wH),x("Resize")},updateItemHTML:function(){var a=t.items[t.index];t.contentContainer.detach(),t.content&&t.content.detach(),a.parsed||(a=t.parseEl(t.index));var n=a.type;if(x("BeforeChange",[t.currItem?t.currItem.type:"",n]),t.currItem=a,!t.currTemplate[n]){var o=!!t.st[n]&&t.st[n].markup;x("FirstMarkupParse",o),t.currTemplate[n]=!o||e(o)}i&&i!==a.type&&t.container.removeClass("mfp-"+i+"-holder");var s=t["get"+n.charAt(0).toUpperCase()+n.slice(1)](a,t.currTemplate[n]);t.appendContent(s,n),a.preloaded=!0,x(u,a),i=a.type,t.container.prepend(t.contentContainer),x("AfterChange")},appendContent:function(e,a){t.content=e,e?t.st.showCloseBtn&&t.st.closeBtnInside&&!0===t.currTemplate[a]?t.content.find(".mfp-close").length||t.content.append(y()):t.content=e:t.content="",x("BeforeAppend"),t.container.addClass("mfp-"+a+"-holder"),t.contentContainer.append(t.content)},parseEl:function(a){var n,i=t.items[a];if(i.tagName?i={el:e(i)}:(n=i.type,i={data:i,src:i.src}),i.el){for(var o=t.types,s=0;s<o.length;s++)if(i.el.hasClass("mfp-"+o[s])){n=o[s];break}i.src=i.el.attr("data-mfp-src"),i.src||(i.src=i.el.attr("href"))}return i.type=n||t.st.type||"inline",i.index=a,i.parsed=!0,t.items[a]=i,x("ElementParse",i),t.items[a]},addGroup:function(e,a){var n=function(n){n.mfpEl=this,t._openClick(n,e,a)};a||(a={});var i="click.magnificPopup";a.mainEl=e,a.items?(a.isObj=!0,e.off(i).on(i,n)):(a.isObj=!1,a.delegate?e.off(i).on(i,a.delegate,n):(a.items=e,e.off(i).on(i,n)))},_openClick:function(a,n,i){if((void 0!==i.midClick?i.midClick:e.magnificPopup.defaults.midClick)||!(2===a.which||a.ctrlKey||a.metaKey||a.altKey||a.shiftKey)){var o=void 0!==i.disableOn?i.disableOn:e.magnificPopup.defaults.disableOn;if(o)if(e.isFunction(o)){if(!o.call(t))return!0}else if(_.width()<o)return!0;a.type&&(a.preventDefault(),t.isOpen&&a.stopPropagation()),i.el=e(a.mfpEl),i.delegate&&(i.items=n.find(i.delegate)),t.open(i)}},updateStatus:function(e,n){if(t.preloader){a!==e&&t.container.removeClass("mfp-s-"+a),!n&&"loading"===e&&(n=t.st.tLoading);var i={status:e,text:n};x("UpdateStatus",i),e=i.status,n=i.text,t.preloader.html(n),t.preloader.find("a").on("click",function(e){e.stopImmediatePropagation()}),t.container.addClass("mfp-s-"+e),a=e}},_checkIfClose:function(a){if(!e(a).hasClass(v)){var n=t.st.closeOnContentClick,i=t.st.closeOnBgClick;if(n&&i)return!0;if(!t.content||e(a).hasClass("mfp-close")||t.preloader&&a===t.preloader[0])return!0;if(a===t.content[0]||e.contains(t.content[0],a)){if(n)return!0}else if(i&&e.contains(document,a))return!0;return!1}},_addClassToMFP:function(e){t.bgOverlay.addClass(e),t.wrap.addClass(e)},_removeClassFromMFP:function(e){this.bgOverlay.removeClass(e),t.wrap.removeClass(e)},_hasScrollBar:function(e){return(t.isIE7?n.height():document.body.scrollHeight)>(e||_.height())},_setFocus:function(){(t.st.focus?t.content.find(t.st.focus).eq(0):t.wrap).focus()},_onFocusIn:function(a){if(a.target!==t.wrap[0]&&!e.contains(t.wrap[0],a.target))return t._setFocus(),!1},_parseMarkup:function(t,a,n){var i;n.data&&(a=e.extend(n.data,a)),x(c,[t,a,n]),e.each(a,function(a,n){if(void 0===n||!1===n)return!0;if((i=a.split("_")).length>1){var o=t.find(p+"-"+i[0]);if(o.length>0){var s=i[1];"replaceWith"===s?o[0]!==n[0]&&o.replaceWith(n):"img"===s?o.is("img")?o.attr("src",n):o.replaceWith(e("<img>").attr("src",n).attr("class",o.attr("class"))):o.attr(i[1],n)}}else t.find(p+"-"+a).html(n)})},_getScrollbarSize:function(){if(void 0===t.scrollbarSize){var e=document.createElement("div");e.style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(e),t.scrollbarSize=e.offsetWidth-e.clientWidth,document.body.removeChild(e)}return t.scrollbarSize}},e.magnificPopup={instance:null,proto:g.prototype,modules:[],open:function(t,a){return S(),(t=t?e.extend(!0,{},t):{}).isObj=!0,t.index=a||0,this.instance.open(t)},close:function(){return e.magnificPopup.instance&&e.magnificPopup.instance.close()},registerModule:function(t,a){a.options&&(e.magnificPopup.defaults[t]=a.options),e.extend(this.proto,a.proto),this.modules.push(t)},defaults:{disableOn:0,key:null,midClick:!1,mainClass:"",preloader:!0,focus:"",closeOnContentClick:!1,closeOnBgClick:!0,closeBtnInside:!0,showCloseBtn:!0,enableEscapeKey:!0,modal:!1,alignTop:!1,removalDelay:0,prependTo:null,fixedContentPos:"auto",fixedBgPos:"auto",overflowY:"auto",closeMarkup:'<button title="%title%" type="button" class="mfp-close">&#215;</button>',tClose:"Close (Esc)",tLoading:"Loading...",autoFocusLast:!0}},e.fn.magnificPopup=function(a){S();var n=e(this);if("string"==typeof a)if("open"===a){var i,o=b?n.data("magnificPopup"):n[0].magnificPopup,s=parseInt(arguments[1],10)||0;o.items?i=o.items[s]:(i=n,o.delegate&&(i=i.find(o.delegate)),i=i.eq(s)),t._openClick({mfpEl:i},n,o)}else t.isOpen&&t[a].apply(t,Array.prototype.slice.call(arguments,1));else a=e.extend(!0,{},a),b?n.data("magnificPopup",a):n[0].magnificPopup=a,t.addGroup(n,a);return n};var $,k,T,I="inline",M=function(){T&&(k.after(T.addClass($)).detach(),T=null)};e.magnificPopup.registerModule(I,{options:{hiddenClass:"hide",markup:"",tNotFound:"Content not found"},proto:{initInline:function(){t.types.push(I),w(l+"."+I,function(){M()})},getInline:function(a,n){if(M(),a.src){var i=t.st.inline,o=e(a.src);if(o.length){var s=o[0].parentNode;s&&s.tagName&&(k||($=i.hiddenClass,k=C($),$="mfp-"+$),T=o.after(k).detach().removeClass($)),t.updateStatus("ready")}else t.updateStatus("error",i.tNotFound),o=e("<div>");return a.inlineElement=o,o}return t.updateStatus("ready"),t._parseMarkup(n,{},a),n}}});var D,E="ajax",A=function(){D&&e(document.body).removeClass(D)},O=function(){A(),t.req&&t.req.abort()};e.magnificPopup.registerModule(E,{options:{settings:null,cursor:"mfp-ajax-cur",tError:'<a href="%url%">The content</a> could not be loaded.'},proto:{initAjax:function(){t.types.push(E),D=t.st.ajax.cursor,w(l+"."+E,O),w("BeforeChange."+E,O)},getAjax:function(a){D&&e(document.body).addClass(D),t.updateStatus("loading");var n=e.extend({url:a.src,success:function(n,i,o){var s={data:n,xhr:o};x("ParseAjax",s),t.appendContent(e(s.data),E),a.finished=!0,A(),t._setFocus(),setTimeout(function(){t.wrap.addClass(f)},16),t.updateStatus("ready"),x("AjaxContentAdded")},error:function(){A(),a.finished=a.loadError=!0,t.updateStatus("error",t.st.ajax.tError.replace("%url%",a.src))}},t.st.ajax.settings);return t.req=e.ajax(n),""}}});var B,P=function(a){if(a.data&&void 0!==a.data.title)return a.data.title;var n=t.st.image.titleSrc;if(n){if(e.isFunction(n))return n.call(t,a);if(a.el)return a.el.attr(n)||""}return""};e.magnificPopup.registerModule("image",{options:{markup:'<div class="mfp-figure"><div class="mfp-close"></div><figure><div class="mfp-img"></div><figcaption><div class="mfp-bottom-bar"><div class="mfp-title"></div><div class="mfp-counter"></div></div></figcaption></figure></div>',cursor:"mfp-zoom-out-cur",titleSrc:"title",verticalFit:!0,tError:'<a href="%url%">The image</a> could not be loaded.'},proto:{initImage:function(){var a=t.st.image,n=".image";t.types.push("image"),w(d+n,function(){"image"===t.currItem.type&&a.cursor&&e(document.body).addClass(a.cursor)}),w(l+n,function(){a.cursor&&e(document.body).removeClass(a.cursor),_.off("resize"+p)}),w("Resize"+n,t.resizeImage),t.isLowIE&&w("AfterChange",t.resizeImage)},resizeImage:function(){var e=t.currItem;if(e&&e.img&&t.st.image.verticalFit){var a=0;t.isLowIE&&(a=parseInt(e.img.css("padding-top"),10)+parseInt(e.img.css("padding-bottom"),10)),e.img.css("max-height",t.wH-a)}},_onImageHasSize:function(e){e.img&&(e.hasSize=!0,B&&clearInterval(B),e.isCheckingImgSize=!1,x("ImageHasSize",e),e.imgHidden&&(t.content&&t.content.removeClass("mfp-loading"),e.imgHidden=!1))},findImageSize:function(e){var a=0,n=e.img[0],i=function(o){B&&clearInterval(B),B=setInterval(function(){n.naturalWidth>0?t._onImageHasSize(e):(a>200&&clearInterval(B),3===++a?i(10):40===a?i(50):100===a&&i(500))},o)};i(1)},getImage:function(a,n){var i=0,o=function(){a&&(a.img[0].complete?(a.img.off(".mfploader"),a===t.currItem&&(t._onImageHasSize(a),t.updateStatus("ready")),a.hasSize=!0,a.loaded=!0,x("ImageLoadComplete")):++i<200?setTimeout(o,100):s())},s=function(){a&&(a.img.off(".mfploader"),a===t.currItem&&(t._onImageHasSize(a),t.updateStatus("error",l.tError.replace("%url%",a.src))),a.hasSize=!0,a.loaded=!0,a.loadError=!0)},l=t.st.image,r=n.find(".mfp-img");if(r.length){var c=document.createElement("img");c.className="mfp-img",a.el&&a.el.find("img").length&&(c.alt=a.el.find("img").attr("alt")),a.img=e(c).on("load.mfploader",o).on("error.mfploader",s),c.src=a.src,r.is("img")&&(a.img=a.img.clone()),(c=a.img[0]).naturalWidth>0?a.hasSize=!0:c.width||(a.hasSize=!1)}return t._parseMarkup(n,{title:P(a),img_replaceWith:a.img},a),t.resizeImage(),a.hasSize?(B&&clearInterval(B),a.loadError?(n.addClass("mfp-loading"),t.updateStatus("error",l.tError.replace("%url%",a.src))):(n.removeClass("mfp-loading"),t.updateStatus("ready")),n):(t.updateStatus("loading"),a.loading=!0,a.hasSize||(a.imgHidden=!0,n.addClass("mfp-loading"),t.findImageSize(a)),n)}}});var L;e.magnificPopup.registerModule("zoom",{options:{enabled:!1,easing:"ease-in-out",duration:300,opener:function(e){return e.is("img")?e:e.find("img")}},proto:{initZoom:function(){var e,a=t.st.zoom,n=".zoom";if(a.enabled&&t.supportsTransition){var i,o,s=a.duration,c=function(e){var t=e.clone().removeAttr("style").removeAttr("class").addClass("mfp-animated-image"),n="all "+a.duration/1e3+"s "+a.easing,i={position:"fixed",zIndex:9999,left:0,top:0,"-webkit-backface-visibility":"hidden"},o="transition";return i["-webkit-"+o]=i["-moz-"+o]=i["-o-"+o]=i[o]=n,t.css(i),t},d=function(){t.content.css("visibility","visible")};w("BuildControls"+n,function(){if(t._allowZoom()){if(clearTimeout(i),t.content.css("visibility","hidden"),!(e=t._getItemToZoom()))return void d();(o=c(e)).css(t._getOffset()),t.wrap.append(o),i=setTimeout(function(){o.css(t._getOffset(!0)),i=setTimeout(function(){d(),setTimeout(function(){o.remove(),e=o=null,x("ZoomAnimationEnded")},16)},s)},16)}}),w(r+n,function(){if(t._allowZoom()){if(clearTimeout(i),t.st.removalDelay=s,!e){if(!(e=t._getItemToZoom()))return;o=c(e)}o.css(t._getOffset(!0)),t.wrap.append(o),t.content.css("visibility","hidden"),setTimeout(function(){o.css(t._getOffset())},16)}}),w(l+n,function(){t._allowZoom()&&(d(),o&&o.remove(),e=null)})}},_allowZoom:function(){return"image"===t.currItem.type},_getItemToZoom:function(){return!!t.currItem.hasSize&&t.currItem.img},_getOffset:function(a){var n,i=(n=a?t.currItem.img:t.st.zoom.opener(t.currItem.el||t.currItem)).offset(),o=parseInt(n.css("padding-top"),10),s=parseInt(n.css("padding-bottom"),10);i.top-=e(window).scrollTop()-o;var l={width:n.width(),height:(b?n.innerHeight():n[0].offsetHeight)-s-o};return void 0===L&&(L=void 0!==document.createElement("p").style.MozTransform),L?l["-moz-transform"]=l.transform="translate("+i.left+"px,"+i.top+"px)":(l.left=i.left,l.top=i.top),l}}});var z="iframe",N=function(e){if(t.currTemplate[z]){var a=t.currTemplate[z].find("iframe");a.length&&(e||(a[0].src="//about:blank"),t.isIE8&&a.css("display",e?"block":"none"))}};e.magnificPopup.registerModule(z,{options:{markup:'<div class="mfp-iframe-scaler"><div class="mfp-close"></div><iframe class="mfp-iframe" src="//about:blank" frameborder="0" allowfullscreen></iframe></div>',srcAction:"iframe_src",patterns:{youtube:{index:"youtube.com",id:"v=",src:"//www.youtube.com/embed/%id%?autoplay=1"},vimeo:{index:"vimeo.com/",id:"/",src:"//player.vimeo.com/video/%id%?autoplay=1"},gmaps:{index:"//maps.google.",src:"%id%&output=embed"}}},proto:{initIframe:function(){t.types.push(z),w("BeforeChange",function(e,t,a){t!==a&&(t===z?N():a===z&&N(!0))}),w(l+"."+z,function(){N()})},getIframe:function(a,n){var i=a.src,o=t.st.iframe;e.each(o.patterns,function(){if(i.indexOf(this.index)>-1)return this.id&&(i="string"==typeof this.id?i.substr(i.lastIndexOf(this.id)+this.id.length,i.length):this.id.call(this,i)),i=this.src.replace("%id%",i),!1});var s={};return o.srcAction&&(s[o.srcAction]=i),t._parseMarkup(n,s,a),t.updateStatus("ready"),n}}});var H=function(e){var a=t.items.length;return e>a-1?e-a:e<0?a+e:e},W=function(e,t,a){return e.replace(/%curr%/gi,t+1).replace(/%total%/gi,a)};e.magnificPopup.registerModule("gallery",{options:{enabled:!1,arrowMarkup:'<button title="%title%" type="button" class="mfp-arrow mfp-arrow-%dir%"></button>',preload:[0,2],navigateByImgClick:!0,arrows:!0,tPrev:"Previous (Left arrow key)",tNext:"Next (Right arrow key)",tCounter:"%curr% of %total%"},proto:{initGallery:function(){var a=t.st.gallery,i=".mfp-gallery";if(t.direction=!0,!a||!a.enabled)return!1;o+=" mfp-gallery",w(d+i,function(){a.navigateByImgClick&&t.wrap.on("click"+i,".mfp-img",function(){if(t.items.length>1)return t.next(),!1}),n.on("keydown"+i,function(e){37===e.keyCode?t.prev():39===e.keyCode&&t.next()})}),w("UpdateStatus"+i,function(e,a){a.text&&(a.text=W(a.text,t.currItem.index,t.items.length))}),w(c+i,function(e,n,i,o){var s=t.items.length;i.counter=s>1?W(a.tCounter,o.index,s):""}),w("BuildControls"+i,function(){if(t.items.length>1&&a.arrows&&!t.arrowLeft){var n=a.arrowMarkup,i=t.arrowLeft=e(n.replace(/%title%/gi,a.tPrev).replace(/%dir%/gi,"left")).addClass(v),o=t.arrowRight=e(n.replace(/%title%/gi,a.tNext).replace(/%dir%/gi,"right")).addClass(v);i.click(function(){t.prev()}),o.click(function(){t.next()}),t.container.append(i.add(o))}}),w(u+i,function(){t._preloadTimeout&&clearTimeout(t._preloadTimeout),t._preloadTimeout=setTimeout(function(){t.preloadNearbyImages(),t._preloadTimeout=null},16)}),w(l+i,function(){n.off(i),t.wrap.off("click"+i),t.arrowRight=t.arrowLeft=null})},next:function(){t.direction=!0,t.index=H(t.index+1),t.updateItemHTML()},prev:function(){t.direction=!1,t.index=H(t.index-1),t.updateItemHTML()},goTo:function(e){t.direction=e>=t.index,t.index=e,t.updateItemHTML()},preloadNearbyImages:function(){var e,a=t.st.gallery.preload,n=Math.min(a[0],t.items.length),i=Math.min(a[1],t.items.length);for(e=1;e<=(t.direction?i:n);e++)t._preloadItem(t.index+e);for(e=1;e<=(t.direction?n:i);e++)t._preloadItem(t.index-e)},_preloadItem:function(a){if(a=H(a),!t.items[a].preloaded){var n=t.items[a];n.parsed||(n=t.parseEl(a)),x("LazyLoad",n),"image"===n.type&&(n.img=e('<img class="mfp-img" />').on("load.mfploader",function(){n.hasSize=!0}).on("error.mfploader",function(){n.hasSize=!0,n.loadError=!0,x("LazyLoadError",n)}).attr("src",n.src)),n.preloaded=!0}}}});var R="retina";e.magnificPopup.registerModule(R,{options:{replaceSrc:function(e){return e.src.replace(/\.\w+$/,function(e){return"@2x"+e})},ratio:1},proto:{initRetina:function(){if(window.devicePixelRatio>1){var e=t.st.retina,a=e.ratio;(a=isNaN(a)?a():a)>1&&(w("ImageHasSize."+R,function(e,t){t.img.css({"max-width":t.img[0].naturalWidth/a,width:"100%"})}),w("ElementParse."+R,function(t,n){n.src=e.replaceSrc(n,a)}))}}}}),S()}),function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?module.exports=e:e(jQuery)}(function(e){function t(t){var s=t||window.event,l=r.call(arguments,1),c=0,u=0,h=0,p=0,f=0,m=0;if((t=e.event.fix(s)).type="mousewheel","detail"in s&&(h=-1*s.detail),"wheelDelta"in s&&(h=s.wheelDelta),"wheelDeltaY"in s&&(h=s.wheelDeltaY),"wheelDeltaX"in s&&(u=-1*s.wheelDeltaX),"axis"in s&&s.axis===s.HORIZONTAL_AXIS&&(u=-1*h,h=0),c=0===h?u:h,"deltaY"in s&&(c=h=-1*s.deltaY),"deltaX"in s&&(u=s.deltaX,0===h&&(c=-1*u)),0!==h||0!==u){if(1===s.deltaMode){var v=e.data(this,"mousewheel-line-height");c*=v,h*=v,u*=v}else if(2===s.deltaMode){var g=e.data(this,"mousewheel-page-height");c*=g,h*=g,u*=g}if(p=Math.max(Math.abs(h),Math.abs(u)),(!o||o>p)&&(o=p,n(s,p)&&(o/=40)),n(s,p)&&(c/=40,u/=40,h/=40),c=Math[c>=1?"floor":"ceil"](c/o),u=Math[u>=1?"floor":"ceil"](u/o),h=Math[h>=1?"floor":"ceil"](h/o),d.settings.normalizeOffset&&this.getBoundingClientRect){var b=this.getBoundingClientRect();f=t.clientX-b.left,m=t.clientY-b.top}return t.deltaX=u,t.deltaY=h,t.deltaFactor=o,t.offsetX=f,t.offsetY=m,t.deltaMode=0,l.unshift(t,c,u,h),i&&clearTimeout(i),i=setTimeout(a,200),(e.event.dispatch||e.event.handle).apply(this,l)}}function a(){o=null}function n(e,t){return d.settings.adjustOldDeltas&&"mousewheel"===e.type&&t%120==0}var i,o,s=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],l="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],r=Array.prototype.slice;if(e.event.fixHooks)for(var c=s.length;c;)e.event.fixHooks[s[--c]]=e.event.mouseHooks;var d=e.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var a=l.length;a;)this.addEventListener(l[--a],t,!1);else this.onmousewheel=t;e.data(this,"mousewheel-line-height",d.getLineHeight(this)),e.data(this,"mousewheel-page-height",d.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var a=l.length;a;)this.removeEventListener(l[--a],t,!1);else this.onmousewheel=null;e.removeData(this,"mousewheel-line-height"),e.removeData(this,"mousewheel-page-height")},getLineHeight:function(t){var a=e(t),n=a["offsetParent"in e.fn?"offsetParent":"parent"]();return n.length||(n=e("body")),parseInt(n.css("fontSize"),10)||parseInt(a.css("fontSize"),10)||16},getPageHeight:function(t){return e(t).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};e.fn.extend({mousewheel:function(e){return e?this.bind("mousewheel",e):this.trigger("mousewheel")},unmousewheel:function(e){return this.unbind("mousewheel",e)}})}),function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?module.exports=e:e(jQuery)}(function(e){function t(t){var s=t||window.event,l=r.call(arguments,1),c=0,u=0,h=0,p=0,f=0,m=0;if((t=e.event.fix(s)).type="mousewheel","detail"in s&&(h=-1*s.detail),"wheelDelta"in s&&(h=s.wheelDelta),"wheelDeltaY"in s&&(h=s.wheelDeltaY),"wheelDeltaX"in s&&(u=-1*s.wheelDeltaX),"axis"in s&&s.axis===s.HORIZONTAL_AXIS&&(u=-1*h,h=0),c=0===h?u:h,"deltaY"in s&&(c=h=-1*s.deltaY),"deltaX"in s&&(u=s.deltaX,0===h&&(c=-1*u)),0!==h||0!==u){if(1===s.deltaMode){var v=e.data(this,"mousewheel-line-height");c*=v,h*=v,u*=v}else if(2===s.deltaMode){var g=e.data(this,"mousewheel-page-height");c*=g,h*=g,u*=g}if(p=Math.max(Math.abs(h),Math.abs(u)),(!o||o>p)&&(o=p,n(s,p)&&(o/=40)),n(s,p)&&(c/=40,u/=40,h/=40),c=Math[c>=1?"floor":"ceil"](c/o),u=Math[u>=1?"floor":"ceil"](u/o),h=Math[h>=1?"floor":"ceil"](h/o),d.settings.normalizeOffset&&this.getBoundingClientRect){var b=this.getBoundingClientRect();f=t.clientX-b.left,m=t.clientY-b.top}return t.deltaX=u,t.deltaY=h,t.deltaFactor=o,t.offsetX=f,t.offsetY=m,t.deltaMode=0,l.unshift(t,c,u,h),i&&clearTimeout(i),i=setTimeout(a,200),(e.event.dispatch||e.event.handle).apply(this,l)}}function a(){o=null}function n(e,t){return d.settings.adjustOldDeltas&&"mousewheel"===e.type&&t%120==0}var i,o,s=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],l="onwheel"in document||document.documentMode>=9?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],r=Array.prototype.slice;if(e.event.fixHooks)for(var c=s.length;c;)e.event.fixHooks[s[--c]]=e.event.mouseHooks;var d=e.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var a=l.length;a;)this.addEventListener(l[--a],t,!1);else this.onmousewheel=t;e.data(this,"mousewheel-line-height",d.getLineHeight(this)),e.data(this,"mousewheel-page-height",d.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var a=l.length;a;)this.removeEventListener(l[--a],t,!1);else this.onmousewheel=null;e.removeData(this,"mousewheel-line-height"),e.removeData(this,"mousewheel-page-height")},getLineHeight:function(t){var a=e(t),n=a["offsetParent"in e.fn?"offsetParent":"parent"]();return n.length||(n=e("body")),parseInt(n.css("fontSize"),10)||parseInt(a.css("fontSize"),10)||16},getPageHeight:function(t){return e(t).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};e.fn.extend({mousewheel:function(e){return e?this.bind("mousewheel",e):this.trigger("mousewheel")},unmousewheel:function(e){return this.unbind("mousewheel",e)}})}),function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"undefined"!=typeof module&&module.exports?module.exports=e:e(jQuery,window,document)}(function(e){var t,a,n;t="function"==typeof define&&define.amd,a="undefined"!=typeof module&&module.exports,n="https:"==document.location.protocol?"https:":"http:",t||(a?require("jquery-mousewheel")(e):e.event.special.mousewheel||e("head").append(decodeURI("%3Cscript src="+n+"//cdnjs.cloudflare.com/ajax/libs/jquery-mousewheel/3.1.13/jquery.mousewheel.min.js%3E%3C/script%3E"))),function(){var t,a="mCustomScrollbar",n="mCS",i=".mCustomScrollbar",o={setTop:0,setLeft:0,axis:"y",scrollbarPosition:"inside",scrollInertia:950,autoDraggerLength:!0,alwaysShowScrollbar:0,snapOffset:0,mouseWheel:{enable:!0,scrollAmount:"auto",axis:"y",deltaFactor:"auto",disableOver:["select","option","keygen","datalist","textarea"]},scrollButtons:{scrollType:"stepless",scrollAmount:"auto"},keyboard:{enable:!0,scrollType:"stepless",scrollAmount:"auto"},contentTouchScroll:25,documentTouchScroll:!0,advanced:{autoScrollOnFocus:"input,textarea,select,button,datalist,keygen,a[tabindex],area,object,[contenteditable='true']",updateOnContentResize:!0,updateOnImageLoad:"auto",autoUpdateTimeout:60},theme:"light",callbacks:{onTotalScrollOffset:0,onTotalScrollBackOffset:0,alwaysTriggerOffsets:!0}},s=0,l={},r=window.attachEvent&&!window.addEventListener?1:0,c=!1,d=["mCSB_dragger_onDrag","mCSB_scrollTools_onDrag","mCS_img_loaded","mCS_disabled","mCS_destroyed","mCS_no_scrollbar","mCS-autoHide","mCS-dir-rtl","mCS_no_scrollbar_y","mCS_no_scrollbar_x","mCS_y_hidden","mCS_x_hidden","mCSB_draggerContainer","mCSB_buttonUp","mCSB_buttonDown","mCSB_buttonLeft","mCSB_buttonRight"],u={init:function(t){var t=e.extend(!0,{},o,t),a=h.call(this);if(t.live){var r=t.liveSelector||this.selector||i,c=e(r);if("off"===t.live)return void f(r);l[r]=setTimeout(function(){c.mCustomScrollbar(t),"once"===t.live&&c.length&&f(r)},500)}else f(r);return t.setWidth=t.set_width?t.set_width:t.setWidth,t.setHeight=t.set_height?t.set_height:t.setHeight,t.axis=t.horizontalScroll?"x":m(t.axis),t.scrollInertia=t.scrollInertia>0&&t.scrollInertia<17?17:t.scrollInertia,"object"!=typeof t.mouseWheel&&1==t.mouseWheel&&(t.mouseWheel={enable:!0,scrollAmount:"auto",axis:"y",preventDefault:!1,deltaFactor:"auto",normalizeDelta:!1,invert:!1}),t.mouseWheel.scrollAmount=t.mouseWheelPixels?t.mouseWheelPixels:t.mouseWheel.scrollAmount,t.mouseWheel.normalizeDelta=t.advanced.normalizeMouseWheelDelta?t.advanced.normalizeMouseWheelDelta:t.mouseWheel.normalizeDelta,t.scrollButtons.scrollType=v(t.scrollButtons.scrollType),p(t),e(a).each(function(){var a=e(this);if(!a.data(n)){a.data(n,{idx:++s,opt:t,scrollRatio:{y:null,x:null},overflowed:null,contentReset:{y:null,x:null},bindEvents:!1,tweenRunning:!1,sequential:{},langDir:a.css("direction"),cbOffsets:null,trigger:null,poll:{size:{o:0,n:0},img:{o:0,n:0},change:{o:0,n:0}}});var i=a.data(n),o=i.opt,l=a.data("mcs-axis"),r=a.data("mcs-scrollbar-position"),c=a.data("mcs-theme");l&&(o.axis=l),r&&(o.scrollbarPosition=r),c&&(o.theme=c,p(o)),g.call(this),i&&o.callbacks.onCreate&&"function"==typeof o.callbacks.onCreate&&o.callbacks.onCreate.call(this),e("#mCSB_"+i.idx+"_container img:not(."+d[2]+")").addClass(d[2]),u.update.call(null,a)}})},update:function(t,a){var i=t||h.call(this);return e(i).each(function(){var t=e(this);if(t.data(n)){var i=t.data(n),o=i.opt,s=e("#mCSB_"+i.idx+"_container"),l=e("#mCSB_"+i.idx),r=[e("#mCSB_"+i.idx+"_dragger_vertical"),e("#mCSB_"+i.idx+"_dragger_horizontal")];if(!s.length)return;i.tweenRunning&&X(t),a&&i&&o.callbacks.onBeforeUpdate&&"function"==typeof o.callbacks.onBeforeUpdate&&o.callbacks.onBeforeUpdate.call(this),t.hasClass(d[3])&&t.removeClass(d[3]),t.hasClass(d[4])&&t.removeClass(d[4]),l.css("max-height","none"),l.height()!==t.height()&&l.css("max-height",t.height()),_.call(this),"y"===o.axis||o.advanced.autoExpandHorizontalScroll||s.css("width",b(s)),i.overflowed=S.call(this),I.call(this),o.autoDraggerLength&&C.call(this),x.call(this),k.call(this);var c=[Math.abs(s[0].offsetTop),Math.abs(s[0].offsetLeft)];"x"!==o.axis&&(i.overflowed[0]?r[0].height()>r[0].parent().height()?$.call(this):(Z(t,c[0].toString(),{dir:"y",dur:0,overwrite:"none"}),i.contentReset.y=null):($.call(this),"y"===o.axis?T.call(this):"yx"===o.axis&&i.overflowed[1]&&Z(t,c[1].toString(),{dir:"x",dur:0,overwrite:"none"}))),"y"!==o.axis&&(i.overflowed[1]?r[1].width()>r[1].parent().width()?$.call(this):(Z(t,c[1].toString(),{dir:"x",dur:0,overwrite:"none"}),i.contentReset.x=null):($.call(this),"x"===o.axis?T.call(this):"yx"===o.axis&&i.overflowed[0]&&Z(t,c[0].toString(),{dir:"y",dur:0,overwrite:"none"}))),a&&i&&(2===a&&o.callbacks.onImageLoad&&"function"==typeof o.callbacks.onImageLoad?o.callbacks.onImageLoad.call(this):3===a&&o.callbacks.onSelectorChange&&"function"==typeof o.callbacks.onSelectorChange?o.callbacks.onSelectorChange.call(this):o.callbacks.onUpdate&&"function"==typeof o.callbacks.onUpdate&&o.callbacks.onUpdate.call(this)),Y.call(this)}})},scrollTo:function(t,a){if(void 0!==t&&null!=t){var i=h.call(this);return e(i).each(function(){var i=e(this);if(i.data(n)){var o=i.data(n),s=o.opt,l={trigger:"external",scrollInertia:s.scrollInertia,scrollEasing:"mcsEaseInOut",moveDragger:!1,timeout:60,callbacks:!0,onStart:!0,onUpdate:!0,onComplete:!0},r=e.extend(!0,{},l,a),c=j.call(this,t),d=r.scrollInertia>0&&r.scrollInertia<17?17:r.scrollInertia;c[0]=U.call(this,c[0],"y"),c[1]=U.call(this,c[1],"x"),r.moveDragger&&(c[0]*=o.scrollRatio.y,c[1]*=o.scrollRatio.x),r.dur=ne()?0:d,setTimeout(function(){null!==c[0]&&void 0!==c[0]&&"x"!==s.axis&&o.overflowed[0]&&(r.dir="y",r.overwrite="all",Z(i,c[0].toString(),r)),null!==c[1]&&void 0!==c[1]&&"y"!==s.axis&&o.overflowed[1]&&(r.dir="x",r.overwrite="none",Z(i,c[1].toString(),r))},r.timeout)}})}},stop:function(){var t=h.call(this);return e(t).each(function(){var t=e(this);t.data(n)&&X(t)})},disable:function(t){var a=h.call(this);return e(a).each(function(){var a=e(this);a.data(n)&&(a.data(n),Y.call(this,"remove"),T.call(this),t&&$.call(this),I.call(this,!0),a.addClass(d[3]))})},destroy:function(){var t=h.call(this);return e(t).each(function(){var i=e(this);if(i.data(n)){var o=i.data(n),s=o.opt,l=e("#mCSB_"+o.idx),r=e("#mCSB_"+o.idx+"_container"),c=e(".mCSB_"+o.idx+"_scrollbar");s.live&&f(s.liveSelector||e(t).selector),Y.call(this,"remove"),T.call(this),$.call(this),i.removeData(n),V(this,"mcs"),c.remove(),r.find("img."+d[2]).removeClass(d[2]),l.replaceWith(r.contents()),i.removeClass(a+" _"+n+"_"+o.idx+" "+d[6]+" "+d[7]+" "+d[5]+" "+d[3]).addClass(d[4])}})}},h=function(){return"object"!=typeof e(this)||e(this).length<1?i:this},p=function(t){t.autoDraggerLength=!(e.inArray(t.theme,["rounded","rounded-dark","rounded-dots","rounded-dots-dark"])>-1)&&t.autoDraggerLength,t.autoExpandScrollbar=!(e.inArray(t.theme,["rounded-dots","rounded-dots-dark","3d","3d-dark","3d-thick","3d-thick-dark","inset","inset-dark","inset-2","inset-2-dark","inset-3","inset-3-dark"])>-1)&&t.autoExpandScrollbar,t.scrollButtons.enable=!(e.inArray(t.theme,["minimal","minimal-dark"])>-1)&&t.scrollButtons.enable,t.autoHideScrollbar=e.inArray(t.theme,["minimal","minimal-dark"])>-1||t.autoHideScrollbar,t.scrollbarPosition=e.inArray(t.theme,["minimal","minimal-dark"])>-1?"outside":t.scrollbarPosition},f=function(e){l[e]&&(clearTimeout(l[e]),V(l,e))},m=function(e){return"yx"===e||"xy"===e||"auto"===e?"yx":"x"===e||"horizontal"===e?"x":"y"},v=function(e){return"stepped"===e||"pixels"===e||"step"===e||"click"===e?"stepped":"stepless"},g=function(){var t=e(this),i=t.data(n),o=i.opt,s=o.autoExpandScrollbar?" "+d[1]+"_expand":"",l=["<div id='mCSB_"+i.idx+"_scrollbar_vertical' class='mCSB_scrollTools mCSB_"+i.idx+"_scrollbar mCS-"+o.theme+" mCSB_scrollTools_vertical"+s+"'><div class='"+d[12]+"'><div id='mCSB_"+i.idx+"_dragger_vertical' class='mCSB_dragger' style='position:absolute;'><div class='mCSB_dragger_bar' /></div><div class='mCSB_draggerRail' /></div></div>","<div id='mCSB_"+i.idx+"_scrollbar_horizontal' class='mCSB_scrollTools mCSB_"+i.idx+"_scrollbar mCS-"+o.theme+" mCSB_scrollTools_horizontal"+s+"'><div class='"+d[12]+"'><div id='mCSB_"+i.idx+"_dragger_horizontal' class='mCSB_dragger' style='position:absolute;'><div class='mCSB_dragger_bar' /></div><div class='mCSB_draggerRail' /></div></div>"],r="yx"===o.axis?"mCSB_vertical_horizontal":"x"===o.axis?"mCSB_horizontal":"mCSB_vertical",c="yx"===o.axis?l[0]+l[1]:"x"===o.axis?l[1]:l[0],u="yx"===o.axis?"<div id='mCSB_"+i.idx+"_container_wrapper' class='mCSB_container_wrapper' />":"",h=o.autoHideScrollbar?" "+d[6]:"",p="x"!==o.axis&&"rtl"===i.langDir?" "+d[7]:"";o.setWidth&&t.css("width",o.setWidth),o.setHeight&&t.css("height",o.setHeight),o.setLeft="y"!==o.axis&&"rtl"===i.langDir?"989999px":o.setLeft,t.addClass(a+" _"+n+"_"+i.idx+h+p).wrapInner("<div id='mCSB_"+i.idx+"' class='mCustomScrollBox mCS-"+o.theme+" "+r+"'><div id='mCSB_"+i.idx+"_container' class='mCSB_container' style='position:relative; top:"+o.setTop+"; left:"+o.setLeft+";' dir='"+i.langDir+"' /></div>");var f=e("#mCSB_"+i.idx),m=e("#mCSB_"+i.idx+"_container");"y"===o.axis||o.advanced.autoExpandHorizontalScroll||m.css("width",b(m)),"outside"===o.scrollbarPosition?("static"===t.css("position")&&t.css("position","relative"),t.css("overflow","visible"),f.addClass("mCSB_outside").after(c)):(f.addClass("mCSB_inside").append(c),m.wrap(u)),w.call(this);var v=[e("#mCSB_"+i.idx+"_dragger_vertical"),e("#mCSB_"+i.idx+"_dragger_horizontal")];v[0].css("min-height",v[0].height()),v[1].css("min-width",v[1].width())},b=function(t){var a=[t[0].scrollWidth,Math.max.apply(Math,t.children().map(function(){return e(this).outerWidth(!0)}).get())],n=t.parent().width();return a[0]>n?a[0]:a[1]>n?a[1]:"100%"},_=function(){var t=e(this),a=t.data(n),i=a.opt,o=e("#mCSB_"+a.idx+"_container");if(i.advanced.autoExpandHorizontalScroll&&"y"!==i.axis){o.css({width:"auto","min-width":0,"overflow-x":"scroll"});var s=Math.ceil(o[0].scrollWidth);3===i.advanced.autoExpandHorizontalScroll||2!==i.advanced.autoExpandHorizontalScroll&&s>o.parent().width()?o.css({width:s,"min-width":"100%","overflow-x":"inherit"}):o.css({"overflow-x":"inherit",position:"absolute"}).wrap("<div class='mCSB_h_wrapper' style='position:relative; left:0; width:999999px;' />").css({width:Math.ceil(o[0].getBoundingClientRect().right+.4)-Math.floor(o[0].getBoundingClientRect().left),"min-width":"100%",position:"relative"}).unwrap()}},w=function(){var t=e(this),a=t.data(n),i=a.opt,o=e(".mCSB_"+a.idx+"_scrollbar:first"),s=te(i.scrollButtons.tabindex)?"tabindex='"+i.scrollButtons.tabindex+"'":"",l=["<a href='#' class='"+d[13]+"' "+s+" />","<a href='#' class='"+d[14]+"' "+s+" />","<a href='#' class='"+d[15]+"' "+s+" />","<a href='#' class='"+d[16]+"' "+s+" />"],r=["x"===i.axis?l[2]:l[0],"x"===i.axis?l[3]:l[1],l[2],l[3]];i.scrollButtons.enable&&o.prepend(r[0]).append(r[1]).next(".mCSB_scrollTools").prepend(r[2]).append(r[3])},C=function(){var t=e(this),a=t.data(n),i=e("#mCSB_"+a.idx),o=e("#mCSB_"+a.idx+"_container"),s=[e("#mCSB_"+a.idx+"_dragger_vertical"),e("#mCSB_"+a.idx+"_dragger_horizontal")],l=[i.height()/o.outerHeight(!1),i.width()/o.outerWidth(!1)],c=[parseInt(s[0].css("min-height")),Math.round(l[0]*s[0].parent().height()),parseInt(s[1].css("min-width")),Math.round(l[1]*s[1].parent().width())],d=r&&c[1]<c[0]?c[0]:c[1],u=r&&c[3]<c[2]?c[2]:c[3];s[0].css({height:d,"max-height":s[0].parent().height()-10}).find(".mCSB_dragger_bar").css({"line-height":c[0]+"px"}),s[1].css({width:u,"max-width":s[1].parent().width()-10})},x=function(){var t=e(this),a=t.data(n),i=e("#mCSB_"+a.idx),o=e("#mCSB_"+a.idx+"_container"),s=[e("#mCSB_"+a.idx+"_dragger_vertical"),e("#mCSB_"+a.idx+"_dragger_horizontal")],l=[o.outerHeight(!1)-i.height(),o.outerWidth(!1)-i.width()],r=[l[0]/(s[0].parent().height()-s[0].height()),l[1]/(s[1].parent().width()-s[1].width())];a.scrollRatio={y:r[0],x:r[1]}},y=function(e,t,a){var n=a?d[0]+"_expanded":"",i=e.closest(".mCSB_scrollTools");"active"===t?(e.toggleClass(d[0]+" "+n),i.toggleClass(d[1]),e[0]._draggable=e[0]._draggable?0:1):e[0]._draggable||("hide"===t?(e.removeClass(d[0]),i.removeClass(d[1])):(e.addClass(d[0]),i.addClass(d[1])))},S=function(){var t=e(this),a=t.data(n),i=e("#mCSB_"+a.idx),o=e("#mCSB_"+a.idx+"_container"),s=null==a.overflowed?o.height():o.outerHeight(!1),l=null==a.overflowed?o.width():o.outerWidth(!1),r=o[0].scrollHeight,c=o[0].scrollWidth;return r>s&&(s=r),c>l&&(l=c),[s>i.height(),l>i.width()]},$=function(){var t=e(this),a=t.data(n),i=a.opt,o=e("#mCSB_"+a.idx),s=e("#mCSB_"+a.idx+"_container"),l=[e("#mCSB_"+a.idx+"_dragger_vertical"),e("#mCSB_"+a.idx+"_dragger_horizontal")];if(X(t),("x"!==i.axis&&!a.overflowed[0]||"y"===i.axis&&a.overflowed[0])&&(l[0].add(s).css("top",0),Z(t,"_resetY")),"y"!==i.axis&&!a.overflowed[1]||"x"===i.axis&&a.overflowed[1]){var r=dx=0;"rtl"===a.langDir&&(r=o.width()-s.outerWidth(!1),dx=Math.abs(r/a.scrollRatio.x)),s.css("left",r),l[1].css("left",dx),Z(t,"_resetX")}},k=function(){var t,a=e(this),i=a.data(n),o=i.opt;i.bindEvents||(D.call(this),o.contentTouchScroll&&E.call(this),A.call(this),o.mouseWheel.enable&&function n(){t=setTimeout(function(){e.event.special.mousewheel?(clearTimeout(t),O.call(a[0])):n()},100)}(),N.call(this),W.call(this),o.advanced.autoScrollOnFocus&&H.call(this),o.scrollButtons.enable&&R.call(this),o.keyboard.enable&&q.call(this),i.bindEvents=!0)},T=function(){var t=e(this),a=t.data(n),i=a.opt,o=n+"_"+a.idx,s=".mCSB_"+a.idx+"_scrollbar",l=e("#mCSB_"+a.idx+",#mCSB_"+a.idx+"_container,#mCSB_"+a.idx+"_container_wrapper,"+s+" ."+d[12]+",#mCSB_"+a.idx+"_dragger_vertical,#mCSB_"+a.idx+"_dragger_horizontal,"+s+">a"),r=e("#mCSB_"+a.idx+"_container");i.advanced.releaseDraggableSelectors&&l.add(e(i.advanced.releaseDraggableSelectors)),i.advanced.extraDraggableSelectors&&l.add(e(i.advanced.extraDraggableSelectors)),a.bindEvents&&(e(document).add(e(!P()||top.document)).unbind("."+o),l.each(function(){e(this).unbind("."+o)}),clearTimeout(t[0]._focusTimeout),V(t[0],"_focusTimeout"),clearTimeout(a.sequential.step),V(a.sequential,"step"),clearTimeout(r[0].onCompleteTimeout),V(r[0],"onCompleteTimeout"),a.bindEvents=!1)},I=function(t){var a=e(this),i=a.data(n),o=i.opt,s=e("#mCSB_"+i.idx+"_container_wrapper"),l=s.length?s:e("#mCSB_"+i.idx+"_container"),r=[e("#mCSB_"+i.idx+"_scrollbar_vertical"),e("#mCSB_"+i.idx+"_scrollbar_horizontal")],c=[r[0].find(".mCSB_dragger"),r[1].find(".mCSB_dragger")];"x"!==o.axis&&(i.overflowed[0]&&!t?(r[0].add(c[0]).add(r[0].children("a")).css("display","block"),l.removeClass(d[8]+" "+d[10])):(o.alwaysShowScrollbar?(2!==o.alwaysShowScrollbar&&c[0].css("display","none"),l.removeClass(d[10])):(r[0].css("display","none"),l.addClass(d[10])),l.addClass(d[8]))),"y"!==o.axis&&(i.overflowed[1]&&!t?(r[1].add(c[1]).add(r[1].children("a")).css("display","block"),l.removeClass(d[9]+" "+d[11])):(o.alwaysShowScrollbar?(2!==o.alwaysShowScrollbar&&c[1].css("display","none"),l.removeClass(d[11])):(r[1].css("display","none"),l.addClass(d[11])),l.addClass(d[9]))),i.overflowed[0]||i.overflowed[1]?a.removeClass(d[5]):a.addClass(d[5])},M=function(t){var a=t.type,n=t.target.ownerDocument!==document&&null!==frameElement?[e(frameElement).offset().top,e(frameElement).offset().left]:null,i=P()&&t.target.ownerDocument!==top.document&&null!==frameElement?[e(t.view.frameElement).offset().top,e(t.view.frameElement).offset().left]:[0,0];switch(a){case"pointerdown":case"MSPointerDown":case"pointermove":case"MSPointerMove":case"pointerup":case"MSPointerUp":return n?[t.originalEvent.pageY-n[0]+i[0],t.originalEvent.pageX-n[1]+i[1],!1]:[t.originalEvent.pageY,t.originalEvent.pageX,!1];case"touchstart":case"touchmove":case"touchend":var o=t.originalEvent.touches[0]||t.originalEvent.changedTouches[0],s=t.originalEvent.touches.length||t.originalEvent.changedTouches.length;return t.target.ownerDocument!==document?[o.screenY,o.screenX,s>1]:[o.pageY,o.pageX,s>1];default:return n?[t.pageY-n[0]+i[0],t.pageX-n[1]+i[1],!1]:[t.pageY,t.pageX,!1]}},D=function(){function t(e,t,n,i){if(p[0].idleTimer=d.scrollInertia<233?250:0,a.attr("id")===h[1])var o="x",r=(a[0].offsetLeft-t+i)*l.scrollRatio.x;else var o="y",r=(a[0].offsetTop-e+n)*l.scrollRatio.y;Z(s,r.toString(),{dir:o,drag:!0})}var a,i,o,s=e(this),l=s.data(n),d=l.opt,u=n+"_"+l.idx,h=["mCSB_"+l.idx+"_dragger_vertical","mCSB_"+l.idx+"_dragger_horizontal"],p=e("#mCSB_"+l.idx+"_container"),f=e("#"+h[0]+",#"+h[1]),m=d.advanced.releaseDraggableSelectors?f.add(e(d.advanced.releaseDraggableSelectors)):f,v=d.advanced.extraDraggableSelectors?e(!P()||top.document).add(e(d.advanced.extraDraggableSelectors)):e(!P()||top.document);f.bind("contextmenu."+u,function(e){e.preventDefault()}).bind("mousedown."+u+" touchstart."+u+" pointerdown."+u+" MSPointerDown."+u,function(t){if(t.stopImmediatePropagation(),t.preventDefault(),J(t)){c=!0,r&&(document.onselectstart=function(){return!1}),L.call(p,!1),X(s);var n=(a=e(this)).offset(),l=M(t)[0]-n.top,u=M(t)[1]-n.left,h=a.height()+n.top,f=a.width()+n.left;h>l&&l>0&&f>u&&u>0&&(i=l,o=u),y(a,"active",d.autoExpandScrollbar)}}).bind("touchmove."+u,function(e){e.stopImmediatePropagation(),e.preventDefault();var n=a.offset(),s=M(e)[0]-n.top,l=M(e)[1]-n.left;t(i,o,s,l)}),e(document).add(v).bind("mousemove."+u+" pointermove."+u+" MSPointerMove."+u,function(e){if(a){var n=a.offset(),s=M(e)[0]-n.top,l=M(e)[1]-n.left;if(i===s&&o===l)return;t(i,o,s,l)}}).add(m).bind("mouseup."+u+" touchend."+u+" pointerup."+u+" MSPointerUp."+u,function(){a&&(y(a,"active",d.autoExpandScrollbar),a=null),c=!1,r&&(document.onselectstart=null),L.call(p,!0)})},E=function(){function a(e){if(!ee(e)||c||M(e)[2])t=0;else{t=1,x=0,y=0,d=1,S.removeClass("mCS_touch_action");var a=D.offset();u=M(e)[0]-a.top,h=M(e)[1]-a.left,z=[M(e)[0],M(e)[1]]}}function i(e){if(ee(e)&&!c&&!M(e)[2]&&(k.documentTouchScroll||e.preventDefault(),e.stopImmediatePropagation(),(!y||x)&&d)){v=K();var t=I.offset(),a=M(e)[0]-t.top,n=M(e)[1]-t.left,i="mcsLinearOut";if(A.push(a),O.push(n),z[2]=Math.abs(M(e)[0]-z[0]),z[3]=Math.abs(M(e)[1]-z[1]),$.overflowed[0])var o=E[0].parent().height()-E[0].height(),s=u-a>0&&a-u>-o*$.scrollRatio.y&&(2*z[3]<z[2]||"yx"===k.axis);if($.overflowed[1])var l=E[1].parent().width()-E[1].width(),p=h-n>0&&n-h>-l*$.scrollRatio.x&&(2*z[2]<z[3]||"yx"===k.axis);s||p?(W||e.preventDefault(),x=1):(y=1,S.addClass("mCS_touch_action")),W&&e.preventDefault(),w="yx"===k.axis?[u-a,h-n]:"x"===k.axis?[null,h-n]:[u-a,null],D[0].idleTimer=250,$.overflowed[0]&&r(w[0],B,i,"y","all",!0),$.overflowed[1]&&r(w[1],B,i,"x",L,!0)}}function o(e){if(!ee(e)||c||M(e)[2])t=0;else{t=1,e.stopImmediatePropagation(),X(S),m=K();var a=I.offset();p=M(e)[0]-a.top,f=M(e)[1]-a.left,A=[],O=[]}}function s(e){if(ee(e)&&!c&&!M(e)[2]){d=0,e.stopImmediatePropagation(),x=0,y=0,g=K();var t=I.offset(),a=M(e)[0]-t.top,n=M(e)[1]-t.left;if(!(g-v>30)){var i="mcsEaseOut",o=2.5>(_=1e3/(g-m)),s=o?[A[A.length-2],O[O.length-2]]:[0,0];b=o?[a-s[0],n-s[1]]:[a-p,n-f];var u=[Math.abs(b[0]),Math.abs(b[1])];_=o?[Math.abs(b[0]/4),Math.abs(b[1]/4)]:[_,_];var h=[Math.abs(D[0].offsetTop)-b[0]*l(u[0]/_[0],_[0]),Math.abs(D[0].offsetLeft)-b[1]*l(u[1]/_[1],_[1])];w="yx"===k.axis?[h[0],h[1]]:"x"===k.axis?[null,h[1]]:[h[0],null],C=[4*u[0]+k.scrollInertia,4*u[1]+k.scrollInertia];var S=parseInt(k.contentTouchScroll)||0;w[0]=u[0]>S?w[0]:0,w[1]=u[1]>S?w[1]:0,$.overflowed[0]&&r(w[0],C[0],i,"y",L,!1),$.overflowed[1]&&r(w[1],C[1],i,"x",L,!1)}}}function l(e,t){var a=[1.5*t,2*t,t/1.5,t/2];return e>90?t>4?a[0]:a[3]:e>60?t>3?a[3]:a[2]:e>30?t>8?a[1]:t>6?a[0]:t>4?t:a[2]:t>8?t:a[3]}function r(e,t,a,n,i,o){e&&Z(S,e.toString(),{dur:t,scrollEasing:a,dir:n,overwrite:i,drag:o})}var d,u,h,p,f,m,v,g,b,_,w,C,x,y,S=e(this),$=S.data(n),k=$.opt,T=n+"_"+$.idx,I=e("#mCSB_"+$.idx),D=e("#mCSB_"+$.idx+"_container"),E=[e("#mCSB_"+$.idx+"_dragger_vertical"),e("#mCSB_"+$.idx+"_dragger_horizontal")],A=[],O=[],B=0,L="yx"===k.axis?"none":"all",z=[],N=D.find("iframe"),H=["touchstart."+T+" pointerdown."+T+" MSPointerDown."+T,"touchmove."+T+" pointermove."+T+" MSPointerMove."+T,"touchend."+T+" pointerup."+T+" MSPointerUp."+T],W=void 0!==document.body.style.touchAction&&""!==document.body.style.touchAction;D.bind(H[0],function(e){a(e)}).bind(H[1],function(e){i(e)}),I.bind(H[0],function(e){o(e)}).bind(H[2],function(e){s(e)}),N.length&&N.each(function(){e(this).bind("load",function(){P(this)&&e(this.contentDocument||this.contentWindow.document).bind(H[0],function(e){a(e),o(e)}).bind(H[1],function(e){i(e)}).bind(H[2],function(e){s(e)})})})},A=function(){function a(e,t,a){r.type=a&&i?"stepped":"stepless",r.scrollAmount=10,F(o,e,t,"mcsLinearOut",a?60:null)}var i,o=e(this),s=o.data(n),l=s.opt,r=s.sequential,d=n+"_"+s.idx,u=e("#mCSB_"+s.idx+"_container"),h=u.parent();u.bind("mousedown."+d,function(){t||i||(i=1,c=!0)}).add(document).bind("mousemove."+d,function(e){if(!t&&i&&(window.getSelection?window.getSelection().toString():document.selection&&"Control"!=document.selection.type&&document.selection.createRange().text)){var n=u.offset(),o=M(e)[0]-n.top+u[0].offsetTop,c=M(e)[1]-n.left+u[0].offsetLeft;o>0&&o<h.height()&&c>0&&c<h.width()?r.step&&a("off",null,"stepped"):("x"!==l.axis&&s.overflowed[0]&&(0>o?a("on",38):o>h.height()&&a("on",40)),"y"!==l.axis&&s.overflowed[1]&&(0>c?a("on",37):c>h.width()&&a("on",39)))}}).bind("mouseup."+d+" dragend."+d,function(){t||(i&&(i=0,a("off",null)),c=!1)})},O=function(){function t(t,n){if(X(a),!z(a,t.target)){var s="auto"!==o.mouseWheel.deltaFactor?parseInt(o.mouseWheel.deltaFactor):r&&t.deltaFactor<100?100:t.deltaFactor||100,d=o.scrollInertia;if("x"===o.axis||"x"===o.mouseWheel.axis)var u="x",h=[Math.round(s*i.scrollRatio.x),parseInt(o.mouseWheel.scrollAmount)],p="auto"!==o.mouseWheel.scrollAmount?h[1]:h[0]>=l.width()?.9*l.width():h[0],f=Math.abs(e("#mCSB_"+i.idx+"_container")[0].offsetLeft),m=c[1][0].offsetLeft,v=c[1].parent().width()-c[1].width(),g="y"===o.mouseWheel.axis?t.deltaY||n:t.deltaX;else var u="y",h=[Math.round(s*i.scrollRatio.y),parseInt(o.mouseWheel.scrollAmount)],p="auto"!==o.mouseWheel.scrollAmount?h[1]:h[0]>=l.height()?.9*l.height():h[0],f=Math.abs(e("#mCSB_"+i.idx+"_container")[0].offsetTop),m=c[0][0].offsetTop,v=c[0].parent().height()-c[0].height(),g=t.deltaY||n;"y"===u&&!i.overflowed[0]||"x"===u&&!i.overflowed[1]||((o.mouseWheel.invert||t.webkitDirectionInvertedFromDevice)&&(g=-g),o.mouseWheel.normalizeDelta&&(g=0>g?-1:1),(g>0&&0!==m||0>g&&m!==v||o.mouseWheel.preventDefault)&&(t.stopImmediatePropagation(),t.preventDefault()),t.deltaFactor<5&&!o.mouseWheel.normalizeDelta&&(p=t.deltaFactor,d=17),Z(a,(f-g*p).toString(),{dir:u,dur:d}))}}if(e(this).data(n)){var a=e(this),i=a.data(n),o=i.opt,s=n+"_"+i.idx,l=e("#mCSB_"+i.idx),c=[e("#mCSB_"+i.idx+"_dragger_vertical"),e("#mCSB_"+i.idx+"_dragger_horizontal")],d=e("#mCSB_"+i.idx+"_container").find("iframe");d.length&&d.each(function(){e(this).bind("load",function(){P(this)&&e(this.contentDocument||this.contentWindow.document).bind("mousewheel."+s,function(e,a){t(e,a)})})}),l.bind("mousewheel."+s,function(e,a){t(e,a)})}},B=new Object,P=function(t){var a=!1,n=!1,i=null;if(void 0===t?n="#empty":void 0!==e(t).attr("id")&&(n=e(t).attr("id")),!1!==n&&void 0!==B[n])return B[n];if(t){try{var o=t.contentDocument||t.contentWindow.document;i=o.body.innerHTML}catch(e){}a=null!==i}else{try{var o=top.document;i=o.body.innerHTML}catch(e){}a=null!==i}return!1!==n&&(B[n]=a),a},L=function(e){var t=this.find("iframe");if(t.length){var a=e?"auto":"none";t.css("pointer-events",a)}},z=function(t,a){var i=a.nodeName.toLowerCase(),o=t.data(n).opt.mouseWheel.disableOver;return e.inArray(i,o)>-1&&!(e.inArray(i,["select","textarea"])>-1&&!e(a).is(":focus"))},N=function(){var t,a=e(this),i=a.data(n),o=n+"_"+i.idx,s=e("#mCSB_"+i.idx+"_container"),l=s.parent(),r=e(".mCSB_"+i.idx+"_scrollbar ."+d[12]);r.bind("mousedown."+o+" touchstart."+o+" pointerdown."+o+" MSPointerDown."+o,function(a){c=!0,e(a.target).hasClass("mCSB_dragger")||(t=1)}).bind("touchend."+o+" pointerup."+o+" MSPointerUp."+o,function(){c=!1}).bind("click."+o,function(n){if(t&&(t=0,e(n.target).hasClass(d[12])||e(n.target).hasClass("mCSB_draggerRail"))){X(a);var o=e(this),r=o.find(".mCSB_dragger");if(o.parent(".mCSB_scrollTools_horizontal").length>0){if(!i.overflowed[1])return;var c="x",u=n.pageX>r.offset().left?-1:1,h=Math.abs(s[0].offsetLeft)-u*(.9*l.width())}else{if(!i.overflowed[0])return;var c="y",u=n.pageY>r.offset().top?-1:1,h=Math.abs(s[0].offsetTop)-u*(.9*l.height())}Z(a,h.toString(),{dir:c,scrollEasing:"mcsEaseInOut"})}})},H=function(){var t=e(this),a=t.data(n),i=a.opt,o=n+"_"+a.idx,s=e("#mCSB_"+a.idx+"_container"),l=s.parent();s.bind("focusin."+o,function(){var a=e(document.activeElement),n=s.find(".mCustomScrollBox").length;a.is(i.advanced.autoScrollOnFocus)&&(X(t),clearTimeout(t[0]._focusTimeout),t[0]._focusTimer=n?17*n:0,t[0]._focusTimeout=setTimeout(function(){var e=[ae(a)[0],ae(a)[1]],n=[s[0].offsetTop,s[0].offsetLeft],o=[n[0]+e[0]>=0&&n[0]+e[0]<l.height()-a.outerHeight(!1),n[1]+e[1]>=0&&n[0]+e[1]<l.width()-a.outerWidth(!1)],r="yx"!==i.axis||o[0]||o[1]?"all":"none";"x"===i.axis||o[0]||Z(t,e[0].toString(),{dir:"y",scrollEasing:"mcsEaseInOut",overwrite:r,dur:0}),"y"===i.axis||o[1]||Z(t,e[1].toString(),{dir:"x",scrollEasing:"mcsEaseInOut",overwrite:r,dur:0})},t[0]._focusTimer))})},W=function(){var t=e(this),a=t.data(n),i=n+"_"+a.idx,o=e("#mCSB_"+a.idx+"_container").parent();o.bind("scroll."+i,function(){0===o.scrollTop()&&0===o.scrollLeft()||e(".mCSB_"+a.idx+"_scrollbar").css("visibility","hidden")})},R=function(){var t=e(this),a=t.data(n),i=a.opt,o=a.sequential,s=n+"_"+a.idx,l=".mCSB_"+a.idx+"_scrollbar",r=e(l+">a");r.bind("contextmenu."+s,function(e){e.preventDefault()}).bind("mousedown."+s+" touchstart."+s+" pointerdown."+s+" MSPointerDown."+s+" mouseup."+s+" touchend."+s+" pointerup."+s+" MSPointerUp."+s+" mouseout."+s+" pointerout."+s+" MSPointerOut."+s+" click."+s,function(n){function s(e,a){o.scrollAmount=i.scrollButtons.scrollAmount,F(t,e,a)}if(n.preventDefault(),J(n)){var l=e(this).attr("class");switch(o.type=i.scrollButtons.scrollType,n.type){case"mousedown":case"touchstart":case"pointerdown":case"MSPointerDown":if("stepped"===o.type)return;c=!0,a.tweenRunning=!1,s("on",l);break;case"mouseup":case"touchend":case"pointerup":case"MSPointerUp":case"mouseout":case"pointerout":case"MSPointerOut":if("stepped"===o.type)return;c=!1,o.dir&&s("off",l);break;case"click":if("stepped"!==o.type||a.tweenRunning)return;s("on",l)}}})},q=function(){function t(t){function n(e,t){s.type=o.keyboard.scrollType,s.scrollAmount=o.keyboard.scrollAmount,"stepped"===s.type&&i.tweenRunning||F(a,e,t)}switch(t.type){case"blur":i.tweenRunning&&s.dir&&n("off",null);break;case"keydown":case"keyup":var l=t.keyCode?t.keyCode:t.which,r="on";if("x"!==o.axis&&(38===l||40===l)||"y"!==o.axis&&(37===l||39===l)){if((38===l||40===l)&&!i.overflowed[0]||(37===l||39===l)&&!i.overflowed[1])return;"keyup"===t.type&&(r="off"),e(document.activeElement).is(u)||(t.preventDefault(),t.stopImmediatePropagation(),n(r,l))}else if(33===l||34===l){if((i.overflowed[0]||i.overflowed[1])&&(t.preventDefault(),t.stopImmediatePropagation()),"keyup"===t.type){X(a);var h=34===l?-1:1;if("x"===o.axis||"yx"===o.axis&&i.overflowed[1]&&!i.overflowed[0])var p="x",f=Math.abs(c[0].offsetLeft)-h*(.9*d.width());else var p="y",f=Math.abs(c[0].offsetTop)-h*(.9*d.height());Z(a,f.toString(),{dir:p,scrollEasing:"mcsEaseInOut"})}}else if((35===l||36===l)&&!e(document.activeElement).is(u)&&((i.overflowed[0]||i.overflowed[1])&&(t.preventDefault(),t.stopImmediatePropagation()),"keyup"===t.type)){if("x"===o.axis||"yx"===o.axis&&i.overflowed[1]&&!i.overflowed[0])var p="x",f=35===l?Math.abs(d.width()-c.outerWidth(!1)):0;else var p="y",f=35===l?Math.abs(d.height()-c.outerHeight(!1)):0;Z(a,f.toString(),{dir:p,scrollEasing:"mcsEaseInOut"})}}}var a=e(this),i=a.data(n),o=i.opt,s=i.sequential,l=n+"_"+i.idx,r=e("#mCSB_"+i.idx),c=e("#mCSB_"+i.idx+"_container"),d=c.parent(),u="input,textarea,select,datalist,keygen,[contenteditable='true']",h=c.find("iframe"),p=["blur."+l+" keydown."+l+" keyup."+l];h.length&&h.each(function(){e(this).bind("load",function(){P(this)&&e(this.contentDocument||this.contentWindow.document).bind(p[0],function(e){t(e)})})}),r.attr("tabindex","0").bind(p[0],function(e){t(e)})},F=function(t,a,i,o,s){function l(e){c.snapAmount&&(u.scrollAmount=c.snapAmount instanceof Array?"x"===u.dir[0]?c.snapAmount[1]:c.snapAmount[0]:c.snapAmount);var a="stepped"!==u.type,n=s||(e?a?f/1.5:m:1e3/60),i=e?a?7.5:40:2.5,d=[Math.abs(h[0].offsetTop),Math.abs(h[0].offsetLeft)],p=[r.scrollRatio.y>10?10:r.scrollRatio.y,r.scrollRatio.x>10?10:r.scrollRatio.x],v="x"===u.dir[0]?d[1]+u.dir[1]*(p[1]*i):d[0]+u.dir[1]*(p[0]*i),g="x"===u.dir[0]?d[1]+u.dir[1]*parseInt(u.scrollAmount):d[0]+u.dir[1]*parseInt(u.scrollAmount),b="auto"!==u.scrollAmount?g:v,_=o||(e?a?"mcsLinearOut":"mcsEaseInOut":"mcsLinear"),w=!!e;return e&&17>n&&(b="x"===u.dir[0]?d[1]:d[0]),Z(t,b.toString(),{dir:u.dir[0],scrollEasing:_,dur:n,onComplete:w}),e?void(u.dir=!1):(clearTimeout(u.step),void(u.step=setTimeout(function(){l()},n)))}var r=t.data(n),c=r.opt,u=r.sequential,h=e("#mCSB_"+r.idx+"_container"),p="stepped"===u.type,f=c.scrollInertia<26?26:c.scrollInertia,m=c.scrollInertia<1?17:c.scrollInertia;switch(a){case"on":if(u.dir=[i===d[16]||i===d[15]||39===i||37===i?"x":"y",i===d[13]||i===d[15]||38===i||37===i?-1:1],X(t),te(i)&&"stepped"===u.type)return;l(p);break;case"off":clearTimeout(u.step),V(u,"step"),X(t),(p||r.tweenRunning&&u.dir)&&l(!0)}},j=function(t){var a=e(this).data(n).opt,i=[];return"function"==typeof t&&(t=t()),t instanceof Array?i=t.length>1?[t[0],t[1]]:"x"===a.axis?[null,t[0]]:[t[0],null]:(i[0]=t.y?t.y:t.x||"x"===a.axis?null:t,i[1]=t.x?t.x:t.y||"y"===a.axis?null:t),"function"==typeof i[0]&&(i[0]=i[0]()),"function"==typeof i[1]&&(i[1]=i[1]()),i},U=function(t,a){if(null!=t&&void 0!==t){var i=e(this),o=i.data(n),s=o.opt,l=e("#mCSB_"+o.idx+"_container"),r=l.parent(),c=typeof t;a||(a="x"===s.axis?"x":"y");var d="x"===a?l.outerWidth(!1)-r.width():l.outerHeight(!1)-r.height(),h="x"===a?l[0].offsetLeft:l[0].offsetTop,p="x"===a?"left":"top";switch(c){case"function":return t();case"object":var f=t.jquery?t:e(t);if(!f.length)return;return"x"===a?ae(f)[1]:ae(f)[0];case"string":case"number":if(te(t))return Math.abs(t);if(-1!==t.indexOf("%"))return Math.abs(d*parseInt(t)/100);if(-1!==t.indexOf("-="))return Math.abs(h-parseInt(t.split("-=")[1]));if(-1!==t.indexOf("+=")){var m=h+parseInt(t.split("+=")[1]);return m>=0?0:Math.abs(m)}if(-1!==t.indexOf("px")&&te(t.split("px")[0]))return Math.abs(t.split("px")[0]);if("top"===t||"left"===t)return 0;if("bottom"===t)return Math.abs(r.height()-l.outerHeight(!1));if("right"===t)return Math.abs(r.width()-l.outerWidth(!1));if("first"===t||"last"===t){var f=l.find(":"+t);return"x"===a?ae(f)[1]:ae(f)[0]}return e(t).length?"x"===a?ae(e(t))[1]:ae(e(t))[0]:(l.css(p,t),void u.update.call(null,i[0]))}}},Y=function(t){function a(e){clearTimeout(l[0].autoUpdate),u.update.call(null,i[0],e)}var i=e(this),o=i.data(n),s=o.opt,l=e("#mCSB_"+o.idx+"_container");return t?(clearTimeout(l[0].autoUpdate),void V(l[0],"autoUpdate")):void function t(){return clearTimeout(l[0].autoUpdate),0===i.parents("html").length?void(i=null):void(l[0].autoUpdate=setTimeout(function(){return s.advanced.updateOnSelectorChange&&(o.poll.change.n=function(){!0===s.advanced.updateOnSelectorChange&&(s.advanced.updateOnSelectorChange="*");var e=0,t=l.find(s.advanced.updateOnSelectorChange);return s.advanced.updateOnSelectorChange&&t.length>0&&t.each(function(){e+=this.offsetHeight+this.offsetWidth}),e}(),o.poll.change.n!==o.poll.change.o)?(o.poll.change.o=o.poll.change.n,void a(3)):s.advanced.updateOnContentResize&&(o.poll.size.n=i[0].scrollHeight+i[0].scrollWidth+l[0].offsetHeight+i[0].offsetHeight+i[0].offsetWidth,o.poll.size.n!==o.poll.size.o)?(o.poll.size.o=o.poll.size.n,void a(1)):!s.advanced.updateOnImageLoad||"auto"===s.advanced.updateOnImageLoad&&"y"===s.axis||(o.poll.img.n=l.find("img").length,o.poll.img.n===o.poll.img.o)?void((s.advanced.updateOnSelectorChange||s.advanced.updateOnContentResize||s.advanced.updateOnImageLoad)&&t()):(o.poll.img.o=o.poll.img.n,void l.find("img").each(function(){!function(t){if(e(t).hasClass(d[2]))a();else{var n=new Image;n.onload=function(e,t){return function(){return t.apply(e,arguments)}}(n,function(){this.onload=null,e(t).addClass(d[2]),a(2)}),n.src=t.src}}(this)}))},s.advanced.autoUpdateTimeout))}()},X=function(t){var a=t.data(n),i=e("#mCSB_"+a.idx+"_container,#mCSB_"+a.idx+"_container_wrapper,#mCSB_"+a.idx+"_dragger_vertical,#mCSB_"+a.idx+"_dragger_horizontal");i.each(function(){Q.call(this)})},Z=function(t,a,i){function o(e){return l&&r.callbacks[e]&&"function"==typeof r.callbacks[e]}function s(){var e=[h[0].offsetTop,h[0].offsetLeft],a=[g[0].offsetTop,g[0].offsetLeft],n=[h.outerHeight(!1),h.outerWidth(!1)],o=[u.height(),u.width()];t[0].mcs={content:h,top:e[0],left:e[1],draggerTop:a[0],draggerLeft:a[1],topPct:Math.round(100*Math.abs(e[0])/(Math.abs(n[0])-o[0])),leftPct:Math.round(100*Math.abs(e[1])/(Math.abs(n[1])-o[1])),direction:i.dir}}var l=t.data(n),r=l.opt,c={trigger:"internal",dir:"y",scrollEasing:"mcsEaseOut",drag:!1,dur:r.scrollInertia,overwrite:"all",callbacks:!0,onStart:!0,onUpdate:!0,onComplete:!0},i=e.extend(c,i),d=[i.dur,i.drag?0:i.dur],u=e("#mCSB_"+l.idx),h=e("#mCSB_"+l.idx+"_container"),p=h.parent(),f=r.callbacks.onTotalScrollOffset?j.call(t,r.callbacks.onTotalScrollOffset):[0,0],m=r.callbacks.onTotalScrollBackOffset?j.call(t,r.callbacks.onTotalScrollBackOffset):[0,0];if(l.trigger=i.trigger,0===p.scrollTop()&&0===p.scrollLeft()||(e(".mCSB_"+l.idx+"_scrollbar").css("visibility","visible"),p.scrollTop(0).scrollLeft(0)),"_resetY"!==a||l.contentReset.y||(o("onOverflowYNone")&&r.callbacks.onOverflowYNone.call(t[0]),l.contentReset.y=1),"_resetX"!==a||l.contentReset.x||(o("onOverflowXNone")&&r.callbacks.onOverflowXNone.call(t[0]),l.contentReset.x=1),"_resetY"!==a&&"_resetX"!==a){if(!l.contentReset.y&&t[0].mcs||!l.overflowed[0]||(o("onOverflowY")&&r.callbacks.onOverflowY.call(t[0]),l.contentReset.x=null),!l.contentReset.x&&t[0].mcs||!l.overflowed[1]||(o("onOverflowX")&&r.callbacks.onOverflowX.call(t[0]),l.contentReset.x=null),r.snapAmount){var v=r.snapAmount instanceof Array?"x"===i.dir?r.snapAmount[1]:r.snapAmount[0]:r.snapAmount;a=function(e,t,a){return Math.round(e/t)*t-a}(a,v,r.snapOffset)}switch(i.dir){case"x":var g=e("#mCSB_"+l.idx+"_dragger_horizontal"),b="left",_=h[0].offsetLeft,w=[u.width()-h.outerWidth(!1),g.parent().width()-g.width()],C=[a,0===a?0:a/l.scrollRatio.x],x=f[1],S=m[1],$=x>0?x/l.scrollRatio.x:0,k=S>0?S/l.scrollRatio.x:0;break;case"y":var g=e("#mCSB_"+l.idx+"_dragger_vertical"),b="top",_=h[0].offsetTop,w=[u.height()-h.outerHeight(!1),g.parent().height()-g.height()],C=[a,0===a?0:a/l.scrollRatio.y],x=f[0],S=m[0],$=x>0?x/l.scrollRatio.y:0,k=S>0?S/l.scrollRatio.y:0}C[1]<0||0===C[0]&&0===C[1]?C=[0,0]:C[1]>=w[1]?C=[w[0],w[1]]:C[0]=-C[0],t[0].mcs||(s(),o("onInit")&&r.callbacks.onInit.call(t[0])),clearTimeout(h[0].onCompleteTimeout),G(g[0],b,Math.round(C[1]),d[1],i.scrollEasing),!l.tweenRunning&&(0===_&&C[0]>=0||_===w[0]&&C[0]<=w[0])||G(h[0],b,Math.round(C[0]),d[0],i.scrollEasing,i.overwrite,{onStart:function(){i.callbacks&&i.onStart&&!l.tweenRunning&&(o("onScrollStart")&&(s(),r.callbacks.onScrollStart.call(t[0])),l.tweenRunning=!0,y(g),l.cbOffsets=[r.callbacks.alwaysTriggerOffsets||_>=w[0]+x,r.callbacks.alwaysTriggerOffsets||-S>=_])},onUpdate:function(){i.callbacks&&i.onUpdate&&o("whileScrolling")&&(s(),r.callbacks.whileScrolling.call(t[0]))},onComplete:function(){if(i.callbacks&&i.onComplete){"yx"===r.axis&&clearTimeout(h[0].onCompleteTimeout);var e=h[0].idleTimer||0;h[0].onCompleteTimeout=setTimeout(function(){o("onScroll")&&(s(),r.callbacks.onScroll.call(t[0])),o("onTotalScroll")&&C[1]>=w[1]-$&&l.cbOffsets[0]&&(s(),r.callbacks.onTotalScroll.call(t[0])),o("onTotalScrollBack")&&C[1]<=k&&l.cbOffsets[1]&&(s(),r.callbacks.onTotalScrollBack.call(t[0])),l.tweenRunning=!1,h[0].idleTimer=0,y(g,"hide")},e)}}})}},G=function(e,t,a,n,i,o,s){function l(){b.stop||(m||u.call(),m=K()-f,r(),m>=b.time&&(b.time=m>b.time?m+c-(m-b.time):m+c-1,b.time<m+1&&(b.time=m+1)),b.time<n?b.id=d(l):p.call())}function r(){n>0?(b.currVal=function(e,t,a,n,i){switch(i){case"linear":case"mcsLinear":return a*e/n+t;case"mcsLinearOut":return e/=n,e--,a*Math.sqrt(1-e*e)+t;case"easeInOutSmooth":return 1>(e/=n/2)?a/2*e*e+t:-a/2*(--e*(e-2)-1)+t;case"easeInOutStrong":return 1>(e/=n/2)?a/2*Math.pow(2,10*(e-1))+t:(e--,a/2*(2-Math.pow(2,-10*e))+t);case"easeInOut":case"mcsEaseInOut":return 1>(e/=n/2)?a/2*e*e*e+t:a/2*((e-=2)*e*e+2)+t;case"easeOutSmooth":return e/=n,-a*(--e*e*e*e-1)+t;case"easeOutStrong":return a*(1-Math.pow(2,-10*e/n))+t;case"easeOut":case"mcsEaseOut":default:var o=(e/=n)*e,s=o*e;return t+a*(.499999999999997*s*o+-2.5*o*o+5.5*s+-6.5*o+4*e)}}(b.time,v,_,n,i),g[t]=Math.round(b.currVal)+"px"):g[t]=a+"px",h.call()}e._mTween||(e._mTween={top:{},left:{}});var c,d,s=s||{},u=s.onStart||function(){},h=s.onUpdate||function(){},p=s.onComplete||function(){},f=K(),m=0,v=e.offsetTop,g=e.style,b=e._mTween[t];"left"===t&&(v=e.offsetLeft);var _=a-v;b.stop=0,"none"!==o&&null!=b.id&&(window.requestAnimationFrame?window.cancelAnimationFrame(b.id):clearTimeout(b.id),b.id=null),c=1e3/60,b.time=m+c,d=window.requestAnimationFrame?window.requestAnimationFrame:function(e){return r(),setTimeout(e,.01)},b.id=d(l)},K=function(){return window.performance&&window.performance.now?window.performance.now():window.performance&&window.performance.webkitNow?window.performance.webkitNow():Date.now?Date.now():(new Date).getTime()},Q=function(){var e=this;e._mTween||(e._mTween={top:{},left:{}});for(var t=["top","left"],a=0;a<t.length;a++){var n=t[a];e._mTween[n].id&&(window.requestAnimationFrame?window.cancelAnimationFrame(e._mTween[n].id):clearTimeout(e._mTween[n].id),e._mTween[n].id=null,e._mTween[n].stop=1)}},V=function(e,t){try{delete e[t]}catch(a){e[t]=null}},J=function(e){return!(e.which&&1!==e.which)},ee=function(e){var t=e.originalEvent.pointerType;return!(t&&"touch"!==t&&2!==t)},te=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},ae=function(e){var t=e.parents(".mCSB_container");return[e.offset().top-t.offset().top,e.offset().left-t.offset().left]},ne=function(){var e=function(){var e=["webkit","moz","ms","o"];if("hidden"in document)return"hidden";for(var t=0;t<e.length;t++)if(e[t]+"Hidden"in document)return e[t]+"Hidden";return null}();return!!e&&document[e]};e.fn[a]=function(t){return u[t]?u[t].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof t&&t?void e.error("Method "+t+" does not exist"):u.init.apply(this,arguments)},e[a]=function(t){return u[t]?u[t].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof t&&t?void e.error("Method "+t+" does not exist"):u.init.apply(this,arguments)},e[a].defaults=o,window[a]=!0,e(window).bind("load",function(){e(i)[a](),e.extend(e.expr[":"],{mcsInView:e.expr[":"].mcsInView||function(t){var a,n,i=e(t),o=i.parents(".mCSB_container");if(o.length)return a=o.parent(),(n=[o[0].offsetTop,o[0].offsetLeft])[0]+ae(i)[0]>=0&&n[0]+ae(i)[0]<a.height()-i.outerHeight(!1)&&n[1]+ae(i)[1]>=0&&n[1]+ae(i)[1]<a.width()-i.outerWidth(!1)},mcsInSight:e.expr[":"].mcsInSight||function(t,a,n){var i,o,s,l,r=e(t),c=r.parents(".mCSB_container"),d="exact"===n[3]?[[1,0],[1,0]]:[[.9,.1],[.6,.4]];if(c.length)return i=[r.outerHeight(!1),r.outerWidth(!1)],s=[c[0].offsetTop+ae(r)[0],c[0].offsetLeft+ae(r)[1]],o=[c.parent()[0].offsetHeight,c.parent()[0].offsetWidth],s[0]-o[0]*(l=[i[0]<o[0]?d[0]:d[1],i[1]<o[1]?d[0]:d[1]])[0][0]<0&&s[0]+i[0]-o[0]*l[0][1]>=0&&s[1]-o[1]*l[1][0]<0&&s[1]+i[1]-o[1]*l[1][1]>=0},mcsOverflow:e.expr[":"].mcsOverflow||function(t){var a=e(t).data(n);if(a)return a.overflowed[0]||a.overflowed[1]}})})}()}),function(e){e.fn.scrollTabs=function(t){var a=function(a){t=e.extend({},e.fn.scrollTabs.defaultOptions,t),"ul"===e(this).prop("tagName").toLowerCase()?this.itemTag="li":this.itemTag="span",e(this).addClass("scroll_tabs_container"),null!==e(this).css("position")&&"static"!==e(this).css("position")||e(this).css("position","relative"),e(this.itemTag,this).last().addClass("scroll_tab_last"),e(this.itemTag,this).first().addClass("scroll_tab_first"),e(this).html("<div class='scroll_tab_left_button'></div><div class='scroll_tab_inner'><span class='scroll_tab_left_finisher'>&nbsp;</span>"+e(this).html()+"<span class='scroll_tab_right_finisher'>&nbsp;</span></div><div class='scroll_tab_right_button'></div>"),e(".scroll_tab_inner > span.scroll_tab_left_finisher",this).css({display:"none"}),e(".scroll_tab_inner > span.scroll_tab_right_finisher",this).css({display:"none"});var i=this;e(".scroll_tab_inner",this).css({margin:"0px",overflow:"hidden","white-space":"nowrap","-ms-text-overflow":"clip","text-overflow":"clip","font-size":"0px",position:"absolute",top:"0px",left:t.left_arrow_size+"px",right:t.right_arrow_size+"px"}),e.isFunction(e.fn.mousewheel)&&e(".scroll_tab_inner",this).mousewheel(function(t,n){"none"!==e(".scroll_tab_right_button",i).css("display")&&(this.scrollLeft-=30*n,a.scrollPos=this.scrollLeft,t.preventDefault())}),e(".scroll_tab_inner",i).animate({scrollLeft:a.scrollPos+"px"},0),e(".scroll_tab_left_button",this).css({position:"absolute",left:"0px",top:"0px",width:t.left_arrow_size+"px",cursor:"pointer"}),e(".scroll_tab_right_button",this).css({position:"absolute",right:"0px",top:"0px",width:t.right_arrow_size+"px",cursor:"pointer"}),e(".scroll_tab_inner > "+i.itemTag,i).css({display:"-moz-inline-stack",display:"inline-block",zoom:1,"*display":"inline",_height:"40px","-webkit-user-select":"none","-khtml-user-select":"none","-moz-user-select":"none","-ms-user-select":"none","-o-user-select":"none","user-select":"none"});var o,s=function(){var a=e(".scroll_tab_inner",i).outerWidth();e(".scroll_tab_inner",i)[0].scrollWidth>a?(e(".scroll_tab_right_button",i).show(),e(".scroll_tab_left_button",i).show(),e(".scroll_tab_inner",i).css({left:t.left_arrow_size+"px",right:t.right_arrow_size+"px"}),e(".scroll_tab_left_finisher",i).css("display","none"),e(".scroll_tab_right_finisher",i).css("display","none"),e(".scroll_tab_inner",i)[0].scrollWidth-a==e(".scroll_tab_inner",i).scrollLeft()?e(".scroll_tab_right_button",i).addClass("scroll_arrow_disabled").addClass("scroll_tab_right_button_disabled"):e(".scroll_tab_right_button",i).removeClass("scroll_arrow_disabled").removeClass("scroll_tab_right_button_disabled"),0==e(".scroll_tab_inner",i).scrollLeft()?e(".scroll_tab_left_button",i).addClass("scroll_arrow_disabled").addClass("scroll_tab_left_button_disabled"):e(".scroll_tab_left_button",i).removeClass("scroll_arrow_disabled").removeClass("scroll_tab_left_button_disabled")):(e(".scroll_tab_right_button",i).hide(),e(".scroll_tab_left_button",i).hide(),e(".scroll_tab_inner",i).css({left:"0px",right:"0px"}),e(".scroll_tab_inner > "+i.itemTag+":not(.scroll_tab_right_finisher):not(.scroll_tab_left_finisher):visible",i).size()>0&&(e(".scroll_tab_left_finisher",i).css("display","inline-block"),e(".scroll_tab_right_finisher",i).css("display","inline-block")))};s(),a.delay_timer=setInterval(function(){s()},500),e(".scroll_tab_right_button",this).mousedown(function(n){n.stopPropagation();var s=function(){var n=e(".scroll_tab_inner",i).scrollLeft();a.scrollPos=Math.min(n+t.scroll_distance,e(".scroll_tab_inner",i)[0].scrollWidth-e(".scroll_tab_inner",i).outerWidth()),e(".scroll_tab_inner",i).animate({scrollLeft:n+t.scroll_distance+"px"},t.scroll_duration)};s(),o=setInterval(function(){s()},t.scroll_duration)}).bind("mouseup mouseleave",function(){clearInterval(o)}).mouseover(function(){e(this).addClass("scroll_arrow_over").addClass("scroll_tab_right_button_over")}).mouseout(function(){e(this).removeClass("scroll_arrow_over").removeClass("scroll_tab_right_button_over")}),e(".scroll_tab_left_button",this).mousedown(function(n){n.stopPropagation();var s=function(){var n=e(".scroll_tab_inner",i).scrollLeft();a.scrollPos=Math.max(n-t.scroll_distance,0),e(".scroll_tab_inner",i).animate({scrollLeft:n-t.scroll_distance+"px"},t.scroll_duration)};s(),o=setInterval(function(){s()},t.scroll_duration)}).bind("mouseup mouseleave",function(){clearInterval(o)}).mouseover(function(){e(this).addClass("scroll_arrow_over").addClass("scroll_tab_left_button_over")}).mouseout(function(){e(this).removeClass("scroll_arrow_over").removeClass("scroll_tab_left_button_over")}),e(".scroll_tab_inner > "+this.itemTag+("span"!==this.itemTag?", .scroll_tab_inner > span":""),this).mouseover(function(){e(this).addClass("scroll_tab_over"),e(this).hasClass("scroll_tab_left_finisher")&&e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_first",i).addClass("scroll_tab_over").addClass("scroll_tab_first_over"),e(this).hasClass("scroll_tab_right_finisher")&&e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_last",i).addClass("scroll_tab_over").addClass("scroll_tab_last_over"),(e(this).hasClass("scroll_tab_first")||e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_last",i).hasClass("scroll_tab_first"))&&e(".scroll_tab_inner > span.scroll_tab_left_finisher",i).addClass("scroll_tab_over").addClass("scroll_tab_left_finisher_over"),(e(this).hasClass("scroll_tab_last")||e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_first",i).hasClass("scroll_tab_last"))&&e(".scroll_tab_inner > span.scroll_tab_right_finisher",i).addClass("scroll_tab_over").addClass("scroll_tab_right_finisher_over")}).mouseout(function(){e(this).removeClass("scroll_tab_over"),e(this).hasClass("scroll_tab_left_finisher")&&e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_first",i).removeClass("scroll_tab_over").removeClass("scroll_tab_first_over"),e(this).hasClass("scroll_tab_right_finisher")&&e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_last",i).removeClass("scroll_tab_over").removeClass("scroll_tab_last_over"),(e(this).hasClass("scroll_tab_first")||e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_last",i).hasClass("scroll_tab_first"))&&e(".scroll_tab_inner > span.scroll_tab_left_finisher",i).removeClass("scroll_tab_over").removeClass("scroll_tab_left_finisher_over"),(e(this).hasClass("scroll_tab_last")||e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_first",i).hasClass("scroll_tab_last"))&&e(".scroll_tab_inner > span.scroll_tab_right_finisher",i).removeClass("scroll_tab_over").removeClass("scroll_tab_right_finisher_over")}).click(function(o){o.stopPropagation(),e(".tab_selected",i).removeClass("tab_selected scroll_tab_first_selected scroll_tab_last_selected scroll_tab_left_finisher_selected scroll_tab_right_finisher_selected"),e(this).addClass("tab_selected");var s=this;e(this).hasClass("scroll_tab_left_finisher")&&(s=e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_first",i).addClass("tab_selected").addClass("scroll_tab_first_selected")),e(this).hasClass("scroll_tab_right_finisher")&&(s=e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_last",i).addClass("tab_selected").addClass("scroll_tab_last_selected")),(e(this).hasClass("scroll_tab_first")||e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_last",i).hasClass("scroll_tab_first"))&&e(".scroll_tab_inner > span.scroll_tab_left_finisher",i).addClass("tab_selected").addClass("scroll_tab_left_finisher_selected"),(e(this).hasClass("scroll_tab_last")||e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_first",i).hasClass("scroll_tab_last"))&&e(".scroll_tab_inner > span.scroll_tab_right_finisher",i).addClass("tab_selected").addClass("scroll_tab_left_finisher_selected"),n.call(i,a),t.click_callback.call(s,o)}),e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_first",i).hasClass("tab_selected")&&e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_left_finisher",i).addClass("tab_selected").addClass("scroll_tab_left_finisher_selected"),e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_last",i).hasClass("tab_selected")&&e(".scroll_tab_inner > "+i.itemTag+".scroll_tab_right_finisher",i).addClass("tab_selected").addClass("scroll_tab_right_finisher_selected")},n=function(a){var n=e(".tab_selected:not(.scroll_tab_right_finisher, .scroll_tab_left_finisher)",this),i=e(".scroll_tab_inner",this).scrollLeft(),o=e(".scroll_tab_inner",this).width();n&&void 0!==n&&n.position()&&void 0!==n.position()&&(n.position().left<0?(a.scrollPos=Math.max(i+n.position().left+1,0),e(".scroll_tab_inner",this).animate({scrollLeft:i+n.position().left+1+"px"},t.scroll_duration)):n.position().left+n.outerWidth()>o&&(a.scrollPos=Math.min(i+(n.position().left+n.outerWidth()-o),e(".scroll_tab_inner",this)[0].scrollWidth-e(".scroll_tab_inner",this).outerWidth()),e(".scroll_tab_inner",this).animate({scrollLeft:i+(n.position().left+n.outerWidth()-o)+"px"},t.scroll_duration)))},i=[];return this.each(function(){var t=e(this).html(),o={scrollPos:0};a.call(this,o);var s=this;i.push({domObject:s,state:o,addTab:function(n,i){void 0===i&&(i=e(".scroll_tab_inner > "+s.itemTag,s).length-("span"===s.itemTag?2:0)),e(".scroll_tab_inner > "+s.itemTag+".scroll_tab_last",s).removeClass("scroll_tab_last"),e(".scroll_tab_inner > "+s.itemTag+".scroll_tab_first",s).removeClass("scroll_tab_first"),t="";var l=0;e(".scroll_tab_inner > "+s.itemTag,s).each(function(){if(e(this).hasClass("scroll_tab_left_finisher")||e(this).hasClass("scroll_tab_right_finisher"))return!0;i==l&&(t+=n),t+=e(this).clone().wrap("<div>").parent().html(),l++}),i>=l&&(t+=n),this.destroy(),a.call(s,o),this.refreshFirstLast()},removeTabs:function(t){e(".scroll_tab_left_finisher",s).remove(),e(".scroll_tab_right_finisher",s).remove(),e(t,s).remove(),e(".scroll_tab_inner > "+s.itemTag+".scroll_tab_last",s).removeClass("scroll_tab_last"),e(".scroll_tab_inner > "+s.itemTag+".scroll_tab_first",s).removeClass("scroll_tab_first"),this.refreshState()},destroy:function(){clearInterval(o.delay_timer),e(s).html(t),e(s).removeClass("scroll_tabs_container")},refreshState:function(){e(".scroll_tab_inner > "+s.itemTag+".scroll_tab_last",s).removeClass("scroll_tab_last"),e(".scroll_tab_inner > "+s.itemTag+".scroll_tab_first",s).removeClass("scroll_tab_first"),t=e(".scroll_tab_inner",s).html(),this.destroy(),a.call(s,o),this.refreshFirstLast()},clearTabs:function(){t="",this.destroy(),a.call(s,o),this.refreshFirstLast()},refreshFirstLast:function(){var t=e(".scroll_tab_inner > "+s.itemTag+".scroll_tab_last",s),a=e(".scroll_tab_inner > "+s.itemTag+".scroll_tab_first",s);if(t.removeClass("scroll_tab_last"),a.removeClass("scroll_tab_first"),t.hasClass("tab_selected")&&e(".scroll_tab_inner > span.scroll_tab_right_finisher",s).removeClass("tab_selected scroll_tab_right_finisher_selected"),a.hasClass("tab_selected")&&e(".scroll_tab_inner > span.scroll_tab_left_finisher",s).removeClass("tab_selected scroll_tab_left_finisher_selected"),e(".scroll_tab_inner > "+s.itemTag+":not(.scroll_tab_right_finisher):not(.scroll_tab_left_finisher):visible",s).size()>0){var n=e(".scroll_tab_inner > "+s.itemTag+":not(.scroll_tab_right_finisher):visible",s).last(),i=e(".scroll_tab_inner > "+s.itemTag+":not(.scroll_tab_left_finisher):visible",s).first();n.addClass("scroll_tab_last"),i.addClass("scroll_tab_first"),n.hasClass("tab_selected")&&e(".scroll_tab_inner > span.scroll_tab_right_finisher",s).addClass("tab_selected").addClass("scroll_tab_right_finisher_selected"),i.hasClass("tab_selected")&&e(".scroll_tab_inner > span.scroll_tab_left_finisher",s).addClass("tab_selected").addClass("scroll_tab_right_finisher_selected")}else e(".scroll_tab_inner > span.scroll_tab_right_finisher",s).hide(),e(".scroll_tab_inner > span.scroll_tab_left_finisher",s).hide()},hideTabs:function(t){e(t,s).css("display","none"),this.refreshFirstLast()},showTabs:function(t){e(t,s).css({display:"-moz-inline-stack",display:"inline-block","*display":"inline"}),this.refreshFirstLast()},scrollSelectedIntoView:function(){n.call(s,o)}})}),1==this.length?i[0]:i},e.fn.scrollTabs.defaultOptions={scroll_distance:300,scroll_duration:300,left_arrow_size:26,right_arrow_size:26,click_callback:function(t){var a=e(this).attr("rel");a&&(window.location.href=a)}}}(jQuery),function(e){e.fn.SumoSelect=function(t){var a=e.extend({placeholder:"Select Here",csvDispCount:3,captionFormat:"{0} Selected",captionFormatAllSelected:"{0} all selected!",floatWidth:400,forceCustomRendering:!1,nativeOnDevice:["Android","BlackBerry","iPhone","iPad","iPod","Opera Mini","IEMobile","Silk"],outputAsCSV:!1,csvSepChar:",",okCancelInMulti:!1,triggerChangeCombined:!0,selectAll:!1,search:!1,searchText:"Search...",noMatch:'No matches for "{0}"',prefix:"",locale:["OK","Cancel","Select All"],up:!1},t),n=this.each(function(){var t=this;!this.sumo&&e(this).is("select")&&(this.sumo={E:e(t),is_multi:e(t).attr("multiple"),select:"",caption:"",placeholder:"",optDiv:"",CaptionCont:"",ul:"",is_floating:!1,is_opened:!1,mob:!1,Pstate:[],createElems:function(){var t=this;return t.E.wrap('<div class="SumoSelect" tabindex="0">'),t.select=t.E.parent(),t.caption=e("<span>"),t.CaptionCont=e('<p class="CaptionCont"><label><i></i></label></p>').addClass("SelectBox").attr("style",t.E.attr("style")).prepend(t.caption),t.select.append(t.CaptionCont),t.is_multi||(a.okCancelInMulti=!1),t.E.attr("disabled")&&t.select.addClass("disabled").removeAttr("tabindex"),a.outputAsCSV&&t.is_multi&&t.E.attr("name")&&(t.select.append(e('<input class="HEMANT123" type="hidden" />').attr("name",t.E.attr("name")).val(t.getSelStr())),t.E.removeAttr("name")),t.isMobile()&&!a.forceCustomRendering?void t.setNativeMobile():(t.E.attr("name")&&t.select.addClass("sumo_"+t.E.attr("name")),t.E.addClass("SumoUnder").attr("tabindex","-1"),t.optDiv=e('<div class="optWrapper '+(a.up?"up":"")+'">'),t.floatingList(),t.ul=e('<ul class="options">'),t.optDiv.append(t.ul),a.selectAll&&t.SelAll(),a.search&&t.Search(),t.ul.append(t.prepItems(t.E.children())),t.is_multi&&t.multiSelelect(),t.select.append(t.optDiv),t.basicEvents(),void t.selAllState())},prepItems:function(t,a){var n=[],i=this;return e(t).each(function(t,o){o=e(o),n.push(o.is("optgroup")?e('<li class="group '+(o[0].disabled?"disabled":"")+'"><label>'+o.attr("label")+"</label><ul></ul><li>").find("ul").append(i.prepItems(o.children(),o[0].disabled)).end():i.createLi(o,a))}),n},createLi:function(t,a){return t.attr("value")||t.attr("value",t.val()),li=e('<li class="opt"><label>'+t.text()+"</label></li>"),li.data("opt",t),t.data("li",li),this.is_multi&&li.prepend("<span><i></i></span>"),(t[0].disabled||a)&&(li=li.addClass("disabled")),this.onOptClick(li),t[0].selected&&li.addClass("selected"),t.attr("class")&&li.addClass(t.attr("class")),li},getSelStr:function(){return sopt=[],this.E.find("option:selected").each(function(){sopt.push(e(this).val())}),sopt.join(a.csvSepChar)},multiSelelect:function(){var t=this;t.optDiv.addClass("multiple"),t.okbtn=e('<p class="btnOk">'+a.locale[0]+"</p>").click(function(){a.triggerChangeCombined&&(changed=!1,t.E.find("option:selected").length!=t.Pstate.length?changed=!0:t.E.find("option").each(function(e,a){a.selected&&t.Pstate.indexOf(e)<0&&(changed=!0)}),changed&&(t.callChange(),t.setText())),t.hideOpts()}),t.cancelBtn=e('<p class="btnCancel">'+a.locale[1]+"</p>").click(function(){t._cnbtn(),t.hideOpts()}),t.optDiv.append(e('<div class="MultiControls">').append(t.okbtn).append(t.cancelBtn))},_cnbtn:function(){var e=this;e.E.find("option:selected").each(function(){this.selected=!1}),e.optDiv.find("li.selected").removeClass("selected");for(var t=0;t<e.Pstate.length;t++)e.E.find("option")[e.Pstate[t]].selected=!0,e.ul.find("li.opt").eq(e.Pstate[t]).addClass("selected");e.selAllState()},SelAll:function(){var t=this;t.is_multi&&(t.selAll=e('<p class="select-all"><span><i></i></span><label>'+a.locale[2]+"</label></p>"),t.selAll.on("click",function(){t.selAll.toggleClass("selected"),t.optDiv.find("li.opt").not(".hidden").each(function(a,n){n=e(n),t.selAll.hasClass("selected")?n.hasClass("selected")||n.trigger("click"):n.hasClass("selected")&&n.trigger("click")})}),t.optDiv.prepend(t.selAll))},Search:function(){var t=this,n=t.CaptionCont.addClass("search"),i=e('<p class="no-match">');t.ftxt=e('<input type="text" class="search-txt" value="" placeholder="'+a.searchText+'">').on("click",function(e){e.stopPropagation()}),n.append(t.ftxt),t.optDiv.children("ul").after(i),t.ftxt.on("keyup.sumo",function(){var n=t.optDiv.find("ul.options li.opt").each(function(a,n){(n=e(n)).text().toLowerCase().indexOf(t.ftxt.val().toLowerCase())>-1?n.removeClass("hidden"):n.addClass("hidden")}).not(".hidden");i.html(a.noMatch.replace(/\{0\}/g,t.ftxt.val())).toggle(!n.length),t.selAllState()})},selAllState:function(){var t=this;if(a.selectAll){var n=0,i=0;t.optDiv.find("li.opt").not(".hidden").each(function(t,a){e(a).hasClass("selected")&&n++,e(a).hasClass("disabled")||i++}),n==i?t.selAll.removeClass("partial").addClass("selected"):0==n?t.selAll.removeClass("selected partial"):t.selAll.addClass("partial")}},showOpts:function(){var t=this;t.E.attr("disabled")||(t.is_opened=!0,t.select.addClass("open"),t.ftxt?t.ftxt.focus():t.select.focus(),e(document).on("click.sumo",function(e){if(!t.select.is(e.target)&&0===t.select.has(e.target).length){if(!t.is_opened)return;t.hideOpts(),a.okCancelInMulti&&t._cnbtn()}}),t.is_floating&&(H=t.optDiv.children("ul").outerHeight()+2,t.is_multi&&(H+=parseInt(t.optDiv.css("padding-bottom"))),t.optDiv.css("height",H),e("body").addClass("sumoStopScroll")),t.setPstate())},setPstate:function(){var e=this;e.is_multi&&(e.is_floating||a.okCancelInMulti)&&(e.Pstate=[],e.E.find("option").each(function(t,a){a.selected&&e.Pstate.push(t)}))},callChange:function(){this.E.trigger("change").trigger("click")},hideOpts:function(){var t=this;t.is_opened&&(t.is_opened=!1,t.select.removeClass("open").find("ul li.sel").removeClass("sel"),e(document).off("click.sumo"),t.select.focus(),e("body").removeClass("sumoStopScroll"),a.search&&(t.ftxt.val(""),t.optDiv.find("ul.options li").removeClass("hidden"),t.optDiv.find(".no-match").toggle(!1)))},setOnOpen:function(){var e=this,t=e.optDiv.find("li.opt:not(.hidden)").eq(a.search?0:e.E[0].selectedIndex);e.optDiv.find("li.sel").removeClass("sel"),t.addClass("sel"),e.showOpts()},nav:function(e){var t,a=this,n=a.ul.find("li.opt:not(.disabled, .hidden)"),i=a.ul.find("li.opt.sel:not(.hidden)"),o=n.index(i);if(a.is_opened&&i.length){if(e&&o>0)t=n.eq(o-1);else{if(!(!e&&o<n.length-1&&o>-1))return;t=n.eq(o+1)}i.removeClass("sel"),i=t.addClass("sel");var s=a.ul,l=s.scrollTop(),r=i.position().top+l;r>=l+s.height()-i.outerHeight()&&s.scrollTop(r-s.height()+i.outerHeight()),l>r&&s.scrollTop(r)}else a.setOnOpen()},basicEvents:function(){var t=this;t.CaptionCont.click(function(e){t.E.trigger("click"),t.is_opened?t.hideOpts():t.showOpts(),e.stopPropagation()}),t.select.on("keydown.sumo",function(e){switch(e.which){case 38:t.nav(!0);break;case 40:t.nav(!1);break;case 32:if(a.search&&t.ftxt.is(e.target))return;case 13:t.is_opened?t.optDiv.find("ul li.sel").trigger("click"):t.setOnOpen();break;case 9:case 27:return a.okCancelInMulti&&t._cnbtn(),void t.hideOpts();default:return}e.preventDefault()}),e(window).on("resize.sumo",function(){t.floatingList()})},onOptClick:function(t){var n=this;t.click(function(){var t=e(this);t.hasClass("disabled")||(txt="",n.is_multi?(t.toggleClass("selected"),t.data("opt")[0].selected=t.hasClass("selected"),n.selAllState()):(t.parent().find("li.selected").removeClass("selected"),t.toggleClass("selected"),t.data("opt")[0].selected=!0),n.is_multi&&a.triggerChangeCombined&&(n.is_floating||a.okCancelInMulti)||(n.setText(),n.callChange()),n.is_multi||n.hideOpts())})},setText:function(){var t=this;if(t.placeholder="",t.is_multi){for(sels=t.E.find(":selected").not(":disabled"),i=0;i<sels.length;i++){if(i+1>=a.csvDispCount&&a.csvDispCount){sels.length==t.E.find("option").length&&a.captionFormatAllSelected?t.placeholder=a.captionFormatAllSelected.replace(/\{0\}/g,sels.length)+",":t.placeholder=a.captionFormat.replace(/\{0\}/g,sels.length)+",";break}t.placeholder+=e(sels[i]).text()+", "}t.placeholder=t.placeholder.replace(/,([^,]*)$/,"$1")}else t.placeholder=t.E.find(":selected").not(":disabled").text();return is_placeholder=!1,t.placeholder||(is_placeholder=!0,t.placeholder=t.E.attr("placeholder"),t.placeholder||(t.placeholder=t.E.find("option:disabled:selected").text())),t.placeholder=t.placeholder?a.prefix+" "+t.placeholder:a.placeholder,t.caption.html(t.placeholder),t.CaptionCont.attr("title",t.placeholder),csvField=t.select.find("input.HEMANT123"),csvField.length&&csvField.val(t.getSelStr()),is_placeholder?t.caption.addClass("placeholder"):t.caption.removeClass("placeholder"),t.placeholder},isMobile:function(){for(var e=navigator.userAgent||navigator.vendor||window.opera,t=0;t<a.nativeOnDevice.length;t++)if(e.toString().toLowerCase().indexOf(a.nativeOnDevice[t].toLowerCase())>0)return a.nativeOnDevice[t];return!1},setNativeMobile:function(){var e=this;e.E.addClass("SelectClass"),e.mob=!0,e.E.change(function(){e.setText()})},floatingList:function(){var t=this;t.is_floating=e(window).width()<=a.floatWidth,t.optDiv.toggleClass("isFloating",t.is_floating),t.is_floating||t.optDiv.css("height",""),t.optDiv.toggleClass("okCancelInMulti",a.okCancelInMulti&&!t.is_floating)},vRange:function(e){if(opts=this.E.find("option"),opts.length<=e||0>e)throw"index out of bounds";return this},toggSel:function(t,a){var n=this;"number"==typeof a?(n.vRange(a),opt=n.E.find("option")[a]):opt=n.E.find('option[value="'+a+'"]')[0]||0,opt&&!opt.disabled&&opt.selected!=t&&(opt.selected=t,n.mob||e(opt).data("li").toggleClass("selected",t),n.callChange(),n.setPstate(),n.setText(),n.selAllState())},toggDis:function(e,t){var a=this.vRange(t);a.E.find("option")[t].disabled=e,e&&(a.E.find("option")[t].selected=!1),a.mob||a.optDiv.find("ul.options li").eq(t).toggleClass("disabled",e).removeClass("selected"),a.setText()},toggSumo:function(e){var t=this;return t.enabled=e,t.select.toggleClass("disabled",e),e?(t.E.attr("disabled","disabled"),t.select.removeAttr("tabindex")):(t.E.removeAttr("disabled"),t.select.attr("tabindex","0")),t},toggSelAll:function(t){var a=this;a.E.find("option").each(function(){a.E.find("option")[e(this).index()].disabled||(a.E.find("option")[e(this).index()].selected=t,a.mob||a.optDiv.find("ul.options li").eq(e(this).index()).toggleClass("selected",t),a.setText())}),!a.mob&&a.selAll&&a.selAll.removeClass("partial").toggleClass("selected",t),a.callChange(),a.setPstate()},reload:function(){var t=this.unload();return e(t).SumoSelect(a)},unload:function(){var e=this;return e.select.before(e.E),e.E.show(),a.outputAsCSV&&e.is_multi&&e.select.find("input.HEMANT123").length&&e.E.attr("name",e.select.find("input.HEMANT123").attr("name")),e.select.remove(),delete t.sumo,t},add:function(a,n,i){if(void 0===a)throw"No value to add";var o=this;if(opts=o.E.find("option"),"number"==typeof n&&(i=n,n=a),void 0===n&&(n=a),opt=e("<option></option>").val(a).html(n),opts.length<i)throw"index out of bounds";return void 0===i||opts.length==i?(o.E.append(opt),o.mob||o.ul.append(o.createLi(opt))):(opts.eq(i).before(opt),o.mob||o.ul.find("li.opt").eq(i).before(o.createLi(opt))),t},remove:function(e){var t=this.vRange(e);t.E.find("option").eq(e).remove(),t.mob||t.optDiv.find("ul.options li").eq(e).remove(),t.setText()},selectItem:function(e){this.toggSel(!0,e)},unSelectItem:function(e){this.toggSel(!1,e)},selectAll:function(){this.toggSelAll(!0)},unSelectAll:function(){this.toggSelAll(!1)},disableItem:function(e){this.toggDis(!0,e)},enableItem:function(e){this.toggDis(!1,e)},enabled:!0,enable:function(){return this.toggSumo(!1)},disable:function(){return this.toggSumo(!0)},init:function(){var e=this;return e.createElems(),e.setText(),e}},t.sumo.init())});return 1==n.length?n[0]:n}}(jQuery),function(e){var t=!0;e.flexslider=function(a,n){var i=e(a);i.vars=e.extend({},e.flexslider.defaults,n);var o,s=i.vars.namespace,l=window.navigator&&window.navigator.msPointerEnabled&&window.MSGesture,r=("ontouchstart"in window||l||window.DocumentTouch&&document instanceof DocumentTouch)&&i.vars.touch,c="click touchend MSPointerUp keyup",d="",u="vertical"===i.vars.direction,h=i.vars.reverse,p=i.vars.itemWidth>0,f="fade"===i.vars.animation,m=""!==i.vars.asNavFor,v={};e.data(a,"flexslider",i),v={init:function(){i.animating=!1,i.currentSlide=parseInt(i.vars.startAt?i.vars.startAt:0,10),isNaN(i.currentSlide)&&(i.currentSlide=0),i.animatingTo=i.currentSlide,i.atEnd=0===i.currentSlide||i.currentSlide===i.last,i.containerSelector=i.vars.selector.substr(0,i.vars.selector.search(" ")),i.slides=e(i.vars.selector,i),i.container=e(i.containerSelector,i),i.count=i.slides.length,i.syncExists=e(i.vars.sync).length>0,"slide"===i.vars.animation&&(i.vars.animation="swing"),i.prop=u?"top":"marginLeft",i.args={},i.manualPause=!1,i.stopped=!1,i.started=!1,i.startTimeout=null,i.transitions=!i.vars.video&&!f&&i.vars.useCSS&&function(){var e=document.createElement("div"),t=["perspectiveProperty","WebkitPerspective","MozPerspective","OPerspective","msPerspective"];for(var a in t)if(void 0!==e.style[t[a]])return i.pfx=t[a].replace("Perspective","").toLowerCase(),i.prop="-"+i.pfx+"-transform",!0;return!1}(),i.ensureAnimationEnd="",""!==i.vars.controlsContainer&&(i.controlsContainer=e(i.vars.controlsContainer).length>0&&e(i.vars.controlsContainer)),""!==i.vars.manualControls&&(i.manualControls=e(i.vars.manualControls).length>0&&e(i.vars.manualControls)),""!==i.vars.customDirectionNav&&(i.customDirectionNav=2===e(i.vars.customDirectionNav).length&&e(i.vars.customDirectionNav)),i.vars.randomize&&(i.slides.sort(function(){return Math.round(Math.random())-.5}),i.container.empty().append(i.slides)),i.doMath(),i.setup("init"),i.vars.controlNav&&v.controlNav.setup(),i.vars.directionNav&&v.directionNav.setup(),i.vars.keyboard&&(1===e(i.containerSelector).length||i.vars.multipleKeyboard)&&e(document).bind("keyup",function(e){var t=e.keyCode;if(!i.animating&&(39===t||37===t)){var a=39===t?i.getTarget("next"):37===t&&i.getTarget("prev");i.flexAnimate(a,i.vars.pauseOnAction)}}),i.vars.mousewheel&&i.bind("mousewheel",function(e,t,a,n){e.preventDefault();var o=0>t?i.getTarget("next"):i.getTarget("prev");i.flexAnimate(o,i.vars.pauseOnAction)}),i.vars.pausePlay&&v.pausePlay.setup(),i.vars.slideshow&&i.vars.pauseInvisible&&v.pauseInvisible.init(),i.vars.slideshow&&(i.vars.pauseOnHover&&i.hover(function(){i.manualPlay||i.manualPause||i.pause()},function(){i.manualPause||i.manualPlay||i.stopped||i.play()}),i.vars.pauseInvisible&&v.pauseInvisible.isHidden()||(i.vars.initDelay>0?i.startTimeout=setTimeout(i.play,i.vars.initDelay):i.play())),m&&v.asNav.setup(),r&&i.vars.touch&&v.touch(),(!f||f&&i.vars.smoothHeight)&&e(window).bind("resize orientationchange focus",v.resize),i.find("img").attr("draggable","false"),setTimeout(function(){i.vars.start(i)},200)},asNav:{setup:function(){i.asNav=!0,i.animatingTo=Math.floor(i.currentSlide/i.move),i.currentItem=i.currentSlide,i.slides.removeClass(s+"active-slide").eq(i.currentItem).addClass(s+"active-slide"),l?(a._slider=i,i.slides.each(function(){var t=this;t._gesture=new MSGesture,t._gesture.target=t,t.addEventListener("MSPointerDown",function(e){e.preventDefault(),e.currentTarget._gesture&&e.currentTarget._gesture.addPointer(e.pointerId)},!1),t.addEventListener("MSGestureTap",function(t){t.preventDefault();var a=e(this),n=a.index();e(i.vars.asNavFor).data("flexslider").animating||a.hasClass("active")||(i.direction=i.currentItem<n?"next":"prev",i.flexAnimate(n,i.vars.pauseOnAction,!1,!0,!0))})})):i.slides.on(c,function(t){t.preventDefault();var a=e(this),n=a.index();0>=a.offset().left-e(i).scrollLeft()&&a.hasClass(s+"active-slide")?i.flexAnimate(i.getTarget("prev"),!0):e(i.vars.asNavFor).data("flexslider").animating||a.hasClass(s+"active-slide")||(i.direction=i.currentItem<n?"next":"prev",i.flexAnimate(n,i.vars.pauseOnAction,!1,!0,!0))})}},controlNav:{setup:function(){i.manualControls?v.controlNav.setupManual():v.controlNav.setupPaging()},setupPaging:function(){var t,a,n="thumbnails"===i.vars.controlNav?"control-thumbs":"control-paging",o=1;if(i.controlNavScaffold=e('<ol class="'+s+"control-nav "+s+n+'"></ol>'),i.pagingCount>1)for(var l=0;l<i.pagingCount;l++){void 0===(a=i.slides.eq(l)).attr("data-thumb-alt")&&a.attr("data-thumb-alt","");var r=""!==a.attr("data-thumb-alt")?r=' alt="'+a.attr("data-thumb-alt")+'"':"";if(t="thumbnails"===i.vars.controlNav?'<img src="'+a.attr("data-thumb")+'"'+r+"/>":'<a href="#">'+o+"</a>","thumbnails"===i.vars.controlNav&&!0===i.vars.thumbCaptions){var u=a.attr("data-thumbcaption");""!==u&&void 0!==u&&(t+='<span class="'+s+'caption">'+u+"</span>")}i.controlNavScaffold.append("<li>"+t+"</li>"),o++}i.controlsContainer?e(i.controlsContainer).append(i.controlNavScaffold):i.append(i.controlNavScaffold),v.controlNav.set(),v.controlNav.active(),i.controlNavScaffold.delegate("a, img",c,function(t){if(t.preventDefault(),""===d||d===t.type){var a=e(this),n=i.controlNav.index(a);a.hasClass(s+"active")||(i.direction=n>i.currentSlide?"next":"prev",i.flexAnimate(n,i.vars.pauseOnAction))}""===d&&(d=t.type),v.setToClearWatchedEvent()})},setupManual:function(){i.controlNav=i.manualControls,v.controlNav.active(),i.controlNav.bind(c,function(t){if(t.preventDefault(),""===d||d===t.type){var a=e(this),n=i.controlNav.index(a);a.hasClass(s+"active")||(n>i.currentSlide?i.direction="next":i.direction="prev",i.flexAnimate(n,i.vars.pauseOnAction))}""===d&&(d=t.type),v.setToClearWatchedEvent()})},set:function(){var t="thumbnails"===i.vars.controlNav?"img":"a";i.controlNav=e("."+s+"control-nav li "+t,i.controlsContainer?i.controlsContainer:i)},active:function(){i.controlNav.removeClass(s+"active").eq(i.animatingTo).addClass(s+"active")},update:function(t,a){i.pagingCount>1&&"add"===t?i.controlNavScaffold.append(e('<li><a href="#">'+i.count+"</a></li>")):1===i.pagingCount?i.controlNavScaffold.find("li").remove():i.controlNav.eq(a).closest("li").remove(),v.controlNav.set(),i.pagingCount>1&&i.pagingCount!==i.controlNav.length?i.update(a,t):v.controlNav.active()}},directionNav:{setup:function(){var t=e('<ul class="'+s+'direction-nav"><li class="'+s+'nav-prev"><a class="'+s+'prev" href="#">'+i.vars.prevText+'</a></li><li class="'+s+'nav-next"><a class="'+s+'next" href="#">'+i.vars.nextText+"</a></li></ul>");i.customDirectionNav?i.directionNav=i.customDirectionNav:i.controlsContainer?(e(i.controlsContainer).append(t),i.directionNav=e("."+s+"direction-nav li a",i.controlsContainer)):(i.append(t),i.directionNav=e("."+s+"direction-nav li a",i)),v.directionNav.update(),i.directionNav.bind(c,function(t){var a;t.preventDefault(),""!==d&&d!==t.type||(a=e(this).hasClass(s+"next")?i.getTarget("next"):i.getTarget("prev"),i.flexAnimate(a,i.vars.pauseOnAction)),""===d&&(d=t.type),v.setToClearWatchedEvent()})},update:function(){var e=s+"disabled";1===i.pagingCount?i.directionNav.addClass(e).attr("tabindex","-1"):i.vars.animationLoop?i.directionNav.removeClass(e).removeAttr("tabindex"):0===i.animatingTo?i.directionNav.removeClass(e).filter("."+s+"prev").addClass(e).attr("tabindex","-1"):i.animatingTo===i.last?i.directionNav.removeClass(e).filter("."+s+"next").addClass(e).attr("tabindex","-1"):i.directionNav.removeClass(e).removeAttr("tabindex")}},pausePlay:{setup:function(){var t=e('<div class="'+s+'pauseplay"><a href="#"></a></div>');i.controlsContainer?(i.controlsContainer.append(t),i.pausePlay=e("."+s+"pauseplay a",i.controlsContainer)):(i.append(t),i.pausePlay=e("."+s+"pauseplay a",i)),v.pausePlay.update(i.vars.slideshow?s+"pause":s+"play"),i.pausePlay.bind(c,function(t){t.preventDefault(),""!==d&&d!==t.type||(e(this).hasClass(s+"pause")?(i.manualPause=!0,i.manualPlay=!1,i.pause()):(i.manualPause=!1,i.manualPlay=!0,i.play())),""===d&&(d=t.type),v.setToClearWatchedEvent()})},update:function(e){"play"===e?i.pausePlay.removeClass(s+"pause").addClass(s+"play").html(i.vars.playText):i.pausePlay.removeClass(s+"play").addClass(s+"pause").html(i.vars.pauseText)}},touch:function(){var e,t,n,o,s,r,c,d,m,v=!1,g=0,b=0,_=0;l?(a.style.msTouchAction="none",a._gesture=new MSGesture,a._gesture.target=a,a.addEventListener("MSPointerDown",function(e){e.stopPropagation(),i.animating?e.preventDefault():(i.pause(),a._gesture.addPointer(e.pointerId),_=0,o=u?i.h:i.w,r=Number(new Date),n=p&&h&&i.animatingTo===i.last?0:p&&h?i.limit-(i.itemW+i.vars.itemMargin)*i.move*i.animatingTo:p&&i.currentSlide===i.last?i.limit:p?(i.itemW+i.vars.itemMargin)*i.move*i.currentSlide:h?(i.last-i.currentSlide+i.cloneOffset)*o:(i.currentSlide+i.cloneOffset)*o)},!1),a._slider=i,a.addEventListener("MSGestureChange",function(e){e.stopPropagation();var t=e.target._slider;if(t){var i=-e.translationX,l=-e.translationY;return s=_+=u?l:i,v=u?Math.abs(_)<Math.abs(-i):Math.abs(_)<Math.abs(-l),e.detail===e.MSGESTURE_FLAG_INERTIA?void setImmediate(function(){a._gesture.stop()}):void((!v||Number(new Date)-r>500)&&(e.preventDefault(),!f&&t.transitions&&(t.vars.animationLoop||(s=_/(0===t.currentSlide&&0>_||t.currentSlide===t.last&&_>0?Math.abs(_)/o+2:1)),t.setProps(n+s,"setTouch"))))}},!1),a.addEventListener("MSGestureEnd",function(a){a.stopPropagation();var i=a.target._slider;if(i){if(i.animatingTo===i.currentSlide&&!v&&null!==s){var l=h?-s:s,c=l>0?i.getTarget("next"):i.getTarget("prev");i.canAdvance(c)&&(Number(new Date)-r<550&&Math.abs(l)>50||Math.abs(l)>o/2)?i.flexAnimate(c,i.vars.pauseOnAction):f||i.flexAnimate(i.currentSlide,i.vars.pauseOnAction,!0)}e=null,t=null,s=null,n=null,_=0}},!1)):(c=function(s){i.animating?s.preventDefault():(window.navigator.msPointerEnabled||1===s.touches.length)&&(i.pause(),o=u?i.h:i.w,r=Number(new Date),g=s.touches[0].pageX,b=s.touches[0].pageY,n=p&&h&&i.animatingTo===i.last?0:p&&h?i.limit-(i.itemW+i.vars.itemMargin)*i.move*i.animatingTo:p&&i.currentSlide===i.last?i.limit:p?(i.itemW+i.vars.itemMargin)*i.move*i.currentSlide:h?(i.last-i.currentSlide+i.cloneOffset)*o:(i.currentSlide+i.cloneOffset)*o,e=u?b:g,t=u?g:b,a.addEventListener("touchmove",d,!1),a.addEventListener("touchend",m,!1))},d=function(a){g=a.touches[0].pageX,b=a.touches[0].pageY,s=u?e-b:e-g;(!(v=u?Math.abs(s)<Math.abs(g-t):Math.abs(s)<Math.abs(b-t))||Number(new Date)-r>500)&&(a.preventDefault(),!f&&i.transitions&&(i.vars.animationLoop||(s/=0===i.currentSlide&&0>s||i.currentSlide===i.last&&s>0?Math.abs(s)/o+2:1),i.setProps(n+s,"setTouch")))},m=function(l){if(a.removeEventListener("touchmove",d,!1),i.animatingTo===i.currentSlide&&!v&&null!==s){var c=h?-s:s,u=c>0?i.getTarget("next"):i.getTarget("prev");i.canAdvance(u)&&(Number(new Date)-r<550&&Math.abs(c)>50||Math.abs(c)>o/2)?i.flexAnimate(u,i.vars.pauseOnAction):f||i.flexAnimate(i.currentSlide,i.vars.pauseOnAction,!0)}a.removeEventListener("touchend",m,!1),e=null,t=null,s=null,n=null},a.addEventListener("touchstart",c,!1))},resize:function(){!i.animating&&i.is(":visible")&&(p||i.doMath(),f?v.smoothHeight():p?(i.slides.width(i.computedW),i.update(i.pagingCount),i.setProps()):u?(i.viewport.height(i.h),i.setProps(i.h,"setTotal")):(i.vars.smoothHeight&&v.smoothHeight(),i.newSlides.width(i.computedW),i.setProps(i.computedW,"setTotal")))},smoothHeight:function(e){if(!u||f){var t=f?i:i.viewport;e?t.animate({height:i.slides.eq(i.animatingTo).innerHeight()},e):t.innerHeight(i.slides.eq(i.animatingTo).innerHeight())}},sync:function(t){var a=e(i.vars.sync).data("flexslider"),n=i.animatingTo;switch(t){case"animate":a.flexAnimate(n,i.vars.pauseOnAction,!1,!0);break;case"play":a.playing||a.asNav||a.play();break;case"pause":a.pause()}},uniqueID:function(t){return t.filter("[id]").add(t.find("[id]")).each(function(){var t=e(this);t.attr("id",t.attr("id")+"_clone")}),t},pauseInvisible:{visProp:null,init:function(){var e=v.pauseInvisible.getHiddenProp();if(e){var t=e.replace(/[H|h]idden/,"")+"visibilitychange";document.addEventListener(t,function(){v.pauseInvisible.isHidden()?i.startTimeout?clearTimeout(i.startTimeout):i.pause():i.started?i.play():i.vars.initDelay>0?setTimeout(i.play,i.vars.initDelay):i.play()})}},isHidden:function(){var e=v.pauseInvisible.getHiddenProp();return!!e&&document[e]},getHiddenProp:function(){var e=["webkit","moz","ms","o"];if("hidden"in document)return"hidden";for(var t=0;t<e.length;t++)if(e[t]+"Hidden"in document)return e[t]+"Hidden";return null}},setToClearWatchedEvent:function(){clearTimeout(o),o=setTimeout(function(){d=""},3e3)}},i.flexAnimate=function(t,a,n,o,l){if(i.vars.animationLoop||t===i.currentSlide||(i.direction=t>i.currentSlide?"next":"prev"),m&&1===i.pagingCount&&(i.direction=i.currentItem<t?"next":"prev"),!i.animating&&(i.canAdvance(t,l)||n)&&i.is(":visible")){if(m&&o){var c=e(i.vars.asNavFor).data("flexslider");if(i.atEnd=0===t||t===i.count-1,c.flexAnimate(t,!0,!1,!0,l),i.direction=i.currentItem<t?"next":"prev",c.direction=i.direction,Math.ceil((t+1)/i.visible)-1===i.currentSlide||0===t)return i.currentItem=t,i.slides.removeClass(s+"active-slide").eq(t).addClass(s+"active-slide"),!1;i.currentItem=t,i.slides.removeClass(s+"active-slide").eq(t).addClass(s+"active-slide"),t=Math.floor(t/i.visible)}if(i.animating=!0,i.animatingTo=t,a&&i.pause(),i.vars.before(i),i.syncExists&&!l&&v.sync("animate"),i.vars.controlNav&&v.controlNav.active(),p||i.slides.removeClass(s+"active-slide").eq(t).addClass(s+"active-slide"),i.atEnd=0===t||t===i.last,i.vars.directionNav&&v.directionNav.update(),t===i.last&&(i.vars.end(i),i.vars.animationLoop||i.pause()),f)r?(i.slides.eq(i.currentSlide).css({opacity:0,zIndex:1}),i.slides.eq(t).css({opacity:1,zIndex:2}),i.wrapup(_)):(i.slides.eq(i.currentSlide).css({zIndex:1}).animate({opacity:0},i.vars.animationSpeed,i.vars.easing),i.slides.eq(t).css({zIndex:2}).animate({opacity:1},i.vars.animationSpeed,i.vars.easing,i.wrapup));else{var d,g,b,_=u?i.slides.filter(":first").height():i.computedW;p?(d=i.vars.itemMargin,g=(b=(i.itemW+d)*i.move*i.animatingTo)>i.limit&&1!==i.visible?i.limit:b):g=0===i.currentSlide&&t===i.count-1&&i.vars.animationLoop&&"next"!==i.direction?h?(i.count+i.cloneOffset)*_:0:i.currentSlide===i.last&&0===t&&i.vars.animationLoop&&"prev"!==i.direction?h?0:(i.count+1)*_:h?(i.count-1-t+i.cloneOffset)*_:(t+i.cloneOffset)*_,i.setProps(g,"",i.vars.animationSpeed),i.transitions?(i.vars.animationLoop&&i.atEnd||(i.animating=!1,i.currentSlide=i.animatingTo),i.container.unbind("webkitTransitionEnd transitionend"),i.container.bind("webkitTransitionEnd transitionend",function(){clearTimeout(i.ensureAnimationEnd),i.wrapup(_)}),clearTimeout(i.ensureAnimationEnd),i.ensureAnimationEnd=setTimeout(function(){i.wrapup(_)},i.vars.animationSpeed+100)):i.container.animate(i.args,i.vars.animationSpeed,i.vars.easing,function(){i.wrapup(_)})}i.vars.smoothHeight&&v.smoothHeight(i.vars.animationSpeed)}},i.wrapup=function(e){f||p||(0===i.currentSlide&&i.animatingTo===i.last&&i.vars.animationLoop?i.setProps(e,"jumpEnd"):i.currentSlide===i.last&&0===i.animatingTo&&i.vars.animationLoop&&i.setProps(e,"jumpStart")),i.animating=!1,i.currentSlide=i.animatingTo,i.vars.after(i)},i.animateSlides=function(){!i.animating&&t&&i.flexAnimate(i.getTarget("next"))},i.pause=function(){clearInterval(i.animatedSlides),i.animatedSlides=null,i.playing=!1,i.vars.pausePlay&&v.pausePlay.update("play"),i.syncExists&&v.sync("pause")},i.play=function(){i.playing&&clearInterval(i.animatedSlides),i.animatedSlides=i.animatedSlides||setInterval(i.animateSlides,i.vars.slideshowSpeed),i.started=i.playing=!0,i.vars.pausePlay&&v.pausePlay.update("pause"),i.syncExists&&v.sync("play")},i.stop=function(){i.pause(),i.stopped=!0},i.canAdvance=function(e,t){var a=m?i.pagingCount-1:i.last;return!!t||(!(!m||i.currentItem!==i.count-1||0!==e||"prev"!==i.direction)||(!m||0!==i.currentItem||e!==i.pagingCount-1||"next"===i.direction)&&(!(e===i.currentSlide&&!m)&&(!!i.vars.animationLoop||(!i.atEnd||0!==i.currentSlide||e!==a||"next"===i.direction)&&(!i.atEnd||i.currentSlide!==a||0!==e||"next"!==i.direction))))},i.getTarget=function(e){return i.direction=e,"next"===e?i.currentSlide===i.last?0:i.currentSlide+1:0===i.currentSlide?i.last:i.currentSlide-1},i.setProps=function(e,t,a){var n=function(){var a=e||(i.itemW+i.vars.itemMargin)*i.move*i.animatingTo;return-1*function(){if(p)return"setTouch"===t?e:h&&i.animatingTo===i.last?0:h?i.limit-(i.itemW+i.vars.itemMargin)*i.move*i.animatingTo:i.animatingTo===i.last?i.limit:a;switch(t){case"setTotal":return h?(i.count-1-i.currentSlide+i.cloneOffset)*e:(i.currentSlide+i.cloneOffset)*e;case"setTouch":return e;case"jumpEnd":return h?e:i.count*e;case"jumpStart":return h?i.count*e:e;default:return e}}()+"px"}();i.transitions&&(n=u?"translate3d(0,"+n+",0)":"translate3d("+n+",0,0)",a=void 0!==a?a/1e3+"s":"0s",i.container.css("-"+i.pfx+"-transition-duration",a),i.container.css("transition-duration",a)),i.args[i.prop]=n,(i.transitions||void 0===a)&&i.container.css(i.args),i.container.css("transform",n)},i.setup=function(t){var a,n;f?(i.slides.css({width:"100%",float:"left",marginRight:"-100%",position:"relative"}),"init"===t&&(r?i.slides.css({opacity:0,display:"block",webkitTransition:"opacity "+i.vars.animationSpeed/1e3+"s ease",zIndex:1}).eq(i.currentSlide).css({opacity:1,zIndex:2}):0==i.vars.fadeFirstSlide?i.slides.css({opacity:0,display:"block",zIndex:1}).eq(i.currentSlide).css({zIndex:2}).css({opacity:1}):i.slides.css({opacity:0,display:"block",zIndex:1}).eq(i.currentSlide).css({zIndex:2}).animate({opacity:1},i.vars.animationSpeed,i.vars.easing)),i.vars.smoothHeight&&v.smoothHeight()):("init"===t&&(i.viewport=e('<div class="'+s+'viewport"></div>').css({overflow:"hidden",position:"relative"}).appendTo(i).append(i.container),i.cloneCount=0,i.cloneOffset=0,h&&(n=e.makeArray(i.slides).reverse(),i.slides=e(n),i.container.empty().append(i.slides))),i.vars.animationLoop&&!p&&(i.cloneCount=2,i.cloneOffset=1,"init"!==t&&i.container.find(".clone").remove(),i.container.append(v.uniqueID(i.slides.first().clone().addClass("clone")).attr("aria-hidden","true")).prepend(v.uniqueID(i.slides.last().clone().addClass("clone")).attr("aria-hidden","true"))),i.newSlides=e(i.vars.selector,i),a=h?i.count-1-i.currentSlide+i.cloneOffset:i.currentSlide+i.cloneOffset,u&&!p?(i.container.height(200*(i.count+i.cloneCount)+"%").css("position","absolute").width("100%"),setTimeout(function(){i.newSlides.css({display:"block"}),i.doMath(),i.viewport.height(i.h),i.setProps(a*i.h,"init")},"init"===t?100:0)):(i.container.width(200*(i.count+i.cloneCount)+"%"),i.setProps(a*i.computedW,"init"),setTimeout(function(){i.doMath(),i.newSlides.css({width:i.computedW,marginRight:i.computedM,float:"left",display:"block"}),i.vars.smoothHeight&&v.smoothHeight()},"init"===t?100:0)));p||i.slides.removeClass(s+"active-slide").eq(i.currentSlide).addClass(s+"active-slide"),i.vars.init(i)},i.doMath=function(){var e=i.slides.first(),t=i.vars.itemMargin,a=i.vars.minItems,n=i.vars.maxItems;i.w=void 0===i.viewport?i.width():i.viewport.width(),i.h=e.height(),i.boxPadding=e.outerWidth()-e.width(),p?(i.itemT=i.vars.itemWidth+t,i.itemM=t,i.minW=a?a*i.itemT:i.w,i.maxW=n?n*i.itemT-t:i.w,i.itemW=i.minW>i.w?(i.w-t*(a-1))/a:i.maxW<i.w?(i.w-t*(n-1))/n:i.vars.itemWidth>i.w?i.w:i.vars.itemWidth,i.visible=Math.floor(i.w/i.itemW),i.move=i.vars.move>0&&i.vars.move<i.visible?i.vars.move:i.visible,i.pagingCount=Math.ceil((i.count-i.visible)/i.move+1),i.last=i.pagingCount-1,i.limit=1===i.pagingCount?0:i.vars.itemWidth>i.w?i.itemW*(i.count-1)+t*(i.count-1):(i.itemW+t)*i.count-i.w-t):(i.itemW=i.w,i.itemM=t,i.pagingCount=i.count,i.last=i.count-1),i.computedW=i.itemW-i.boxPadding,i.computedM=i.itemM},i.update=function(e,t){i.doMath(),p||(e<i.currentSlide?i.currentSlide+=1:e<=i.currentSlide&&0!==e&&(i.currentSlide-=1),i.animatingTo=i.currentSlide),i.vars.controlNav&&!i.manualControls&&("add"===t&&!p||i.pagingCount>i.controlNav.length?v.controlNav.update("add"):("remove"===t&&!p||i.pagingCount<i.controlNav.length)&&(p&&i.currentSlide>i.last&&(i.currentSlide-=1,i.animatingTo-=1),v.controlNav.update("remove",i.last))),i.vars.directionNav&&v.directionNav.update()},i.addSlide=function(t,a){var n=e(t);i.count+=1,i.last=i.count-1,u&&h?void 0!==a?i.slides.eq(i.count-a).after(n):i.container.prepend(n):void 0!==a?i.slides.eq(a).before(n):i.container.append(n),i.update(a,"add"),i.slides=e(i.vars.selector+":not(.clone)",i),i.setup(),i.vars.added(i)},i.removeSlide=function(t){var a=isNaN(t)?i.slides.index(e(t)):t;i.count-=1,i.last=i.count-1,isNaN(t)?e(t,i.slides).remove():u&&h?i.slides.eq(i.last).remove():i.slides.eq(t).remove(),i.doMath(),i.update(a,"remove"),i.slides=e(i.vars.selector+":not(.clone)",i),i.setup(),i.vars.removed(i)},v.init()},e(window).blur(function(e){t=!1}).focus(function(e){t=!0}),e.flexslider.defaults={namespace:"flex-",selector:".slides > li",animation:"fade",easing:"swing",direction:"horizontal",reverse:!1,animationLoop:!0,smoothHeight:!1,startAt:0,slideshow:!0,slideshowSpeed:7e3,animationSpeed:600,initDelay:0,randomize:!1,fadeFirstSlide:!0,thumbCaptions:!1,pauseOnAction:!0,pauseOnHover:!1,pauseInvisible:!0,useCSS:!0,touch:!0,video:!1,controlNav:!0,directionNav:!0,prevText:"Previous",nextText:"Next",keyboard:!0,multipleKeyboard:!1,mousewheel:!1,pausePlay:!1,pauseText:"Pause",playText:"Play",controlsContainer:"",manualControls:"",customDirectionNav:"",sync:"",asNavFor:"",itemWidth:0,itemMargin:0,minItems:1,maxItems:0,move:0,allowOneSlide:!0,start:function(){},before:function(){},after:function(){},end:function(){},added:function(){},removed:function(){},init:function(){}},e.fn.flexslider=function(t){if(void 0===t&&(t={}),"object"==typeof t)return this.each(function(){var a=e(this),n=t.selector?t.selector:".slides > li",i=a.find(n);1===i.length&&!1===t.allowOneSlide||0===i.length?(i.fadeIn(400),t.start&&t.start(a)):void 0===a.data("flexslider")&&new e.flexslider(this,t)});var a=e(this).data("flexslider");switch(t){case"play":a.play();break;case"pause":a.pause();break;case"stop":a.stop();break;case"next":a.flexAnimate(a.getTarget("next"),!0);break;case"prev":case"previous":a.flexAnimate(a.getTarget("prev"),!0);break;default:"number"==typeof t&&a.flexAnimate(t,!0)}}}(jQuery),$(document).ready(function(){$("header .desktop ul li.top-nav-container a").click(function(e){e.preventDefault(),$("header .links a.language").removeClass("opened"),$("header .languages").removeAttr("style"),$("header .links a.bd-sites").removeClass("opened"),$("header .sites").removeAttr("style"),$("header .mega-menu").slideToggle(),$(this).toggleClass("opened")}),$("header .mega-menu a.close").click(function(e){e.preventDefault(),clearNav()}),$(document).mouseup(function(e){var t=$("header .mega-menu, header .desktop ul li:first-child a");t.is(e.target)||0!==t.has(e.target).length||(t.hide().removeAttr("style"),$("header .desktop ul li:first-child a").removeClass("opened"))}),$(".mega-menu .tab-content").hide(),$(".mega-menu .tab-wrap > .tab-content:first-child").show(),$(".mega-menu .tab-nav li:first-child").addClass("on"),$(".mega-menu .tab-nav li").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(".mega-menu .tab-wrap > .tab-content").hide();var t=$(this).find("a").attr("href");$(t).show()}),$(".mega-menu .accordion-list li a.trigger").click(function(e){e.preventDefault(),$(this).next().is(":visible")?($(this).next("ul").slideToggle(),$(this).toggleClass("opened")):($(".mega-menu .accordion-list li > ul").hide(),$(".mega-menu .accordion-list li a.trigger").removeClass("opened"),$(this).next("ul").slideToggle(),$(this).toggleClass("opened")),$(".mega-menu .accordion-list li > ul").mCustomScrollbar({alwaysShowScrollbar:1})}),$(".mobile .accordion-list li a.trigger").click(function(e){e.preventDefault(),$(this).toggleClass("opened"),$(this).next().slideToggle()}),$("header .links a.language").click(function(e){e.preventDefault(),$("header .desktop ul li:first-child a").removeClass("opened"),$("header .mega-menu").removeAttr("style"),$("header .languages").slideToggle(),$(this).toggleClass("opened")}),$("header .languages a.close").click(function(e){e.preventDefault(),clearNav()}),$(document).mouseup(function(e){var t=$("header .languages, header .links a.language");t.is(e.target)||0!==t.has(e.target).length||(t.hide().removeAttr("style"),$("header .links a.language").removeClass("opened"))}),$("header .links a.user").click(function(e){e.preventDefault(),$("header .desktop ul li:first-child a").removeClass("opened"),$("header .mega-menu").removeAttr("style"),$("header .user-options").slideToggle(),$(this).toggleClass("opened")}),$("header .user-options a.close").click(function(e){e.preventDefault(),clearNav()}),$(document).mouseup(function(e){var t=$("header .user-options, header .links a.user");t.is(e.target)||0!==t.has(e.target).length||(t.hide().removeAttr("style"),$("header .links a.user").removeClass("opened"))}),$("header .links a.bd-sites").click(function(e){e.preventDefault(),$("header .desktop ul li:first-child a").removeClass("opened"),$("header .mega-menu").removeAttr("style"),$("header .sites").slideToggle(),$(this).toggleClass("opened")}),$("header .sites a.close").click(function(e){e.preventDefault(),clearNav()}),$(document).mouseup(function(e){var t=$("header .sites, header .links a.bd-sites");t.is(e.target)||0!==t.has(e.target).length||(t.hide().removeAttr("style"),$("header .links a.bd-sites").removeClass("opened"))}),$("header a.show-search").click(function(e){e.preventDefault(),clearNav(),$("header form.search").show().animate({width:"100%"}),$("header nav.desktop.float-right").css({opacity:"0.2"})}),$(document).mouseup(function(e){var t=$("header form.search");t.is(e.target)||0!==t.has(e.target).length||(t.hide().removeAttr("style"),$("header nav.desktop.float-right").removeAttr("style"))}),$("nav.sub .sub-menu-drop .tab-content").hide(),$("nav.sub .sub-menu-drop .tab-wrap > .tab-content:first-child").show(),$("nav.sub .sub-menu-drop .tab-nav li:first-child").addClass("on"),$("nav.sub .sub-menu-drop .tab-nav li").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on");var t=$(this).find("a").attr("href");$(t).siblings().hide(),$(t).show()}),$("nav.sub a.show-search").click(function(e){e.preventDefault(),clearNav(),$("nav.sub form.search").show().animate({width:"100%"}),$("nav.sub ul, nav.sub .label").css({opacity:"0.2"})}),$(document).mouseup(function(e){var t=$("nav.sub form.search");t.is(e.target)||0!==t.has(e.target).length||(t.hide().removeAttr("style"),$("nav.sub ul, nav.sub .label").removeAttr("style"))}),$("nav.sub a.show-cart").click(function(e){e.preventDefault(),clearNav(),$("nav.sub .mini-cart-wrap").fadeIn(200)}),$(document).mouseup(function(e){var t=$("nav.sub .mini-cart-wrap");t.is(e.target)||0!==t.has(e.target).length||t.hide().removeAttr("style")}),$("header a.mobile-toggle").click(function(e){e.preventDefault(),$("nav.mobile").slideToggle(),$(this).toggleClass("opened")}),$(".crumbs li.on > a").click(function(e){e.preventDefault(),$(this).parent().find("ul").slideToggle(),$(this).parent().toggleClass("opened")}),$(document).mouseup(function(e){var t=$(".crumbs li.on > ul, .crumbs li.on > a");t.is(e.target)||0!==t.has(e.target).length||(t.hide().removeAttr("style"),$(".crumbs li.on").removeClass("opened"))}),$("nav.solution-indicator a.indicator-toggle").click(function(e){e.preventDefault(),$("nav.solution-indicator .section-list").slideToggle(),$(this).toggleClass("opened")}),$("nav.solution-indicator .section-list > a").click(function(e){e.preventDefault();var t=$(this).attr("href");$("html, body").animate({scrollTop:$(t).offset().top-59},500),$(this).parent().slideUp(),$("nav.solution-indicator a.indicator-toggle").removeClass("opened")}),$(".timeline .sections > a, .timeline .icons > a, a.scroll-to-link").click(function(e){e.preventDefault();var t=$(this).attr("href");$("html, body").animate({scrollTop:$(t).offset().top-59},500)}),$("#chat a.chat-toggle").click(function(e){e.preventDefault(),$("#chat .content").toggleClass("opened"),$(this).toggleClass("opened")}),$("#top").click(function(e){e.preventDefault(),$("html, body").animate({scrollTop:0},"500")}),$("a.scroll-button, a.scroll-link").click(function(e){e.preventDefault();var t=$(this).attr("href");$("html, body").animate({scrollTop:$(t).offset().top-180},500)}),$(".scroll-to-section li a").click(function(e){e.preventDefault(),$(this).parent().siblings().removeClass("on");var t=$(this).attr("href");$("html, body").animate({scrollTop:$(t).offset().top-59},500)}),$(".mobile-scroll-to-section li a").click(function(e){e.preventDefault(),$(this).parent().siblings().removeClass("on");var t=$(this).attr("href");$("html, body").animate({scrollTop:$(t).offset().top-0},500),$("header a.mobile-toggle").removeClass("opened"),$("nav.mobile").hide()}),$(".scroll-to-nav a").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on");var t=$(this).attr("href");$("html, body").animate({scrollTop:$(t).offset().top-59},500)}),$(".search-suggestions a.close").click(function(e){e.preventDefault(),$(".search-suggestions").slideUp()}),$(".filter-columns a.filter-toggle").click(function(e){e.preventDefault(),$(this).toggleClass("closed"),$(this).next().children(".filter-columns-wrap").slideToggle(200)}),$(".way-arrow a.spot").click(function(e){e.preventDefault(),$(".way-arrow .spot-wrap").removeClass("opened"),$(this).parent().toggleClass("opened")});var e=0;$(".turn-right").on("click",function(t){t.preventDefault(),e+=120,$(".wheel").css({transform:"rotate("+e+"deg)"}),$(".wheel .circle").css({transform:"rotate("+-e+"deg)"}),$(".wheel .text").hide().delay(600).fadeIn(),$(".hub .spoke").hide().delay(600).fadeIn();var a=$(".wheel .circle.on");a.removeClass("on"),a.prev().addClass("on"),0==a.prev().length&&$(".wheel .circle").last().addClass("on");var n=$(".wheel .circle.on").attr("rel");$(".wheel-content .content").hide(),$("#"+n).fadeIn(1200);var i=$(".wheel .circle.reverse");i.removeClass("reverse"),i.prev().addClass("reverse"),0==i.prev().length&&$(".wheel .circle").last().addClass("reverse"),$(".wheel-wrap").removeClass("center")}),$(".turn-left").on("click",function(t){t.preventDefault(),e-=120,$(".wheel").css({transform:"rotate("+e+"deg)"}),$(".wheel .circle").css({transform:"rotate("+-e+"deg)"}),$(".wheel .text").hide().delay(600).fadeIn(),$(".hub .spoke").hide().delay(600).fadeIn();var a=$(".wheel .circle.on");a.removeClass("on"),a.next().addClass("on"),0==a.next().length&&$(".wheel .circle").first().addClass("on");var n=$(".wheel .circle.on").attr("rel");$(".wheel-content .content").hide(),$("#"+n).fadeIn(1200);var i=$(".wheel .circle.reverse");i.removeClass("reverse"),i.next().addClass("reverse"),0==i.next().length&&$(".wheel .circle").first().addClass("reverse"),$(".wheel-wrap").removeClass("center")}),$(".center-spot").on("click",function(e){e.preventDefault();var t=$(this).attr("rel");$(".wheel-wrap").addClass("center"),$(".wheel-content .content").hide(),$("#"+t).fadeIn(1200)}),$(".right-spot").on("click",function(e){e.preventDefault();var t=$(".wheel .circle.on").attr("rel");$(".wheel-content .content").hide(),$("#"+t).fadeIn(1200),$(".wheel-wrap").removeClass("center")}),$(".tip-toggle").on("click",function(e){e.preventDefault(),$(this).hasClass("opened")?($(this).toggleClass("opened"),$(this).parent().next().slideToggle(200)):($(".tip-toggle").removeClass("opened"),$(".tip-toggle").parent().next().hide(),$(this).toggleClass("opened"),$(this).parent().next().slideToggle(200))}),$(".star-toggle").on("click",function(e){e.preventDefault(),$(this).parent().hasClass("opened")?($(this).parent().toggleClass("opened"),$(this).next().fadeToggle(200)):($(".star-toggle").parent().removeClass("opened"),$(".star-toggle").next().fadeOut(200),$(this).parent().toggleClass("opened"),$(this).next().fadeToggle(200))})}),$(window).resize(function(){timelineBreakpointCheck(),desktopNavBreakpointCheck(),mobileNavBreakpointCheck()}),$(document).scroll(function(){$(this).scrollTop()>1?$("#page").addClass("scrolled"):$("#page").removeClass("scrolled")}),$(window).scroll(function(){$(this).scrollTop()>100?$("#top").addClass("fixed"):$("#top").removeClass("fixed"),$(this).scrollTop()>60?$("nav.solution-indicator").fadeIn():$("nav.solution-indicator").fadeOut();var e=$(this).scrollTop();$(".timeline-point").each(function(){var t=$(this).offset().top-60,a=$(this).attr("rel");t<=e&&($("nav.solution-indicator").attr("id","indicate-"+a),$(".timeline-section").attr("id",a))}),$(".scroll-to-point").each(function(){var t=$(this).offset().top-60,a=$(this).attr("rel");t<=e&&($(".scroll-to-section").attr("id",a),$(".scroll-to-wrap").attr("id",a))}),0===$(window).scrollTop()&&($(".scroll-to-section").removeAttr("id"),$(".scroll-to-wrap").removeAttr("id"))}),$(document).ready(function(){if($("#alert a").click(function(e){e.preventDefault(),$("#alert").slideToggle()}),$("table.hover tr").not("tr.divider").click(function(e){e.preventDefault(),$(this).toggleClass("on")}),$(".more-content").hide(),$(".more-button").click(function(e){e.preventDefault();var t=$(this).attr("rel");$(".more-content."+t).slideToggle(),$(this).toggleClass("less")}),$(".accordions .copy, .accordions .nested, .accordion .copy, .accordion .nested").hide(),$(document.body).on("click",".accordions .top.toggle, .accordion .top.toggle",function(e){e.preventDefault(),$(this).toggleClass("opened"),$(this).next().slideToggle(),$(".copy .flexslider").resize()}),$(document.body).on("click",".accordions .top .toggle, .accordion .top .toggle",function(e){e.preventDefault(),$(this).toggleClass("opened"),$(this).parent().next().slideToggle()}),$(".ordered-accordion-copy").hide(),$(document.body).on("click",".ordered-accordion .plus-minus",function(e){e.preventDefault(),$(this).toggleClass("opened"),$(this).parent().next().slideToggle()}),$(".faqs .answer").hide(),$(".faqs .question").css("cursor","pointer"),$(".faqs .question").click(function(e){e.preventDefault(),$(this).toggleClass("opened"),$(this).next().slideToggle()}),$(".product-block, .person-block, .js-link").css("cursor","pointer"),$(".product-block, .person-block, .js-link").click(function(e){e.preventDefault(),window.location=$(this).find("a.go-to-link").attr("href")}),$(".slide-select-list .toggle").click(function(e){e.preventDefault(),$(this).toggleClass("opened"),$(this).next().slideToggle()}),$(".button-drop .toggle").click(function(e){e.preventDefault(),$(this).toggleClass("opened"),$(this).next().slideToggle()}),$(".mobile-side-nav-toggle span").click(function(e){e.preventDefault(),$(this).toggleClass("opened"),$(this).parent().next().slideToggle()}),$(".side-nav-toggle h5 a").click(function(e){e.preventDefault(),$(this).toggleClass("opened"),$(this).parent().next().slideToggle()}),$(".promotion a.close").click(function(e){e.preventDefault(),$(this).parent().fadeOut()}),$(".switch a").click(function(e){e.preventDefault(),$(this).parent().children().removeClass("active"),$(this).addClass("active")}),$(".scroll-left .on").length){var e=$(".scroll-left .on").position().left;$(".scroll-left").scrollLeft(e-15)}if($(".select-tabs .toggle").click(function(e){e.preventDefault(),$(this).next().slideToggle(),$(this).toggleClass("opened")}),$(".select-tabs .select-tab-wrap .tab").hide(),$(".select-tabs .select-tab-wrap > .tab:first-child").show(),$(".select-tab .options a").click(function(e){e.preventDefault();var t=$(this).text();$(this).parent().prev(".toggle").removeClass("opened"),$(this).closest(".select-tab").find(".default").text(t),$(".select-tabs .select-tab-wrap").children().hide();var a=$(this).attr("href");$(a).show()}),$(document).mouseup(function(e){var t=$(".select-tab .toggle.opened");t.is(e.target)||0!==t.has(e.target).length||($(".select-tab .toggle").removeClass("opened"),$(".select-tab .options").slideUp())}),$(".content-tabs .toggle").click(function(e){e.preventDefault(),$(this).next().slideToggle(),$(this).toggleClass("opened")}),$(".content-tabs .content-tab-wrap .tab").hide(),$(".content-tabs .content-tab-wrap > .tab:first-child").show(),$(".content-tab-nav .options a:first-child").addClass("on"),$(".content-tab-nav .options a").click(function(e){e.preventDefault();var t=$(this).text();$(this).parent().prev(".toggle").removeClass("opened"),$(this).closest(".content-tab-nav").find(".default").text(t),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(".content-tabs .content-tab-wrap").children().hide();var a=$(this).attr("href");$(a).show(),$(".content-tabs .flexslider").resize()}),$(document).mouseup(function(e){var t=$(".content-tab-nav .toggle.opened");t.is(e.target)||0!==t.has(e.target).length||($(".content-tab-nav .toggle").removeClass("opened"),$(".content-tab-nav .options").slideUp())}),$(".tabs .tab-wrap .tab").hide(),$(".product-tabs .product-tab").hide(),$(document.body).on("click",".tabs .tab-nav a",function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(this).parent().next().children().hide();var t=$(this).attr("href");$(t).show()}),$(".product-tabs .links a").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(".product-tab").hide();var t=$(this).attr("href");$(t).show()}),window.location.hash){if($(".tabs")[0]&&($('.tabs .tab-nav a[href="'+window.location.hash+'"]').addClass("on"),$(".tabs .tab-wrap "+window.location.hash).show(),$("html, body").animate({scrollTop:$(".tabs .tab-nav").offset().top-80})),$(".product-tabs")[0]){var t=$(".product-tabs .links").offset().top;$('.product-tabs .links a[href="'+window.location.hash+'"]').addClass("on"),$(".product-tabs .tab-wrap "+window.location.hash).show(),$("html, body").animate({scrollTop:t-100})}}else $(".tabs .tab-wrap > .tab:first-child").show(),$(".tabs .tab-nav a:first-child").addClass("on"),$(".product-tabs .tab-wrap > .product-tab:first-child").show(),$(".product-tabs .links a:first-child").addClass("on");$(".home-tabs .tab-wrap > .tab:first-child").addClass("show-tab"),$(".home-tabs .tab-nav a:first-child").addClass("on"),$(".home-tabs .tab-nav a").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(this).parent().next().children().removeClass("show-tab");var t=$(this).attr("href");$(t).addClass("show-tab")}),$(".background-color-tabs .tab-wrap > .tab").hide(),$(".background-color-tabs .tab-wrap > .tab:first-child").show(),$(".background-color-tabs .tab-nav a").click(function(e){e.preventDefault(),$(".background-color-tabs .tab-wrap").children().hide();var t=$(this).attr("href");$(t).show()}),$(".background-tabs .tab-wrap > .tab").hide(),$(".background-tabs .tab-wrap > .tab:first-child").show(),$(".background-tabs .tab-nav a:first-child").addClass("on"),$(".background-tabs .tab-nav a").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(".background-tabs .tab-wrap").children().hide();var t=$(this).attr("href");$(t).show()}),$(".section-tabs .tab-wrap .tab").hide(),$(".section-tabs .tab-wrap > .tab.show-tab").show(),$(".section-tabs .tab-nav a.show-tab").addClass("on"),$(".section-tabs .tab-nav a").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(".section-tabs .tab-wrap").children().hide();var t=$(this).attr("href");$(t).show()}),$(".tab-block .tab-nav li:first-child").addClass("on"),$(".tab-block .tab-nav li").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(this).parent().next().children().hide();var t=$(this).find("a").attr("href");$(t).show()}),$(".tab-explore .tab-nav li:first-child").addClass("on"),$(".tab-explore .tab-nav li").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(this).parent().next().children().hide();var t=$(this).find("a").attr("href");$(t).show();var a=$(".tab-explore .tab-wrap .tab:visible").attr("id");$(".tab-explore .tab-nav .first").removeAttr("rel").attr("rel",a)}),$(".tour-tabs .tour-tab-nav li:first-child").addClass("on"),$(".tour-tabs .tour-tab-nav li").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(this).parent().next().children().hide();var t=$(this).find("a").attr("href");$(t).show()}),$(".hot-spot-content .spot").first().show(),$(".hot-spots a.spot-link").first().addClass("on"),$(".hot-spots a.spot-link").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(".hot-spot-content .spot").hide();var t=$(this).attr("href");$(t).show(),$("html, body").animate({scrollTop:$(t).offset().top-79},500)}),$(".vid-wrap.swap").hide(),$("a.vid-swap").click(function(e){e.preventDefault();var t=$(this).attr("href");$(t).show(),$(t).next().hide()}),$(".modal-tabs .modal-tab-nav li:first-child").addClass("on"),$(".modal-tabs .modal-tab-nav li").click(function(e){e.preventDefault(),$(this).siblings().removeClass("on"),$(this).addClass("on"),$(this).parent().next().find(".tab-wrap").children().hide();var t=$(this).find("a").attr("href");$(t).show()}),$(".section-modal-link").click(function(e){e.preventDefault();var t=$(this).attr("href");$(t).css({top:$(this).offset().top+40}).fadeIn(),$(t+" .indicator").css({left:$(this).offset().left+$(this).width()/2-18}).fadeIn()}),$(".section-modal-box").click(function(e){e.preventDefault();var t=$(this).attr("rel");$(".section-modal").hide(),$("#"+t).css({top:$(this).offset().top+$(this).height()+60}).fadeIn(),$("#"+t+" .indicator").css({left:$(this).offset().left+$(this).width()/2+15}).fadeIn()}),$(".close-section-modal").click(function(e){e.preventDefault(),$(this).parent().fadeOut()}),$(window).resize(function(){$(".section-modal").hide()}),$(".tabs .scroll-tabs-wrap .tab").hide(),$(".tabs .scroll-tabs-wrap > .tab:first-child").show(),$(".scroll-tabs").scrollTabs({scroll_distance:350,scroll_duration:350,left_arrow_size:20,right_arrow_size:20,click_callback:function(e){$(this).siblings().removeClass("on"),$(this).addClass("on"),$(".scroll-tabs-wrap").children().hide();var t=$(this).attr("rel");$("#"+t).show()}}),$(".scroll_tab_first").addClass("on"),$("#to").datepicker({showOn:"button",buttonText:" "}),$("#from").datepicker({showOn:"button",buttonText:" "}),$("#time-frame").slider({range:!0,min:1,max:24,values:[9,13],step:1,slide:function(e,t){$("#time-frame-amount").val(t.values[0]+" - "+t.values[1]+" hrs")}}),$("#time-frame-amount").val($("#time-frame").slider("values",0)+" - "+$("#time-frame").slider("values",1)+" hrs"),$("#range").slider({range:"min",value:6,min:1,max:24,step:1,slide:function(e,t){$("#range-amount").val(t.value+" hrs")}}),$("#range-amount").val($("#range").slider("value")+"hrs"),$(".scroll-filters, .scroll-quickshop, .scroll-cart, .scroll-list, .scroll-language, .scroll-content, .tab-block .tab-wrap .tab, .tab-explore .tab-wrap .tab").mCustomScrollbar({alwaysShowScrollbar:1,scrollButtons:{enable:!0}}),$("select").SumoSelect(),$(".match-height").matchHeight(),$(".track-height").matchHeight({byRow:!1})}),$(window).on("load",function(e){equalHeight(),navHeightCheck(),sourceOrder(),crumbsCheck()}),$(window).resize(function(){window.matchMedia("(min-width:768px)").matches&&($(".side-nav").removeAttr("style"),$(".mobile-side-nav-toggle span").removeClass("opened")),$("div.equals div.equal").removeAttr("style"),equalHeight(),navHeightCheck(),sourceOrder(),crumbsCheck()});

function pluploadQueueWidget(e,t,i,n,r,o){$(document).ready(function(){$(function(){$(e).pluploadQueue({runtimes:"html5",multi_selection:!0,dragdrop:!0,url:t,chunk_size:"128kb",unique_names:!0,rename:!0,multipart:!0,file_data_name:"file",multiple_queues:!0,filters:{max_file_size:"1000mb",mime_types:[{title:"Files",extensions:n}],prevent_duplicates:!0},init:{FilesAdded:function(e,n){$.ajaxSetup({timeout:12e4}),plupload.each(n,function(n){$.ajax({type:"POST",url:t,method:"POST",data:$.extend({src_filename:n.name,name:n.name,filesize:n.size,upload_check:1},i),success:function(t,i,r){void 0!==t.error&&t.error?(e.removeFile(n),showMessage("error",t.error)):console.log(t.msg)},error:function(e,t,i){console.log("Your files have not been uploaded.")},cache:!1,dataType:"json"})})},BeforeUpload:function(e,t){e.settings.multipart_params=$.extend({src_filename:t.name,filesize:t.size},i)},FileUploaded:function(e,t,i){$.ajax({type:"GET",url:o,success:function(e){$(r).html(e.output)},cache:!1,dataType:"json"})}}})})})}!function(e,t){var i=function(){var e={};return function(){!function(e,t){"use strict";function i(e,i,n){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(i===t)throw"invalid module definition, dependencies must be specified";if(n===t)throw"invalid module definition, definition function must be specified";!function(e,t){for(var i,n=[],a=0;a<e.length;++a){if(!(i=o[e[a]]||r(e[a])))throw"module definition dependecy not found: "+e[a];n.push(i)}t.apply(null,n)}(i,function(){o[e]=n.apply(null,arguments)})}function n(e){return!!o[e]}function r(t){for(var i=e,n=t.split(/[.\/]/),r=0;r<n.length;++r){if(!i[n[r]])return;i=i[n[r]]}return i}var o={};i("moxie/core/utils/Basic",[],function(){function e(e){return void 0===e?"undefined":null===e?"null":e.nodeType?"node":{}.toString.call(e).match(/\s([a-z|A-Z]+)/)[1].toLowerCase()}function t(){return n(!1,!1,arguments)}function i(i){switch(e(i)){case"array":return Array.prototype.slice.call(i);case"object":return t({},i)}return i}function n(t,a,s){var u,l=s[0];return r(s,function(s,c){c>0&&r(s,function(r,s){var c=-1!==o(e(r),["array","object"]);return!!(r===u||t&&l[s]===u)||(c&&a&&(r=i(r)),void(e(l[s])===e(r)&&c?n(t,a,[l[s],r]):l[s]=r))})}),l}function r(e,t){var i,n,r,o;if(e){try{i=e.length}catch(e){i=o}if(i===o||"number"!=typeof i){for(n in e)if(e.hasOwnProperty(n)&&!1===t(e[n],n))return}else for(r=0;i>r;r++)if(!1===t(e[r],r))return}}function o(e,t){if(t){if(Array.prototype.indexOf)return Array.prototype.indexOf.call(t,e);for(var i=0,n=t.length;n>i;i++)if(t[i]===e)return i}return-1}var a=function(){var e=0;return function(t){var i,n=(new Date).getTime().toString(32);for(i=0;5>i;i++)n+=Math.floor(65535*Math.random()).toString(32);return(t||"o_")+n+(e++).toString(32)}}();return{guid:a,typeOf:e,extend:t,extendIf:function(){return n(!0,!1,arguments)},extendImmutable:function(){return n(!1,!0,arguments)},extendImmutableIf:function(){return n(!0,!0,arguments)},clone:function(t){switch(e(t)){case"array":return n(!1,!0,[[],t]);case"object":return n(!1,!0,[{},t]);default:return t}},inherit:function(e,t){function i(){this.constructor=e}for(var n in t)({}).hasOwnProperty.call(t,n)&&(e[n]=t[n]);return i.prototype=t.prototype,e.prototype=new i,e.parent=t.prototype,e},each:r,isEmptyObj:function(t){var i;if(!t||"object"!==e(t))return!0;for(i in t)return!1;return!0},inSeries:function(t,i){var n=t.length;"function"!==e(i)&&(i=function(){}),t&&t.length||i(),function r(o){"function"===e(t[o])&&t[o](function(e){++o<n&&!e?r(o):i(e)})}(0)},inParallel:function(e,t){var i=0,n=e.length,o=new Array(n);r(e,function(e,r){e(function(e){if(e)return t(e);var a=[].slice.call(arguments);a.shift(),o[r]=a,++i===n&&(o.unshift(null),t.apply(this,o))})})},inArray:o,arrayDiff:function(t,i){var n=[];for(var r in"array"!==e(t)&&(t=[t]),"array"!==e(i)&&(i=[i]),t)-1===o(t[r],i)&&n.push(t[r]);return!!n.length&&n},arrayIntersect:function(e,t){var i=[];return r(e,function(e){-1!==o(e,t)&&i.push(e)}),i.length?i:null},toArray:function(e){var t,i=[];for(t=0;t<e.length;t++)i[t]=e[t];return i},trim:function(e){return e?String.prototype.trim?String.prototype.trim.call(e):e.toString().replace(/^\s*/,"").replace(/\s*$/,""):e},sprintf:function(e){var t=[].slice.call(arguments,1);return e.replace(/%([a-z])/g,function(e,i){var n=t.shift();switch(i){case"s":return n+"";case"d":return parseInt(n,10);case"f":return parseFloat(n);case"c":return"";default:return n}})},parseSizeStr:function(e){if("string"!=typeof e)return e;var t,i={t:1099511627776,g:1073741824,m:1048576,k:1024};return e=/^([0-9\.]+)([tmgk]?)$/.exec(e.toLowerCase().replace(/[^0-9\.tmkg]/g,"")),t=e[2],e=+e[1],i.hasOwnProperty(t)&&(e*=i[t]),Math.floor(e)},delay:function(e,t){var i=this;setTimeout(function(){e.call(i)},t||1)}}}),i("moxie/core/utils/Encode",[],function(){var e=function(e){return unescape(encodeURIComponent(e))},t=function(e){return decodeURIComponent(escape(e))};return{utf8_encode:e,utf8_decode:t,atob:function(e,i){if("function"==typeof window.atob)return i?t(window.atob(e)):window.atob(e);var n,r,o,a,s,u,l,c,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",p=0,m=0,f="",h=[];if(!e)return e;e+="";do{a=d.indexOf(e.charAt(p++)),s=d.indexOf(e.charAt(p++)),u=d.indexOf(e.charAt(p++)),l=d.indexOf(e.charAt(p++)),n=255&(c=a<<18|s<<12|u<<6|l)>>16,r=255&c>>8,o=255&c,h[m++]=64==u?String.fromCharCode(n):64==l?String.fromCharCode(n,r):String.fromCharCode(n,r,o)}while(p<e.length);return f=h.join(""),i?t(f):f},btoa:function(t,i){if(i&&(t=e(t)),"function"==typeof window.btoa)return window.btoa(t);var n,r,o,a,s,u,l,c,d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",p=0,m=0,f="",h=[];if(!t)return t;do{n=t.charCodeAt(p++),r=t.charCodeAt(p++),o=t.charCodeAt(p++),a=63&(c=n<<16|r<<8|o)>>18,s=63&c>>12,u=63&c>>6,l=63&c,h[m++]=d.charAt(a)+d.charAt(s)+d.charAt(u)+d.charAt(l)}while(p<t.length);f=h.join("");var g=t.length%3;return(g?f.slice(0,g-3):f)+"===".slice(g||3)}}}),i("moxie/core/utils/Env",["moxie/core/utils/Basic"],function(e){var i=function(e){var t="function",i="object",n="name",r="version",o={has:function(e,t){return-1!==t.toLowerCase().indexOf(e.toLowerCase())},lowerize:function(e){return e.toLowerCase()}},a={rgx:function(){for(var n,r,o,a,s,u,l,c=0,d=arguments;c<d.length;c+=2){var p=d[c],m=d[c+1];if(void 0===n)for(a in n={},m)typeof(s=m[a])===i?n[s[0]]=e:n[s]=e;for(r=o=0;r<p.length;r++)if(u=p[r].exec(this.getUA())){for(a=0;a<m.length;a++)l=u[++o],typeof(s=m[a])===i&&s.length>0?2==s.length?n[s[0]]=typeof s[1]==t?s[1].call(this,l):s[1]:3==s.length?n[s[0]]=typeof s[1]!==t||s[1].exec&&s[1].test?l?l.replace(s[1],s[2]):e:l?s[1].call(this,l,s[2]):e:4==s.length&&(n[s[0]]=l?s[3].call(this,l.replace(s[1],s[2])):e):n[s]=l||e;break}if(u)break}return n},str:function(t,n){for(var r in n)if(typeof n[r]===i&&n[r].length>0){for(var a=0;a<n[r].length;a++)if(o.has(n[r][a],t))return"?"===r?e:r}else if(o.has(n[r],t))return"?"===r?e:r;return t}},s={browser:{oldsafari:{major:{1:["/8","/1","/3"],2:"/4","?":"/"},version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}}},device:{sprint:{model:{"Evo Shift 4G":"7373KT"},vendor:{HTC:"APA",Sprint:"Sprint"}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2000:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",RT:"ARM"}}}},u={browser:[[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]+).+version\/([\w\.-]+)/i,/(opera).+version\/([\w\.]+)/i,/(opera)[\/\s]+([\w\.]+)/i],[n,r],[/\s(opr)\/([\w\.]+)/i],[[n,"Opera"],r],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]+)*/i,/(avant\s|iemobile|slim|baidu)(?:browser)?[\/\s]?([\w\.]*)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(rekonq)\/([\w\.]+)*/i,/(chromium|flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi)\/([\w\.-]+)/i],[n,r],[/(trident).+rv[:\s]([\w\.]+).+like\sgecko/i],[[n,"IE"],r],[/(edge)\/((\d+)?[\w\.]+)/i],[n,r],[/(yabrowser)\/([\w\.]+)/i],[[n,"Yandex"],r],[/(comodo_dragon)\/([\w\.]+)/i],[[n,/_/g," "],r],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i,/(uc\s?browser|qqbrowser)[\/\s]?([\w\.]+)/i],[n,r],[/(dolfin)\/([\w\.]+)/i],[[n,"Dolphin"],r],[/((?:android.+)crmo|crios)\/([\w\.]+)/i],[[n,"Chrome"],r],[/XiaoMi\/MiuiBrowser\/([\w\.]+)/i],[r,[n,"MIUI Browser"]],[/android.+version\/([\w\.]+)\s+(?:mobile\s?safari|safari)/i],[r,[n,"Android Browser"]],[/FBAV\/([\w\.]+);/i],[r,[n,"Facebook"]],[/version\/([\w\.]+).+?mobile\/\w+\s(safari)/i],[r,[n,"Mobile Safari"]],[/version\/([\w\.]+).+?(mobile\s?safari|safari)/i],[r,n],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[n,[r,a.str,s.browser.oldsafari.version]],[/(konqueror)\/([\w\.]+)/i,/(webkit|khtml)\/([\w\.]+)/i],[n,r],[/(navigator|netscape)\/([\w\.-]+)/i],[[n,"Netscape"],r],[/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix)\/([\w\.-]+)/i,/(mozilla)\/([\w\.]+).+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]+)*/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[n,r]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[r,[n,"EdgeHTML"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[n,r],[/rv\:([\w\.]+).*(gecko)/i],[r,n]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[n,r],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*|windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)/i],[n,[r,a.str,s.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[n,"Windows"],[r,a.str,s.os.windows.version]],[/\((bb)(10);/i],[[n,"BlackBerry"],r],[/(blackberry)\w*\/?([\w\.]+)*/i,/(tizen)[\/\s]([\w\.]+)/i,/(android|webos|palm\os|qnx|bada|rim\stablet\sos|meego|contiki)[\/\s-]?([\w\.]+)*/i,/linux;.+(sailfish);/i],[n,r],[/(symbian\s?os|symbos|s60(?=;))[\/\s-]?([\w\.]+)*/i],[[n,"Symbian"],r],[/\((series40);/i],[n],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[n,"Firefox OS"],r],[/(nintendo|playstation)\s([wids3portablevu]+)/i,/(mint)[\/\s\(]?(\w+)*/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|[open]*suse|gentoo|arch|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus)[\/\s-]?([\w\.-]+)*/i,/(hurd|linux)\s?([\w\.]+)*/i,/(gnu)\s?([\w\.]+)*/i],[n,r],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[n,"Chromium OS"],r],[/(sunos)\s?([\w\.]+\d)*/i],[[n,"Solaris"],r],[/\s([frentopc-]{0,4}bsd|dragonfly)\s?([\w\.]+)*/i],[n,r],[/(ip[honead]+)(?:.*os\s*([\w]+)*\slike\smac|;\sopera)/i],[[n,"iOS"],[r,/_/g,"."]],[/(mac\sos\sx)\s?([\w\s\.]+\w)*/i,/(macintosh|mac(?=_powerpc)\s)/i],[[n,"Mac OS"],[r,/_/g,"."]],[/((?:open)?solaris)[\/\s-]?([\w\.]+)*/i,/(haiku)\s(\w+)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.]*)*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms)/i,/(unix)\s?([\w\.]+)*/i],[n,r]]};return function(e){var t=e||(window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:"");this.getBrowser=function(){return a.rgx.apply(this,u.browser)},this.getEngine=function(){return a.rgx.apply(this,u.engine)},this.getOS=function(){return a.rgx.apply(this,u.os)},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS()}},this.getUA=function(){return t},this.setUA=function(e){return t=e,this},this.setUA(t)}}(),n=function(){var i={access_global_ns:function(){return!!window.moxie},define_property:!1,create_canvas:function(){var e=document.createElement("canvas"),t=!(!e.getContext||!e.getContext("2d"));return i.create_canvas=t,t},return_response_type:function(t){try{if(-1!==e.inArray(t,["","text","document"]))return!0;if(window.XMLHttpRequest){var i=new XMLHttpRequest;if(i.open("get","/"),"responseType"in i)return i.responseType=t,i.responseType===t}}catch(e){}return!1},use_blob_uri:function(){var e=window.URL;return i.use_blob_uri=e&&"createObjectURL"in e&&"revokeObjectURL"in e&&("IE"!==o.browser||o.verComp(o.version,"11.0.46",">=")),i.use_blob_uri},use_data_uri:function(){var e=new Image;return e.onload=function(){i.use_data_uri=1===e.width&&1===e.height},setTimeout(function(){e.src="data:image/gif;base64,R0lGODlhAQABAIAAAP8AAAAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw=="},1),!1}(),use_data_uri_over32kb:function(){return i.use_data_uri&&("IE"!==o.browser||o.version>=9)},use_data_uri_of:function(e){return i.use_data_uri&&33e3>e||i.use_data_uri_over32kb()},use_fileinput:function(){if(navigator.userAgent.match(/(Android (1.0|1.1|1.5|1.6|2.0|2.1))|(Windows Phone (OS 7|8.0))|(XBLWP)|(ZuneWP)|(w(eb)?OSBrowser)|(webOS)|(Kindle\/(1.0|2.0|2.5|3.0))/))return!1;var e=document.createElement("input");return e.setAttribute("type","file"),i.use_fileinput=!e.disabled},use_webgl:function(){var e,n=document.createElement("canvas"),r=null;try{r=n.getContext("webgl")||n.getContext("experimental-webgl")}catch(e){}return r||(r=null),e=!!r,i.use_webgl=e,n=t,e}};return function(t){var n=[].slice.call(arguments);return n.shift(),"function"===e.typeOf(i[t])?i[t].apply(this,n):!!i[t]}}(),r=(new i).getResult(),o={can:n,uaParser:i,browser:r.browser.name,version:r.browser.version,os:r.os.name,osVersion:r.os.version,verComp:function(e,t,i){var n=0,r=0,o=0,a={dev:-6,alpha:-5,a:-5,beta:-4,b:-4,RC:-3,rc:-3,"#":-2,p:1,pl:1},s=function(e){return(e=(e=(""+e).replace(/[_\-+]/g,".")).replace(/([^.\d]+)/g,".$1.").replace(/\.{2,}/g,".")).length?e.split("."):[-8]},u=function(e){return e?isNaN(e)?a[e]||-7:parseInt(e,10):0};for(e=s(e),t=s(t),r=Math.max(e.length,t.length),n=0;r>n;n++)if(e[n]!=t[n]){if(e[n]=u(e[n]),t[n]=u(t[n]),e[n]<t[n]){o=-1;break}if(e[n]>t[n]){o=1;break}}if(!i)return o;switch(i){case">":case"gt":return o>0;case">=":case"ge":return o>=0;case"<=":case"le":return 0>=o;case"==":case"=":case"eq":return 0===o;case"<>":case"!=":case"ne":return 0!==o;case"":case"<":case"lt":return 0>o;default:return null}},swf_url:"../flash/Moxie.swf",xap_url:"../silverlight/Moxie.xap",global_event_dispatcher:"moxie.core.EventTarget.instance.dispatchEvent"};return o.OS=o.os,o}),i("moxie/core/Exceptions",["moxie/core/utils/Basic"],function(e){function t(e,t){var i;for(i in e)if(e[i]===t)return i;return null}return{RuntimeError:function(){function i(e,i){this.code=e,this.name=t(n,e),this.message=this.name+(i||": RuntimeError "+this.code)}var n={NOT_INIT_ERR:1,EXCEPTION_ERR:3,NOT_SUPPORTED_ERR:9,JS_ERR:4};return e.extend(i,n),i.prototype=Error.prototype,i}(),OperationNotAllowedException:function(){function t(e){this.code=e,this.name="OperationNotAllowedException"}return e.extend(t,{NOT_ALLOWED_ERR:1}),t.prototype=Error.prototype,t}(),ImageError:function(){function i(e){this.code=e,this.name=t(n,e),this.message=this.name+": ImageError "+this.code}var n={WRONG_FORMAT:1,MAX_RESOLUTION_ERR:2,INVALID_META_ERR:3};return e.extend(i,n),i.prototype=Error.prototype,i}(),FileException:function(){function i(e){this.code=e,this.name=t(n,e),this.message=this.name+": FileException "+this.code}var n={NOT_FOUND_ERR:1,SECURITY_ERR:2,ABORT_ERR:3,NOT_READABLE_ERR:4,ENCODING_ERR:5,NO_MODIFICATION_ALLOWED_ERR:6,INVALID_STATE_ERR:7,SYNTAX_ERR:8};return e.extend(i,n),i.prototype=Error.prototype,i}(),DOMException:function(){function i(e){this.code=e,this.name=t(n,e),this.message=this.name+": DOMException "+this.code}var n={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25};return e.extend(i,n),i.prototype=Error.prototype,i}(),EventException:function(){function t(e){this.code=e,this.name="EventException"}return e.extend(t,{UNSPECIFIED_EVENT_TYPE_ERR:0}),t.prototype=Error.prototype,t}()}}),i("moxie/core/utils/Dom",["moxie/core/utils/Env"],function(e){var t=function(e,t){if(!e.className)return!1;var i=new RegExp("(^|\\s+)"+t+"(\\s+|$)");return i.test(e.className)};return{get:function(e){return"string"!=typeof e?e:document.getElementById(e)},hasClass:t,addClass:function(e,i){t(e,i)||(e.className=e.className?e.className.replace(/\s+$/,"")+" "+i:i)},removeClass:function(e,t){if(e.className){var i=new RegExp("(^|\\s+)"+t+"(\\s+|$)");e.className=e.className.replace(i,function(e,t,i){return" "===t&&" "===i?" ":""})}},getStyle:function(e,t){return e.currentStyle?e.currentStyle[t]:window.getComputedStyle?window.getComputedStyle(e,null)[t]:void 0},getPos:function(t,i){function n(e){var t,i,n=0,r=0;return e&&(i=e.getBoundingClientRect(),t="CSS1Compat"===l.compatMode?l.documentElement:l.body,n=i.left+t.scrollLeft,r=i.top+t.scrollTop),{x:n,y:r}}var r,o,a,s=0,u=0,l=document;if(t=t,i=i||l.body,t&&t.getBoundingClientRect&&"IE"===e.browser&&(!l.documentMode||l.documentMode<8))return o=n(t),a=n(i),{x:o.x-a.x,y:o.y-a.y};for(r=t;r&&r!=i&&r.nodeType;)s+=r.offsetLeft||0,u+=r.offsetTop||0,r=r.offsetParent;for(r=t.parentNode;r&&r!=i&&r.nodeType;)s-=r.scrollLeft||0,u-=r.scrollTop||0,r=r.parentNode;return{x:s,y:u}},getSize:function(e){return{w:e.offsetWidth||e.clientWidth,h:e.offsetHeight||e.clientHeight}}}}),i("moxie/core/EventTarget",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic"],function(e,t,i){function n(){this.uid=i.guid()}var r={};return i.extend(n.prototype,{init:function(){this.uid||(this.uid=i.guid("uid_"))},addEventListener:function(e,t,n,o){var a,s=this;return this.hasOwnProperty("uid")||(this.uid=i.guid("uid_")),e=i.trim(e),/\s/.test(e)?void i.each(e.split(/\s+/),function(e){s.addEventListener(e,t,n,o)}):(e=e.toLowerCase(),n=parseInt(n,10)||0,(a=r[this.uid]&&r[this.uid][e]||[]).push({fn:t,priority:n,scope:o||this}),r[this.uid]||(r[this.uid]={}),void(r[this.uid][e]=a))},hasEventListener:function(e){var t;return e?(e=e.toLowerCase(),t=r[this.uid]&&r[this.uid][e]):t=r[this.uid],t||!1},removeEventListener:function(e,t){var n,o,a=this;if(e=e.toLowerCase(),/\s/.test(e))i.each(e.split(/\s+/),function(e){a.removeEventListener(e,t)});else if(n=r[this.uid]&&r[this.uid][e]){if(t){for(o=n.length-1;o>=0;o--)if(n[o].fn===t){n.splice(o,1);break}}else n=[];n.length||(delete r[this.uid][e],i.isEmptyObj(r[this.uid])&&delete r[this.uid])}},removeAllEventListeners:function(){r[this.uid]&&delete r[this.uid]},dispatchEvent:function(e){var n,o,a,s,u,l={},c=!0;if("string"!==i.typeOf(e)){if(s=e,"string"!==i.typeOf(s.type))throw new t.EventException(t.EventException.UNSPECIFIED_EVENT_TYPE_ERR);e=s.type,s.total!==u&&s.loaded!==u&&(l.total=s.total,l.loaded=s.loaded),l.async=s.async||!1}if(-1!==e.indexOf("::")?function(t){n=t[0],e=t[1]}(e.split("::")):n=this.uid,e=e.toLowerCase(),o=r[n]&&r[n][e]){o.sort(function(e,t){return t.priority-e.priority}),(a=[].slice.call(arguments)).shift(),l.type=e,a.unshift(l);var d=[];i.each(o,function(e){a[0].target=e.scope,l.async?d.push(function(t){setTimeout(function(){t(!1===e.fn.apply(e.scope,a))},1)}):d.push(function(t){t(!1===e.fn.apply(e.scope,a))})}),d.length&&i.inSeries(d,function(e){c=!e})}return c},bindOnce:function(e,t,i,n){var r=this;r.bind.call(this,e,function i(){return r.unbind(e,i),t.apply(this,arguments)},i,n)},bind:function(){this.addEventListener.apply(this,arguments)},unbind:function(){this.removeEventListener.apply(this,arguments)},unbindAll:function(){this.removeAllEventListeners.apply(this,arguments)},trigger:function(){return this.dispatchEvent.apply(this,arguments)},handleEventProps:function(e){var t=this;this.bind(e.join(" "),function(e){var t="on"+e.type.toLowerCase();"function"===i.typeOf(this[t])&&this[t].apply(this,arguments)}),i.each(e,function(e){e="on"+e.toLowerCase(e),"undefined"===i.typeOf(t[e])&&(t[e]=null)})}}),n.instance=new n,n}),i("moxie/runtime/Runtime",["moxie/core/utils/Env","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/EventTarget"],function(e,t,i,n){function r(e,n,o,s,u){var l,c=this,d=t.guid(n+"_"),p=u||"browser";e=e||{},a[d]=this,o=t.extend({access_binary:!1,access_image_binary:!1,display_media:!1,do_cors:!1,drag_and_drop:!1,filter_by_extension:!0,resize_image:!1,report_upload_progress:!1,return_response_headers:!1,return_response_type:!1,return_status_code:!0,send_custom_headers:!1,select_file:!1,select_folder:!1,select_multiple:!0,send_binary_string:!1,send_browser_cookies:!0,send_multipart:!0,slice_blob:!1,stream_upload:!1,summon_file_dialog:!1,upload_filesize:!0,use_http_method:!0},o),e.preferred_caps&&(p=r.getMode(s,e.preferred_caps,p)),l=function(){var e={};return{exec:function(t,i,n,r){return l[i]&&(e[t]||(e[t]={context:this,instance:new l[i]}),e[t].instance[n])?e[t].instance[n].apply(this,r):void 0},removeInstance:function(t){delete e[t]},removeAllInstances:function(){var i=this;t.each(e,function(e,n){"function"===t.typeOf(e.instance.destroy)&&e.instance.destroy.call(e.context),i.removeInstance(n)})}}}(),t.extend(this,{initialized:!1,uid:d,type:n,mode:r.getMode(s,e.required_caps,p),shimid:d+"_container",clients:0,options:e,can:function(e,i){var n=arguments[2]||o;if("string"===t.typeOf(e)&&"undefined"===t.typeOf(i)&&(e=r.parseCaps(e)),"object"===t.typeOf(e)){for(var a in e)if(!this.can(a,e[a],n))return!1;return!0}return"function"===t.typeOf(n[e])?n[e].call(this,i):i===n[e]},getShimContainer:function(){var e,n=i.get(this.shimid);return n||(e=i.get(this.options.container)||document.body,(n=document.createElement("div")).id=this.shimid,n.className="moxie-shim moxie-shim-"+this.type,t.extend(n.style,{position:"absolute",top:"0px",left:"0px",width:"1px",height:"1px",overflow:"hidden"}),e.appendChild(n),e=null),n},getShim:function(){return l},shimExec:function(e,t){var i=[].slice.call(arguments,2);return c.getShim().exec.call(this,this.uid,e,t,i)},exec:function(e,t){var i=[].slice.call(arguments,2);return c[e]&&c[e][t]?c[e][t].apply(this,i):c.shimExec.apply(this,arguments)},destroy:function(){if(c){var e=i.get(this.shimid);e&&e.parentNode.removeChild(e),l&&l.removeAllInstances(),this.unbindAll(),delete a[this.uid],this.uid=null,d=c=l=e=null}}}),this.mode&&e.required_caps&&!this.can(e.required_caps)&&(this.mode=!1)}var o={},a={};return r.order="html5,flash,silverlight,html4",r.getRuntime=function(e){return!!a[e]&&a[e]},r.addConstructor=function(e,t){t.prototype=n.instance,o[e]=t},r.getConstructor=function(e){return o[e]||null},r.getInfo=function(e){var t=r.getRuntime(e);return t?{uid:t.uid,type:t.type,mode:t.mode,can:function(){return t.can.apply(t,arguments)}}:null},r.parseCaps=function(e){var i={};return"string"!==t.typeOf(e)?e||{}:(t.each(e.split(","),function(e){i[e]=!0}),i)},r.can=function(e,t){var i,n,o=r.getConstructor(e);return!!o&&(i=new o({required_caps:t}),n=i.mode,i.destroy(),!!n)},r.thatCan=function(e,t){var i=(t||r.order).split(/\s*,\s*/);for(var n in i)if(r.can(i[n],e))return i[n];return null},r.getMode=function(e,i,n){var r=null;if("undefined"===t.typeOf(n)&&(n="browser"),i&&!t.isEmptyObj(e)){if(t.each(i,function(i,n){if(e.hasOwnProperty(n)){var o=e[n](i);if("string"==typeof o&&(o=[o]),r){if(!(r=t.arrayIntersect(r,o)))return r=!1}else r=o}}),r)return-1!==t.inArray(n,r)?n:r[0];if(!1===r)return!1}return n},r.getGlobalEventTarget=function(){if(/^moxie\./.test(e.global_event_dispatcher)&&!e.can("access_global_ns")){var i=t.guid("moxie_event_target_");window[i]=function(e,t){n.instance.dispatchEvent(e,t)},e.global_event_dispatcher=i}return e.global_event_dispatcher},r.capTrue=function(){return!0},r.capFalse=function(){return!1},r.capTest=function(e){return function(){return!!e}},r}),i("moxie/runtime/RuntimeClient",["moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/Runtime"],function(e,t,i,n){return function(){var e;i.extend(this,{connectRuntime:function(r){var o,a=this;if("string"===i.typeOf(r)?o=r:"string"===i.typeOf(r.ruid)&&(o=r.ruid),o){if(e=n.getRuntime(o))return a.ruid=o,e.clients++,e;throw new t.RuntimeError(t.RuntimeError.NOT_INIT_ERR)}!function i(o){var s,u;return o.length?(s=o.shift().toLowerCase(),(u=n.getConstructor(s))?(e=new u(r),e.bind("Init",function(){e.initialized=!0,setTimeout(function(){e.clients++,a.ruid=e.uid,a.trigger("RuntimeInit",e)},1)}),e.bind("Error",function(){e.destroy(),i(o)}),e.bind("Exception",function(e,i){var n=i.name+"(#"+i.code+")"+(i.message?", from: "+i.message:"");a.trigger("RuntimeError",new t.RuntimeError(t.RuntimeError.EXCEPTION_ERR,n))}),e.mode?void e.init():void e.trigger("Error")):void i(o)):(a.trigger("RuntimeError",new t.RuntimeError(t.RuntimeError.NOT_INIT_ERR)),void(e=null))}((r.runtime_order||n.order).split(/\s*,\s*/))},disconnectRuntime:function(){e&&--e.clients<=0&&e.destroy(),e=null},getRuntime:function(){return e&&e.uid?e:e=null},exec:function(){return e?e.exec.apply(this,arguments):null},can:function(t){return!!e&&e.can(t)}})}}),i("moxie/file/Blob",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient"],function(e,t,i){var n={};return function r(o,a){function s(t,i,o){var a,s=n[this.uid];return"string"===e.typeOf(s)&&s.length?((a=new r(null,{type:o,size:i-t})).detach(s.substr(t,a.size)),a):null}i.call(this),o&&this.connectRuntime(o),a?"string"===e.typeOf(a)&&(a={data:a}):a={},e.extend(this,{uid:a.uid||e.guid("uid_"),ruid:o,size:a.size||0,type:a.type||"",slice:function(e,t,i){return this.isDetached()?s.apply(this,arguments):this.getRuntime().exec.call(this,"Blob","slice",this.getSource(),e,t,i)},getSource:function(){return n[this.uid]?n[this.uid]:null},detach:function(e){if(this.ruid&&(this.getRuntime().exec.call(this,"Blob","destroy"),this.disconnectRuntime(),this.ruid=null),"data:"==(e=e||"").substr(0,5)){var i=e.indexOf(";base64,");this.type=e.substring(5,i),e=t.atob(e.substring(i+8))}this.size=e.length,n[this.uid]=e},isDetached:function(){return!this.ruid&&"string"===e.typeOf(n[this.uid])},destroy:function(){this.detach(),delete n[this.uid]}}),a.data?this.detach(a.data):n[this.uid]=a}}),i("moxie/core/I18n",["moxie/core/utils/Basic"],function(e){var t={};return{addI18n:function(i){return e.extend(t,i)},translate:function(e){return t[e]||e},_:function(e){return this.translate(e)},sprintf:function(t){var i=[].slice.call(arguments,1);return t.replace(/%[a-z]/g,function(){var t=i.shift();return"undefined"!==e.typeOf(t)?t:""})}}}),i("moxie/core/utils/Mime",["moxie/core/utils/Basic","moxie/core/I18n"],function(e,t){var i={},n={},r=function(e){var t,r,o,a=e.split(/,/);for(t=0;t<a.length;t+=2){for(o=a[t+1].split(/ /),r=0;r<o.length;r++)i[o[r]]=a[t];n[a[t]]=o}},o=function(t){var i=[];return e.each(t,function(t){if("*"===(t=t.toLowerCase()))return i=[],!1;var r=t.match(/^(\w+)\/(\*|\w+)$/);r&&("*"===r[2]?e.each(n,function(e,t){new RegExp("^"+r[1]+"/").test(t)&&[].push.apply(i,n[t])}):n[t]&&[].push.apply(i,n[t]))}),i},a=function(e){var t=e&&e.match(/\.([^.]+)$/);return t?t[1].toLowerCase():""};return r("application/msword,doc dot,application/pdf,pdf,application/pgp-signature,pgp,application/postscript,ps ai eps,application/rtf,rtf,application/vnd.ms-excel,xls xlb xlt xla,application/vnd.ms-powerpoint,ppt pps pot ppa,application/zip,zip,application/x-shockwave-flash,swf swfl,application/vnd.openxmlformats-officedocument.wordprocessingml.document,docx,application/vnd.openxmlformats-officedocument.wordprocessingml.template,dotx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,xlsx,application/vnd.openxmlformats-officedocument.presentationml.presentation,pptx,application/vnd.openxmlformats-officedocument.presentationml.template,potx,application/vnd.openxmlformats-officedocument.presentationml.slideshow,ppsx,application/x-javascript,js,application/json,json,audio/mpeg,mp3 mpga mpega mp2,audio/x-wav,wav,audio/x-m4a,m4a,audio/ogg,oga ogg,audio/aiff,aiff aif,audio/flac,flac,audio/aac,aac,audio/ac3,ac3,audio/x-ms-wma,wma,image/bmp,bmp,image/gif,gif,image/jpeg,jpg jpeg jpe,image/photoshop,psd,image/png,png,image/svg+xml,svg svgz,image/tiff,tiff tif,text/plain,asc txt text diff log,text/html,htm html xhtml,text/css,css,text/csv,csv,text/rtf,rtf,video/mpeg,mpeg mpg mpe m2v,video/quicktime,qt mov,video/mp4,mp4,video/x-m4v,m4v,video/x-flv,flv,video/x-ms-wmv,wmv,video/avi,avi,video/webm,webm,video/3gpp,3gpp 3gp,video/3gpp2,3g2,video/vnd.rn-realvideo,rv,video/ogg,ogv,video/x-matroska,mkv,application/vnd.oasis.opendocument.formula-template,otf,application/octet-stream,exe"),{mimes:i,extensions:n,addMimeType:r,extList2mimes:function(t,i){var n,r,o,a,s=[];for(r=0;r<t.length;r++)for(n=t[r].extensions.toLowerCase().split(/\s*,\s*/),o=0;o<n.length;o++){if("*"===n[o])return[];if(a=s[n[o]],i&&/^\w+$/.test(n[o]))s.push("."+n[o]);else if(a&&-1===e.inArray(a,s))s.push(a);else if(!a)return[]}return s},mimes2exts:o,mimes2extList:function(i){var n=[],r=[];return"string"===e.typeOf(i)&&(i=e.trim(i).split(/\s*,\s*/)),r=o(i),n.push({title:t.translate("Files"),extensions:r.length?r.join(","):"*"}),n},getFileExtension:a,getFileMime:function(e){return i[a(e)]||""}}}),i("moxie/file/FileInput",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Mime","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/I18n","moxie/runtime/Runtime","moxie/runtime/RuntimeClient"],function(e,t,i,n,r,o,a,s,u){function l(t){var o,l,d;if(-1!==e.inArray(e.typeOf(t),["string","node"])&&(t={browse_button:t}),!(l=n.get(t.browse_button)))throw new r.DOMException(r.DOMException.NOT_FOUND_ERR);d={accept:[{title:a.translate("All Files"),extensions:"*"}],multiple:!1,required_caps:!1,container:l.parentNode||document.body},"string"==typeof(t=e.extend({},d,t)).required_caps&&(t.required_caps=s.parseCaps(t.required_caps)),"string"==typeof t.accept&&(t.accept=i.mimes2extList(t.accept)),(o=n.get(t.container))||(o=document.body),"static"===n.getStyle(o,"position")&&(o.style.position="relative"),o=l=null,u.call(this),e.extend(this,{uid:e.guid("uid_"),ruid:null,shimid:null,files:null,init:function(){var i=this;i.bind("RuntimeInit",function(r,o){i.ruid=o.uid,i.shimid=o.shimid,i.bind("Ready",function(){i.trigger("Refresh")},999),i.bind("Refresh",function(){var i,r,a,s,u;a=n.get(t.browse_button),s=n.get(o.shimid),a&&(i=n.getPos(a,n.get(t.container)),r=n.getSize(a),u=parseInt(n.getStyle(a,"z-index"),10)||0,s&&e.extend(s.style,{top:i.y+"px",left:i.x+"px",width:r.w+"px",height:r.h+"px",zIndex:u+1})),s=a=null}),o.exec.call(i,"FileInput","init",t)}),i.connectRuntime(e.extend({},t,{required_caps:{select_file:!0}}))},getOption:function(e){return t[e]},setOption:function(e,n){if(t.hasOwnProperty(e)){var o=t[e];switch(e){case"accept":"string"==typeof n&&(n=i.mimes2extList(n));break;case"container":case"required_caps":throw new r.FileException(r.FileException.NO_MODIFICATION_ALLOWED_ERR)}t[e]=n,this.exec("FileInput","setOption",e,n),this.trigger("OptionChanged",e,n,o)}},disable:function(t){var i=this.getRuntime();i&&this.exec("FileInput","disable","undefined"===e.typeOf(t)||t)},refresh:function(){this.trigger("Refresh")},destroy:function(){var t=this.getRuntime();t&&(t.exec.call(this,"FileInput","destroy"),this.disconnectRuntime()),"array"===e.typeOf(this.files)&&e.each(this.files,function(e){e.destroy()}),this.files=null,this.unbindAll()}}),this.handleEventProps(c)}var c=["ready","change","cancel","mouseenter","mouseleave","mousedown","mouseup"];return l.prototype=o.instance,l}),i("moxie/file/File",["moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/file/Blob"],function(e,t,i){function n(n,r){var o;if(r||(r={}),i.apply(this,arguments),this.type||(this.type=t.getFileMime(r.name)),r.name)o=(o=r.name.replace(/\\/g,"/")).substr(o.lastIndexOf("/")+1);else if(this.type){var a=this.type.split("/")[0];o=e.guid((""!==a?a:"file")+"_"),t.extensions[this.type]&&(o+="."+t.extensions[this.type][0])}e.extend(this,{name:o||e.guid("file_"),relativePath:"",lastModifiedDate:r.lastModifiedDate||(new Date).toLocaleString()})}return n.prototype=i.prototype,n}),i("moxie/file/FileDrop",["moxie/core/I18n","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/core/utils/Basic","moxie/core/utils/Env","moxie/file/File","moxie/runtime/RuntimeClient","moxie/core/EventTarget","moxie/core/utils/Mime"],function(e,t,i,n,r,o,a,s,u){function l(i){var r,o=this;"string"==typeof i&&(i={drop_zone:i}),r={accept:[{title:e.translate("All Files"),extensions:"*"}],required_caps:{drag_and_drop:!0}},(i="object"==typeof i?n.extend({},r,i):r).container=t.get(i.drop_zone)||document.body,"static"===t.getStyle(i.container,"position")&&(i.container.style.position="relative"),"string"==typeof i.accept&&(i.accept=u.mimes2extList(i.accept)),a.call(o),n.extend(o,{uid:n.guid("uid_"),ruid:null,files:null,init:function(){o.bind("RuntimeInit",function(e,t){o.ruid=t.uid,t.exec.call(o,"FileDrop","init",i),o.dispatchEvent("ready")}),o.connectRuntime(i)},destroy:function(){var e=this.getRuntime();e&&(e.exec.call(this,"FileDrop","destroy"),this.disconnectRuntime()),this.files=null,this.unbindAll()}}),this.handleEventProps(c)}var c=["ready","dragenter","dragleave","drop","error"];return l.prototype=s.instance,l}),i("moxie/file/FileReader",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/core/Exceptions","moxie/core/EventTarget","moxie/file/Blob","moxie/runtime/RuntimeClient"],function(e,t,i,n,r,o){function a(){function n(e,n){if(this.trigger("loadstart"),this.readyState===a.LOADING)return this.trigger("error",new i.DOMException(i.DOMException.INVALID_STATE_ERR)),void this.trigger("loadend");if(!(n instanceof r))return this.trigger("error",new i.DOMException(i.DOMException.NOT_FOUND_ERR)),void this.trigger("loadend");if(this.result=null,this.readyState=a.LOADING,n.isDetached()){var o=n.getSource();switch(e){case"readAsText":case"readAsBinaryString":this.result=o;break;case"readAsDataURL":this.result="data:"+n.type+";base64,"+t.btoa(o)}this.readyState=a.DONE,this.trigger("load"),this.trigger("loadend")}else this.connectRuntime(n.ruid),this.exec("FileReader","read",e,n)}o.call(this),e.extend(this,{uid:e.guid("uid_"),readyState:a.EMPTY,result:null,error:null,readAsBinaryString:function(e){n.call(this,"readAsBinaryString",e)},readAsDataURL:function(e){n.call(this,"readAsDataURL",e)},readAsText:function(e){n.call(this,"readAsText",e)},abort:function(){this.result=null,-1===e.inArray(this.readyState,[a.EMPTY,a.DONE])&&(this.readyState===a.LOADING&&(this.readyState=a.DONE),this.exec("FileReader","abort"),this.trigger("abort"),this.trigger("loadend"))},destroy:function(){this.abort(),this.exec("FileReader","destroy"),this.disconnectRuntime(),this.unbindAll()}}),this.handleEventProps(s),this.bind("Error",function(e,t){this.readyState=a.DONE,this.error=t},999),this.bind("Load",function(){this.readyState=a.DONE},999)}var s=["loadstart","progress","load","abort","error","loadend"];return a.EMPTY=0,a.LOADING=1,a.DONE=2,a.prototype=n.instance,a}),i("moxie/core/utils/Url",["moxie/core/utils/Basic"],function(e){var t=function(i,n){var r,o=["source","scheme","authority","userInfo","user","pass","host","port","relative","path","directory","file","query","fragment"],a=o.length,s={},u=/^(?:([^:\/?#]+):)?(?:\/\/()(?:(?:()(?:([^:@\/]*):?([^:@\/]*))?@)?(\[[\da-fA-F:]+\]|[^:\/?#]*)(?::(\d*))?))?()(?:(()(?:(?:[^?#\/]*\/)*)()(?:[^?#]*))(?:\\?([^#]*))?(?:#(.*))?)/.exec(i||""),l=/^\/\/\w/.test(i);switch(e.typeOf(n)){case"undefined":n=t(document.location.href,!1);break;case"string":n=t(n,!1)}for(;a--;)u[a]&&(s[o[a]]=u[a]);if(r=!l&&!s.scheme,(l||r)&&(s.scheme=n.scheme),r){s.host=n.host,s.port=n.port;var c="";/^[^\/]/.test(s.path)&&(c=n.path,c=/\/[^\/]*\.[^\/]*$/.test(c)?c.replace(/\/[^\/]+$/,"/"):c.replace(/\/?$/,"/")),s.path=c+(s.path||"")}return s.port||(s.port={http:80,https:443}[s.scheme]||80),s.port=parseInt(s.port,10),s.path||(s.path="/"),delete s.source,s};return{parseUrl:t,resolveUrl:function(e){var i="object"==typeof e?e:t(e);return i.scheme+"://"+i.host+(i.port!=={http:80,https:443}[i.scheme]?":"+i.port:"")+i.path+(i.query?i.query:"")},hasSameOrigin:function(e){function i(e){return[e.scheme,e.host,e.port].join("/")}return"string"==typeof e&&(e=t(e)),i(t())===i(e)}}}),i("moxie/runtime/RuntimeTarget",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(e,t,i){function n(){this.uid=e.guid("uid_"),t.call(this),this.destroy=function(){this.disconnectRuntime(),this.unbindAll()}}return n.prototype=i.instance,n}),i("moxie/file/FileReaderSync",["moxie/core/utils/Basic","moxie/runtime/RuntimeClient","moxie/core/utils/Encode"],function(e,t,i){return function(){function n(e,t){if(!t.isDetached()){var n=this.connectRuntime(t.ruid).exec.call(this,"FileReaderSync","read",e,t);return this.disconnectRuntime(),n}var r=t.getSource();switch(e){case"readAsBinaryString":return r;case"readAsDataURL":return"data:"+t.type+";base64,"+i.btoa(r);case"readAsText":for(var o="",a=0,s=r.length;s>a;a++)o+=String.fromCharCode(r[a]);return o}}t.call(this),e.extend(this,{uid:e.guid("uid_"),readAsBinaryString:function(e){return n.call(this,"readAsBinaryString",e)},readAsDataURL:function(e){return n.call(this,"readAsDataURL",e)},readAsText:function(e){return n.call(this,"readAsText",e)}})}}),i("moxie/xhr/FormData",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/file/Blob"],function(e,t,i){return function(){var e,n=[];t.extend(this,{append:function(r,o){var a=this,s=t.typeOf(o);o instanceof i?e={name:r,value:o}:"array"===s?(r+="[]",t.each(o,function(e){a.append(r,e)})):"object"===s?t.each(o,function(e,t){a.append(r+"["+t+"]",e)}):"null"===s||"undefined"===s||"number"===s&&isNaN(o)?a.append(r,"false"):n.push({name:r,value:o.toString()})},hasBlob:function(){return!!this.getBlob()},getBlob:function(){return e&&e.value||null},getBlobName:function(){return e&&e.name||null},each:function(i){t.each(n,function(e){i(e.value,e.name)}),e&&i(e.value,e.name)},destroy:function(){e=null,n=[]}})}}),i("moxie/xhr/XMLHttpRequest",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/EventTarget","moxie/core/utils/Encode","moxie/core/utils/Url","moxie/runtime/Runtime","moxie/runtime/RuntimeTarget","moxie/file/Blob","moxie/file/FileReaderSync","moxie/xhr/FormData","moxie/core/utils/Env","moxie/core/utils/Mime"],function(e,t,i,n,r,o,a,s,u,l,c,d){function p(){this.uid=e.guid("uid_")}function m(){function i(e,t){return b.hasOwnProperty(e)?1===arguments.length?c.can("define_property")?b[e]:w[e]:void(c.can("define_property")?b[e]=t:w[e]=t):void 0}function u(t){function n(){y&&(y.destroy(),y=null),s.dispatchEvent("loadend"),s=null}function r(r){y.bind("LoadStart",function(e){i("readyState",m.LOADING),s.dispatchEvent("readystatechange"),s.dispatchEvent(e),D&&s.upload.dispatchEvent(e)}),y.bind("Progress",function(e){i("readyState")!==m.LOADING&&(i("readyState",m.LOADING),s.dispatchEvent("readystatechange")),s.dispatchEvent(e)}),y.bind("UploadProgress",function(e){D&&s.upload.dispatchEvent({type:"progress",lengthComputable:!1,total:e.total,loaded:e.loaded})}),y.bind("Load",function(t){i("readyState",m.DONE),i("status",Number(r.exec.call(y,"XMLHttpRequest","getStatus")||0)),i("statusText",f[i("status")]||""),i("response",r.exec.call(y,"XMLHttpRequest","getResponse",i("responseType"))),~e.inArray(i("responseType"),["text",""])?i("responseText",i("response")):"document"===i("responseType")&&i("responseXML",i("response")),M=r.exec.call(y,"XMLHttpRequest","getAllResponseHeaders"),s.dispatchEvent("readystatechange"),i("status")>0?(D&&s.upload.dispatchEvent(t),s.dispatchEvent(t)):(N=!0,s.dispatchEvent("error")),n()}),y.bind("Abort",function(e){s.dispatchEvent(e),n()}),y.bind("Error",function(e){N=!0,i("readyState",m.DONE),s.dispatchEvent("readystatechange"),C=!0,s.dispatchEvent(e),n()}),r.exec.call(y,"XMLHttpRequest","send",{url:g,method:v,async:R,user:_,password:x,headers:I,mimeType:T,encoding:A,responseType:s.responseType,withCredentials:s.withCredentials,options:L},t)}var s=this;(new Date).getTime(),y=new a,"string"==typeof L.required_caps&&(L.required_caps=o.parseCaps(L.required_caps)),L.required_caps=e.extend({},L.required_caps,{return_response_type:s.responseType}),t instanceof l&&(L.required_caps.send_multipart=!0),e.isEmptyObj(I)||(L.required_caps.send_custom_headers=!0),F||(L.required_caps.do_cors=!0),L.ruid?r(y.connectRuntime(L)):(y.bind("RuntimeInit",function(e,t){r(t)}),y.bind("RuntimeError",function(e,t){s.dispatchEvent("RuntimeError",t)}),y.connectRuntime(L))}var g,v,_,x,y,E,w=this,b={timeout:0,readyState:m.UNSENT,withCredentials:!1,status:0,statusText:"",responseType:"",responseXML:null,responseText:null,response:null},R=!0,I={},A=null,T=null,S=!1,O=!1,D=!1,C=!1,N=!1,F=!1,L={},M="";e.extend(this,b,{uid:e.guid("uid_"),upload:new p,open:function(o,a,s,u,l){var c;if(!o||!a)throw new t.DOMException(t.DOMException.SYNTAX_ERR);if(/[\u0100-\uffff]/.test(o)||n.utf8_encode(o)!==o)throw new t.DOMException(t.DOMException.SYNTAX_ERR);if(~e.inArray(o.toUpperCase(),["CONNECT","DELETE","GET","HEAD","OPTIONS","POST","PUT","TRACE","TRACK"])&&(v=o.toUpperCase()),~e.inArray(v,["CONNECT","TRACE","TRACK"]))throw new t.DOMException(t.DOMException.SECURITY_ERR);if(a=n.utf8_encode(a),c=r.parseUrl(a),F=r.hasSameOrigin(c),g=r.resolveUrl(a),(u||l)&&!F)throw new t.DOMException(t.DOMException.INVALID_ACCESS_ERR);if(_=u||c.user,x=l||c.pass,!1===(R=s||!0)&&(i("timeout")||i("withCredentials")||""!==i("responseType")))throw new t.DOMException(t.DOMException.INVALID_ACCESS_ERR);S=!R,O=!1,I={},function(){i("responseText",""),i("responseXML",null),i("response",null),i("status",0),i("statusText",""),null}.call(this),i("readyState",m.OPENED),this.dispatchEvent("readystatechange")},setRequestHeader:function(r,o){if(i("readyState")!==m.OPENED||O)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(/[\u0100-\uffff]/.test(r)||n.utf8_encode(r)!==r)throw new t.DOMException(t.DOMException.SYNTAX_ERR);return r=e.trim(r).toLowerCase(),!~e.inArray(r,["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","content-transfer-encoding","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","user-agent","via"])&&!/^(proxy\-|sec\-)/.test(r)&&(I[r]?I[r]+=", "+o:I[r]=o,!0)},hasRequestHeader:function(e){return e&&I[e.toLowerCase()]||!1},getAllResponseHeaders:function(){return M||""},getResponseHeader:function(t){return t=t.toLowerCase(),N||~e.inArray(t,["set-cookie","set-cookie2"])?null:M&&""!==M&&(E||(E={},e.each(M.split(/\r\n/),function(t){var i=t.split(/:\s+/);2===i.length&&(i[0]=e.trim(i[0]),E[i[0].toLowerCase()]={header:i[0],value:e.trim(i[1])})})),E.hasOwnProperty(t))?E[t].header+": "+E[t].value:null},overrideMimeType:function(n){var r,o;if(~e.inArray(i("readyState"),[m.LOADING,m.DONE]))throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(n=e.trim(n.toLowerCase()),/;/.test(n)&&(r=n.match(/^([^;]+)(?:;\scharset\=)?(.*)$/))&&(n=r[1],r[2]&&(o=r[2])),!d.mimes[n])throw new t.DOMException(t.DOMException.SYNTAX_ERR);n,o},send:function(i,r){if(L="string"===e.typeOf(r)?{ruid:r}:r||{},this.readyState!==m.OPENED||O)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);if(i instanceof s)L.ruid=i.ruid,T=i.type||"application/octet-stream";else if(i instanceof l){if(i.hasBlob()){var o=i.getBlob();L.ruid=o.ruid,T=o.type||"application/octet-stream"}}else"string"==typeof i&&(A="UTF-8",T="text/plain;charset=UTF-8",i=n.utf8_encode(i));this.withCredentials||(this.withCredentials=L.required_caps&&L.required_caps.send_browser_cookies&&!F),D=!S&&this.upload.hasEventListener(),N=!1,C=!i,S||(O=!0),u.call(this,i)},abort:function(){if(N=!0,S=!1,~e.inArray(i("readyState"),[m.UNSENT,m.OPENED,m.DONE]))i("readyState",m.UNSENT);else{if(i("readyState",m.DONE),O=!1,!y)throw new t.DOMException(t.DOMException.INVALID_STATE_ERR);y.getRuntime().exec.call(y,"XMLHttpRequest","abort",C),C=!0}},destroy:function(){y&&("function"===e.typeOf(y.destroy)&&y.destroy(),y=null),this.unbindAll(),this.upload&&(this.upload.unbindAll(),this.upload=null)}}),this.handleEventProps(h.concat(["readystatechange"])),this.upload.handleEventProps(h)}var f={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",306:"Reserved",307:"Temporary Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Requested Range Not Satisfiable",417:"Expectation Failed",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",426:"Upgrade Required",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",510:"Not Extended"};p.prototype=i.instance;var h=["loadstart","progress","abort","error","load","timeout","loadend"];return m.UNSENT=0,m.OPENED=1,m.HEADERS_RECEIVED=2,m.LOADING=3,m.DONE=4,m.prototype=i.instance,m}),i("moxie/runtime/Transporter",["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/runtime/RuntimeClient","moxie/core/EventTarget"],function(e,t,i,n){function r(){function n(){c=d=0,l=this.result=null}function o(t,i){var n=this;u=i,n.bind("TransportingProgress",function(t){d=t.loaded,c>d&&-1===e.inArray(n.state,[r.IDLE,r.DONE])&&a.call(n)},999),n.bind("TransportingComplete",function(){d=c,n.state=r.DONE,l=null,n.result=u.exec.call(n,"Transporter","getAsBlob",t||"")},999),n.state=r.BUSY,n.trigger("TransportingStarted"),a.call(n)}function a(){var e,i=c-d;p>i&&(p=i),e=t.btoa(l.substr(d,p)),u.exec.call(this,"Transporter","receive",e,c)}var s,u,l,c,d,p;i.call(this),e.extend(this,{uid:e.guid("uid_"),state:r.IDLE,result:null,transport:function(t,i,r){var a=this;if(r=e.extend({chunk_size:204798},r),(s=r.chunk_size%3)&&(r.chunk_size+=3-s),p=r.chunk_size,n.call(this),l=t,c=t.length,"string"===e.typeOf(r)||r.ruid)o.call(a,i,this.connectRuntime(r));else{var u=function(e,t){a.unbind("RuntimeInit",u),o.call(a,i,t)};this.bind("RuntimeInit",u),this.connectRuntime(r)}},abort:function(){var e=this;e.state=r.IDLE,u&&(u.exec.call(e,"Transporter","clear"),e.trigger("TransportingAborted")),n.call(e)},destroy:function(){this.unbindAll(),u=null,this.disconnectRuntime(),n.call(this)}})}return r.IDLE=0,r.BUSY=1,r.DONE=2,r.prototype=n.instance,r}),i("moxie/image/Image",["moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/file/FileReaderSync","moxie/xhr/XMLHttpRequest","moxie/runtime/Runtime","moxie/runtime/RuntimeClient","moxie/runtime/Transporter","moxie/core/utils/Env","moxie/core/EventTarget","moxie/file/Blob","moxie/file/File","moxie/core/utils/Encode"],function(e,t,i,n,r,o,a,s,u,l,c,d,p){function m(){function n(t){var o=e.typeOf(t);try{if(t instanceof m){if(!t.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);(function(t,i){var n=this.connectRuntime(t.ruid);this.ruid=n.uid,n.exec.call(this,"Image","loadFromImage",t,"undefined"===e.typeOf(i)||i)}).apply(this,arguments)}else if(t instanceof c){if(!~e.inArray(t.type,["image/jpeg","image/png"]))throw new i.ImageError(i.ImageError.WRONG_FORMAT);l.apply(this,arguments)}else if(-1!==e.inArray(o,["blob","file"]))n.call(this,new d(null,t),arguments[1]);else if("string"===o)"data:"===t.substr(0,5)?n.call(this,new c(null,{data:t}),arguments[1]):function(e,t){var i,n=this;(i=new r).open("get",e),i.responseType="blob",i.onprogress=function(e){n.trigger(e)},i.onload=function(){l.call(n,i.response,!0)},i.onerror=function(e){n.trigger(e)},i.onloadend=function(){i.destroy()},i.bind("RuntimeError",function(e,t){n.trigger("RuntimeError",t)}),i.send(null,t)}.apply(this,arguments);else{if("node"!==o||"img"!==t.nodeName.toLowerCase())throw new i.DOMException(i.DOMException.TYPE_MISMATCH_ERR);n.call(this,t.src,arguments[1])}}catch(e){this.trigger("error",e.code)}}function l(t,i){function n(e){r.ruid=e.uid,e.exec.call(r,"Image","loadFromBlob",t)}var r=this;r.name=t.name||"",t.isDetached()?(this.bind("RuntimeInit",function(e,t){n(t)}),i&&"string"==typeof i.required_caps&&(i.required_caps=o.parseCaps(i.required_caps)),this.connectRuntime(e.extend({required_caps:{access_image_binary:!0,resize_image:!0}},i))):n(this.connectRuntime(t.ruid))}a.call(this),e.extend(this,{uid:e.guid("uid_"),ruid:null,name:"",size:0,width:0,height:0,type:"",meta:{},clone:function(){this.load.apply(this,arguments)},load:function(){n.apply(this,arguments)},resize:function(t){var n,r,o=this,a={x:0,y:0,width:o.width,height:o.height},s=e.extendIf({width:o.width,height:o.height,type:o.type||"image/jpeg",quality:90,crop:!1,fit:!0,preserveHeaders:!0,resample:"default",multipass:!0},t);try{if(!o.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);if(o.width>m.MAX_RESIZE_WIDTH||o.height>m.MAX_RESIZE_HEIGHT)throw new i.ImageError(i.ImageError.MAX_RESOLUTION_ERR);if(n=o.meta&&o.meta.tiff&&o.meta.tiff.Orientation||1,-1!==e.inArray(n,[5,6,7,8])){var u=s.width;s.width=s.height,s.height=u}if(s.crop){switch(r=Math.max(s.width/o.width,s.height/o.height),t.fit?(a.width=Math.min(Math.ceil(s.width/r),o.width),a.height=Math.min(Math.ceil(s.height/r),o.height),r=s.width/a.width):(a.width=Math.min(s.width,o.width),a.height=Math.min(s.height,o.height),r=1),"boolean"==typeof s.crop&&(s.crop="cc"),s.crop.toLowerCase().replace(/_/,"-")){case"rb":case"right-bottom":a.x=o.width-a.width,a.y=o.height-a.height;break;case"cb":case"center-bottom":a.x=Math.floor((o.width-a.width)/2),a.y=o.height-a.height;break;case"lb":case"left-bottom":a.x=0,a.y=o.height-a.height;break;case"lt":case"left-top":a.x=0,a.y=0;break;case"ct":case"center-top":a.x=Math.floor((o.width-a.width)/2),a.y=0;break;case"rt":case"right-top":a.x=o.width-a.width,a.y=0;break;case"rc":case"right-center":case"right-middle":a.x=o.width-a.width,a.y=Math.floor((o.height-a.height)/2);break;case"lc":case"left-center":case"left-middle":a.x=0,a.y=Math.floor((o.height-a.height)/2);break;case"cc":case"center-center":case"center-middle":default:a.x=Math.floor((o.width-a.width)/2),a.y=Math.floor((o.height-a.height)/2)}a.x=Math.max(a.x,0),a.y=Math.max(a.y,0)}else(r=Math.min(s.width/o.width,s.height/o.height))>1&&!s.fit&&(r=1);this.exec("Image","resize",a,r,s)}catch(e){o.trigger("error",e.code)}},downsize:function(t){var i,n={width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90,crop:!1,fit:!1,preserveHeaders:!0,resample:"default"};i="object"==typeof t?e.extend(n,t):e.extend(n,{width:arguments[0],height:arguments[1],crop:arguments[2],preserveHeaders:arguments[3]}),this.resize(i)},crop:function(e,t,i){this.downsize(e,t,!0,i)},getAsCanvas:function(){if(!u.can("create_canvas"))throw new i.RuntimeError(i.RuntimeError.NOT_SUPPORTED_ERR);return this.exec("Image","getAsCanvas")},getAsBlob:function(e,t){if(!this.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);return this.exec("Image","getAsBlob",e||"image/jpeg",t||90)},getAsDataURL:function(e,t){if(!this.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);return this.exec("Image","getAsDataURL",e||"image/jpeg",t||90)},getAsBinaryString:function(e,t){var i=this.getAsDataURL(e,t);return p.atob(i.substring(i.indexOf("base64,")+7))},embed:function(n,r){var o,a=this,l=e.extend({width:this.width,height:this.height,type:this.type||"image/jpeg",quality:90,fit:!0,resample:"nearest"},r);try{if(!(n=t.get(n)))throw new i.DOMException(i.DOMException.INVALID_NODE_TYPE_ERR);if(!this.size)throw new i.DOMException(i.DOMException.INVALID_STATE_ERR);this.width>m.MAX_RESIZE_WIDTH||(this.height,m.MAX_RESIZE_HEIGHT);var c=new m;return c.bind("Resize",function(){(function(t,r){var l=this;if(u.can("create_canvas")){var c=l.getAsCanvas();if(c)return n.appendChild(c),c=null,l.destroy(),void a.trigger("embedded")}var d=l.getAsDataURL(t,r);if(!d)throw new i.ImageError(i.ImageError.WRONG_FORMAT);if(u.can("use_data_uri_of",d.length))n.innerHTML='<img src="'+d+'" width="'+l.width+'" height="'+l.height+'" alt="" />',l.destroy(),a.trigger("embedded");else{var m=new s;m.bind("TransportingComplete",function(){o=a.connectRuntime(this.result.ruid),a.bind("Embedded",function(){e.extend(o.getShimContainer().style,{top:"0px",left:"0px",width:l.width+"px",height:l.height+"px"}),o=null},999),o.exec.call(a,"ImageView","display",this.result.uid,width,height),l.destroy()}),m.transport(p.atob(d.substring(d.indexOf("base64,")+7)),t,{required_caps:{display_media:!0},runtime_order:"flash,silverlight",container:n})}}).call(this,l.type,l.quality)}),c.bind("Load",function(){this.downsize(l)}),this.meta.thumb&&this.meta.thumb.width>=l.width&&this.meta.thumb.height>=l.height?c.load(this.meta.thumb.data):c.clone(this,!1),c}catch(e){this.trigger("error",e.code)}},destroy:function(){this.ruid&&(this.getRuntime().exec.call(this,"Image","destroy"),this.disconnectRuntime()),this.meta&&this.meta.thumb&&this.meta.thumb.data.destroy(),this.unbindAll()}}),this.handleEventProps(f),this.bind("Load Resize",function(){return function(e){try{return e||(e=this.exec("Image","getInfo")),this.size=e.size,this.width=e.width,this.height=e.height,this.type=e.type,this.meta=e.meta,""===this.name&&(this.name=e.name),!0}catch(e){return this.trigger("error",e.code),!1}}.call(this)},999)}var f=["progress","load","error","resize","embedded"];return m.MAX_RESIZE_WIDTH=8192,m.MAX_RESIZE_HEIGHT=8192,m.prototype=l.instance,m}),i("moxie/runtime/html5/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(e,t,i,r){var o="html5",a={};return i.addConstructor(o,function(t){var s=this,u=i.capTest,l=i.capTrue,c=e.extend({access_binary:u(window.FileReader||window.File&&window.File.getAsDataURL),access_image_binary:function(){return s.can("access_binary")&&!!a.Image},display_media:u((r.can("create_canvas")||r.can("use_data_uri_over32kb"))&&n("moxie/image/Image")),do_cors:u(window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest),drag_and_drop:u(function(){var e=document.createElement("div");return("draggable"in e||"ondragstart"in e&&"ondrop"in e)&&("IE"!==r.browser||r.verComp(r.version,9,">"))}()),filter_by_extension:u(!("Chrome"===r.browser&&r.verComp(r.version,28,"<")||"IE"===r.browser&&r.verComp(r.version,10,"<")||"Safari"===r.browser&&r.verComp(r.version,7,"<")||"Firefox"===r.browser&&r.verComp(r.version,37,"<"))),return_response_headers:l,return_response_type:function(e){return!("json"!==e||!window.JSON)||r.can("return_response_type",e)},return_status_code:l,report_upload_progress:u(window.XMLHttpRequest&&(new XMLHttpRequest).upload),resize_image:function(){return s.can("access_binary")&&r.can("create_canvas")},select_file:function(){return r.can("use_fileinput")&&window.File},select_folder:function(){return s.can("select_file")&&("Chrome"===r.browser&&r.verComp(r.version,21,">=")||"Firefox"===r.browser&&r.verComp(r.version,42,">="))},select_multiple:function(){return!(!s.can("select_file")||"Safari"===r.browser&&"Windows"===r.os||"iOS"===r.os&&r.verComp(r.osVersion,"7.0.0",">")&&r.verComp(r.osVersion,"8.0.0","<"))},send_binary_string:u(window.XMLHttpRequest&&((new XMLHttpRequest).sendAsBinary||window.Uint8Array&&window.ArrayBuffer)),send_custom_headers:u(window.XMLHttpRequest),send_multipart:function(){return!!(window.XMLHttpRequest&&(new XMLHttpRequest).upload&&window.FormData)||s.can("send_binary_string")},slice_blob:u(window.File&&(File.prototype.mozSlice||File.prototype.webkitSlice||File.prototype.slice)),stream_upload:function(){return s.can("slice_blob")&&s.can("send_multipart")},summon_file_dialog:function(){return s.can("select_file")&&!("Firefox"===r.browser&&r.verComp(r.version,4,"<")||"Opera"===r.browser&&r.verComp(r.version,12,"<")||"IE"===r.browser&&r.verComp(r.version,10,"<"))},upload_filesize:l,use_http_method:l},arguments[2]);i.call(this,t,arguments[1]||o,c),e.extend(this,{init:function(){this.trigger("Init")},destroy:function(e){return function(){e.call(s),e=s=null}}(this.destroy)}),e.extend(this.getShim(),a)}),a}),i("moxie/runtime/html5/file/Blob",["moxie/runtime/html5/Runtime","moxie/file/Blob"],function(e,t){return e.Blob=function(){this.slice=function(){return new t(this.getRuntime().uid,function(e,t,i){var n;if(!window.File.prototype.slice)return(n=window.File.prototype.webkitSlice||window.File.prototype.mozSlice)?n.call(e,t,i):null;try{return e.slice(),e.slice(t,i)}catch(n){return e.slice(t,i-t)}}.apply(this,arguments))},this.destroy=function(){this.getRuntime().getShim().removeInstance(this.uid)}}}),i("moxie/core/utils/Events",["moxie/core/utils/Basic"],function(e){function t(){this.returnValue=!1}function i(){this.cancelBubble=!0}var n={},r="moxie_"+e.guid(),o=function(t,i,o){var a,s;if(i=i.toLowerCase(),t[r]&&n[t[r]]&&n[t[r]][i]){for(var u=(a=n[t[r]][i]).length-1;u>=0&&(a[u].orig!==o&&a[u].key!==o||(t.removeEventListener?t.removeEventListener(i,a[u].func,!1):t.detachEvent&&t.detachEvent("on"+i,a[u].func),a[u].orig=null,a[u].func=null,a.splice(u,1),o===s));u--);if(a.length||delete n[t[r]][i],e.isEmptyObj(n[t[r]])){delete n[t[r]];try{delete t[r]}catch(e){t[r]=s}}}};return{addEvent:function(o,a,s,u){var l,c;a=a.toLowerCase(),o.addEventListener?(l=s,o.addEventListener(a,l,!1)):o.attachEvent&&(l=function(){var e=window.event;e.target||(e.target=e.srcElement),e.preventDefault=t,e.stopPropagation=i,s(e)},o.attachEvent("on"+a,l)),o[r]||(o[r]=e.guid()),n.hasOwnProperty(o[r])||(n[o[r]]={}),(c=n[o[r]]).hasOwnProperty(a)||(c[a]=[]),c[a].push({func:l,orig:s,key:u})},removeEvent:o,removeAllEvents:function(t,i){t&&t[r]&&e.each(n[t[r]],function(e,n){o(t,n,i)})}}}),i("moxie/runtime/html5/file/FileInput",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,t,i,n,r,o,a){return e.FileInput=function(){var e,s;i.extend(this,{init:function(u){var l,c,d,p,m,f,h=this,g=h.getRuntime();e=u,d=o.extList2mimes(e.accept,g.can("filter_by_extension")),(c=g.getShimContainer()).innerHTML='<input id="'+g.uid+'" type="file" style="font-size:999px;opacity:0;"'+(e.multiple&&g.can("select_multiple")?"multiple":"")+(e.directory&&g.can("select_folder")?"webkitdirectory directory":"")+(d?' accept="'+d.join(",")+'"':"")+" />",l=n.get(g.uid),i.extend(l.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),p=n.get(e.browse_button),s=n.getStyle(p,"z-index")||"auto",g.can("summon_file_dialog")&&("static"===n.getStyle(p,"position")&&(p.style.position="relative"),r.addEvent(p,"click",function(e){var t=n.get(g.uid);t&&!t.disabled&&t.click(),e.preventDefault()},h.uid),h.bind("Refresh",function(){m=parseInt(s,10)||1,n.get(e.browse_button).style.zIndex=m,this.getRuntime().getShimContainer().style.zIndex=m-1})),f=g.can("summon_file_dialog")?p:c,r.addEvent(f,"mouseover",function(){h.trigger("mouseenter")},h.uid),r.addEvent(f,"mouseout",function(){h.trigger("mouseleave")},h.uid),r.addEvent(f,"mousedown",function(){h.trigger("mousedown")},h.uid),r.addEvent(n.get(e.container),"mouseup",function(){h.trigger("mouseup")},h.uid),(g.can("summon_file_dialog")?l:p).setAttribute("tabindex",-1),l.onchange=function n(){if(h.files=[],i.each(this.files,function(i){var n="";return!(!e.directory||"."!=i.name)||(i.webkitRelativePath&&(n="/"+i.webkitRelativePath.replace(/^\//,"")),(i=new t(g.uid,i)).relativePath=n,void h.files.push(i))}),"IE"!==a.browser&&"IEMobile"!==a.browser)this.value="";else{var r=this.cloneNode(!0);this.parentNode.replaceChild(r,this),r.onchange=n}h.files.length&&h.trigger("change")},h.trigger({type:"ready",async:!0}),c=null},setOption:function(e,t){var i=this.getRuntime(),r=n.get(i.uid);switch(e){case"accept":if(t){var a=t.mimes||o.extList2mimes(t,i.can("filter_by_extension"));r.setAttribute("accept",a.join(","))}else r.removeAttribute("accept");break;case"directory":t&&i.can("select_folder")?(r.setAttribute("directory",""),r.setAttribute("webkitdirectory","")):(r.removeAttribute("directory"),r.removeAttribute("webkitdirectory"));break;case"multiple":t&&i.can("select_multiple")?r.setAttribute("multiple",""):r.removeAttribute("multiple")}},disable:function(e){var t,i=this.getRuntime();(t=n.get(i.uid))&&(t.disabled=!!e)},destroy:function(){var t=this.getRuntime(),i=t.getShim(),o=t.getShimContainer(),a=e&&n.get(e.container),u=e&&n.get(e.browse_button);a&&r.removeAllEvents(a,this.uid),u&&(r.removeAllEvents(u,this.uid),u.style.zIndex=s),o&&(r.removeAllEvents(o,this.uid),o.innerHTML=""),i.removeInstance(this.uid),e=o=a=u=i=null}})}}),i("moxie/runtime/html5/file/FileDrop",["moxie/runtime/html5/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime"],function(e,t,i,n,r,o){return e.FileDrop=function(){function e(e){if(!e.dataTransfer||!e.dataTransfer.types)return!1;var t=i.toArray(e.dataTransfer.types||[]);return-1!==i.inArray("Files",t)||-1!==i.inArray("public.file-url",t)||-1!==i.inArray("application/x-moz-file",t)}function a(e,i){if(s(e)){var n=new t(d,e);n.relativePath=i||"",p.push(n)}}function s(e){if(!m.length)return!0;var t=o.getFileExtension(e.name);return!t||-1!==i.inArray(t,m)}function u(e,t){var n=[];i.each(e,function(e){n.push(function(t){l(e,t)})}),i.inSeries(n,function(){t()})}function l(e,t){e.isFile?e.file(function(i){a(i,e.fullPath),t()},function(){t()}):e.isDirectory?function(e,t){var i=[],n=e.createReader();!function e(t){n.readEntries(function(n){n.length?([].push.apply(i,n),e(t)):t()},t)}(function(){u(i,t)})}(e,t):t()}var c,d,p=[],m=[];i.extend(this,{init:function(t){var n,o=this;c=t,d=o.ruid,m=function(e){for(var t=[],n=0;n<e.length;n++)[].push.apply(t,e[n].extensions.split(/\s*,\s*/));return-1===i.inArray("*",t)?t:[]}(c.accept),n=c.container,r.addEvent(n,"dragover",function(t){e(t)&&(t.preventDefault(),t.dataTransfer.dropEffect="copy")},o.uid),r.addEvent(n,"drop",function(t){e(t)&&(t.preventDefault(),p=[],t.dataTransfer.items&&t.dataTransfer.items[0].webkitGetAsEntry?function(e,t){var n=[];i.each(e,function(e){var t=e.webkitGetAsEntry();t&&(t.isFile?a(e.getAsFile(),t.fullPath):n.push(t))}),n.length?u(n,t):t()}(t.dataTransfer.items,function(){o.files=p,o.trigger("drop")}):(i.each(t.dataTransfer.files,function(e){a(e)}),o.files=p,o.trigger("drop")))},o.uid),r.addEvent(n,"dragenter",function(){o.trigger("dragenter")},o.uid),r.addEvent(n,"dragleave",function(){o.trigger("dragleave")},o.uid)},destroy:function(){r.removeAllEvents(c&&n.get(c.container),this.uid),d=p=m=c=null,this.getRuntime().getShim().removeInstance(this.uid)}})}}),i("moxie/runtime/html5/file/FileReader",["moxie/runtime/html5/Runtime","moxie/core/utils/Encode","moxie/core/utils/Basic"],function(e,t,i){return e.FileReader=function(){function e(e){return t.atob(e.substring(e.indexOf("base64,")+7))}var n,r=!1;i.extend(this,{read:function(t,o){var a=this;a.result="",(n=new window.FileReader).addEventListener("progress",function(e){a.trigger(e)}),n.addEventListener("load",function(t){a.result=r?e(n.result):n.result,a.trigger(t)}),n.addEventListener("error",function(e){a.trigger(e,n.error)}),n.addEventListener("loadend",function(e){n=null,a.trigger(e)}),"function"===i.typeOf(n[t])?(r=!1,n[t](o.getSource())):"readAsBinaryString"===t&&(r=!0,n.readAsDataURL(o.getSource()))},abort:function(){n&&n.abort()},destroy:function(){n=null,this.getRuntime().getShim().removeInstance(this.uid)}})}}),i("moxie/runtime/html5/xhr/XMLHttpRequest",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/utils/Mime","moxie/core/utils/Url","moxie/file/File","moxie/file/Blob","moxie/xhr/FormData","moxie/core/Exceptions","moxie/core/utils/Env"],function(e,t,i,n,r,o,a,s,u){return e.XMLHttpRequest=function(){function e(e,t){var i,n,r=this;i=t.getBlob().getSource(),(n=new window.FileReader).onload=function(){t.append(t.getBlobName(),new o(null,{type:i.type,data:n.result})),p.send.call(r,e,t)},n.readAsBinaryString(i)}function l(e){var t="----moxieboundary"+(new Date).getTime(),i="--",n="\r\n",r="",a=this.getRuntime();if(!a.can("send_binary_string"))throw new s.RuntimeError(s.RuntimeError.NOT_SUPPORTED_ERR);return c.setRequestHeader("Content-Type","multipart/form-data; boundary="+t),e.each(function(e,a){r+=e instanceof o?i+t+n+'Content-Disposition: form-data; name="'+a+'"; filename="'+unescape(encodeURIComponent(e.name||"blob"))+'"'+n+"Content-Type: "+(e.type||"application/octet-stream")+n+n+e.getSource()+n:i+t+n+'Content-Disposition: form-data; name="'+a+'"'+n+n+unescape(encodeURIComponent(e))+n}),r+=i+t+i+n}var c,d,p=this;t.extend(this,{send:function(i,r){var s=this,p="Mozilla"===u.browser&&u.verComp(u.version,4,">=")&&u.verComp(u.version,7,"<"),m="Android Browser"===u.browser,f=!1;if(d=i.url.replace(/^.+?\/([\w\-\.]+)$/,"$1").toLowerCase(),(c=!window.XMLHttpRequest||"IE"===u.browser&&u.verComp(u.version,8,"<")?function(){for(var e=["Msxml2.XMLHTTP.6.0","Microsoft.XMLHTTP"],t=0;t<e.length;t++)try{return new ActiveXObject(e[t])}catch(e){}}():new window.XMLHttpRequest).open(i.method,i.url,i.async,i.user,i.password),r instanceof o)r.isDetached()&&(f=!0),r=r.getSource();else if(r instanceof a){if(r.hasBlob())if(r.getBlob().isDetached())r=l.call(s,r),f=!0;else if((p||m)&&"blob"===t.typeOf(r.getBlob().getSource())&&window.FileReader)return void e.call(s,i,r);if(r instanceof a){var h=new window.FormData;r.each(function(e,t){e instanceof o?h.append(t,e.getSource()):h.append(t,e)}),r=h}}c.upload?(i.withCredentials&&(c.withCredentials=!0),c.addEventListener("load",function(e){s.trigger(e)}),c.addEventListener("error",function(e){s.trigger(e)}),c.addEventListener("progress",function(e){s.trigger(e)}),c.upload.addEventListener("progress",function(e){s.trigger({type:"UploadProgress",loaded:e.loaded,total:e.total})})):c.onreadystatechange=function(){switch(c.readyState){case 1:case 2:break;case 3:var e,t;try{n.hasSameOrigin(i.url)&&(e=c.getResponseHeader("Content-Length")||0),c.responseText&&(t=c.responseText.length)}catch(i){e=t=0}s.trigger({type:"progress",lengthComputable:!!e,total:parseInt(e,10),loaded:t});break;case 4:c.onreadystatechange=function(){};try{if(c.status>=200&&c.status<400){s.trigger("load");break}}catch(e){}s.trigger("error")}},t.isEmptyObj(i.headers)||t.each(i.headers,function(e,t){c.setRequestHeader(t,e)}),""!==i.responseType&&"responseType"in c&&(c.responseType="json"!==i.responseType||u.can("return_response_type","json")?i.responseType:"text"),f?c.sendAsBinary?c.sendAsBinary(r):function(){for(var e=new Uint8Array(r.length),t=0;t<r.length;t++)e[t]=255&r.charCodeAt(t);c.send(e.buffer)}():c.send(r),s.trigger("loadstart")},getStatus:function(){try{if(c)return c.status}catch(e){}return 0},getResponse:function(e){var t=this.getRuntime();try{switch(e){case"blob":var n=new r(t.uid,c.response),o=c.getResponseHeader("Content-Disposition");if(o){var a=o.match(/filename=([\'\"'])([^\1]+)\1/);a&&(d=a[2])}return n.name=d,n.type||(n.type=i.getFileMime(d)),n;case"json":return u.can("return_response_type","json")?c.response:200===c.status&&window.JSON?JSON.parse(c.responseText):null;case"document":return function(e){var t=e.responseXML,i=e.responseText;return"IE"===u.browser&&i&&t&&!t.documentElement&&/[^\/]+\/[^\+]+\+xml/.test(e.getResponseHeader("Content-Type"))&&((t=new window.ActiveXObject("Microsoft.XMLDOM")).async=!1,t.validateOnParse=!1,t.loadXML(i)),t&&("IE"===u.browser&&0!==t.parseError||!t.documentElement||"parsererror"===t.documentElement.tagName)?null:t}(c);default:return""!==c.responseText?c.responseText:null}}catch(e){return null}},getAllResponseHeaders:function(){try{return c.getAllResponseHeaders()}catch(e){}return""},abort:function(){c&&c.abort()},destroy:function(){p=d=null,this.getRuntime().getShim().removeInstance(this.uid)}})}}),i("moxie/runtime/html5/utils/BinaryReader",["moxie/core/utils/Basic"],function(e){function t(e){e instanceof ArrayBuffer?i.apply(this,arguments):n.apply(this,arguments)}function i(t){var i=new DataView(t);e.extend(this,{readByteAt:function(e){return i.getUint8(e)},writeByteAt:function(e,t){i.setUint8(e,t)},SEGMENT:function(e,n,r){switch(arguments.length){case 2:return t.slice(e,e+n);case 1:return t.slice(e);case 3:if(null===r&&(r=new ArrayBuffer),r instanceof ArrayBuffer){var o=new Uint8Array(this.length()-n+r.byteLength);e>0&&o.set(new Uint8Array(t.slice(0,e)),0),o.set(new Uint8Array(r),e),o.set(new Uint8Array(t.slice(e+n)),e+r.byteLength),this.clear(),t=o.buffer,i=new DataView(t);break}default:return t}},length:function(){return t?t.byteLength:0},clear:function(){i=t=null}})}function n(t){function i(e,i,n){n=3===arguments.length?n:t.length-i-1,t=t.substr(0,i)+e+t.substr(n+i)}e.extend(this,{readByteAt:function(e){return t.charCodeAt(e)},writeByteAt:function(e,t){i(String.fromCharCode(t),e,1)},SEGMENT:function(e,n,r){switch(arguments.length){case 1:return t.substr(e);case 2:return t.substr(e,n);case 3:i(null!==r?r:"",e,n);break;default:return t}},length:function(){return t?t.length:0},clear:function(){t=null}})}return e.extend(t.prototype,{littleEndian:!1,read:function(e,t){var i,n,r;if(e+t>this.length())throw new Error("You are trying to read outside the source boundaries.");for(n=this.littleEndian?0:-8*(t-1),r=0,i=0;t>r;r++)i|=this.readByteAt(e+r)<<Math.abs(n+8*r);return i},write:function(e,t,i){var n,r;if(e>this.length())throw new Error("You are trying to write outside the source boundaries.");for(n=this.littleEndian?0:-8*(i-1),r=0;i>r;r++)this.writeByteAt(e+r,255&t>>Math.abs(n+8*r))},BYTE:function(e){return this.read(e,1)},SHORT:function(e){return this.read(e,2)},LONG:function(e){return this.read(e,4)},SLONG:function(e){var t=this.read(e,4);return t>2147483647?t-4294967296:t},CHAR:function(e){return String.fromCharCode(this.read(e,1))},STRING:function(e,t){return this.asArray("CHAR",e,t).join("")},asArray:function(e,t,i){for(var n=[],r=0;i>r;r++)n[r]=this[e](t+r);return n}}),t}),i("moxie/runtime/html5/image/JPEGHeaders",["moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(e,t){return function i(n){var r,o,a,s=[],u=0;if(65496!==(r=new e(n)).SHORT(0))throw r.clear(),new t.ImageError(t.ImageError.WRONG_FORMAT);for(o=2;o<=r.length();)if((a=r.SHORT(o))>=65488&&65495>=a)o+=2;else{if(65498===a||65497===a)break;u=r.SHORT(o+2)+2,a>=65505&&65519>=a&&s.push({hex:a,name:"APP"+(15&a),start:o,length:u,segment:r.SEGMENT(o,u)}),o+=u}return r.clear(),{headers:s,restore:function(t){var i,n,r;for(r=new e(t),o=65504==r.SHORT(2)?4+r.SHORT(4):2,n=0,i=s.length;i>n;n++)r.SEGMENT(o,0,s[n].segment),o+=s[n].length;return t=r.SEGMENT(),r.clear(),t},strip:function(t){var n,r,o,a;for(o=new i(t),r=o.headers,o.purge(),n=new e(t),a=r.length;a--;)n.SEGMENT(r[a].start,r[a].length,"");return t=n.SEGMENT(),n.clear(),t},get:function(e){for(var t=[],i=0,n=s.length;n>i;i++)s[i].name===e.toUpperCase()&&t.push(s[i].segment);return t},set:function(e,t){var i,n,r,o=[];for("string"==typeof t?o.push(t):o=t,i=n=0,r=s.length;r>i&&(s[i].name===e.toUpperCase()&&(s[i].segment=o[n],s[i].length=o[n].length,n++),!(n>=o.length));i++);},purge:function(){this.headers=s=[]}}}}),i("moxie/runtime/html5/image/ExifParser",["moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader","moxie/core/Exceptions"],function(e,i,n){function r(o){function a(i,r){var o,a,s,u,d,p,m,f,h=this,g=[],v={},_={1:"BYTE",7:"UNDEFINED",2:"ASCII",3:"SHORT",4:"LONG",5:"RATIONAL",9:"SLONG",10:"SRATIONAL"},x={BYTE:1,UNDEFINED:1,ASCII:1,SHORT:2,LONG:4,RATIONAL:8,SLONG:4,SRATIONAL:8};for(o=h.SHORT(i),a=0;o>a;a++)if(g=[],m=i+2+12*a,(s=r[h.SHORT(m)])!==t){if(u=_[h.SHORT(m+=2)],d=h.LONG(m+=2),!(p=x[u]))throw new n.ImageError(n.ImageError.INVALID_META_ERR);if(m+=4,p*d>4&&(m=h.LONG(m)+c.tiffHeader),m+p*d>=this.length())throw new n.ImageError(n.ImageError.INVALID_META_ERR);"ASCII"!==u?(g=h.asArray(u,m,d),f=1==d?g[0]:g,v[s]=l.hasOwnProperty(s)&&"object"!=typeof f?l[s][f]:f):v[s]=e.trim(h.STRING(m,d).replace(/\0$/,""))}return v}var s,u,l,c,d,p;if(i.call(this,o),u={tiff:{274:"Orientation",270:"ImageDescription",271:"Make",272:"Model",305:"Software",34665:"ExifIFDPointer",34853:"GPSInfoIFDPointer"},exif:{36864:"ExifVersion",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelYDimension",36867:"DateTimeOriginal",33434:"ExposureTime",33437:"FNumber",34855:"ISOSpeedRatings",37377:"ShutterSpeedValue",37378:"ApertureValue",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37386:"FocalLength",41986:"ExposureMode",41987:"WhiteBalance",41990:"SceneCaptureType",41988:"DigitalZoomRatio",41992:"Contrast",41993:"Saturation",41994:"Sharpness"},gps:{0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude"},thumb:{513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength"}},l={ColorSpace:{1:"sRGB",0:"Uncalibrated"},MeteringMode:{0:"Unknown",1:"Average",2:"CenterWeightedAverage",3:"Spot",4:"MultiSpot",5:"Pattern",6:"Partial",255:"Other"},LightSource:{1:"Daylight",2:"Fliorescent",3:"Tungsten",4:"Flash",9:"Fine weather",10:"Cloudy weather",11:"Shade",12:"Daylight fluorescent (D 5700 - 7100K)",13:"Day white fluorescent (N 4600 -5400K)",14:"Cool white fluorescent (W 3900 - 4500K)",15:"White fluorescent (WW 3200 - 3700K)",17:"Standard light A",18:"Standard light B",19:"Standard light C",20:"D55",21:"D65",22:"D75",23:"D50",24:"ISO studio tungsten",255:"Other"},Flash:{0:"Flash did not fire",1:"Flash fired",5:"Strobe return light not detected",7:"Strobe return light detected",9:"Flash fired, compulsory flash mode",13:"Flash fired, compulsory flash mode, return light not detected",15:"Flash fired, compulsory flash mode, return light detected",16:"Flash did not fire, compulsory flash mode",24:"Flash did not fire, auto mode",25:"Flash fired, auto mode",29:"Flash fired, auto mode, return light not detected",31:"Flash fired, auto mode, return light detected",32:"No flash function",65:"Flash fired, red-eye reduction mode",69:"Flash fired, red-eye reduction mode, return light not detected",71:"Flash fired, red-eye reduction mode, return light detected",73:"Flash fired, compulsory flash mode, red-eye reduction mode",77:"Flash fired, compulsory flash mode, red-eye reduction mode, return light not detected",79:"Flash fired, compulsory flash mode, red-eye reduction mode, return light detected",89:"Flash fired, auto mode, red-eye reduction mode",93:"Flash fired, auto mode, return light not detected, red-eye reduction mode",95:"Flash fired, auto mode, return light detected, red-eye reduction mode"},ExposureMode:{0:"Auto exposure",1:"Manual exposure",2:"Auto bracket"},WhiteBalance:{0:"Auto white balance",1:"Manual white balance"},SceneCaptureType:{0:"Standard",1:"Landscape",2:"Portrait",3:"Night scene"},Contrast:{0:"Normal",1:"Soft",2:"Hard"},Saturation:{0:"Normal",1:"Low saturation",2:"High saturation"},Sharpness:{0:"Normal",1:"Soft",2:"Hard"},GPSLatitudeRef:{N:"North latitude",S:"South latitude"},GPSLongitudeRef:{E:"East longitude",W:"West longitude"}},d=(c={tiffHeader:10}).tiffHeader,s={clear:this.clear},e.extend(this,{read:function(){try{return r.prototype.read.apply(this,arguments)}catch(e){throw new n.ImageError(n.ImageError.INVALID_META_ERR)}},write:function(){try{return r.prototype.write.apply(this,arguments)}catch(e){throw new n.ImageError(n.ImageError.INVALID_META_ERR)}},UNDEFINED:function(){return this.BYTE.apply(this,arguments)},RATIONAL:function(e){return this.LONG(e)/this.LONG(e+4)},SRATIONAL:function(e){return this.SLONG(e)/this.SLONG(e+4)},ASCII:function(e){return this.CHAR(e)},TIFF:function(){return p||null},EXIF:function(){var t=null;if(c.exifIFD){try{t=a.call(this,c.exifIFD,u.exif)}catch(e){return null}if(t.ExifVersion&&"array"===e.typeOf(t.ExifVersion)){for(var i=0,n="";i<t.ExifVersion.length;i++)n+=String.fromCharCode(t.ExifVersion[i]);t.ExifVersion=n}}return t},GPS:function(){var t=null;if(c.gpsIFD){try{t=a.call(this,c.gpsIFD,u.gps)}catch(e){return null}t.GPSVersionID&&"array"===e.typeOf(t.GPSVersionID)&&(t.GPSVersionID=t.GPSVersionID.join("."))}return t},thumb:function(){if(c.IFD1)try{var e=a.call(this,c.IFD1,u.thumb);if("JPEGInterchangeFormat"in e)return this.SEGMENT(c.tiffHeader+e.JPEGInterchangeFormat,e.JPEGInterchangeFormatLength)}catch(e){}return null},setExif:function(e,t){return("PixelXDimension"===e||"PixelYDimension"===e)&&function(e,t,i){var n,r,o,a=0;if("string"==typeof t){var s=u[e.toLowerCase()];for(var l in s)if(s[l]===t){t=l;break}}n=c[e.toLowerCase()+"IFD"],r=this.SHORT(n);for(var d=0;r>d;d++)if(o=n+12*d+2,this.SHORT(o)==t){a=o+8;break}if(!a)return!1;try{this.write(a,i,4)}catch(e){return!1}return!0}.call(this,"exif",e,t)},clear:function(){s.clear(),o=u=l=p=c=s=null}}),65505!==this.SHORT(0)||"EXIF\0"!==this.STRING(4,5).toUpperCase())throw new n.ImageError(n.ImageError.INVALID_META_ERR);if(this.littleEndian=18761==this.SHORT(d),42!==this.SHORT(d+=2))throw new n.ImageError(n.ImageError.INVALID_META_ERR);c.IFD0=c.tiffHeader+this.LONG(d+=2),"ExifIFDPointer"in(p=a.call(this,c.IFD0,u.tiff))&&(c.exifIFD=c.tiffHeader+p.ExifIFDPointer,delete p.ExifIFDPointer),"GPSInfoIFDPointer"in p&&(c.gpsIFD=c.tiffHeader+p.GPSInfoIFDPointer,delete p.GPSInfoIFDPointer),e.isEmptyObj(p)&&(p=null);var m=this.LONG(c.IFD0+12*this.SHORT(c.IFD0)+2);m&&(c.IFD1=c.tiffHeader+m)}return r.prototype=i.prototype,r}),i("moxie/runtime/html5/image/JPEG",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEGHeaders","moxie/runtime/html5/utils/BinaryReader","moxie/runtime/html5/image/ExifParser"],function(e,t,i,n,r){return function(o){function a(e){var t,i,n=0;for(e||(e=s);n<=e.length();){if((t=e.SHORT(n+=2))>=65472&&65475>=t)return n+=5,{height:e.SHORT(n),width:e.SHORT(n+=2)};i=e.SHORT(n+=2),n+=i-2}return null}var s,u,l,c;if(65496!==(s=new n(o)).SHORT(0))throw new t.ImageError(t.ImageError.WRONG_FORMAT);u=new i(o);try{l=new r(u.get("app1")[0])}catch(e){}c=a.call(this),e.extend(this,{type:"image/jpeg",size:s.length(),width:c&&c.width||0,height:c&&c.height||0,setExif:function(t,i){return!!l&&("object"===e.typeOf(t)?e.each(t,function(e,t){l.setExif(t,e)}):l.setExif(t,i),void u.set("app1",l.SEGMENT()))},writeHeaders:function(){return arguments.length?u.restore(arguments[0]):u.restore(o)},stripHeaders:function(e){return u.strip(e)},purge:function(){(function(){l&&u&&s&&(l.clear(),u.purge(),s.clear(),c=u=l=s=null)}).call(this)}}),l&&(this.meta={tiff:l.TIFF(),exif:l.EXIF(),gps:l.GPS(),thumb:function(){var e,t,i=l.thumb();return i&&(e=new n(i),t=a(e),e.clear(),t)?(t.data=i,t):null}()})}}),i("moxie/runtime/html5/image/PNG",["moxie/core/Exceptions","moxie/core/utils/Basic","moxie/runtime/html5/utils/BinaryReader"],function(e,t,i){return function(n){function r(){o&&(o.clear(),n=a=o=null)}var o,a;o=new i(n),function(){var t=0,i=0,n=[35152,20039,3338,6666];for(i=0;i<n.length;i++,t+=2)if(n[i]!=o.SHORT(t))throw new e.ImageError(e.ImageError.WRONG_FORMAT)}(),a=function(){var e,t;return"IHDR"==(e=function(e){var t,i,n,r;return t=o.LONG(e),i=o.STRING(e+=4,4),n=e+=4,r=o.LONG(e+t),{length:t,type:i,start:n,CRC:r}}.call(this,8)).type?(t=e.start,{width:o.LONG(t),height:o.LONG(t+=4)}):null}.call(this),t.extend(this,{type:"image/png",size:o.length(),width:a.width,height:a.height,purge:function(){r.call(this)}}),r.call(this)}}),i("moxie/runtime/html5/image/ImageInfo",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/html5/image/JPEG","moxie/runtime/html5/image/PNG"],function(e,t,i,n){return function(r){var o,a=[i,n];o=function(){for(var e=0;e<a.length;e++)try{return new a[e](r)}catch(e){}throw new t.ImageError(t.ImageError.WRONG_FORMAT)}(),e.extend(this,{type:"",size:0,width:0,height:0,setExif:function(){},writeHeaders:function(e){return e},stripHeaders:function(e){return e},purge:function(){r=null}}),e.extend(this,o),this.purge=function(){o.purge(),o=null}}}),i("moxie/runtime/html5/image/ResizerCanvas",[],function(){return{scale:function e(t,i,n){var r=t.width>t.height?"width":"height",o=Math.round(t[r]*i),a=!1;"nearest"!==n&&(.5>i||i>2)&&(i=.5>i?.5:2,a=!0);var s=function(e,t){var i=e.width,n=e.height,r=Math.round(i*t),o=Math.round(n*t),a=document.createElement("canvas");return a.width=r,a.height=o,a.getContext("2d").drawImage(e,0,0,i,n,0,0,r,o),e=null,a}(t,i);return a?e(s,o/s[r],n):s}}}),i("moxie/runtime/html5/image/Image",["moxie/runtime/html5/Runtime","moxie/core/utils/Basic","moxie/core/Exceptions","moxie/core/utils/Encode","moxie/file/Blob","moxie/file/File","moxie/runtime/html5/image/ImageInfo","moxie/runtime/html5/image/ResizerCanvas","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,t,i,n,r,o,a,s,u){return e.Image=function(){function e(){if(!g&&!f)throw new i.ImageError(i.DOMException.INVALID_STATE_ERR);return g||f}function l(){var t=e();return"canvas"==t.nodeName.toLowerCase()?t:((g=document.createElement("canvas")).width=t.width,g.height=t.height,g.getContext("2d").drawImage(t,0,0),g)}function c(e){return n.atob(e.substring(e.indexOf("base64,")+7))}function d(e){var t=this;(f=new Image).onerror=function(){m.call(this),t.trigger("error",i.ImageError.WRONG_FORMAT)},f.onload=function(){t.trigger("load")},f.src="data:"==e.substr(0,5)?e:function(e,t){return"data:"+(t||"")+";base64,"+n.btoa(e)}(e,_.type)}function p(e,i){var n=Math.PI/180,r=document.createElement("canvas"),o=r.getContext("2d"),a=e.width,s=e.height;switch(t.inArray(i,[5,6,7,8])>-1?(r.width=s,r.height=a):(r.width=a,r.height=s),i){case 2:o.translate(a,0),o.scale(-1,1);break;case 3:o.translate(a,s),o.rotate(180*n);break;case 4:o.translate(0,s),o.scale(1,-1);break;case 5:o.rotate(90*n),o.scale(1,-1);break;case 6:o.rotate(90*n),o.translate(0,-s);break;case 7:o.rotate(90*n),o.translate(a,-s),o.scale(-1,1);break;case 8:o.rotate(-90*n),o.translate(-a,0)}return o.drawImage(e,0,0,a,s),r}function m(){h&&(h.purge(),h=null),v=f=g=_=null,y=!1}var f,h,g,v,_,x=this,y=!1,E=!0;t.extend(this,{loadFromBlob:function(e){var t=this.getRuntime(),n=!(arguments.length>1)||arguments[1];if(!t.can("access_binary"))throw new i.RuntimeError(i.RuntimeError.NOT_SUPPORTED_ERR);return _=e,e.isDetached()?(v=e.getSource(),void d.call(this,v)):void function(e,t){var n,r=this;return window.FileReader?((n=new FileReader).onload=function(){t.call(r,this.result)},n.onerror=function(){r.trigger("error",i.ImageError.WRONG_FORMAT)},void n.readAsDataURL(e)):t.call(this,e.getAsDataURL())}.call(this,e.getSource(),function(e){n&&(v=c(e)),d.call(this,e)})},loadFromImage:function(e,t){this.meta=e.meta,_=new o(null,{name:e.name,size:e.size,type:e.type}),d.call(this,t?v=e.getAsBinaryString():e.getAsDataURL())},getInfo:function(){var t,i=this.getRuntime();return!h&&v&&i.can("access_image_binary")&&(h=new a(v)),t={width:e().width||0,height:e().height||0,type:_.type||u.getFileMime(_.name),size:v&&v.length||_.size||0,name:_.name||"",meta:null},E&&(t.meta=h&&h.meta||this.meta||{},!t.meta||!t.meta.thumb||t.meta.thumb.data instanceof r||(t.meta.thumb.data=new r(null,{type:"image/jpeg",data:t.meta.thumb.data}))),t},resize:function(t,i,n){var r=document.createElement("canvas");if(r.width=t.width,r.height=t.height,r.getContext("2d").drawImage(e(),t.x,t.y,t.width,t.height,0,0,r.width,r.height),g=s.scale(r,i),!(E=n.preserveHeaders)){var o=this.meta&&this.meta.tiff&&this.meta.tiff.Orientation||1;g=p(g,o)}this.width=g.width,this.height=g.height,y=!0,this.trigger("Resize")},getAsCanvas:function(){return g||(g=l()),g.id=this.uid+"_canvas",g},getAsBlob:function(e,t){return e!==this.type?(y=!0,new o(null,{name:_.name||"",type:e,data:x.getAsDataURL(e,t)})):new o(null,{name:_.name||"",type:e,data:x.getAsBinaryString(e,t)})},getAsDataURL:function(e){var t=arguments[1]||90;if(!y)return f.src;if(l(),"image/jpeg"!==e)return g.toDataURL("image/png");try{return g.toDataURL("image/jpeg",t/100)}catch(e){return g.toDataURL("image/jpeg")}},getAsBinaryString:function(e,t){if(!y)return v||(v=c(x.getAsDataURL(e,t))),v;if("image/jpeg"!==e)v=c(x.getAsDataURL(e,t));else{var i;t||(t=90),l();try{i=g.toDataURL("image/jpeg",t/100)}catch(e){i=g.toDataURL("image/jpeg")}v=c(i),h&&(v=h.stripHeaders(v),E&&(h.meta&&h.meta.exif&&h.setExif({PixelXDimension:this.width,PixelYDimension:this.height}),v=h.writeHeaders(v)),h.purge(),h=null)}return y=!1,v},destroy:function(){x=null,m.call(this),this.getRuntime().getShim().removeInstance(this.uid)}})}}),i("moxie/runtime/flash/Runtime",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/runtime/Runtime"],function(e,t,i,r,o){function a(e){var n=i.get(e);n&&"OBJECT"==n.nodeName&&("IE"===t.browser?(n.style.display="none",function t(){4==n.readyState?function(e){var t=i.get(e);if(t){for(var n in t)"function"==typeof t[n]&&(t[n]=null);t.parentNode.removeChild(t)}}(e):setTimeout(t,10)}()):n.parentNode.removeChild(n))}var s="flash",u={};return o.addConstructor(s,function(l){var c,d=this;l=e.extend({swf_url:t.swf_url},l),o.call(this,l,s,{access_binary:function(e){return e&&"browser"===d.mode},access_image_binary:function(e){return e&&"browser"===d.mode},display_media:o.capTest(n("moxie/image/Image")),do_cors:o.capTrue,drag_and_drop:!1,report_upload_progress:function(){return"client"===d.mode},resize_image:o.capTrue,return_response_headers:!1,return_response_type:function(t){return!("json"!==t||!window.JSON)||(!e.arrayDiff(t,["","text","document"])||"browser"===d.mode)},return_status_code:function(t){return"browser"===d.mode||!e.arrayDiff(t,[200,404])},select_file:o.capTrue,select_multiple:o.capTrue,send_binary_string:function(e){return e&&"browser"===d.mode},send_browser_cookies:function(e){return e&&"browser"===d.mode},send_custom_headers:function(e){return e&&"browser"===d.mode},send_multipart:o.capTrue,slice_blob:function(e){return e&&"browser"===d.mode},stream_upload:function(e){return e&&"browser"===d.mode},summon_file_dialog:!1,upload_filesize:function(t){return e.parseSizeStr(t)<=2097152||"client"===d.mode},use_http_method:function(t){return!e.arrayDiff(t,["GET","POST"])}},{access_binary:function(e){return e?"browser":"client"},access_image_binary:function(e){return e?"browser":"client"},report_upload_progress:function(e){return e?"browser":"client"},return_response_type:function(t){return e.arrayDiff(t,["","text","json","document"])?"browser":["client","browser"]},return_status_code:function(t){return e.arrayDiff(t,[200,404])?"browser":["client","browser"]},send_binary_string:function(e){return e?"browser":"client"},send_browser_cookies:function(e){return e?"browser":"client"},send_custom_headers:function(e){return e?"browser":"client"},slice_blob:function(e){return e?"browser":"client"},stream_upload:function(e){return e?"client":"browser"},upload_filesize:function(t){return e.parseSizeStr(t)>=2097152?"client":"browser"}},"client"),function(){var e;try{e=(e=navigator.plugins["Shockwave Flash"]).description}catch(t){try{e=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(t){e="0.0"}}return e=e.match(/\d+/g),parseFloat(e[0]+"."+e[1])}()<11.3&&(this.mode=!1),e.extend(this,{getShim:function(){return i.get(this.uid)},shimExec:function(e,t){var i=[].slice.call(arguments,2);return d.getShim().exec(this.uid,e,t,i)},init:function(){var i,n,a;a=this.getShimContainer(),e.extend(a.style,{position:"absolute",top:"-8px",left:"-8px",width:"9px",height:"9px",overflow:"hidden"}),i='<object id="'+this.uid+'" type="application/x-shockwave-flash" data="'+l.swf_url+'" ',"IE"===t.browser&&(i+='classid="clsid:d27cdb6e-ae6d-11cf-96b8-************" '),i+='width="100%" height="100%" style="outline:0"><param name="movie" value="'+l.swf_url+'" /><param name="flashvars" value="uid='+escape(this.uid)+"&target="+o.getGlobalEventTarget()+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>',"IE"===t.browser?(n=document.createElement("div"),a.appendChild(n),n.outerHTML=i,n=a=null):a.innerHTML=i,c=setTimeout(function(){d&&!d.initialized&&d.trigger("Error",new r.RuntimeError(r.RuntimeError.NOT_INIT_ERR))},5e3)},destroy:function(e){return function(){a(d.uid),e.call(d),clearTimeout(c),l=c=e=d=null}}(this.destroy)},u)}),u}),i("moxie/runtime/flash/file/Blob",["moxie/runtime/flash/Runtime","moxie/file/Blob"],function(e,t){var i={slice:function(e,i,n,r){var o=this.getRuntime();return 0>i?i=Math.max(e.size+i,0):i>0&&(i=Math.min(i,e.size)),0>n?n=Math.max(e.size+n,0):n>0&&(n=Math.min(n,e.size)),(e=o.shimExec.call(this,"Blob","slice",i,n,r||""))&&(e=new t(o.uid,e)),e}};return e.Blob=i}),i("moxie/runtime/flash/file/FileInput",["moxie/runtime/flash/Runtime","moxie/file/File","moxie/core/utils/Dom","moxie/core/utils/Basic"],function(e,t,i,n){var r={init:function(e){var r=this,o=this.getRuntime(),a=i.get(e.browse_button);a&&(a.setAttribute("tabindex",-1),a=null),this.bind("Change",function(){var e=o.shimExec.call(r,"FileInput","getFiles");r.files=[],n.each(e,function(e){r.files.push(new t(o.uid,e))})},999),this.getRuntime().shimExec.call(this,"FileInput","init",{accept:e.accept,multiple:e.multiple}),this.trigger("ready")}};return e.FileInput=r}),i("moxie/runtime/flash/file/FileReader",["moxie/runtime/flash/Runtime","moxie/core/utils/Encode"],function(e,t){function i(e,i){switch(i){case"readAsText":return t.atob(e,"utf8");case"readAsBinaryString":return t.atob(e);case"readAsDataURL":return e}return null}var n={read:function(e,t){var n=this;return n.result="","readAsDataURL"===e&&(n.result="data:"+(t.type||"")+";base64,"),n.bind("Progress",function(t,r){r&&(n.result+=i(r,e))},999),n.getRuntime().shimExec.call(this,"FileReader","readAsBase64",t.uid)}};return e.FileReader=n}),i("moxie/runtime/flash/file/FileReaderSync",["moxie/runtime/flash/Runtime","moxie/core/utils/Encode"],function(e,t){function i(e,i){switch(i){case"readAsText":return t.atob(e,"utf8");case"readAsBinaryString":return t.atob(e);case"readAsDataURL":return e}return null}var n={read:function(e,t){var n,r=this.getRuntime();return(n=r.shimExec.call(this,"FileReaderSync","readAsBase64",t.uid))?("readAsDataURL"===e&&(n="data:"+(t.type||"")+";base64,"+n),i(n,e,t.type)):null}};return e.FileReaderSync=n}),i("moxie/runtime/flash/runtime/Transporter",["moxie/runtime/flash/Runtime","moxie/file/Blob"],function(e,t){var i={getAsBlob:function(e){var i=this.getRuntime(),n=i.shimExec.call(this,"Transporter","getAsBlob",e);return n?new t(i.uid,n):null}};return e.Transporter=i}),i("moxie/runtime/flash/xhr/XMLHttpRequest",["moxie/runtime/flash/Runtime","moxie/core/utils/Basic","moxie/file/Blob","moxie/file/File","moxie/file/FileReaderSync","moxie/runtime/flash/file/FileReaderSync","moxie/xhr/FormData","moxie/runtime/Transporter","moxie/runtime/flash/runtime/Transporter"],function(e,t,i,n,r,o,a,s){var u={send:function(e,n){function r(){e.transport=d.mode,d.shimExec.call(c,"XMLHttpRequest","send",e,n)}function o(e,t){d.shimExec.call(c,"XMLHttpRequest","appendBlob",e,t.uid),n=null,r()}function u(e,t){var i=new s;i.bind("TransportingComplete",function(){t(this.result)}),i.transport(e.getSource(),e.type,{ruid:d.uid})}var l,c=this,d=c.getRuntime();if(t.isEmptyObj(e.headers)||t.each(e.headers,function(e,t){d.shimExec.call(c,"XMLHttpRequest","setRequestHeader",t,e.toString())}),n instanceof a)if(n.each(function(e,t){e instanceof i?l=t:d.shimExec.call(c,"XMLHttpRequest","append",t,e)}),n.hasBlob()){var p=n.getBlob();p.isDetached()?u(p,function(e){p.destroy(),o(l,e)}):o(l,p)}else n=null,r();else n instanceof i?n.isDetached()?u(n,function(e){n.destroy(),n=e.uid,r()}):(n=n.uid,r()):r()},getResponse:function(e){var i,o,a=this.getRuntime();if(o=a.shimExec.call(this,"XMLHttpRequest","getResponseAsBlob")){if(o=new n(a.uid,o),"blob"===e)return o;try{if(i=new r,~t.inArray(e,["","text"]))return i.readAsText(o);if("json"===e&&window.JSON)return JSON.parse(i.readAsText(o))}finally{o.destroy()}}return null},abort:function(){var e=this.getRuntime();e.shimExec.call(this,"XMLHttpRequest","abort"),this.dispatchEvent("readystatechange"),this.dispatchEvent("abort")}};return e.XMLHttpRequest=u}),i("moxie/runtime/flash/image/Image",["moxie/runtime/flash/Runtime","moxie/core/utils/Basic","moxie/runtime/Transporter","moxie/file/Blob","moxie/file/FileReaderSync"],function(e,t,i,n,r){var o={loadFromBlob:function(e){function t(e){r.shimExec.call(n,"Image","loadFromBlob",e.uid),n=r=null}var n=this,r=n.getRuntime();if(e.isDetached()){var o=new i;o.bind("TransportingComplete",function(){t(o.result.getSource())}),o.transport(e.getSource(),e.type,{ruid:r.uid})}else t(e.getSource())},loadFromImage:function(e){var t=this.getRuntime();return t.shimExec.call(this,"Image","loadFromImage",e.uid)},getInfo:function(){var e=this.getRuntime(),t=e.shimExec.call(this,"Image","getInfo");return t.meta&&t.meta.thumb&&t.meta.thumb.data&&!(e.meta.thumb.data instanceof n)&&(t.meta.thumb.data=new n(e.uid,t.meta.thumb.data)),t},getAsBlob:function(e,t){var i=this.getRuntime(),r=i.shimExec.call(this,"Image","getAsBlob",e,t);return r?new n(i.uid,r):null},getAsDataURL:function(){var e=this.getRuntime(),t=e.Image.getAsBlob.apply(this,arguments);return t?(new r).readAsDataURL(t):null}};return e.Image=o}),i("moxie/runtime/silverlight/Runtime",["moxie/core/utils/Basic","moxie/core/utils/Env","moxie/core/utils/Dom","moxie/core/Exceptions","moxie/runtime/Runtime"],function(e,t,i,r,o){var a="silverlight",s={};return o.addConstructor(a,function(u){var l,c=this;u=e.extend({xap_url:t.xap_url},u),o.call(this,u,a,{access_binary:o.capTrue,access_image_binary:o.capTrue,display_media:o.capTest(n("moxie/image/Image")),do_cors:o.capTrue,drag_and_drop:!1,report_upload_progress:o.capTrue,resize_image:o.capTrue,return_response_headers:function(e){return e&&"client"===c.mode},return_response_type:function(e){return"json"!==e||!!window.JSON},return_status_code:function(t){return"client"===c.mode||!e.arrayDiff(t,[200,404])},select_file:o.capTrue,select_multiple:o.capTrue,send_binary_string:o.capTrue,send_browser_cookies:function(e){return e&&"browser"===c.mode},send_custom_headers:function(e){return e&&"client"===c.mode},send_multipart:o.capTrue,slice_blob:o.capTrue,stream_upload:!0,summon_file_dialog:!1,upload_filesize:o.capTrue,use_http_method:function(t){return"client"===c.mode||!e.arrayDiff(t,["GET","POST"])}},{return_response_headers:function(e){return e?"client":"browser"},return_status_code:function(t){return e.arrayDiff(t,[200,404])?"client":["client","browser"]},send_browser_cookies:function(e){return e?"browser":"client"},send_custom_headers:function(e){return e?"client":"browser"},use_http_method:function(t){return e.arrayDiff(t,["GET","POST"])?"client":["client","browser"]}}),function(e){var t,i,n,r,o,a=!1,s=0;try{try{new ActiveXObject("AgControl.AgControl").IsVersionSupported(e)&&(a=!0),null}catch(l){var u=navigator.plugins["Silverlight Plug-In"];if(u){for("1.0.30226.2"===(t=u.description)&&(t="2.0.30226.2"),i=t.split(".");i.length>3;)i.pop();for(;i.length<4;)i.push(0);for(n=e.split(".");n.length>4;)n.pop();do{r=parseInt(n[s],10),o=parseInt(i[s],10),s++}while(s<n.length&&r===o);o>=r&&!isNaN(r)&&(a=!0)}}}catch(e){a=!1}return a}("2.0.31005.0")&&"Opera"!==t.browser||(this.mode=!1),e.extend(this,{getShim:function(){return i.get(this.uid).content.Moxie},shimExec:function(e,t){var i=[].slice.call(arguments,2);return c.getShim().exec(this.uid,e,t,i)},init:function(){this.getShimContainer().innerHTML='<object id="'+this.uid+'" data="data:application/x-silverlight," type="application/x-silverlight-2" width="100%" height="100%" style="outline:none;"><param name="source" value="'+u.xap_url+'"/><param name="background" value="Transparent"/><param name="windowless" value="true"/><param name="enablehtmlaccess" value="true"/><param name="initParams" value="uid='+this.uid+",target="+o.getGlobalEventTarget()+'"/></object>',l=setTimeout(function(){c&&!c.initialized&&c.trigger("Error",new r.RuntimeError(r.RuntimeError.NOT_INIT_ERR))},"Windows"!==t.OS?1e4:5e3)},destroy:function(e){return function(){e.call(c),clearTimeout(l),u=l=e=c=null}}(this.destroy)},s)}),s}),i("moxie/runtime/silverlight/file/Blob",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/file/Blob"],function(e,t,i){return e.Blob=t.extend({},i)}),i("moxie/runtime/silverlight/file/FileInput",["moxie/runtime/silverlight/Runtime","moxie/file/File","moxie/core/utils/Dom","moxie/core/utils/Basic"],function(e,t,i,n){function r(e){for(var t="",i=0;i<e.length;i++)t+=(""!==t?"|":"")+e[i].title+" | *."+e[i].extensions.replace(/,/g,";*.");return t}var o={init:function(e){var o=this,a=this.getRuntime(),s=i.get(e.browse_button);s&&(s.setAttribute("tabindex",-1),s=null),this.bind("Change",function(){var e=a.shimExec.call(o,"FileInput","getFiles");o.files=[],n.each(e,function(e){o.files.push(new t(a.uid,e))})},999),a.shimExec.call(this,"FileInput","init",r(e.accept),e.multiple),this.trigger("ready")},setOption:function(e,t){"accept"==e&&(t=r(t)),this.getRuntime().shimExec.call(this,"FileInput","setOption",e,t)}};return e.FileInput=o}),i("moxie/runtime/silverlight/file/FileDrop",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Dom","moxie/core/utils/Events"],function(e,t,i){var n={init:function(){var e,n=this,r=n.getRuntime();return e=r.getShimContainer(),i.addEvent(e,"dragover",function(e){e.preventDefault(),e.stopPropagation(),e.dataTransfer.dropEffect="copy"},n.uid),i.addEvent(e,"dragenter",function(e){e.preventDefault();var i=t.get(r.uid).dragEnter(e);i&&e.stopPropagation()},n.uid),i.addEvent(e,"drop",function(e){e.preventDefault();var i=t.get(r.uid).dragDrop(e);i&&e.stopPropagation()},n.uid),r.shimExec.call(this,"FileDrop","init")}};return e.FileDrop=n}),i("moxie/runtime/silverlight/file/FileReader",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/file/FileReader"],function(e,t,i){return e.FileReader=t.extend({},i)}),i("moxie/runtime/silverlight/file/FileReaderSync",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/file/FileReaderSync"],function(e,t,i){return e.FileReaderSync=t.extend({},i)}),i("moxie/runtime/silverlight/runtime/Transporter",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/runtime/Transporter"],function(e,t,i){return e.Transporter=t.extend({},i)}),i("moxie/runtime/silverlight/xhr/XMLHttpRequest",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/runtime/flash/xhr/XMLHttpRequest","moxie/runtime/silverlight/file/FileReaderSync","moxie/runtime/silverlight/runtime/Transporter"],function(e,t,i){return e.XMLHttpRequest=t.extend({},i)}),i("moxie/runtime/silverlight/image/Image",["moxie/runtime/silverlight/Runtime","moxie/core/utils/Basic","moxie/file/Blob","moxie/runtime/flash/image/Image"],function(e,t,i,n){return e.Image=t.extend({},n,{getInfo:function(){var e=this.getRuntime(),n={meta:{}},r=e.shimExec.call(this,"Image","getInfo");return r.meta&&(t.each(["tiff","exif","gps","thumb"],function(e){var t,i,o,a,s=r.meta[e];if(s&&s.keys)for(n.meta[e]={},i=0,o=s.keys.length;o>i;i++)t=s.keys[i],(a=s[t])&&(/^(\d|[1-9]\d+)$/.test(a)?a=parseInt(a,10):/^\d*\.\d+$/.test(a)&&(a=parseFloat(a)),n.meta[e][t]=a)}),n.meta&&n.meta.thumb&&n.meta.thumb.data&&!(e.meta.thumb.data instanceof i)&&(n.meta.thumb.data=new i(e.uid,n.meta.thumb.data))),n.width=parseInt(r.width,10),n.height=parseInt(r.height,10),n.size=parseInt(r.size,10),n.type=r.type,n.name=r.name,n},resize:function(e,t,i){this.getRuntime().shimExec.call(this,"Image","resize",e.x,e.y,e.width,e.height,t,i.preserveHeaders,i.resample)}})}),i("moxie/runtime/html4/Runtime",["moxie/core/utils/Basic","moxie/core/Exceptions","moxie/runtime/Runtime","moxie/core/utils/Env"],function(e,t,i,r){var o="html4",a={};return i.addConstructor(o,function(t){var s=this,u=i.capTest,l=i.capTrue;i.call(this,t,o,{access_binary:u(window.FileReader||window.File&&File.getAsDataURL),access_image_binary:!1,display_media:u((r.can("create_canvas")||r.can("use_data_uri_over32kb"))&&n("moxie/image/Image")),do_cors:!1,drag_and_drop:!1,filter_by_extension:u(!("Chrome"===r.browser&&r.verComp(r.version,28,"<")||"IE"===r.browser&&r.verComp(r.version,10,"<")||"Safari"===r.browser&&r.verComp(r.version,7,"<")||"Firefox"===r.browser&&r.verComp(r.version,37,"<"))),resize_image:function(){return a.Image&&s.can("access_binary")&&r.can("create_canvas")},report_upload_progress:!1,return_response_headers:!1,return_response_type:function(t){return!("json"!==t||!window.JSON)||!!~e.inArray(t,["text","document",""])},return_status_code:function(t){return!e.arrayDiff(t,[200,404])},select_file:function(){return r.can("use_fileinput")},select_multiple:!1,send_binary_string:!1,send_custom_headers:!1,send_multipart:!0,slice_blob:!1,stream_upload:function(){return s.can("select_file")},summon_file_dialog:function(){return s.can("select_file")&&!("Firefox"===r.browser&&r.verComp(r.version,4,"<")||"Opera"===r.browser&&r.verComp(r.version,12,"<")||"IE"===r.browser&&r.verComp(r.version,10,"<"))},upload_filesize:l,use_http_method:function(t){return!e.arrayDiff(t,["GET","POST"])}}),e.extend(this,{init:function(){this.trigger("Init")},destroy:function(e){return function(){e.call(s),e=s=null}}(this.destroy)}),e.extend(this.getShim(),a)}),a}),i("moxie/runtime/html4/file/FileInput",["moxie/runtime/html4/Runtime","moxie/file/File","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Events","moxie/core/utils/Mime","moxie/core/utils/Env"],function(e,t,i,n,r,o,a){return e.FileInput=function(){function e(){var o,l,d,p,m,f,h=this,g=h.getRuntime();f=i.guid("uid_"),o=g.getShimContainer(),s&&((d=n.get(s+"_form"))&&(i.extend(d.style,{top:"100%"}),d.firstChild.setAttribute("tabindex",-1))),(p=document.createElement("form")).setAttribute("id",f+"_form"),p.setAttribute("method","post"),p.setAttribute("enctype","multipart/form-data"),p.setAttribute("encoding","multipart/form-data"),i.extend(p.style,{overflow:"hidden",position:"absolute",top:0,left:0,width:"100%",height:"100%"}),(m=document.createElement("input")).setAttribute("id",f),m.setAttribute("type","file"),m.setAttribute("accept",c.join(",")),g.can("summon_file_dialog")&&m.setAttribute("tabindex",-1),i.extend(m.style,{fontSize:"999px",opacity:0}),p.appendChild(m),o.appendChild(p),i.extend(m.style,{position:"absolute",top:0,left:0,width:"100%",height:"100%"}),"IE"===a.browser&&a.verComp(a.version,10,"<")&&i.extend(m.style,{filter:"progid:DXImageTransform.Microsoft.Alpha(opacity=0)"}),m.onchange=function(){var i;this.value&&(i=this.files?this.files[0]:{name:this.value},i=new t(g.uid,i),this.onchange=function(){},e.call(h),h.files=[i],m.setAttribute("id",i.uid),p.setAttribute("id",i.uid+"_form"),h.trigger("change"),m=p=null)},g.can("summon_file_dialog")&&(l=n.get(u.browse_button),r.removeEvent(l,"click",h.uid),r.addEvent(l,"click",function(e){m&&!m.disabled&&m.click(),e.preventDefault()},h.uid)),s=f,o=d=l=null}var s,u,l,c=[];i.extend(this,{init:function(t){var i,a=this,s=a.getRuntime();u=t,c=o.extList2mimes(t.accept,s.can("filter_by_extension")),i=s.getShimContainer(),function(){var e,o,c;e=n.get(t.browse_button),l=n.getStyle(e,"z-index")||"auto",s.can("summon_file_dialog")?("static"===n.getStyle(e,"position")&&(e.style.position="relative"),a.bind("Refresh",function(){o=parseInt(l,10)||1,n.get(u.browse_button).style.zIndex=o,this.getRuntime().getShimContainer().style.zIndex=o-1})):e.setAttribute("tabindex",-1),c=s.can("summon_file_dialog")?e:i,r.addEvent(c,"mouseover",function(){a.trigger("mouseenter")},a.uid),r.addEvent(c,"mouseout",function(){a.trigger("mouseleave")},a.uid),r.addEvent(c,"mousedown",function(){a.trigger("mousedown")},a.uid),r.addEvent(n.get(t.container),"mouseup",function(){a.trigger("mouseup")},a.uid),e=null}(),e.call(this),i=null,a.trigger({type:"ready",async:!0})},setOption:function(e,t){var i,r=this.getRuntime();"accept"==e&&(c=t.mimes||o.extList2mimes(t,r.can("filter_by_extension"))),(i=n.get(s))&&i.setAttribute("accept",c.join(","))},disable:function(e){var t;(t=n.get(s))&&(t.disabled=!!e)},destroy:function(){var e=this.getRuntime(),t=e.getShim(),i=e.getShimContainer(),o=u&&n.get(u.container),a=u&&n.get(u.browse_button);o&&r.removeAllEvents(o,this.uid),a&&(r.removeAllEvents(a,this.uid),a.style.zIndex=l),i&&(r.removeAllEvents(i,this.uid),i.innerHTML=""),t.removeInstance(this.uid),s=c=u=i=o=a=t=null}})}}),i("moxie/runtime/html4/file/FileReader",["moxie/runtime/html4/Runtime","moxie/runtime/html5/file/FileReader"],function(e,t){return e.FileReader=t}),i("moxie/runtime/html4/xhr/XMLHttpRequest",["moxie/runtime/html4/Runtime","moxie/core/utils/Basic","moxie/core/utils/Dom","moxie/core/utils/Url","moxie/core/Exceptions","moxie/core/utils/Events","moxie/file/Blob","moxie/xhr/FormData"],function(e,t,i,n,r,o,a,s){return e.XMLHttpRequest=function(){function e(e){var t,n,r,a,s=this,u=!1;if(c){if(t=c.id.replace(/_iframe$/,""),n=i.get(t+"_form")){for(r=n.getElementsByTagName("input"),a=r.length;a--;)switch(r[a].getAttribute("type")){case"hidden":r[a].parentNode.removeChild(r[a]);break;case"file":u=!0}r=[],u||n.parentNode.removeChild(n),n=null}setTimeout(function(){o.removeEvent(c,"load",s.uid),c.parentNode&&c.parentNode.removeChild(c);var t=s.getRuntime().getShimContainer();t.children.length||t.parentNode.removeChild(t),t=c=null,e()},1)}}var u,l,c;t.extend(this,{send:function(d,p){var m,f,h,g,v=this,_=v.getRuntime();if(u=l=null,p instanceof s&&p.hasBlob()){if(g=p.getBlob(),m=g.uid,h=i.get(m),!(f=i.get(m+"_form")))throw new r.DOMException(r.DOMException.NOT_FOUND_ERR)}else m=t.guid("uid_"),(f=document.createElement("form")).setAttribute("id",m+"_form"),f.setAttribute("method",d.method),f.setAttribute("enctype","multipart/form-data"),f.setAttribute("encoding","multipart/form-data"),_.getShimContainer().appendChild(f);f.setAttribute("target",m+"_iframe"),p instanceof s&&p.each(function(e,i){if(e instanceof a)h&&h.setAttribute("name",i);else{var n=document.createElement("input");t.extend(n,{type:"hidden",name:i,value:e}),h?f.insertBefore(n,h):f.appendChild(n)}}),f.setAttribute("action",d.url),function(){var i=_.getShimContainer()||document.body,r=document.createElement("div");r.innerHTML='<iframe id="'+m+'_iframe" name="'+m+'_iframe" src="javascript:&quot;&quot;" style="display:none"></iframe>',c=r.firstChild,i.appendChild(c),o.addEvent(c,"load",function(){var i;try{i=c.contentWindow.document||c.contentDocument||window.frames[c.id].document,/^4(0[0-9]|1[0-7]|2[2346])\s/.test(i.title)?u=i.title.replace(/^(\d+).*$/,"$1"):(u=200,l=t.trim(i.body.innerHTML),v.trigger({type:"progress",loaded:l.length,total:l.length}),g&&v.trigger({type:"uploadprogress",loaded:g.size||1025,total:g.size||1025}))}catch(t){if(!n.hasSameOrigin(d.url))return void e.call(v,function(){v.trigger("error")});u=404}e.call(v,function(){v.trigger("load")})},v.uid)}(),f.submit(),v.trigger("loadstart")},getStatus:function(){return u},getResponse:function(e){if("json"===e&&"string"===t.typeOf(l)&&window.JSON)try{return JSON.parse(l.replace(/^\s*<pre[^>]*>/,"").replace(/<\/pre>\s*$/,""))}catch(e){return null}return l},abort:function(){var t=this;c&&c.contentWindow&&(c.contentWindow.stop?c.contentWindow.stop():c.contentWindow.document.execCommand?c.contentWindow.document.execCommand("Stop"):c.src="about:blank"),e.call(this,function(){t.dispatchEvent("abort")})},destroy:function(){this.getRuntime().getShim().removeInstance(this.uid)}})}}),i("moxie/runtime/html4/image/Image",["moxie/runtime/html4/Runtime","moxie/runtime/html5/image/Image"],function(e,t){return e.Image=t}),function(i){for(var n=0;n<i.length;n++){for(var r=e,a=i[n],s=a.split(/[.\/]/),u=0;u<s.length-1;++u)r[s[u]]===t&&(r[s[u]]={}),r=r[s[u]];r[s[s.length-1]]=o[a]}}(["moxie/core/utils/Basic","moxie/core/utils/Encode","moxie/core/utils/Env","moxie/core/Exceptions","moxie/core/utils/Dom","moxie/core/EventTarget","moxie/runtime/Runtime","moxie/runtime/RuntimeClient","moxie/file/Blob","moxie/core/I18n","moxie/core/utils/Mime","moxie/file/FileInput","moxie/file/File","moxie/file/FileDrop","moxie/file/FileReader","moxie/core/utils/Url","moxie/runtime/RuntimeTarget","moxie/xhr/FormData","moxie/xhr/XMLHttpRequest","moxie/image/Image","moxie/core/utils/Events","moxie/runtime/html5/image/ResizerCanvas"])}(this)}.apply(e,arguments),e.moxie};"function"==typeof define&&define.amd?define("moxie",[],i):"object"==typeof module&&module.exports?module.exports=i():e.moxie=i()}(this||window),function(e,t){var i=function(){var e={};return function(e){!function(e,t,i){function n(e){function t(e,t,i){var r={chunks:"slice_blob",jpgresize:"send_binary_string",pngresize:"send_binary_string",progress:"report_upload_progress",multi_selection:"select_multiple",dragdrop:"drag_and_drop",drop_element:"drag_and_drop",headers:"send_custom_headers",urlstream_upload:"send_binary_string",canSendBinary:"send_binary",triggerDialog:"summon_file_dialog"};r[e]?n[r[e]]=t:i||(n[e]=t)}var i=e.required_features,n={};return"string"==typeof i?u.each(i.split(/\s*,\s*/),function(e){t(e,!0)}):"object"==typeof i?u.each(i,function(e,i){t(i,e)}):!0===i&&(e.chunk_size&&e.chunk_size>0&&(n.slice_blob=!0),u.isEmptyObj(e.resize)&&!1!==e.multipart||(n.send_binary_string=!0),e.http_method&&(n.use_http_method=e.http_method),u.each(e,function(e,i){t(i,!!e,!0)})),n}var r=window.setTimeout,o={},a=t.core.utils,s=t.runtime.Runtime,u={VERSION:"2.3.6",STOPPED:1,STARTED:2,QUEUED:1,UPLOADING:2,FAILED:4,DONE:5,GENERIC_ERROR:-100,HTTP_ERROR:-200,IO_ERROR:-300,SECURITY_ERROR:-400,INIT_ERROR:-500,FILE_SIZE_ERROR:-600,FILE_EXTENSION_ERROR:-601,FILE_DUPLICATE_ERROR:-602,IMAGE_FORMAT_ERROR:-700,MEMORY_ERROR:-701,IMAGE_DIMENSIONS_ERROR:-702,moxie:t,mimeTypes:a.Mime.mimes,ua:a.Env,typeOf:a.Basic.typeOf,extend:a.Basic.extend,guid:a.Basic.guid,getAll:function(e){var t,i=[];"array"!==u.typeOf(e)&&(e=[e]);for(var n=e.length;n--;)(t=u.get(e[n]))&&i.push(t);return i.length?i:null},get:a.Dom.get,each:a.Basic.each,getPos:a.Dom.getPos,getSize:a.Dom.getSize,xmlEncode:function(e){var t={"<":"lt",">":"gt","&":"amp",'"':"quot","'":"#39"};return e?(""+e).replace(/[<>&\"\']/g,function(e){return t[e]?"&"+t[e]+";":e}):e},toArray:a.Basic.toArray,inArray:a.Basic.inArray,inSeries:a.Basic.inSeries,addI18n:t.core.I18n.addI18n,translate:t.core.I18n.translate,sprintf:a.Basic.sprintf,isEmptyObj:a.Basic.isEmptyObj,hasClass:a.Dom.hasClass,addClass:a.Dom.addClass,removeClass:a.Dom.removeClass,getStyle:a.Dom.getStyle,addEvent:a.Events.addEvent,removeEvent:a.Events.removeEvent,removeAllEvents:a.Events.removeAllEvents,cleanName:function(e){var t,i;for(i=[/[\300-\306]/g,"A",/[\340-\346]/g,"a",/\307/g,"C",/\347/g,"c",/[\310-\313]/g,"E",/[\350-\353]/g,"e",/[\314-\317]/g,"I",/[\354-\357]/g,"i",/\321/g,"N",/\361/g,"n",/[\322-\330]/g,"O",/[\362-\370]/g,"o",/[\331-\334]/g,"U",/[\371-\374]/g,"u"],t=0;t<i.length;t+=2)e=e.replace(i[t],i[t+1]);return e=(e=e.replace(/\s+/g,"_")).replace(/[^a-z0-9_\-\.]+/gi,"")},buildUrl:function(e,t){var i="";return u.each(t,function(e,t){i+=(i?"&":"")+encodeURIComponent(t)+"="+encodeURIComponent(e)}),i&&(e+=(e.indexOf("?")>0?"&":"?")+i),e},formatSize:function(e){function t(e,t){return Math.round(e*Math.pow(10,t))/Math.pow(10,t)}if(e===i||/\D/.test(e))return u.translate("N/A");var n=Math.pow(1024,4);return e>n?t(e/n,1)+" "+u.translate("tb"):e>(n/=1024)?t(e/n,1)+" "+u.translate("gb"):e>(n/=1024)?t(e/n,1)+" "+u.translate("mb"):e>1024?Math.round(e/1024)+" "+u.translate("kb"):e+" "+u.translate("b")},parseSize:a.Basic.parseSizeStr,predictRuntime:function(e,t){var i,n;return i=new u.Uploader(e),n=s.thatCan(i.getOption().required_features,t||e.runtimes),i.destroy(),n},addFileFilter:function(e,t){o[e]=t}};u.addFileFilter("mime_types",function(e,t,i){e.length&&!e.regexp.test(t.name)?(this.trigger("Error",{code:u.FILE_EXTENSION_ERROR,message:u.translate("File extension error."),file:t}),i(!1)):i(!0)}),u.addFileFilter("max_file_size",function(e,t,i){e=u.parseSize(e),void 0!==t.size&&e&&t.size>e?(this.trigger("Error",{code:u.FILE_SIZE_ERROR,message:u.translate("File size error."),file:t}),i(!1)):i(!0)}),u.addFileFilter("prevent_duplicates",function(e,t,i){if(e)for(var n=this.files.length;n--;)if(t.name===this.files[n].name&&t.size===this.files[n].size)return this.trigger("Error",{code:u.FILE_DUPLICATE_ERROR,message:u.translate("Duplicate file error."),file:t}),void i(!1);i(!0)}),u.addFileFilter("prevent_empty",function(e,t,n){e&&!t.size&&t.size!==i?(this.trigger("Error",{code:u.FILE_SIZE_ERROR,message:u.translate("File size error."),file:t}),n(!1)):n(!0)}),u.Uploader=function(e){function a(){var e,t,i=0;if(this.state==u.STARTED){for(t=0;t<S.length;t++)e||S[t].status!=u.QUEUED?i++:(e=S[t],this.trigger("BeforeUpload",e)&&(e.status=u.UPLOADING,this.trigger("UploadFile",e)));i==S.length&&(this.state!==u.STOPPED&&(this.state=u.STOPPED,this.trigger("StateChanged")),this.trigger("UploadComplete",S))}}function l(e){e.percent=e.size>0?Math.ceil(e.loaded/e.size*100):100,c()}function c(){var e,t,n,r=0;for(I.reset(),e=0;e<S.length;e++)(t=S[e]).size!==i?(I.size+=t.origSize,n=t.loaded*t.origSize/t.size,(!t.completeTimestamp||t.completeTimestamp>R)&&(r+=n),I.loaded+=n):I.size=i,t.status==u.DONE?I.uploaded++:t.status==u.FAILED?I.failed++:I.queued++;I.size===i?I.percent=S.length>0?Math.ceil(I.uploaded/S.length*100):0:(I.bytesPerSec=Math.ceil(r/((+new Date-R||1)/1e3)),I.percent=I.size>0?Math.ceil(I.loaded/I.size*100):0)}function d(){var e=D[0]||C[0];return!!e&&e.getRuntime().uid}function p(e,i){var n=this,r=0,o=[],a={runtime_order:e.runtimes,required_caps:e.required_features,preferred_caps:O,swf_url:e.flash_swf_url,xap_url:e.silverlight_xap_url};u.each(e.runtimes.split(/\s*,\s*/),function(t){e[t]&&(a[t]=e[t])}),e.browse_button&&u.each(e.browse_button,function(i){o.push(function(o){var l=new t.file.FileInput(u.extend({},a,{accept:e.filters.mime_types,name:e.file_data_name,multiple:e.multi_selection,container:e.container,browse_button:i}));l.onready=function(){var e=s.getInfo(this.ruid);u.extend(n.features,{chunks:e.can("slice_blob"),multipart:e.can("send_multipart"),multi_selection:e.can("select_multiple")}),r++,D.push(this),o()},l.onchange=function(){n.addFile(this.files)},l.bind("mouseenter mouseleave mousedown mouseup",function(t){N||(e.browse_button_hover&&("mouseenter"===t.type?u.addClass(i,e.browse_button_hover):"mouseleave"===t.type&&u.removeClass(i,e.browse_button_hover)),e.browse_button_active&&("mousedown"===t.type?u.addClass(i,e.browse_button_active):"mouseup"===t.type&&u.removeClass(i,e.browse_button_active)))}),l.bind("mousedown",function(){n.trigger("Browse")}),l.bind("error runtimeerror",function(){l=null,o()}),l.init()})}),e.drop_element&&u.each(e.drop_element,function(e){o.push(function(i){var o=new t.file.FileDrop(u.extend({},a,{drop_zone:e}));o.onready=function(){var e=s.getInfo(this.ruid);u.extend(n.features,{chunks:e.can("slice_blob"),multipart:e.can("send_multipart"),dragdrop:e.can("drag_and_drop")}),r++,C.push(this),i()},o.ondrop=function(){n.addFile(this.files)},o.bind("error runtimeerror",function(){o=null,i()}),o.init()})}),u.inSeries(o,function(){"function"==typeof i&&i(r)})}function m(e,n,r,o){var a=new t.image.Image;try{a.onload=function(){n.width>this.width&&n.height>this.height&&n.quality===i&&n.preserve_headers&&!n.crop?(this.destroy(),o(e)):a.downsize(n.width,n.height,n.crop,n.preserve_headers)},a.onresize=function(){var t=this.getAsBlob(e.type,n.quality);this.destroy(),o(t)},a.bind("error runtimeerror",function(){this.destroy(),o(e)}),a.load(e,r)}catch(t){o(e)}}function f(e,i,r){function o(e,i,n){var r=b[e];switch(e){case"max_file_size":"max_file_size"===e&&(b.max_file_size=b.filters.max_file_size=i);break;case"chunk_size":(i=u.parseSize(i))&&(b[e]=i,b.send_file_name=!0);break;case"multipart":b[e]=i,i||(b.send_file_name=!0);break;case"http_method":b[e]="PUT"===i.toUpperCase()?"PUT":"POST";break;case"unique_names":b[e]=i,i&&(b.send_file_name=!0);break;case"filters":"array"===u.typeOf(i)&&(i={mime_types:i}),n?u.extend(b.filters,i):b.filters=i,i.mime_types&&("string"===u.typeOf(i.mime_types)&&(i.mime_types=t.core.utils.Mime.mimes2extList(i.mime_types)),i.mime_types.regexp=function(e){var t=[];return u.each(e,function(e){u.each(e.extensions.split(/,/),function(e){/^\s*\*\s*$/.test(e)?t.push("\\.*"):t.push("\\."+e.replace(new RegExp("["+"/^$.*+?|()[]{}\\".replace(/./g,"\\$&")+"]","g"),"\\$&"))})}),new RegExp("("+t.join("|")+")$","i")}(i.mime_types),b.filters.mime_types=i.mime_types);break;case"resize":b.resize=!!i&&u.extend({preserve_headers:!0,crop:!1},i);break;case"prevent_duplicates":b.prevent_duplicates=b.filters.prevent_duplicates=!!i;break;case"container":case"browse_button":case"drop_element":i="container"===e?u.get(i):u.getAll(i);case"runtimes":case"multi_selection":case"flash_swf_url":case"silverlight_xap_url":b[e]=i,n||(l=!0);break;default:b[e]=i}n||a.trigger("OptionChanged",e,i,r)}var a=this,l=!1;"object"==typeof e?u.each(e,function(e,t){o(t,e,r)}):o(e,i,r),r?(b.required_features=n(u.extend({},b)),O=n(u.extend({},b,{required_features:!0}))):l&&(a.trigger("Destroy"),p.call(a,b,function(e){e?(a.runtime=s.getInfo(d()).type,a.trigger("Init",{runtime:a.runtime}),a.trigger("PostInit")):a.trigger("Error",{code:u.INIT_ERROR,message:u.translate("Init error.")})}))}function h(e,t){if(e.settings.unique_names){var i=t.name.match(/\.([^.]+)$/),n="part";i&&(n=i[1]),t.target_name=t.id+"."+n}}function g(e,i){function n(){d-- >0?r(o,1e3):(i.loaded=f,e.trigger("Error",{code:u.HTTP_ERROR,message:u.translate("HTTP Error."),file:i,response:A.responseText,status:A.status,responseHeaders:A.getAllResponseHeaders()}))}function o(){var t,n,r={};i.status===u.UPLOADING&&e.state!==u.STOPPED&&(e.settings.send_file_name&&(r.name=i.target_name||i.name),c&&p.chunks&&s.size>c?(n=Math.min(c,s.size-f),t=s.slice(f,f+n)):(n=s.size,t=s),c&&p.chunks&&(e.settings.send_chunk_number?(r.chunk=Math.ceil(f/c),r.chunks=Math.ceil(s.size/c)):(r.offset=f,r.total=s.size)),e.trigger("BeforeChunkUpload",i,r,t,f)&&a(r,t,n))}function a(a,c,m){var g;(A=new t.xhr.XMLHttpRequest).upload&&(A.upload.onprogress=function(t){i.loaded=Math.min(i.size,f+t.loaded),e.trigger("UploadProgress",i)}),A.onload=function(){return A.status<200||A.status>=400?void n():(d=e.settings.max_retries,m<s.size?(c.destroy(),f+=m,i.loaded=Math.min(f,s.size),e.trigger("ChunkUploaded",i,{offset:i.loaded,total:s.size,response:A.responseText,status:A.status,responseHeaders:A.getAllResponseHeaders()}),"Android Browser"===u.ua.browser&&e.trigger("UploadProgress",i)):i.loaded=i.size,c=g=null,void(!f||f>=s.size?(i.size!=i.origSize&&(s.destroy(),s=null),e.trigger("UploadProgress",i),i.status=u.DONE,i.completeTimestamp=+new Date,e.trigger("FileUploaded",i,{response:A.responseText,status:A.status,responseHeaders:A.getAllResponseHeaders()})):r(o,1)))},A.onerror=function(){n()},A.onloadend=function(){this.destroy()},e.settings.multipart&&p.multipart?(A.open(e.settings.http_method,l,!0),u.each(e.settings.headers,function(e,t){A.setRequestHeader(t,e)}),g=new t.xhr.FormData,u.each(u.extend(a,e.settings.multipart_params),function(e,t){g.append(t,e)}),g.append(e.settings.file_data_name,c),A.send(g,h)):(l=u.buildUrl(e.settings.url,u.extend(a,e.settings.multipart_params)),A.open(e.settings.http_method,l,!0),u.each(e.settings.headers,function(e,t){A.setRequestHeader(t,e)}),A.hasRequestHeader("Content-Type")||A.setRequestHeader("Content-Type","application/octet-stream"),A.send(c,h))}var s,l=e.settings.url,c=e.settings.chunk_size,d=e.settings.max_retries,p=e.features,f=0,h={runtime_order:e.settings.runtimes,required_caps:e.settings.required_features,preferred_caps:O,swf_url:e.settings.flash_swf_url,xap_url:e.settings.silverlight_xap_url};i.loaded&&(f=i.loaded=c?c*Math.floor(i.loaded/c):0),s=i.getSource(),u.isEmptyObj(e.settings.resize)||-1===u.inArray(s.type,["image/jpeg","image/png"])?o():m(s,e.settings.resize,h,function(e){s=e,i.size=e.size,o()})}function v(e,t){l(t)}function _(e){if(e.state==u.STARTED)R=+new Date;else if(e.state==u.STOPPED)for(var t=e.files.length-1;t>=0;t--)e.files[t].status==u.UPLOADING&&(e.files[t].status=u.QUEUED,c())}function x(){A&&A.abort()}function y(e){c(),r(function(){a.call(e)},1)}function E(e,t){t.code===u.INIT_ERROR?e.destroy():t.code===u.HTTP_ERROR&&(t.file.status=u.FAILED,t.file.completeTimestamp=+new Date,l(t.file),e.state==u.STARTED&&(e.trigger("CancelUpload"),r(function(){a.call(e)},1)))}function w(e){e.stop(),u.each(S,function(e){e.destroy()}),S=[],D.length&&(u.each(D,function(e){e.destroy()}),D=[]),C.length&&(u.each(C,function(e){e.destroy()}),C=[]),O={},N=!1,R=A=null,I.reset()}var b,R,I,A,T=u.guid(),S=[],O={},D=[],C=[],N=!1;b={chunk_size:0,file_data_name:"file",filters:{mime_types:[],max_file_size:0,prevent_duplicates:!1,prevent_empty:!0},flash_swf_url:"js/Moxie.swf",http_method:"POST",max_retries:0,multipart:!0,multi_selection:!0,resize:!1,runtimes:s.order,send_file_name:!0,send_chunk_number:!0,silverlight_xap_url:"js/Moxie.xap"},f.call(this,e,null,!0),I=new u.QueueProgress,u.extend(this,{id:T,uid:T,state:u.STOPPED,features:{},runtime:null,files:S,settings:b,total:I,init:function(){var e,t,i=this;return"function"==typeof(e=i.getOption("preinit"))?e(i):u.each(e,function(e,t){i.bind(t,e)}),function(){this.bind("FilesAdded FilesRemoved",function(e){e.trigger("QueueChanged"),e.refresh()}),this.bind("CancelUpload",x),this.bind("BeforeUpload",h),this.bind("UploadFile",g),this.bind("UploadProgress",v),this.bind("StateChanged",_),this.bind("QueueChanged",c),this.bind("Error",E),this.bind("FileUploaded",y),this.bind("Destroy",w)}.call(i),u.each(["container","browse_button","drop_element"],function(e){return null===i.getOption(e)?(t={code:u.INIT_ERROR,message:u.sprintf(u.translate("%s specified, but cannot be found."),e)},!1):void 0}),t?i.trigger("Error",t):b.browse_button||b.drop_element?void p.call(i,b,function(e){var t=i.getOption("init");"function"==typeof t?t(i):u.each(t,function(e,t){i.bind(t,e)}),e?(i.runtime=s.getInfo(d()).type,i.trigger("Init",{runtime:i.runtime}),i.trigger("PostInit")):i.trigger("Error",{code:u.INIT_ERROR,message:u.translate("Init error.")})}):i.trigger("Error",{code:u.INIT_ERROR,message:u.translate("You must specify either browse_button or drop_element.")})},setOption:function(e,t){f.call(this,e,t,!this.runtime)},getOption:function(e){return e?b[e]:b},refresh:function(){D.length&&u.each(D,function(e){e.trigger("Refresh")}),this.trigger("Refresh")},start:function(){this.state!=u.STARTED&&(this.state=u.STARTED,this.trigger("StateChanged"),a.call(this))},stop:function(){this.state!=u.STOPPED&&(this.state=u.STOPPED,this.trigger("StateChanged"),this.trigger("CancelUpload"))},disableBrowse:function(){N=arguments[0]===i||arguments[0],D.length&&u.each(D,function(e){e.disable(N)}),this.trigger("DisableBrowse",N)},getFile:function(e){var t;for(t=S.length-1;t>=0;t--)if(S[t].id===e)return S[t]},addFile:function(e,i){function n(e,t){var i=[];u.each(s.settings.filters,function(t,n){o[n]&&i.push(function(i){o[n].call(s,t,e,function(e){i(!e)})})}),u.inSeries(i,t)}var a,s=this,l=[],c=[];a=d(),function e(o){var d=u.typeOf(o);if(o instanceof t.file.File){if(!o.ruid&&!o.isDetached()){if(!a)return!1;o.ruid=a,o.connectRuntime(a)}e(new u.File(o))}else o instanceof t.file.Blob?(e(o.getSource()),o.destroy()):o instanceof u.File?(i&&(o.name=i),l.push(function(e){n(o,function(t){t||(S.push(o),c.push(o),s.trigger("FileFiltered",o)),r(e,1)})})):-1!==u.inArray(d,["file","blob"])?e(new t.file.File(null,o)):"node"===d&&"filelist"===u.typeOf(o.files)?u.each(o.files,e):"array"===d&&(i=null,u.each(o,e))}(e),l.length&&u.inSeries(l,function(){c.length&&s.trigger("FilesAdded",c)})},removeFile:function(e){for(var t="string"==typeof e?e:e.id,i=S.length-1;i>=0;i--)if(S[i].id===t)return this.splice(i,1)[0]},splice:function(e,t){var n=S.splice(e===i?0:e,t===i?S.length:t),r=!1;return this.state==u.STARTED&&(u.each(n,function(e){return e.status===u.UPLOADING?(r=!0,!1):void 0}),r&&this.stop()),this.trigger("FilesRemoved",n),u.each(n,function(e){e.destroy()}),r&&this.start(),n},dispatchEvent:function(e){var t,i;if(e=e.toLowerCase(),t=this.hasEventListener(e)){t.sort(function(e,t){return t.priority-e.priority}),(i=[].slice.call(arguments)).shift(),i.unshift(this);for(var n=0;n<t.length;n++)if(!1===t[n].fn.apply(t[n].scope,i))return!1}return!0},bind:function(e,t,i,n){u.Uploader.prototype.bind.call(this,e,t,n,i)},destroy:function(){this.trigger("Destroy"),b=I=null,this.unbindAll()}})},u.Uploader.prototype=t.core.EventTarget.instance,u.File=function(){var e={};return function(t){u.extend(this,{id:u.guid(),name:t.name||t.fileName,type:t.type||"",relativePath:t.relativePath||"",size:t.fileSize||t.size,origSize:t.fileSize||t.size,loaded:0,percent:0,status:u.QUEUED,lastModifiedDate:t.lastModifiedDate||(new Date).toLocaleString(),completeTimestamp:0,getNative:function(){var e=this.getSource().getSource();return-1!==u.inArray(u.typeOf(e),["blob","file"])?e:null},getSource:function(){return e[this.id]?e[this.id]:null},destroy:function(){var t=this.getSource();t&&(t.destroy(),delete e[this.id])}}),e[this.id]=t}}(),u.QueueProgress=function(){var e=this;e.size=0,e.loaded=0,e.uploaded=0,e.failed=0,e.queued=0,e.percent=0,e.bytesPerSec=0,e.reset=function(){e.size=e.loaded=e.uploaded=e.failed=e.queued=e.percent=e.bytesPerSec=0}},e.plupload=u}(this,e)}.apply(e,arguments),e.plupload};"function"==typeof define&&define.amd?define("plupload",["./moxie"],i):"object"==typeof module&&module.exports?module.exports=i(require("./moxie")):e.plupload=i(e.moxie)}(this||window),function(e,t){function i(e){return t.translate(e)||e}var n={};e.fn.pluploadQueue=function(r){return r?(this.each(function(){function o(i){var n;i.status==t.DONE&&(n="plupload_done"),i.status==t.FAILED&&(n="plupload_failed"),i.status==t.QUEUED&&(n="plupload_delete"),i.status==t.UPLOADING&&(n="plupload_uploading");var r=e("#"+i.id).attr("class",n).find("a").css("display","block");i.hint&&r.attr("title",i.hint)}function a(){e("span.plupload_total_status",c).html(l.total.percent+"%"),e("div.plupload_progress_bar",c).css("width",l.total.percent+"%"),e("span.plupload_upload_status",c).html(t.sprintf(i("Uploaded %d/%d files"),l.total.uploaded,l.files.length))}function s(){var n,r=e("ul.plupload_filelist",c).html(""),s=0;e.each(l.files,function(i,a){n="",a.status==t.DONE&&(a.target_name&&(n+='<input type="hidden" name="'+d+"_"+s+'_tmpname" value="'+t.xmlEncode(a.target_name)+'" />'),n+='<input type="hidden" name="'+d+"_"+s+'_name" value="'+t.xmlEncode(a.name)+'" />',n+='<input type="hidden" name="'+d+"_"+s+'_status" value="'+(a.status==t.DONE?"done":"failed")+'" />',s++,e("#"+d+"_count").val(s)),r.append('<li id="'+a.id+'"><div class="plupload_file_name"><span>'+a.name+'</span></div><div class="plupload_file_action"><a href="#"></a></div><div class="plupload_file_status">'+a.percent+'%</div><div class="plupload_file_size">'+t.formatSize(a.size)+'</div><div class="plupload_clearer">&nbsp;</div>'+n+"</li>"),o(a),e("#"+a.id+".plupload_delete a").click(function(t){e("#"+a.id).remove(),l.removeFile(a),t.preventDefault()})}),e("span.plupload_total_file_size",c).html(t.formatSize(l.total.size)),0===l.total.queued?e("span.plupload_add_text",c).html(i("Add Files")):e("span.plupload_add_text",c).html(t.sprintf(i("%d files queued"),l.total.queued)),e("a.plupload_start",c).toggleClass("plupload_disabled",l.files.length==l.total.uploaded+l.total.failed),r[0].scrollTop=r[0].scrollHeight,a(),!l.files.length&&l.features.dragdrop&&l.settings.dragdrop&&e("#"+d+"_filelist").append('<li class="plupload_droptext">'+i("Drag files here.")+"</li>")}function u(){delete n[d],l.destroy(),c.html(p),l=c=p=null}var l,c,d,p;c=e(this),(d=c.attr("id"))||(d=t.guid(),c.attr("id",d)),p=c.html(),function(t,n){n.contents().each(function(t,i){(i=e(i)).is(".plupload")||i.remove()}),n.prepend('<div class="plupload_wrapper plupload_scroll"><div id="'+t+'_container" class="plupload_container"><div class="plupload"><div class="plupload_header"><div class="plupload_header_content"><div class="plupload_header_title">'+i("Select files")+'</div><div class="plupload_header_text">'+i("Add files to the upload queue and click the start button.")+'</div></div></div><div class="plupload_content"><div class="plupload_filelist_header"><div class="plupload_file_name">'+i("Filename")+'</div><div class="plupload_file_action">&nbsp;</div><div class="plupload_file_status"><span>'+i("Status")+'</span></div><div class="plupload_file_size">'+i("Size")+'</div><div class="plupload_clearer">&nbsp;</div></div><ul id="'+t+'_filelist" class="plupload_filelist"></ul><div class="plupload_filelist_footer"><div class="plupload_file_name"><div class="plupload_buttons"><a href="#" class="plupload_button plupload_add" id="'+t+'_browse">'+i("Add Files")+'</a><a href="#" class="plupload_button plupload_start">'+i("Start Upload")+'</a></div><span class="plupload_upload_status"></span></div><div class="plupload_file_action"></div><div class="plupload_file_status"><span class="plupload_total_status">0%</span></div><div class="plupload_file_size"><span class="plupload_total_file_size">0 b</span></div><div class="plupload_progress"><div class="plupload_progress_container"><div class="plupload_progress_bar"></div></div></div><div class="plupload_clearer">&nbsp;</div></div></div></div></div><input type="hidden" id="'+t+'_count" name="'+t+'_count" value="0" /></div>')}(d,c),(r=e.extend({dragdrop:!0,browse_button:d+"_browse",container:d},r)).dragdrop&&(r.drop_element=d+"_filelist"),l=new t.Uploader(r),n[d]=l,l.bind("UploadFile",function(t,i){e("#"+i.id).addClass("plupload_current_file")}),l.bind("Init",function(t,i){!r.unique_names&&r.rename&&c.on("click","#"+d+"_filelist div.plupload_file_name span",function(i){var n,r,o,a=e(i.target),s="";a.closest("li").hasClass("plupload_delete")&&(o=(n=t.getFile(a.parents("li")[0].id)).name,(r=/^(.+)(\.[^.]+)$/.exec(o))&&(o=r[1],s=r[2]),a.hide().after('<input type="text" />'),a.next().val(o).focus().blur(function(){a.show().next().remove()}).keydown(function(t){var i=e(this);13==t.keyCode&&(t.preventDefault(),n.name=i.val()+s,a.html(n.name),i.blur())}))}),e("#"+d+"_container").attr("title","Using runtime: "+i.runtime),e("a.plupload_start",c).click(function(t){e(this).hasClass("plupload_disabled")||l.start(),t.preventDefault()}),e("a.plupload_stop",c).click(function(e){e.preventDefault(),l.stop()}),e("a.plupload_start",c).addClass("plupload_disabled")}),l.bind("Error",function(n,r){var o,a=r.file;a&&(o=r.message,r.details&&(o+=" ("+r.details+")"),r.code==t.FILE_SIZE_ERROR&&alert(i("Error: File too large:")+" "+a.name),r.code==t.FILE_EXTENSION_ERROR&&alert(i("Error: Invalid file extension:")+" "+a.name),a.hint=o,e("#"+a.id).attr("class","plupload_failed").find("a").css("display","block").attr("title",o)),r.code===t.INIT_ERROR&&setTimeout(function(){u()},1)}),l.bind("PostInit",function(t){t.settings.dragdrop&&t.features.dragdrop&&e("#"+d+"_filelist").append('<li class="plupload_droptext">'+i("Drag files here.")+"</li>")}),l.init(),l.bind("StateChanged",function(){l.state===t.STARTED?(e("li.plupload_delete a,div.plupload_buttons",c).hide(),l.disableBrowse(!0),e("span.plupload_upload_status,div.plupload_progress,a.plupload_stop",c).css("display","block"),e("span.plupload_upload_status",c).html("Uploaded "+l.total.uploaded+"/"+l.files.length+" files"),r.multiple_queues&&e("span.plupload_total_status,span.plupload_total_file_size",c).show()):(s(),e("a.plupload_stop,div.plupload_progress",c).hide(),e("a.plupload_delete",c).css("display","block"),r.multiple_queues&&l.total.uploaded+l.total.failed==l.files.length&&(e(".plupload_buttons,.plupload_upload_status",c).css("display","inline"),l.disableBrowse(!1),e(".plupload_start",c).addClass("plupload_disabled"),e("span.plupload_total_status,span.plupload_total_file_size",c).hide()))}),l.bind("FilesAdded",s),l.bind("FilesRemoved",function(){var t=e("#"+d+"_filelist").scrollTop();s(),e("#"+d+"_filelist").scrollTop(t)}),l.bind("FileUploaded",function(e,t){o(t)}),l.bind("UploadProgress",function(t,i){e("#"+i.id+" div.plupload_file_status",c).html(i.percent+"%"),o(i),a()}),r.setup&&r.setup(l)}),this):n[e(this[0]).attr("id")]}}(jQuery,plupload);

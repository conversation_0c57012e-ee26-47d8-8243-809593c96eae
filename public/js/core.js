function ScrollTo(e){$("html,body").animate({scrollTop:$(e).offset().top},"slow")}function writeCookie(e,t,n){var o=new Date;o.setTime(o.getTime()+864e5*n),document.cookie=e+"="+t+";expires="+o.toGMTString()}function AutoFixPageHeight(){if($("#col")){var e=$("#col").height();$("#pagecontent").height();$("#pagecontent").css("min-height",e+40)}}$(document).ready(function(){($("#content_hldr").width()>768||1==$("meta[name=x-bardaccess-app]").attr("content"))&&($(".jquery-tabs").tabs({activate:function(e,t){$(this).tabs("option","active")},create:function(){var e=[];$(this).find("[data-href]").each(function(){if($(this)){var t=$(this);e.push(t.index()),$(this).click(function(){return window.location=t.data("href"),!1})}}),e.length>0&&$(this).tabs({disabled:e})}}),$(".open-tab").click(function(){var e=$(this).data("open-tab"),t=$(".jquery-tabs").find("[data-tab-name='"+e+"']").index();$(".jquery-tabs").tabs("option","active",t)})),$("#searchBtn").click(function(){$("#searchBox").toggle("slow"),$("#searchIcon").toggleClass("fa-search-minus")}),AutoFixPageHeight(),$(".striped").length>0&&$(".striped tr:nth-child(odd)").addClass("color_two"),$("#return").click(function(){return $("html, body").animate({scrollTop:0},"slow"),!1})});var enablepersist="on",collapseprevious="no",contractsymbol='<span style="text-decoration: none">- </span>',expandsymbol='<span style="text-decoration: none">+ </span>',ccollect=[];function getElementbyClass(e,t){var n=[],o=0,c=e.length;for(i=0;i<c;i++)e[i].className==t&&(n[o++]=e[i]);return n}function sweeptoggle(e){for(var t="expand"==e?"block":"none",n=0;ccollect[n];)ccollect[n].style.display=t,n++;revivestatus()}function contractcontent(e){for(var t=0;ccollect[t];)ccollect[t].id!=e&&(ccollect[t].style.display="none"),t++}function expandcontent(e,t){var n=getElementbyClass(e.getElementsByTagName("SPAN"),"showstate");ccollect.length>0&&("yes"==collapseprevious&&contractcontent(t),document.getElementById(t).style.display="block"!=document.getElementById(t).style.display?"block":"none",n.length>0&&("no"==collapseprevious?n[0].innerHTML="block"==document.getElementById(t).style.display?contractsymbol:expandsymbol:revivestatus()))}function revivecontent(){for(contractcontent("omitnothing"),selectedItem=getselectedItem(),selectedComponents=selectedItem.split("|"),i=0;i<selectedComponents.length-1;i++)document.getElementById(selectedComponents[i]).style.display="block"}function revivestatus(){for(var e=0;statecollect[e];)"block"==ccollect[e].style.display?statecollect[e].innerHTML=contractsymbol:statecollect[e].innerHTML=expandsymbol,e++}function get_cookie(e){var t=e+"=",n="";return document.cookie.length>0&&(offset=document.cookie.indexOf(t),-1!=offset&&(offset+=t.length,end=document.cookie.indexOf(";",offset),-1==end&&(end=document.cookie.length),n=unescape(document.cookie.substring(offset,end)))),n}function getselectedItem(){return""!=get_cookie(window.location.pathname)?(selectedItem=get_cookie(window.location.pathname),selectedItem):""}function saveswitchstate(){for(var e=0,t="";ccollect[e];)"block"==ccollect[e].style.display&&(t+=ccollect[e].id+"|"),e++;document.cookie=window.location.pathname+"="+t}function do_onload(){uniqueidn=window.location.pathname+"firsttimeload";var e=document.all?document.all:document.getElementsByTagName("*");ccollect=getElementbyClass(e,"switchcontent"),statecollect=getElementbyClass(e,"showstate"),"on"==enablepersist&&ccollect.length>0&&(document.cookie=""==get_cookie(uniqueidn)?uniqueidn+"=1":uniqueidn+"=0",firsttimeload=1==get_cookie(uniqueidn)?1:0,firsttimeload||revivecontent()),ccollect.length>0&&statecollect.length>0&&revivestatus()}document.getElementById&&(document.write('<style type="text/css">'),document.write(".switchcontent{display:none;}"),document.write("</style>")),window.addEventListener?window.addEventListener("load",do_onload,!1):window.attachEvent?window.attachEvent("onload",do_onload):document.getElementById&&(window.onload=do_onload),"on"==enablepersist&&document.getElementById&&(window.onunload=saveswitchstate);

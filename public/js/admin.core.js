var enablepersist="on",collapseprevious="no",contractsymbol='<span style="text-decoration: none">- </span>',expandsymbol='<span style="text-decoration: none">+ </span>',ccollect=[];function getElementbyClass(e,t){var n=[],o=0,s=e.length;for(i=0;i<s;i++)e[i].className==t&&(n[o++]=e[i]);return n}function sweeptoggle(e){for(var t="expand"==e?"block":"none",n=0;ccollect[n];)ccollect[n].style.display=t,n++;revivestatus()}function contractcontent(e){for(var t=0;ccollect[t];)ccollect[t].id!=e&&(ccollect[t].style.display="none"),t++}function expandcontent(e,t){var n=getElementbyClass(e.getElementsByTagName("SPAN"),"showstate");ccollect.length>0&&("yes"==collapseprevious&&contractcontent(t),document.getElementById(t).style.display="block"!=document.getElementById(t).style.display?"block":"none",n.length>0&&("no"==collapseprevious?n[0].innerHTML="block"==document.getElementById(t).style.display?contractsymbol:expandsymbol:revivestatus()))}function revivecontent(){for(contractcontent("omitnothing"),selectedItem=getselectedItem(),selectedComponents=selectedItem.split("|"),i=0;i<selectedComponents.length-1;i++)document.getElementById(selectedComponents[i]).style.display="block"}function revivestatus(){for(var e=0;statecollect[e];)"block"==ccollect[e].style.display?statecollect[e].innerHTML=contractsymbol:statecollect[e].innerHTML=expandsymbol,e++}function get_cookie(e){var t=e+"=",n="";return document.cookie.length>0&&(offset=document.cookie.indexOf(t),-1!=offset&&(offset+=t.length,end=document.cookie.indexOf(";",offset),-1==end&&(end=document.cookie.length),n=unescape(document.cookie.substring(offset,end)))),n}function getselectedItem(){return""!=get_cookie(window.location.pathname)?(selectedItem=get_cookie(window.location.pathname),selectedItem):""}function saveswitchstate(){for(var e=0,t="";ccollect[e];)"block"==ccollect[e].style.display&&(t+=ccollect[e].id+"|"),e++;document.cookie=window.location.pathname+"="+t}function do_onload(){uniqueidn=window.location.pathname+"firsttimeload";var e=document.all?document.all:document.getElementsByTagName("*");ccollect=getElementbyClass(e,"switchcontent"),statecollect=getElementbyClass(e,"showstate"),"on"==enablepersist&&ccollect.length>0&&(document.cookie=""==get_cookie(uniqueidn)?uniqueidn+"=1":uniqueidn+"=0",firsttimeload=1==get_cookie(uniqueidn)?1:0,firsttimeload||revivecontent()),ccollect.length>0&&statecollect.length>0&&revivestatus()}function ScrollTo(e){$("html,body").animate({scrollTop:$(e).offset().top},"slow")}function writeCookie(e,t,n){var i=new Date;i.setTime(i.getTime()+864e5*n),document.cookie=e+"="+t+";expires="+i.toGMTString()}function returnToTopVisibility(){$("body").height()>$(window).height()?$("#return").fadeIn("slow"):$("#return").fadeOut(0)}function AutoFixPageHeight(){if($("#col")){var e=$("#col").height();$("#pagecontent").height();$("#pagecontent").css("min-height",e+40)}}function confirmDelete(e,t){if(!confirm("Delete selected item?"))return!1;getRequest(e,t),$(e).ajaxSuccess(function(){$(this).remove()})}function removeElement(e){$(document).ready(function(){$(e).fadeOut("slow",function(){$(e).remove()})})}function reloadFileList(){$(document).ready(function(){fadeGetRequest("#file_list","jsonActions.php?action=file_list","loading")})}function setupFileList(){$(document).ready(function(){$("#file_list li").hover(function(){var e=$("#file_list li").index(this);$("#file_list li span.icon_hldr").eq(e).fadeIn(100)},function(){var e=$("#file_list li").index(this);$("#file_list li span.icon_hldr").eq(e).fadeOut(500)})})}function makeListSortable(e,t,n,i,o){$(document).ready(function(){$(e).sortable({items:t,scroll:!0,update:function(t,s){$(e+" input").each(function(e,t){$(this).val(e+1)}),postRequest(n,i,o)}}),$(t).addClass("grab"),$(t).mousedown(function(){$(this).addClass("grabbing")}),$(t).mouseup(function(){$(this).removeClass("grabbing")})})}function ScrollTo(e){$("html,body").animate({scrollTop:$(e).offset().top},"slow")}function recordStatus(e,t,n){switch(e){case"update":$.ajax({async:!1,type:"POST",url:t,dataType:"json",data:n,success:function(e){"success"===e.status?($("#tr"+e.record_id).toggleClass("inactive"),$("#statusMessage").empty().append("The following record was updated: "+e.record_title),$("#statusMessage").toggle("slow").delay(2400).toggle("slow")):"fail"===e.status&&($("#statusMessage").empty().append("There was a problem updating: "+e.record_title+". "+e.message),$("#statusMessage").toggle("slow").delay(2400).toggle("slow"))},error:function(e,t,n){console.log(e.responseText)}});break;case"delete":var i=$("#delete-record-"+n.id).data("record-title");BootstrapDialog.show({message:"Are you sure you want to delete "+i,buttons:[{label:"Cancel",action:function(e){e.close()}},{icon:"fa fa-trash-o",label:"Yes, delete this record",cssClass:"btn-danger",action:function(e){$.ajax({async:!1,type:"POST",url:t,dataType:"json",data:n,success:function(e){"success"===e.status?($("#tr"+e.record_id).toggle("slow"),$("#statusMessage").empty().append("The following record was deleted: "+e.record_title),$("#statusMessage").toggle("slow").delay(2400).toggle("slow")):"fail"===e.status&&($("#statusMessage").empty().append("There was a problem deleting: "+e.record_title+". "+e.message),$("#statusMessage").toggle("slow").delay(2400).toggle("slow"))},error:function(e,t,n){console.log(e.responseText)}}),e.close()}}]})}}function showMessage(e,t){$("#message").fadeIn("fast",function(){$(this).append("<li>"+t+"</li>")}),$("#message").toggleClass(e,"error"===e)}function ajaxRequest(e,t,n){$.get(e,{_token:t}).done(function(e){void 0!==e.error&&e.error?showMessage("error",e.error):void 0!==e.output&&e.output&&($(n).html(e.output),void 0!==e.msg&&e.msg&&showMessage("message",e.msg))},"json").fail(function(){console.log("There was a problem completing the request")})}function postRequest(e,t,n){$.ajax({async:!1,type:"POST",url:e,dataType:"json",data:t,success:function(e){"success"===e.status?($(n).html(e.output),$("#statusMessage").empty().append("The following record was updated: "+e.record_title),$("#statusMessage").toggle("slow").delay(2400).toggle("slow")):"fail"===e.status&&($("#statusMessage").empty().append("There was a problem updating: "+e.record_title+". "+e.message),$("#statusMessage").toggle("slow").delay(2400).toggle("slow"))},error:function(e,t,n){console.log(e.responseText)}})}document.getElementById&&(document.write('<style type="text/css">'),document.write(".switchcontent{display:none;}"),document.write("</style>")),window.addEventListener?window.addEventListener("load",do_onload,!1):window.attachEvent?window.attachEvent("onload",do_onload):document.getElementById&&(window.onload=do_onload),"on"==enablepersist&&document.getElementById&&(window.onunload=saveswitchstate),$(document).ready(function(){($("#container").width()>768||1==$("meta[name=x-bardaccess-app]").attr("content"))&&($(".jquery-tabs").tabs({activate:function(e,t){$(this).tabs("option","active")>=0&&returnToTopVisibility()},create:function(){var e=[];$(this).find("[data-href]").each(function(){if($(this)){var t=$(this);e.push(t.index()),$(this).click(function(){return window.location=t.data("href"),!1})}}),e.length>0&&$(this).tabs({disabled:e})}}),$(".open-tab").click(function(){var e=$(this).data("open-tab"),t=$(".jquery-tabs").find("[data-tab-name='"+e+"']").index();$(".jquery-tabs").tabs("option","active",t)})),$("#searchBtn").click(function(){$("#searchBox").toggle("slow"),$("#searchIcon").toggleClass("fa-search-minus")}),returnToTopVisibility(),AutoFixPageHeight(),$(".striped").length>0&&$(".striped tr:nth-child(odd)").addClass("color_two"),$("#return").click(function(){return $("html, body").animate({scrollTop:0},"slow"),!1})}),$(document).ready(function(){$("#return").click(function(){return $("html, body").animate({scrollTop:0},"slow"),!1})}),function(e,t){"function"==typeof define&&define.amd?define("sifter",t):"object"==typeof exports?module.exports=t():e.Sifter=t()}(this,function(){var e=function(e,t){this.items=e,this.settings=t||{diacritics:!0}};e.prototype.tokenize=function(e){if(!(e=o(String(e||"").toLowerCase()))||!e.length)return[];var t,n,i,r,l=[],c=e.split(/ +/);for(t=0,n=c.length;t<n;t++){if(i=s(c[t]),this.settings.diacritics)for(r in a)a.hasOwnProperty(r)&&(i=i.replace(new RegExp(r,"g"),a[r]));l.push({string:c[t],regex:new RegExp(i,"i")})}return l},e.prototype.iterator=function(e,t){(r(e)?Array.prototype.forEach||function(e){for(var t=0,n=this.length;t<n;t++)e(this[t],t,this)}:function(e){for(var t in this)this.hasOwnProperty(t)&&e(this[t],t,this)}).apply(e,[t])},e.prototype.getScoreFunction=function(e,t){var n,o,s,r;e=this.prepareSearch(e,t),o=e.tokens,n=e.options.fields,s=o.length,r=e.options.nesting;var a=function(e,t){var n,i;return e?-1===(i=(e=String(e||"")).search(t.regex))?0:(n=t.string.length/e.length,0===i&&(n+=.5),n):0},l=function(){var e=n.length;return e?1===e?function(e,t){return a(i(t,n[0],r),e)}:function(t,o){for(var s=0,l=0;s<e;s++)l+=a(i(o,n[s],r),t);return l/e}:function(){return 0}}();return s?1===s?function(e){return l(o[0],e)}:"and"===e.options.conjunction?function(e){for(var t,n=0,i=0;n<s;n++){if((t=l(o[n],e))<=0)return 0;i+=t}return i/s}:function(e){for(var t=0,n=0;t<s;t++)n+=l(o[t],e);return n/s}:function(){return 0}},e.prototype.getSortFunction=function(e,n){var o,s,r,a,l,c,u,d,p,h,g;if(g=!(e=(r=this).prepareSearch(e,n)).query&&n.sort_empty||n.sort,p=function(e,t){return"$score"===e?t.score:i(r.items[t.id],e,n.nesting)},l=[],g)for(o=0,s=g.length;o<s;o++)(e.query||"$score"!==g[o].field)&&l.push(g[o]);if(e.query){for(h=!0,o=0,s=l.length;o<s;o++)if("$score"===l[o].field){h=!1;break}h&&l.unshift({field:"$score",direction:"desc"})}else for(o=0,s=l.length;o<s;o++)if("$score"===l[o].field){l.splice(o,1);break}for(d=[],o=0,s=l.length;o<s;o++)d.push("desc"===l[o].direction?-1:1);return(c=l.length)?1===c?(a=l[0].field,u=d[0],function(e,n){return u*t(p(a,e),p(a,n))}):function(e,n){var i,o,s;for(i=0;i<c;i++)if(s=l[i].field,o=d[i]*t(p(s,e),p(s,n)))return o;return 0}:null},e.prototype.prepareSearch=function(e,t){if("object"==typeof e)return e;var i=(t=n({},t)).fields,o=t.sort,s=t.sort_empty;return i&&!r(i)&&(t.fields=[i]),o&&!r(o)&&(t.sort=[o]),s&&!r(s)&&(t.sort_empty=[s]),{options:t,query:String(e||"").toLowerCase(),tokens:this.tokenize(e),total:0,items:[]}},e.prototype.search=function(e,t){var n,i,o,s,r=this;return i=this.prepareSearch(e,t),t=i.options,e=i.query,s=t.score||r.getScoreFunction(i),e.length?r.iterator(r.items,function(e,o){n=s(e),(!1===t.filter||n>0)&&i.items.push({score:n,id:o})}):r.iterator(r.items,function(e,t){i.items.push({score:1,id:t})}),(o=r.getSortFunction(i,t))&&i.items.sort(o),i.total=i.items.length,"number"==typeof t.limit&&(i.items=i.items.slice(0,t.limit)),i};var t=function(e,t){return"number"==typeof e&&"number"==typeof t?e>t?1:e<t?-1:0:(e=l(String(e||"")))>(t=l(String(t||"")))?1:t>e?-1:0},n=function(e,t){var n,i,o,s;for(n=1,i=arguments.length;n<i;n++)if(s=arguments[n])for(o in s)s.hasOwnProperty(o)&&(e[o]=s[o]);return e},i=function(e,t,n){if(e&&t){if(!n)return e[t];for(var i=t.split(".");i.length&&(e=e[i.shift()]););return e}},o=function(e){return(e+"").replace(/^\s+|\s+$|/g,"")},s=function(e){return(e+"").replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1")},r=Array.isArray||"undefined"!=typeof $&&$.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},a={a:"[aḀḁĂăÂâǍǎȺⱥȦȧẠạÄäÀàÁáĀāÃãÅåąĄÃąĄ]",b:"[b␢βΒB฿𐌁ᛒ]",c:"[cĆćĈĉČčĊċC̄c̄ÇçḈḉȻȼƇƈɕᴄＣｃ]",d:"[dĎďḊḋḐḑḌḍḒḓḎḏĐđD̦d̦ƉɖƊɗƋƌᵭᶁᶑȡᴅＤｄð]",e:"[eÉéÈèÊêḘḙĚěĔĕẼẽḚḛẺẻĖėËëĒēȨȩĘęᶒɆɇȄȅẾếỀềỄễỂểḜḝḖḗḔḕȆȇẸẹỆệⱸᴇＥｅɘǝƏƐε]",f:"[fƑƒḞḟ]",g:"[gɢ₲ǤǥĜĝĞğĢģƓɠĠġ]",h:"[hĤĥĦħḨḩẖẖḤḥḢḣɦʰǶƕ]",i:"[iÍíÌìĬĭÎîǏǐÏïḮḯĨĩĮįĪīỈỉȈȉȊȋỊịḬḭƗɨɨ̆ᵻᶖİiIıɪＩｉ]",j:"[jȷĴĵɈɉʝɟʲ]",k:"[kƘƙꝀꝁḰḱǨǩḲḳḴḵκϰ₭]",l:"[lŁłĽľĻļĹĺḶḷḸḹḼḽḺḻĿŀȽƚⱠⱡⱢɫɬᶅɭȴʟＬｌ]",n:"[nŃńǸǹŇňÑñṄṅŅņṆṇṊṋṈṉN̈n̈ƝɲȠƞᵰᶇɳȵɴＮｎŊŋ]",o:"[oØøÖöÓóÒòÔôǑǒŐőŎŏȮȯỌọƟɵƠơỎỏŌōÕõǪǫȌȍՕօ]",p:"[pṔṕṖṗⱣᵽƤƥᵱ]",q:"[qꝖꝗʠɊɋꝘꝙq̃]",r:"[rŔŕɌɍŘřŖŗṘṙȐȑȒȓṚṛⱤɽ]",s:"[sŚśṠṡṢṣꞨꞩŜŝŠšŞşȘșS̈s̈]",t:"[tŤťṪṫŢţṬṭƮʈȚțṰṱṮṯƬƭ]",u:"[uŬŭɄʉỤụÜüÚúÙùÛûǓǔŰűŬŭƯưỦủŪūŨũŲųȔȕ∪]",v:"[vṼṽṾṿƲʋꝞꝟⱱʋ]",w:"[wẂẃẀẁŴŵẄẅẆẇẈẉ]",x:"[xẌẍẊẋχ]",y:"[yÝýỲỳŶŷŸÿỸỹẎẏỴỵɎɏƳƴ]",z:"[zŹźẐẑŽžŻżẒẓẔẕƵƶ]"},l=function(){var e,t,n,i,o="",s={};for(n in a)if(a.hasOwnProperty(n))for(o+=i=a[n].substring(2,a[n].length-1),e=0,t=i.length;e<t;e++)s[i.charAt(e)]=n;var r=new RegExp("["+o+"]","g");return function(e){return e.replace(r,function(e){return s[e]}).toLowerCase()}}();return e}),function(e,t){"function"==typeof define&&define.amd?define("microplugin",t):"object"==typeof exports?module.exports=t():e.MicroPlugin=t()}(this,function(){var e={mixin:function(e){e.plugins={},e.prototype.initializePlugins=function(e){var n,i,o,s=this,r=[];if(s.plugins={names:[],settings:{},requested:{},loaded:{}},t.isArray(e))for(n=0,i=e.length;n<i;n++)"string"==typeof e[n]?r.push(e[n]):(s.plugins.settings[e[n].name]=e[n].options,r.push(e[n].name));else if(e)for(o in e)e.hasOwnProperty(o)&&(s.plugins.settings[o]=e[o],r.push(o));for(;r.length;)s.require(r.shift())},e.prototype.loadPlugin=function(t){var n=this,i=n.plugins,o=e.plugins[t];if(!e.plugins.hasOwnProperty(t))throw new Error('Unable to find "'+t+'" plugin');i.requested[t]=!0,i.loaded[t]=o.fn.apply(n,[n.plugins.settings[t]||{}]),i.names.push(t)},e.prototype.require=function(e){var t=this,n=t.plugins;if(!t.plugins.loaded.hasOwnProperty(e)){if(n.requested[e])throw new Error('Plugin has circular dependency ("'+e+'")');t.loadPlugin(e)}return n.loaded[e]},e.define=function(t,n){e.plugins[t]={name:t,fn:n}}}},t={isArray:Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}};return e}),function(e,t){"function"==typeof define&&define.amd?define("selectize",["jquery","sifter","microplugin"],t):"object"==typeof exports?module.exports=t(require("jquery"),require("sifter"),require("microplugin")):e.Selectize=t(e.jQuery,e.Sifter,e.MicroPlugin)}(this,function(e,t,n){"use strict";var i=function(e,t){if("string"!=typeof t||t.length){var n="string"==typeof t?new RegExp(t,"i"):t,i=function(e){var t=0;if(3===e.nodeType){var o=e.data.search(n);if(o>=0&&e.data.length>0){var s=e.data.match(n),r=document.createElement("span");r.className="highlight";var a=e.splitText(o),l=(a.splitText(s[0].length),a.cloneNode(!0));r.appendChild(l),a.parentNode.replaceChild(r,a),t=1}}else if(1===e.nodeType&&e.childNodes&&!/(script|style)/i.test(e.tagName)&&("highlight"!==e.className||"SPAN"!==e.tagName))for(var c=0;c<e.childNodes.length;++c)c+=i(e.childNodes[c]);return t};return e.each(function(){i(this)})}};e.fn.removeHighlight=function(){return this.find("span.highlight").each(function(){this.parentNode.firstChild.nodeName;var e=this.parentNode;e.replaceChild(this.firstChild,this),e.normalize()}).end()};var o=function(){};o.prototype={on:function(e,t){this._events=this._events||{},this._events[e]=this._events[e]||[],this._events[e].push(t)},off:function(e,t){var n=arguments.length;return 0===n?delete this._events:1===n?delete this._events[e]:(this._events=this._events||{},void(e in this._events!=0&&this._events[e].splice(this._events[e].indexOf(t),1)))},trigger:function(e){if(this._events=this._events||{},e in this._events!=0)for(var t=0;t<this._events[e].length;t++)this._events[e][t].apply(this,Array.prototype.slice.call(arguments,1))}},o.mixin=function(e){for(var t=["on","off","trigger"],n=0;n<t.length;n++)e.prototype[t[n]]=o.prototype[t[n]]};var s=/Mac/.test(navigator.userAgent),r=s?91:17,a=s?18:17,l=!/android/i.test(window.navigator.userAgent)&&!!document.createElement("input").validity,c=function(e){return void 0!==e},u=function(e){return null==e?null:"boolean"==typeof e?e?"1":"0":e+""},d=function(e){return(e+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")},p={before:function(e,t,n){var i=e[t];e[t]=function(){return n.apply(e,arguments),i.apply(e,arguments)}},after:function(e,t,n){var i=e[t];e[t]=function(){var t=i.apply(e,arguments);return n.apply(e,arguments),t}}},h=function(e,t){var n;return function(){var i=this,o=arguments;window.clearTimeout(n),n=window.setTimeout(function(){e.apply(i,o)},t)}},g=function(e,t,n){var i,o=e.trigger,s={};for(i in e.trigger=function(){var n=arguments[0];if(-1===t.indexOf(n))return o.apply(e,arguments);s[n]=arguments},n.apply(e,[]),e.trigger=o,s)s.hasOwnProperty(i)&&o.apply(e,s[i])},f=function(e){var t={};if("selectionStart"in e)t.start=e.selectionStart,t.length=e.selectionEnd-t.start;else if(document.selection){e.focus();var n=document.selection.createRange(),i=document.selection.createRange().text.length;n.moveStart("character",-e.value.length),t.start=n.text.length-i,t.length=i}return t},v=function(t,n){return t?(w.$testInput||(w.$testInput=e("<span />").css({position:"absolute",top:-99999,left:-99999,width:"auto",padding:0,whiteSpace:"pre"}).appendTo("body")),w.$testInput.text(t),function(e,t,n){var i,o,s={};if(n)for(i=0,o=n.length;i<o;i++)s[n[i]]=e.css(n[i]);else s=e.css();t.css(s)}(n,w.$testInput,["letterSpacing","fontSize","fontFamily","fontWeight","textTransform"]),w.$testInput.width()):0},m=function(e){var t=null,n=function(n,i){var o,s,r,a,l,c,u,d;i=i||{},(n=n||window.event||{}).metaKey||n.altKey||(i.force||!1!==e.data("grow"))&&(o=e.val(),n.type&&"keydown"===n.type.toLowerCase()&&(r=(s=n.keyCode)>=48&&s<=57||s>=65&&s<=90||s>=96&&s<=111||s>=186&&s<=222||32===s,46===s||8===s?(d=f(e[0])).length?o=o.substring(0,d.start)+o.substring(d.start+d.length):8===s&&d.start?o=o.substring(0,d.start-1)+o.substring(d.start+1):46===s&&void 0!==d.start&&(o=o.substring(0,d.start)+o.substring(d.start+1)):r&&(c=n.shiftKey,u=String.fromCharCode(n.keyCode),o+=u=c?u.toUpperCase():u.toLowerCase())),a=e.attr("placeholder"),!o&&a&&(o=a),(l=v(o,e)+4)!==t&&(t=l,e.width(l),e.triggerHandler("resize")))};e.on("keydown keyup update blur",n),n()},y=function(e){var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML},w=function(n,i){var o,s,r,a,l=this;(a=n[0]).selectize=l;var c=window.getComputedStyle&&window.getComputedStyle(a,null);if(r=(r=c?c.getPropertyValue("direction"):a.currentStyle&&a.currentStyle.direction)||n.parents("[dir]:first").attr("dir")||"",e.extend(l,{order:0,settings:i,$input:n,tabIndex:n.attr("tabindex")||"",tagType:"select"===a.tagName.toLowerCase()?1:2,rtl:/rtl/i.test(r),eventNS:".selectize"+ ++w.count,highlightedValue:null,isBlurring:!1,isOpen:!1,isDisabled:!1,isRequired:n.is("[required]"),isInvalid:!1,isLocked:!1,isFocused:!1,isInputHidden:!1,isSetup:!1,isShiftDown:!1,isCmdDown:!1,isCtrlDown:!1,ignoreFocus:!1,ignoreBlur:!1,ignoreHover:!1,hasOptions:!1,currentResults:null,lastValue:"",caretPos:0,loading:0,loadedSearches:{},$activeOption:null,$activeItems:[],optgroups:{},options:{},userOptions:{},items:[],renderCache:{},onSearchChange:null===i.loadThrottle?l.onSearchChange:h(l.onSearchChange,i.loadThrottle)}),l.sifter=new t(this.options,{diacritics:i.diacritics}),l.settings.options){for(o=0,s=l.settings.options.length;o<s;o++)l.registerOption(l.settings.options[o]);delete l.settings.options}if(l.settings.optgroups){for(o=0,s=l.settings.optgroups.length;o<s;o++)l.registerOptionGroup(l.settings.optgroups[o]);delete l.settings.optgroups}l.settings.mode=l.settings.mode||(1===l.settings.maxItems?"single":"multi"),"boolean"!=typeof l.settings.hideSelected&&(l.settings.hideSelected="multi"===l.settings.mode),l.initializePlugins(l.settings.plugins),l.setupCallbacks(),l.setupTemplates(),l.setup()};return o.mixin(w),void 0!==n?n.mixin(w):function(e,t){t||(t={}),console.error("Selectize: Dependency MicroPlugin is missing"),t.explanation&&(console.group&&console.group(),console.error(t.explanation),console.group&&console.groupEnd())}(0,{explanation:'Make sure you either: (1) are using the "standalone" version of Selectize, or (2) require MicroPlugin before you load Selectize.'}),e.extend(w.prototype,{setup:function(){var t,n,i,o,c,u,d,p,h,g,f=this,v=f.settings,y=f.eventNS,w=e(window),$=e(document),b=f.$input;if(d=f.settings.mode,p=b.attr("class")||"",t=e("<div>").addClass(v.wrapperClass).addClass(p).addClass(d),n=e("<div>").addClass(v.inputClass).addClass("items").appendTo(t),i=e('<input type="text" autocomplete="off" />').appendTo(n).attr("tabindex",b.is(":disabled")?"-1":f.tabIndex),u=e(v.dropdownParent||t),o=e("<div>").addClass(v.dropdownClass).addClass(d).hide().appendTo(u),c=e("<div>").addClass(v.dropdownContentClass).appendTo(o),(g=b.attr("id"))&&(i.attr("id",g+"-selectized"),e("label[for='"+g+"']").attr("for",g+"-selectized")),f.settings.copyClassesToDropdown&&o.addClass(p),t.css({width:b[0].style.width}),f.plugins.names.length&&(h="plugin-"+f.plugins.names.join(" plugin-"),t.addClass(h),o.addClass(h)),(null===v.maxItems||v.maxItems>1)&&1===f.tagType&&b.attr("multiple","multiple"),f.settings.placeholder&&i.attr("placeholder",v.placeholder),!f.settings.splitOn&&f.settings.delimiter){var C=f.settings.delimiter.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&");f.settings.splitOn=new RegExp("\\s*"+C+"+\\s*")}b.attr("autocorrect")&&i.attr("autocorrect",b.attr("autocorrect")),b.attr("autocapitalize")&&i.attr("autocapitalize",b.attr("autocapitalize")),i[0].type=b[0].type,f.$wrapper=t,f.$control=n,f.$control_input=i,f.$dropdown=o,f.$dropdown_content=c,o.on("mouseenter mousedown click","[data-disabled]>[data-selectable]",function(e){e.stopImmediatePropagation()}),o.on("mouseenter","[data-selectable]",function(){return f.onOptionHover.apply(f,arguments)}),o.on("mousedown click","[data-selectable]",function(){return f.onOptionSelect.apply(f,arguments)}),function(e,t,n,i){e.on(t,n,function(t){for(var n=t.target;n&&n.parentNode!==e[0];)n=n.parentNode;return t.currentTarget=n,i.apply(this,[t])})}(n,"mousedown","*:not(input)",function(){return f.onItemSelect.apply(f,arguments)}),m(i),n.on({mousedown:function(){return f.onMouseDown.apply(f,arguments)},click:function(){return f.onClick.apply(f,arguments)}}),i.on({mousedown:function(e){e.stopPropagation()},keydown:function(){return f.onKeyDown.apply(f,arguments)},keyup:function(){return f.onKeyUp.apply(f,arguments)},keypress:function(){return f.onKeyPress.apply(f,arguments)},resize:function(){f.positionDropdown.apply(f,[])},blur:function(){return f.onBlur.apply(f,arguments)},focus:function(){return f.ignoreBlur=!1,f.onFocus.apply(f,arguments)},paste:function(){return f.onPaste.apply(f,arguments)}}),$.on("keydown"+y,function(e){f.isCmdDown=e[s?"metaKey":"ctrlKey"],f.isCtrlDown=e[s?"altKey":"ctrlKey"],f.isShiftDown=e.shiftKey}),$.on("keyup"+y,function(e){e.keyCode===a&&(f.isCtrlDown=!1),16===e.keyCode&&(f.isShiftDown=!1),e.keyCode===r&&(f.isCmdDown=!1)}),$.on("mousedown"+y,function(e){if(f.isFocused){if(e.target===f.$dropdown[0]||e.target.parentNode===f.$dropdown[0])return!1;f.$control.has(e.target).length||e.target===f.$control[0]||f.blur(e.target)}}),w.on(["scroll"+y,"resize"+y].join(" "),function(){f.isOpen&&f.positionDropdown.apply(f,arguments)}),w.on("mousemove"+y,function(){f.ignoreHover=!1}),this.revertSettings={$children:b.children().detach(),tabindex:b.attr("tabindex")},b.attr("tabindex",-1).hide().after(f.$wrapper),e.isArray(v.items)&&(f.setValue(v.items),delete v.items),l&&b.on("invalid"+y,function(e){e.preventDefault(),f.isInvalid=!0,f.refreshState()}),f.updateOriginalInput(),f.refreshItems(),f.refreshState(),f.updatePlaceholder(),f.isSetup=!0,b.is(":disabled")&&f.disable(),f.on("change",this.onChange),b.data("selectize",f),b.addClass("selectized"),f.trigger("initialize"),!0===v.preload&&f.onSearchChange("")},setupTemplates:function(){var t=this,n=t.settings.labelField,i=t.settings.optgroupLabelField,o={optgroup:function(e){return'<div class="optgroup">'+e.html+"</div>"},optgroup_header:function(e,t){return'<div class="optgroup-header">'+t(e[i])+"</div>"},option:function(e,t){return'<div class="option">'+t(e[n])+"</div>"},item:function(e,t){return'<div class="item">'+t(e[n])+"</div>"},option_create:function(e,t){return'<div class="create">Add <strong>'+t(e.input)+"</strong>&hellip;</div>"}};t.settings.render=e.extend({},o,t.settings.render)},setupCallbacks:function(){var e,t,n={initialize:"onInitialize",change:"onChange",item_add:"onItemAdd",item_remove:"onItemRemove",clear:"onClear",option_add:"onOptionAdd",option_remove:"onOptionRemove",option_clear:"onOptionClear",optgroup_add:"onOptionGroupAdd",optgroup_remove:"onOptionGroupRemove",optgroup_clear:"onOptionGroupClear",dropdown_open:"onDropdownOpen",dropdown_close:"onDropdownClose",type:"onType",load:"onLoad",focus:"onFocus",blur:"onBlur"};for(e in n)n.hasOwnProperty(e)&&(t=this.settings[n[e]])&&this.on(e,t)},onClick:function(e){var t=this;t.isFocused&&t.isOpen||(t.focus(),e.preventDefault())},onMouseDown:function(t){var n=this,i=t.isDefaultPrevented();if(e(t.target),n.isFocused){if(t.target!==n.$control_input[0])return"single"===n.settings.mode?n.isOpen?n.close():n.open():i||n.setActiveItem(null),!1}else i||window.setTimeout(function(){n.focus()},0)},onChange:function(){this.$input.trigger("change")},onPaste:function(t){var n=this;n.isFull()||n.isInputHidden||n.isLocked?t.preventDefault():n.settings.splitOn&&setTimeout(function(){var t=n.$control_input.val();if(t.match(n.settings.splitOn))for(var i=e.trim(t).split(n.settings.splitOn),o=0,s=i.length;o<s;o++)n.createItem(i[o])},0)},onKeyPress:function(e){if(this.isLocked)return e&&e.preventDefault();var t=String.fromCharCode(e.keyCode||e.which);return this.settings.create&&"multi"===this.settings.mode&&t===this.settings.delimiter?(this.createItem(),e.preventDefault(),!1):void 0},onKeyDown:function(e){var t=(e.target,this.$control_input[0],this);if(!t.isLocked){switch(e.keyCode){case 65:if(t.isCmdDown)return void t.selectAll();break;case 27:return void(t.isOpen&&(e.preventDefault(),e.stopPropagation(),t.close()));case 78:if(!e.ctrlKey||e.altKey)break;case 40:if(!t.isOpen&&t.hasOptions)t.open();else if(t.$activeOption){t.ignoreHover=!0;var n=t.getAdjacentOption(t.$activeOption,1);n.length&&t.setActiveOption(n,!0,!0)}return void e.preventDefault();case 80:if(!e.ctrlKey||e.altKey)break;case 38:if(t.$activeOption){t.ignoreHover=!0;var i=t.getAdjacentOption(t.$activeOption,-1);i.length&&t.setActiveOption(i,!0,!0)}return void e.preventDefault();case 13:return void(t.isOpen&&t.$activeOption&&(t.onOptionSelect({currentTarget:t.$activeOption}),e.preventDefault()));case 37:return void t.advanceSelection(-1,e);case 39:return void t.advanceSelection(1,e);case 9:return t.settings.selectOnTab&&t.isOpen&&t.$activeOption&&(t.onOptionSelect({currentTarget:t.$activeOption}),t.isFull()||e.preventDefault()),void(t.settings.create&&t.createItem()&&e.preventDefault());case 8:case 46:return void t.deleteSelection(e)}return!t.isFull()&&!t.isInputHidden||(s?e.metaKey:e.ctrlKey)?void 0:void e.preventDefault()}9!==e.keyCode&&e.preventDefault()},onKeyUp:function(e){var t=this;if(t.isLocked)return e&&e.preventDefault();var n=t.$control_input.val()||"";t.lastValue!==n&&(t.lastValue=n,t.onSearchChange(n),t.refreshOptions(),t.trigger("type",n))},onSearchChange:function(e){var t=this,n=t.settings.load;n&&(t.loadedSearches.hasOwnProperty(e)||(t.loadedSearches[e]=!0,t.load(function(i){n.apply(t,[e,i])})))},onFocus:function(e){var t=this,n=t.isFocused;if(t.isDisabled)return t.blur(),e&&e.preventDefault(),!1;t.ignoreFocus||(t.isFocused=!0,"focus"===t.settings.preload&&t.onSearchChange(""),n||t.trigger("focus"),t.$activeItems.length||(t.showInput(),t.setActiveItem(null),t.refreshOptions(!!t.settings.openOnFocus)),t.refreshState())},onBlur:function(e,t){var n=this;if(n.isFocused&&(n.isFocused=!1,!n.ignoreFocus)){if(!n.ignoreBlur&&document.activeElement===n.$dropdown_content[0])return n.ignoreBlur=!0,void n.onFocus(e);var i=function(){n.close(),n.setTextboxValue(""),n.setActiveItem(null),n.setActiveOption(null),n.setCaret(n.items.length),n.refreshState(),t&&t.focus&&t.focus(),n.isBlurring=!1,n.ignoreFocus=!1,n.trigger("blur")};n.isBlurring=!0,n.ignoreFocus=!0,n.settings.create&&n.settings.createOnBlur?n.createItem(null,!1,i):i()}},onOptionHover:function(e){this.ignoreHover||this.setActiveOption(e.currentTarget,!1)},onOptionSelect:function(t){var n,i,o=this;t.preventDefault&&(t.preventDefault(),t.stopPropagation()),(i=e(t.currentTarget)).hasClass("create")?o.createItem(null,function(){o.settings.closeAfterSelect&&o.close()}):void 0!==(n=i.attr("data-value"))&&(o.lastQuery=null,o.setTextboxValue(""),o.addItem(n),o.settings.closeAfterSelect?o.close():!o.settings.hideSelected&&t.type&&/mouse/.test(t.type)&&o.setActiveOption(o.getOption(n)))},onItemSelect:function(e){var t=this;t.isLocked||"multi"===t.settings.mode&&(e.preventDefault(),t.setActiveItem(e.currentTarget,e))},load:function(e){var t=this,n=t.$wrapper.addClass(t.settings.loadingClass);t.loading++,e.apply(t,[function(e){t.loading=Math.max(t.loading-1,0),e&&e.length&&(t.addOption(e),t.refreshOptions(t.isFocused&&!t.isInputHidden)),t.loading||n.removeClass(t.settings.loadingClass),t.trigger("load",e)}])},setTextboxValue:function(e){var t=this.$control_input;t.val()!==e&&(t.val(e).triggerHandler("update"),this.lastValue=e)},getValue:function(){return 1===this.tagType&&this.$input.attr("multiple")?this.items:this.items.join(this.settings.delimiter)},setValue:function(e,t){g(this,t?[]:["change"],function(){this.clear(t),this.addItems(e,t)})},setActiveItem:function(t,n){var i,o,s,r,a,l,c,u,d=this;if("single"!==d.settings.mode){if(!(t=e(t)).length)return e(d.$activeItems).removeClass("active"),d.$activeItems=[],void(d.isFocused&&d.showInput());if("mousedown"===(i=n&&n.type.toLowerCase())&&d.isShiftDown&&d.$activeItems.length){for(u=d.$control.children(".active:last"),(r=Array.prototype.indexOf.apply(d.$control[0].childNodes,[u[0]]))>(a=Array.prototype.indexOf.apply(d.$control[0].childNodes,[t[0]]))&&(c=r,r=a,a=c),o=r;o<=a;o++)l=d.$control[0].childNodes[o],-1===d.$activeItems.indexOf(l)&&(e(l).addClass("active"),d.$activeItems.push(l));n.preventDefault()}else"mousedown"===i&&d.isCtrlDown||"keydown"===i&&this.isShiftDown?t.hasClass("active")?(s=d.$activeItems.indexOf(t[0]),d.$activeItems.splice(s,1),t.removeClass("active")):d.$activeItems.push(t.addClass("active")[0]):(e(d.$activeItems).removeClass("active"),d.$activeItems=[t.addClass("active")[0]]);d.hideInput(),this.isFocused||d.focus()}},setActiveOption:function(t,n,i){var o,s,r,a,l,u=this;u.$activeOption&&u.$activeOption.removeClass("active"),u.$activeOption=null,(t=e(t)).length&&(u.$activeOption=t.addClass("active"),!n&&c(n)||(o=u.$dropdown_content.height(),s=u.$activeOption.outerHeight(!0),n=u.$dropdown_content.scrollTop()||0,a=r=u.$activeOption.offset().top-u.$dropdown_content.offset().top+n,l=r-o+s,r+s>o+n?u.$dropdown_content.stop().animate({scrollTop:l},i?u.settings.scrollDuration:0):r<n&&u.$dropdown_content.stop().animate({scrollTop:a},i?u.settings.scrollDuration:0)))},selectAll:function(){var e=this;"single"!==e.settings.mode&&(e.$activeItems=Array.prototype.slice.apply(e.$control.children(":not(input)").addClass("active")),e.$activeItems.length&&(e.hideInput(),e.close()),e.focus())},hideInput:function(){var e=this;e.setTextboxValue(""),e.$control_input.css({opacity:0,position:"absolute",left:e.rtl?1e4:-1e4}),e.isInputHidden=!0},showInput:function(){this.$control_input.css({opacity:1,position:"relative",left:0}),this.isInputHidden=!1},focus:function(){var e=this;e.isDisabled||(e.ignoreFocus=!0,e.$control_input[0].focus(),window.setTimeout(function(){e.ignoreFocus=!1,e.onFocus()},0))},blur:function(e){this.$control_input[0].blur(),this.onBlur(null,e)},getScoreFunction:function(e){return this.sifter.getScoreFunction(e,this.getSearchOptions())},getSearchOptions:function(){var e=this.settings,t=e.sortField;return"string"==typeof t&&(t=[{field:t}]),{fields:e.searchField,conjunction:e.searchConjunction,sort:t,nesting:e.nesting}},search:function(t){var n,i,o,s=this,r=s.settings,a=this.getSearchOptions();if(r.score&&"function"!=typeof(o=s.settings.score.apply(this,[t])))throw new Error('Selectize "score" setting must be a function that returns a function');if(t!==s.lastQuery?(s.lastQuery=t,i=s.sifter.search(t,e.extend(a,{score:o})),s.currentResults=i):i=e.extend(!0,{},s.currentResults),r.hideSelected)for(n=i.items.length-1;n>=0;n--)-1!==s.items.indexOf(u(i.items[n].id))&&i.items.splice(n,1);return i},refreshOptions:function(t){var n,o,s,r,a,l,c,d,p,h,g,f,v,m,w,$;void 0===t&&(t=!0);var b=this,C=e.trim(b.$control_input.val()),O=b.search(C),x=b.$dropdown_content,I=b.$activeOption&&u(b.$activeOption.attr("data-value"));for(r=O.items.length,"number"==typeof b.settings.maxOptions&&(r=Math.min(r,b.settings.maxOptions)),a={},l=[],n=0;n<r;n++)for(c=b.options[O.items[n].id],d=b.render("option",c),p=c[b.settings.optgroupField]||"",o=0,s=(h=e.isArray(p)?p:[p])&&h.length;o<s;o++)p=h[o],b.optgroups.hasOwnProperty(p)||(p=""),a.hasOwnProperty(p)||(a[p]=document.createDocumentFragment(),l.push(p)),a[p].appendChild(d);for(this.settings.lockOptgroupOrder&&l.sort(function(e,t){return(b.optgroups[e].$order||0)-(b.optgroups[t].$order||0)}),g=document.createDocumentFragment(),n=0,r=l.length;n<r;n++)p=l[n],b.optgroups.hasOwnProperty(p)&&a[p].childNodes.length?((f=document.createDocumentFragment()).appendChild(b.render("optgroup_header",b.optgroups[p])),f.appendChild(a[p]),g.appendChild(b.render("optgroup",e.extend({},b.optgroups[p],{html:y(f),dom:f})))):g.appendChild(a[p]);if(x.html(g),b.settings.highlight&&(x.removeHighlight(),O.query.length&&O.tokens.length))for(n=0,r=O.tokens.length;n<r;n++)i(x,O.tokens[n].regex);if(!b.settings.hideSelected)for(n=0,r=b.items.length;n<r;n++)b.getOption(b.items[n]).addClass("selected");(v=b.canCreate(C))&&(x.prepend(b.render("option_create",{input:C})),$=e(x[0].childNodes[0])),b.hasOptions=O.items.length>0||v,b.hasOptions?(O.items.length>0?((w=I&&b.getOption(I))&&w.length?m=w:"single"===b.settings.mode&&b.items.length&&(m=b.getOption(b.items[0])),m&&m.length||(m=$&&!b.settings.addPrecedence?b.getAdjacentOption($,1):x.find("[data-selectable]:first"))):m=$,b.setActiveOption(m),t&&!b.isOpen&&b.open()):(b.setActiveOption(null),t&&b.isOpen&&b.close())},addOption:function(t){var n,i,o,s=this;if(e.isArray(t))for(n=0,i=t.length;n<i;n++)s.addOption(t[n]);else(o=s.registerOption(t))&&(s.userOptions[o]=!0,s.lastQuery=null,s.trigger("option_add",o,t))},registerOption:function(e){var t=u(e[this.settings.valueField]);return null!=t&&!this.options.hasOwnProperty(t)&&(e.$order=e.$order||++this.order,this.options[t]=e,t)},registerOptionGroup:function(e){var t=u(e[this.settings.optgroupValueField]);return!!t&&(e.$order=e.$order||++this.order,this.optgroups[t]=e,t)},addOptionGroup:function(e,t){t[this.settings.optgroupValueField]=e,(e=this.registerOptionGroup(t))&&this.trigger("optgroup_add",e,t)},removeOptionGroup:function(e){this.optgroups.hasOwnProperty(e)&&(delete this.optgroups[e],this.renderCache={},this.trigger("optgroup_remove",e))},clearOptionGroups:function(){this.optgroups={},this.renderCache={},this.trigger("optgroup_clear")},updateOption:function(t,n){var i,o,s,r,a,l,c,d=this;if(t=u(t),s=u(n[d.settings.valueField]),null!==t&&d.options.hasOwnProperty(t)){if("string"!=typeof s)throw new Error("Value must be set in option data");c=d.options[t].$order,s!==t&&(delete d.options[t],-1!==(r=d.items.indexOf(t))&&d.items.splice(r,1,s)),n.$order=n.$order||c,d.options[s]=n,a=d.renderCache.item,l=d.renderCache.option,a&&(delete a[t],delete a[s]),l&&(delete l[t],delete l[s]),-1!==d.items.indexOf(s)&&(i=d.getItem(t),o=e(d.render("item",n)),i.hasClass("active")&&o.addClass("active"),i.replaceWith(o)),d.lastQuery=null,d.isOpen&&d.refreshOptions(!1)}},removeOption:function(e,t){var n=this;e=u(e);var i=n.renderCache.item,o=n.renderCache.option;i&&delete i[e],o&&delete o[e],delete n.userOptions[e],delete n.options[e],n.lastQuery=null,n.trigger("option_remove",e),n.removeItem(e,t)},clearOptions:function(){var t=this;t.loadedSearches={},t.userOptions={},t.renderCache={};var n=t.options;e.each(t.options,function(e,i){-1==t.items.indexOf(e)&&delete n[e]}),t.options=t.sifter.items=n,t.lastQuery=null,t.trigger("option_clear")},getOption:function(e){return this.getElementWithValue(e,this.$dropdown_content.find("[data-selectable]"))},getAdjacentOption:function(t,n){var i=this.$dropdown.find("[data-selectable]"),o=i.index(t)+n;return o>=0&&o<i.length?i.eq(o):e()},getElementWithValue:function(t,n){if(void 0!==(t=u(t))&&null!==t)for(var i=0,o=n.length;i<o;i++)if(n[i].getAttribute("data-value")===t)return e(n[i]);return e()},getItem:function(e){return this.getElementWithValue(e,this.$control.children())},addItems:function(t,n){this.buffer=document.createDocumentFragment();for(var i=this.$control[0].childNodes,o=0;o<i.length;o++)this.buffer.appendChild(i[o]);for(var s=e.isArray(t)?t:[t],r=(o=0,s.length);o<r;o++)this.isPending=o<r-1,this.addItem(s[o],n);var a=this.$control[0];a.insertBefore(this.buffer,a.firstChild),this.buffer=null},addItem:function(t,n){g(this,n?[]:["change"],function(){var i,o,s,r,a,l=this,c=l.settings.mode;t=u(t),-1===l.items.indexOf(t)?l.options.hasOwnProperty(t)&&("single"===c&&l.clear(n),"multi"===c&&l.isFull()||(i=e(l.render("item",l.options[t])),a=l.isFull(),l.items.splice(l.caretPos,0,t),l.insertAtCaret(i),(!l.isPending||!a&&l.isFull())&&l.refreshState(),l.isSetup&&(s=l.$dropdown_content.find("[data-selectable]"),l.isPending||(o=l.getOption(t),r=l.getAdjacentOption(o,1).attr("data-value"),l.refreshOptions(l.isFocused&&"single"!==c),r&&l.setActiveOption(l.getOption(r))),!s.length||l.isFull()?l.close():l.isPending||l.positionDropdown(),l.updatePlaceholder(),l.trigger("item_add",t,i),l.isPending||l.updateOriginalInput({silent:n})))):"single"===c&&l.close()})},removeItem:function(t,n){var i,o,s,r=this;i=t instanceof e?t:r.getItem(t),t=u(i.attr("data-value")),-1!==(o=r.items.indexOf(t))&&(i.remove(),i.hasClass("active")&&(s=r.$activeItems.indexOf(i[0]),r.$activeItems.splice(s,1)),r.items.splice(o,1),r.lastQuery=null,!r.settings.persist&&r.userOptions.hasOwnProperty(t)&&r.removeOption(t,n),o<r.caretPos&&r.setCaret(r.caretPos-1),r.refreshState(),r.updatePlaceholder(),r.updateOriginalInput({silent:n}),r.positionDropdown(),r.trigger("item_remove",t,i))},createItem:function(t,n){var i=this,o=i.caretPos;t=t||e.trim(i.$control_input.val()||"");var s=arguments[arguments.length-1];if("function"!=typeof s&&(s=function(){}),"boolean"!=typeof n&&(n=!0),!i.canCreate(t))return s(),!1;i.lock();var r="function"==typeof i.settings.create?this.settings.create:function(e){var t={};return t[i.settings.labelField]=e,t[i.settings.valueField]=e,t},a=function(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}(function(e){if(i.unlock(),!e||"object"!=typeof e)return s();var t=u(e[i.settings.valueField]);if("string"!=typeof t)return s();i.setTextboxValue(""),i.addOption(e),i.setCaret(o),i.addItem(t),i.refreshOptions(n&&"single"!==i.settings.mode),s(e)}),l=r.apply(this,[t,a]);return void 0!==l&&a(l),!0},refreshItems:function(){this.lastQuery=null,this.isSetup&&this.addItem(this.items),this.refreshState(),this.updateOriginalInput()},refreshState:function(){this.refreshValidityState(),this.refreshClasses()},refreshValidityState:function(){if(!this.isRequired)return!1;var e=!this.items.length;this.isInvalid=e,this.$control_input.prop("required",e),this.$input.prop("required",!e)},refreshClasses:function(){var t=this,n=t.isFull(),i=t.isLocked;t.$wrapper.toggleClass("rtl",t.rtl),t.$control.toggleClass("focus",t.isFocused).toggleClass("disabled",t.isDisabled).toggleClass("required",t.isRequired).toggleClass("invalid",t.isInvalid).toggleClass("locked",i).toggleClass("full",n).toggleClass("not-full",!n).toggleClass("input-active",t.isFocused&&!t.isInputHidden).toggleClass("dropdown-active",t.isOpen).toggleClass("has-options",!e.isEmptyObject(t.options)).toggleClass("has-items",t.items.length>0),t.$control_input.data("grow",!n&&!i)},isFull:function(){return null!==this.settings.maxItems&&this.items.length>=this.settings.maxItems},updateOriginalInput:function(e){var t,n,i,o,s=this;if(e=e||{},1===s.tagType){for(i=[],t=0,n=s.items.length;t<n;t++)o=s.options[s.items[t]][s.settings.labelField]||"",i.push('<option value="'+d(s.items[t])+'" selected="selected">'+d(o)+"</option>");i.length||this.$input.attr("multiple")||i.push('<option value="" selected="selected"></option>'),s.$input.html(i.join(""))}else s.$input.val(s.getValue()),s.$input.attr("value",s.$input.val());s.isSetup&&(e.silent||s.trigger("change",s.$input.val()))},updatePlaceholder:function(){if(this.settings.placeholder){var e=this.$control_input;this.items.length?e.removeAttr("placeholder"):e.attr("placeholder",this.settings.placeholder),e.triggerHandler("update",{force:!0})}},open:function(){var e=this;e.isLocked||e.isOpen||"multi"===e.settings.mode&&e.isFull()||(e.focus(),e.isOpen=!0,e.refreshState(),e.$dropdown.css({visibility:"hidden",display:"block"}),e.positionDropdown(),e.$dropdown.css({visibility:"visible"}),e.trigger("dropdown_open",e.$dropdown))},close:function(){var e=this,t=e.isOpen;"single"===e.settings.mode&&e.items.length&&(e.hideInput(),e.isBlurring||e.$control_input.blur()),e.isOpen=!1,e.$dropdown.hide(),e.setActiveOption(null),e.refreshState(),t&&e.trigger("dropdown_close",e.$dropdown)},positionDropdown:function(){var e=this.$control,t="body"===this.settings.dropdownParent?e.offset():e.position();t.top+=e.outerHeight(!0),this.$dropdown.css({width:e[0].getBoundingClientRect().width,top:t.top,left:t.left})},clear:function(e){var t=this;t.items.length&&(t.$control.children(":not(input)").remove(),t.items=[],t.lastQuery=null,t.setCaret(0),t.setActiveItem(null),t.updatePlaceholder(),t.updateOriginalInput({silent:e}),t.refreshState(),t.showInput(),t.trigger("clear"))},insertAtCaret:function(e){var t=Math.min(this.caretPos,this.items.length),n=e[0],i=this.buffer||this.$control[0];0===t?i.insertBefore(n,i.firstChild):i.insertBefore(n,i.childNodes[t]),this.setCaret(t+1)},deleteSelection:function(t){var n,i,o,s,r,a,l,c,u,d=this;if(o=t&&8===t.keyCode?-1:1,s=f(d.$control_input[0]),d.$activeOption&&!d.settings.hideSelected&&(l=d.getAdjacentOption(d.$activeOption,-1).attr("data-value")),r=[],d.$activeItems.length){for(u=d.$control.children(".active:"+(o>0?"last":"first")),a=d.$control.children(":not(input)").index(u),o>0&&a++,n=0,i=d.$activeItems.length;n<i;n++)r.push(e(d.$activeItems[n]).attr("data-value"));t&&(t.preventDefault(),t.stopPropagation())}else(d.isFocused||"single"===d.settings.mode)&&d.items.length&&(o<0&&0===s.start&&0===s.length?r.push(d.items[d.caretPos-1]):o>0&&s.start===d.$control_input.val().length&&r.push(d.items[d.caretPos]));if(!r.length||"function"==typeof d.settings.onDelete&&!1===d.settings.onDelete.apply(d,[r]))return!1;for(void 0!==a&&d.setCaret(a);r.length;)d.removeItem(r.pop());return d.showInput(),d.positionDropdown(),d.refreshOptions(!0),l&&((c=d.getOption(l)).length&&d.setActiveOption(c)),!0},advanceSelection:function(e,t){var n,i,o,s,r,a=this;0!==e&&(a.rtl&&(e*=-1),n=e>0?"last":"first",i=f(a.$control_input[0]),a.isFocused&&!a.isInputHidden?(s=a.$control_input.val().length,(e<0?0===i.start&&0===i.length:i.start===s)&&!s&&a.advanceCaret(e,t)):(r=a.$control.children(".active:"+n)).length&&(o=a.$control.children(":not(input)").index(r),a.setActiveItem(null),a.setCaret(e>0?o+1:o)))},advanceCaret:function(e,t){var n,i,o=this;0!==e&&(n=e>0?"next":"prev",o.isShiftDown?(i=o.$control_input[n]()).length&&(o.hideInput(),o.setActiveItem(i),t&&t.preventDefault()):o.setCaret(o.caretPos+e))},setCaret:function(t){var n,i,o,s,r=this;if(t="single"===r.settings.mode?r.items.length:Math.max(0,Math.min(r.items.length,t)),!r.isPending)for(n=0,i=(o=r.$control.children(":not(input)")).length;n<i;n++)s=e(o[n]).detach(),n<t?r.$control_input.before(s):r.$control.append(s);r.caretPos=t},lock:function(){this.close(),this.isLocked=!0,this.refreshState()},unlock:function(){this.isLocked=!1,this.refreshState()},disable:function(){var e=this;e.$input.prop("disabled",!0),e.$control_input.prop("disabled",!0).prop("tabindex",-1),e.isDisabled=!0,e.lock()},enable:function(){var e=this;e.$input.prop("disabled",!1),e.$control_input.prop("disabled",!1).prop("tabindex",e.tabIndex),e.isDisabled=!1,e.unlock()},destroy:function(){var t=this,n=t.eventNS,i=t.revertSettings;t.trigger("destroy"),t.off(),t.$wrapper.remove(),t.$dropdown.remove(),t.$input.html("").append(i.$children).removeAttr("tabindex").removeClass("selectized").attr({tabindex:i.tabindex}).show(),t.$control_input.removeData("grow"),t.$input.removeData("selectize"),0==--w.count&&w.$testInput&&(w.$testInput.remove(),w.$testInput=void 0),e(window).off(n),e(document).off(n),e(document.body).off(n),delete t.$input[0].selectize},render:function(t,n){var i,o,s="",r=!1,a=this;return"option"!==t&&"item"!==t||(r=!!(i=u(n[a.settings.valueField]))),r&&(c(a.renderCache[t])||(a.renderCache[t]={}),a.renderCache[t].hasOwnProperty(i))?a.renderCache[t][i]:(s=e(a.settings.render[t].apply(this,[n,d])),"option"===t||"option_create"===t?n[a.settings.disabledField]||s.attr("data-selectable",""):"optgroup"===t&&(o=n[a.settings.optgroupValueField]||"",s.attr("data-group",o),n[a.settings.disabledField]&&s.attr("data-disabled","")),"option"!==t&&"item"!==t||s.attr("data-value",i||""),r&&(a.renderCache[t][i]=s[0]),s[0])},clearCache:function(e){void 0===e?this.renderCache={}:delete this.renderCache[e]},canCreate:function(e){var t=this;if(!t.settings.create)return!1;var n=t.settings.createFilter;return e.length&&("function"!=typeof n||n.apply(t,[e]))&&("string"!=typeof n||new RegExp(n).test(e))&&(!(n instanceof RegExp)||n.test(e))}}),w.count=0,w.defaults={options:[],optgroups:[],plugins:[],delimiter:",",splitOn:null,persist:!0,diacritics:!0,create:!1,createOnBlur:!1,createFilter:null,highlight:!0,openOnFocus:!0,maxOptions:1e3,maxItems:null,hideSelected:null,addPrecedence:!1,selectOnTab:!1,preload:!1,allowEmptyOption:!1,closeAfterSelect:!1,scrollDuration:60,loadThrottle:300,loadingClass:"loading",dataAttr:"data-data",optgroupField:"optgroup",valueField:"value",labelField:"text",disabledField:"disabled",optgroupLabelField:"label",optgroupValueField:"value",lockOptgroupOrder:!1,sortField:"$order",searchField:["text"],searchConjunction:"and",mode:null,wrapperClass:"selectize-control",inputClass:"selectize-input",dropdownClass:"selectize-dropdown",dropdownContentClass:"selectize-dropdown-content",dropdownParent:null,copyClassesToDropdown:!0,render:{}},e.fn.selectize=function(t){var n=e.fn.selectize.defaults,i=e.extend({},n,t),o=i.dataAttr,s=i.labelField,r=i.valueField,a=i.disabledField,l=i.optgroupField,c=i.optgroupLabelField,d=i.optgroupValueField,p=function(t,n){var a,l,c,u,d=t.attr(o);if(d)for(n.options=JSON.parse(d),a=0,l=n.options.length;a<l;a++)n.items.push(n.options[a][r]);else{var p=e.trim(t.val()||"");if(!i.allowEmptyOption&&!p.length)return;for(a=0,l=(c=p.split(i.delimiter)).length;a<l;a++)(u={})[s]=c[a],u[r]=c[a],n.options.push(u);n.items=c}},h=function(t,n){var p,h,g,f,v=n.options,m={},y=function(e){var t=o&&e.attr(o);return"string"==typeof t&&t.length?JSON.parse(t):null},w=function(t,o){t=e(t);var c=u(t.val());if(c||i.allowEmptyOption)if(m.hasOwnProperty(c)){if(o){var d=m[c][l];d?e.isArray(d)?d.push(o):m[c][l]=[d,o]:m[c][l]=o}}else{var p=y(t)||{};p[s]=p[s]||t.text(),p[r]=p[r]||c,p[a]=p[a]||t.prop("disabled"),p[l]=p[l]||o,m[c]=p,v.push(p),t.is(":selected")&&n.items.push(c)}};for(n.maxItems=t.attr("multiple")?null:1,p=0,h=(f=t.children()).length;p<h;p++)"optgroup"===(g=f[p].tagName.toLowerCase())?function(t){var i,o,s,r,l;for((s=(t=e(t)).attr("label"))&&((r=y(t)||{})[c]=s,r[d]=s,r[a]=t.prop("disabled"),n.optgroups.push(r)),i=0,o=(l=e("option",t)).length;i<o;i++)w(l[i],s)}(f[p]):"option"===g&&w(f[p])};return this.each(function(){if(!this.selectize){var o=e(this),s=this.tagName.toLowerCase(),r=o.attr("placeholder")||o.attr("data-placeholder");r||i.allowEmptyOption||(r=o.children('option[value=""]').text());var a={placeholder:r,options:[],optgroups:[],items:[]};"select"===s?h(o,a):p(o,a),new w(o,e.extend(!0,{},n,a,t))}})},e.fn.selectize.defaults=w.defaults,e.fn.selectize.support={validity:l},w.define("drag_drop",function(t){if(!e.fn.sortable)throw new Error('The "drag_drop" plugin requires jQuery UI "sortable".');if("multi"===this.settings.mode){var n=this;n.lock=function(){var e=n.lock;return function(){var t=n.$control.data("sortable");return t&&t.disable(),e.apply(n,arguments)}}(),n.unlock=function(){var e=n.unlock;return function(){var t=n.$control.data("sortable");return t&&t.enable(),e.apply(n,arguments)}}(),n.setup=function(){var t=n.setup;return function(){t.apply(this,arguments);var i=n.$control.sortable({items:"[data-value]",forcePlaceholderSize:!0,disabled:n.isLocked,start:function(e,t){t.placeholder.css("width",t.helper.css("width")),i.css({overflow:"visible"})},stop:function(){i.css({overflow:"hidden"});var t=n.$activeItems?n.$activeItems.slice():null,o=[];i.children("[data-value]").each(function(){o.push(e(this).attr("data-value"))}),n.setValue(o),n.setActiveItem(t)}})}}()}}),w.define("dropdown_header",function(t){var n,i=this;t=e.extend({title:"Untitled",headerClass:"selectize-dropdown-header",titleRowClass:"selectize-dropdown-header-title",labelClass:"selectize-dropdown-header-label",closeClass:"selectize-dropdown-header-close",html:function(e){return'<div class="'+e.headerClass+'"><div class="'+e.titleRowClass+'"><span class="'+e.labelClass+'">'+e.title+'</span><a href="javascript:void(0)" class="'+e.closeClass+'">&times;</a></div></div>'}},t),i.setup=(n=i.setup,function(){n.apply(i,arguments),i.$dropdown_header=e(t.html(t)),i.$dropdown.prepend(i.$dropdown_header)})}),w.define("optgroup_columns",function(t){var n=this;t=e.extend({equalizeWidth:!0,equalizeHeight:!0},t),this.getAdjacentOption=function(t,n){var i=t.closest("[data-group]").find("[data-selectable]"),o=i.index(t)+n;return o>=0&&o<i.length?i.eq(o):e()},this.onKeyDown=function(){var e=n.onKeyDown;return function(t){var i,o,s,r;return!this.isOpen||37!==t.keyCode&&39!==t.keyCode?e.apply(this,arguments):(n.ignoreHover=!0,i=(r=this.$activeOption.closest("[data-group]")).find("[data-selectable]").index(this.$activeOption),void((o=(s=(r=37===t.keyCode?r.prev("[data-group]"):r.next("[data-group]")).find("[data-selectable]")).eq(Math.min(s.length-1,i))).length&&this.setActiveOption(o)))}}();var i=function(){var e,t=i.width,n=document;return void 0===t&&((e=n.createElement("div")).innerHTML='<div style="width:50px;height:50px;position:absolute;left:-50px;top:-50px;overflow:auto;"><div style="width:1px;height:100px;"></div></div>',e=e.firstChild,n.body.appendChild(e),t=i.width=e.offsetWidth-e.clientWidth,n.body.removeChild(e)),t},o=function(){var o,s,r,a,l,c,u;if((s=(u=e("[data-group]",n.$dropdown_content)).length)&&n.$dropdown_content.width()){if(t.equalizeHeight){for(r=0,o=0;o<s;o++)r=Math.max(r,u.eq(o).height());u.css({height:r})}t.equalizeWidth&&(c=n.$dropdown_content.innerWidth()-i(),a=Math.round(c/s),u.css({width:a}),s>1&&(l=c-a*(s-1),u.eq(s-1).css({width:l})))}};(t.equalizeHeight||t.equalizeWidth)&&(p.after(this,"positionDropdown",o),p.after(this,"refreshOptions",o))}),w.define("remove_button",function(t){t=e.extend({label:"&times;",title:"Remove",className:"remove",append:!0},t),"single"!==this.settings.mode?function(t,n){var i,o=t,s='<a href="javascript:void(0)" class="'+n.className+'" tabindex="-1" title="'+d(n.title)+'">'+n.label+"</a>";t.setup=(i=o.setup,function(){if(n.append){var r=o.settings.render.item;o.settings.render.item=function(e){return function(e,t){var n=e.search(/(<\/[^>]+>\s*)$/);return e.substring(0,n)+t+e.substring(n)}(r.apply(t,arguments),s)}}i.apply(t,arguments),t.$control.on("click","."+n.className,function(t){if(t.preventDefault(),!o.isLocked){var n=e(t.currentTarget).parent();o.setActiveItem(n),o.deleteSelection()&&o.setCaret(o.items.length)}})})}(this,t):function(t,n){n.className="remove-single";var i,o=t,s='<a href="javascript:void(0)" class="'+n.className+'" tabindex="-1" title="'+d(n.title)+'">'+n.label+"</a>",r=function(t,n){return e("<span>").append(t).append(n)};t.setup=(i=o.setup,function(){if(n.append){var a=e(o.$input.context).attr("id"),l=(e("#"+a),o.settings.render.item);o.settings.render.item=function(e){return r(l.apply(t,arguments),s)}}i.apply(t,arguments),t.$control.on("click","."+n.className,function(e){e.preventDefault(),o.isLocked||o.clear()})})}(this,t)}),w.define("restore_on_backspace",function(e){var t,n=this;e.text=e.text||function(e){return e[this.settings.labelField]},this.onKeyDown=(t=n.onKeyDown,function(n){var i,o;return 8===n.keyCode&&""===this.$control_input.val()&&!this.$activeItems.length&&(i=this.caretPos-1)>=0&&i<this.items.length?(o=this.options[this.items[i]],this.deleteSelection(n)&&(this.setTextboxValue(e.text.apply(this,[o])),this.refreshOptions(!0)),void n.preventDefault()):t.apply(this,arguments)})}),w});

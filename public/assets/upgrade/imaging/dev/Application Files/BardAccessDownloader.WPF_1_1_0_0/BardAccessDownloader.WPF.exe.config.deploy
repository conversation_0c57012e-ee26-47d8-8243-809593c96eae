<?xml version="1.0"?>
<configuration>
  <configSections>
    <section name="loggingConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.LoggingSettings, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="true" />
    <section name="unity" type="Microsoft.Practices.Unity.Configuration.UnityConfigurationSection, Microsoft.Practices.Unity.Configuration"/>
  </configSections>
  <loggingConfiguration name="" tracingEnabled="false" defaultCategory="General">
    <listeners>
      <add name="Event Log Listener" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FormattedEventLogTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
        listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FormattedEventLogTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
        source="Enterprise Library Logging" formatter="Text Formatter"
        log="" machineName="." traceOutputOptions="None" />
      <add name="Flat File Trace Listener" type="Microsoft.Practices.EnterpriseLibrary.Logging.TraceListeners.FlatFileTraceListener, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
        listenerDataType="Microsoft.Practices.EnterpriseLibrary.Logging.Configuration.FlatFileTraceListenerData, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
        fileName="%appdata%\BardAccessDownloader\trace.log" formatter="Text Formatter"
        traceOutputOptions="DateTime" />
    </listeners>
    <formatters>
      <add type="Microsoft.Practices.EnterpriseLibrary.Logging.Formatters.TextFormatter, Microsoft.Practices.EnterpriseLibrary.Logging, Version=5.0.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"
        template="{title}{newline}&#xA;Timestamp: {timestamp}{newline}&#xA;Message: {message}{newline}&#xA;Extended Properties: {dictionary({key} - {value}{newline})}"
        name="Text Formatter" />
    </formatters>
    <categorySources>
      <add switchValue="Off" name="General">
        <listeners>
          <add name="Flat File Trace Listener" />
        </listeners>
      </add>
      <add switchValue="Off" name="IO">
        <listeners>
          <add name="Flat File Trace Listener" />
        </listeners>
      </add>
      <add switchValue="All" name="Error">
        <listeners>
          <add name="Flat File Trace Listener" />
        </listeners>
      </add>
    </categorySources>
    <specialSources>
      <allEvents switchValue="Off" name="All Events" />
      <notProcessed switchValue="All" name="Unprocessed Category" />
      <errors switchValue="All" name="Logging Errors &amp; Warnings">
        <listeners>
          <add name="Event Log Listener" />
        </listeners>
      </errors>
    </specialSources>
  </loggingConfiguration>
  <connectionStrings>
    <add name="BardModelDataContainer" connectionString="metadata=res://*/BardAccessDownloaderEntities.csdl|res://*/BardAccessDownloaderEntities.ssdl|res://*/BardAccessDownloaderEntities.msl;provider=System.Data.SqlServerCe.4.0;provider connection string=&quot;Data Source=|DataDirectory|\BardAccessDownloader.sdf&quot;"
      providerName="System.Data.EntityClient" />
  </connectionStrings>
  <unity xmlns="http://schemas.microsoft.com/practices/2010/unity">
    <!-- Service Resolver Types -->
    <alias alias="IResumeableDownloadManager" type="BardAccessDownloader.Core.IResumeableDownloadManager, BardAccessDownloader.Core"/>
    <alias alias="ResumeableDownloadManager" type="BardAccessDownloader.Core.ResumeableDownloadManager, BardAccessDownloader.Core"/>
    <alias alias="ISerialNumberXMLWriter" type="BardAccessDownloader.Core.ISerialNumberXMLWriter, BardAccessDownloader.Core"/>
    <alias alias="SerialNumberXMLWriter" type="BardAccessDownloader.Core.SerialNumberXMLWriter, BardAccessDownloader.Core"/>
    <alias alias="IUSBDriveMonitor" type="BardAccessDownloader.Core.IUSBDriveMonitor, BardAccessDownloader.Core"/>
    <alias alias="USBDriveMonitor" type="BardAccessDownloader.Core.USBDriveMonitor, BardAccessDownloader.Core"/>
    <alias alias="IZipManager" type="BardAccessDownloader.Core.IZipManager, BardAccessDownloader.Core"/>
    <alias alias="ZipManager" type="BardAccessDownloader.Core.ZipManager, BardAccessDownloader.Core"/>
    <alias alias="IPhpApi" type="BardAccessDownloader.Core.IPhpApi, BardAccessDownloader.Core"/>
    <alias alias="PhpApi" type="BardAccessDownloader.Core.PhpApi, BardAccessDownloader.Core"/>
    <alias alias="BardDataEntities" type="BardAccessDownloader.Model.BardModelDataContainer, BardAccessDownloader.Model"/>
    <alias alias="DeviceManagerViewModel" type="BardAccessDownloader.UI.ViewModels.DeviceManagerViewModel, BardAccessDownloader.UI"/>
    <alias alias="MainViewModel" type="BardAccessDownloader.UI.ViewModels.MainViewModel, BardAccessDownloader.UI"/>
    <alias alias="ContactInformationViewModel" type="BardAccessDownloader.UI.ViewModels.ContactInformationViewModel, BardAccessDownloader.UI"/>
    <alias alias="DeviceUpdateViewModel" type="BardAccessDownloader.UI.ViewModels.DeviceUpdateViewModel, BardAccessDownloader.UI"/>

    <alias alias="IRepository" type="BardAccessDownloader.Model.IRepository, BardAccessDownloader.Model"/>
    <alias alias="Repository" type="BardAccessDownloader.Model.SDFRepository, BardAccessDownloader.Model"/>

    <alias alias="ILocalDataService" type="BardAccessDownloader.LocalService.ILocalDataService, BardAccessDownloader.LocalService"/>
    <alias alias="LocalDataService" type="BardAccessDownloader.LocalService.LocalDataService, BardAccessDownloader.LocalService"/>
    <alias alias="ILocalFileService" type="BardAccessDownloader.LocalService.ILocalFileService, BardAccessDownloader.LocalService"/>
    <alias alias="LocalFileService" type="BardAccessDownloader.LocalService.LocalFileService, BardAccessDownloader.LocalService"/>
    <alias alias="IUSBStickService" type="BardAccessDownloader.LocalService.IUSBStickService, BardAccessDownloader.LocalService"/>
    <alias alias="USBStickService" type="BardAccessDownloader.LocalService.USBStickService, BardAccessDownloader.LocalService"/>
    <alias alias="IPage" type="BardAccessDownloader.UI.IPage, BardAccessDownloader.UI"/>
    <!-- Default Container -->
    <container>
      <register type="BardDataEntities">
        <constructor></constructor>
      </register>
      <register type="IResumeableDownloadManager" mapTo="ResumeableDownloadManager">
      </register>
      <register type="IUSBDriveMonitor" mapTo="USBDriveMonitor">
      </register>
      <register type="IRepository" mapTo="Repository">
      </register>
      <register type="ILocalDataService" mapTo="LocalDataService"/>
      <register type="ILocalFileService" mapTo="LocalFileService"/>
      <register type="IUSBStickService" mapTo="USBStickService"></register>
      <register type="IPhpApi" mapTo="PhpApi"></register>
      <register type="IZipManager" mapTo="ZipManager"></register>
      <register type="ISerialNumberXMLWriter" mapTo="SerialNumberXMLWriter"></register>
      <register type="DeviceManagerViewModel">
        <lifetime type="singleton"/>
      </register>
      <register type="MainViewModel">
        <lifetime type="singleton"/>
      </register>
      <register type="ContactInformationViewModel">
        <lifetime type="singleton"/>
      </register>
      <register type="DeviceUpdateViewModel">
        <lifetime type="singleton"/>
      </register>

      <!--Page instances-->
      <register name="Page01" type="IPage" mapTo="BardAccessDownloader.WPF.Pages.Page01_welcome, BardAccessDownloader.WPF">
        <lifetime type="singleton"/>
      </register>
      <register name="Page02" type="IPage" mapTo="BardAccessDownloader.WPF.Pages.Page02_contact, BardAccessDownloader.WPF">
        <lifetime type="singleton"/>
      </register>
      <register name="Page03" type="IPage" mapTo="BardAccessDownloader.WPF.Pages.Page03_devicemanager, BardAccessDownloader.WPF">
        <lifetime type="singleton"/>
      </register>
      <register name="Page03a" type="IPage" mapTo="BardAccessDownloader.WPF.Pages.Page03a_deviceedit, BardAccessDownloader.WPF">
        <lifetime type="singleton"/>
      </register>
      <register name="Page04" type="IPage" mapTo="BardAccessDownloader.WPF.Pages.Page04_deviceupdater, BardAccessDownloader.WPF">
        <lifetime type="singleton"/>
      </register>
      <!--End Page Instances-->
      
    </container>
  </unity>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0,Profile=Client"/></startup></configuration>

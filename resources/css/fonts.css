@charset "utf-8";
/* FONTS CSS */

@font-face {
  font-family: 'Open Sans Light';
  src: url('../fonts/OpenSans/OpenSans-Light-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Light-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Light-webfont.ttf') format('truetype'), url('../fonts/OpenSans/OpenSans-Light-webfont.svg#open_sanslight') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans Light';
  src: url('../fonts/OpenSans/OpenSans-Semibold-webfont.eot');
  src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url('../fonts/OpenSans/OpenSans-Semibold-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Semibold-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Semibold-webfont.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans Light';
  src: url('../fonts/OpenSans/OpenSans-Light-Italic-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Light-Italic-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Light-Italic-webfont.ttf') format('truetype'), url('../fonts/OpenSans/OpenSans-Light-Italic-webfont.svg#open_sanslight') format('svg');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Open Sans';
  src: url('../fonts/OpenSans/OpenSans-Regular-webfont.eot');
  src: local('Open Sans'), local('OpenSans'), url('../fonts/OpenSans/OpenSans-Regular-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Regular-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Regular-webfont.ttf') format('truetype'), url('../fonts/OpenSans/OpenSans-Regular-webfont.svg#open_sansregular') format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans Bold';
  src: url('../fonts/OpenSans/OpenSans-Bold-webfont.eot');
  src: local('Open Sans Bold'), local('OpenSans-Bold'), url('../fonts/OpenSans/OpenSans-Bold-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Bold-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Bold-webfont.ttf') format('truetype');
  font-style: normal;
}

@font-face {
  font-family: 'Open Sans ExtraBold';
  src: url('../fonts/OpenSans/OpenSans-ExtraBold-webfont.eot');
  src: local('Open Sans ExtraBold'), local('OpenSans-ExtraBold'), url('../fonts/OpenSans/OpenSans-ExtraBold-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-ExtraBold-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-ExtraBold-webfont.ttf') format('truetype');
  font-style: normal;
}
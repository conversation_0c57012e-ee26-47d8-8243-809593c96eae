@charset "utf-8";

/* CORE CSS */
/*Basic Formatting Phase 2*/
.phase2 h3 {
    margin: 5px 0 20px 0;
    border-bottom: 1px solid #783980;
    font-weight: bold;
}

.phase2.pageLogo {
    margin: 20px 5px 10px 5px;
}

/*Basic Formatting*/
/* Text */
h1 {
    font-weight: bold;
    margin: 0 0 10px 0;
    font-size: 17px;
    color: #005221;
    text-transform: uppercase
}

h2 {
    margin: 0 0 40px 0;
    font-size: 20px;
    line-height: 20px;
    font-weight: normal;
    color: #222;
    text-transform: uppercase;
    position: relative;
}

h3 {
    margin: 5px 0 20px 0;
    padding: 0;
    font-size: 22px;
    line-height: 20px;
    font-weight: normal;
    color: #783980;
    border-bottom: medium none;
    text-transform: uppercase;
}

h4 {
    color: rgba(0, 0, 0, 0.55);
    font-size: 20px;
    margin-top: 35px;
    margin-bottom: 5px;
}

h5 {
    color: #000;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}

h6 {
    color: #374f5e;
    font-size: 18px;
    margin-top: 35px;
    margin-bottom: 5px;
}

img {
    display: block;
    height: auto;
    max-width: 100%;
}

.subtitles {
    color: #BF362F;
    font-weight: bold;
    font-size: 14px
}

.left {
    text-align: left;
}

.right {
    text-align: right;
}

.center {
    text-align: center;
    margin: 0 auto;
}

.extrabold {
    font-weight: bolder;
    font-family: 'Open Sans ExtraBold';
}

.bold {
    font-weight: bold;
}

.light {
    font-weight: lighter;
}

.italic {
    font-style: italic
}

.footnote {
    font-style: italic;
    font-size: 11px;
    margin-top: 30px;
    line-height: 16px;
}

.disclaimer {
    margin-top: 30px;
    font-style: italic;
}

.text-reference {
    font-size: 13px;
    line-height: 23px;
    color: #555
}

.directions {
    color: rgba(0, 0, 0, 0.55);
}

.headline {
    font-size: 18px;
    line-height: 24px;
}

.smallcaps {
    font-variant: small-caps;
}

.p-test {
    font-size: 14px;
    line-height: 20px;
}

.nowrap {
    white-space: nowrap;
}

.unbold {
    font-weight: normal
}

/* This is used to rotate text vertically*/
.rotate {
    /* Safari */
    -webkit-transform: rotate(90deg);
    /* Firefox */
    -moz-transform: rotate(90deg);
    /* IE */
    -ms-transform: rotate(90deg);
    /* Opera */
    -o-transform: rotate(90deg);
    /* Internet Explorer */
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
}

ol.numeral li {
    list-style: decimal
}

ol.alpha li {
    list-style: upper-alpha
}

ul.nobullet {
    list-style: none;
    padding-left: 0;
}

ul lo.nobullet {
    list-style: none;
    padding-left: 0;
}

ul.pdf {
    padding: 5px 8px 20px 40px;
    margin: 0;
    list-style: url(../img/blts/pdficon_small.gif);
}

ul li {
    margin-bottom: 10px;
}

.indent {
    margin-left: 30px
}

.underline {
    text-decoration: underline;
}

.no_underline {
    text-decoration: none;
    border: none;
}

.uppercase {
    text-transform: uppercase;
}

.lowercase {
    text-transform: lowercase;
}

.img_link {
    border: none;
}

.open-tab {
    cursor: pointer;
}

.line {
    background: url(../img/bkgrds/3px-line.png) no-repeat center right;
}

.border-right {
    background: url(../img/bkgrds/col-separator.jpg) no-repeat center right;
    width: 14px;
    height: 124px;
}

.border-right-300 {
    background: url(../img/bkgrds/col-separator-300.png) no-repeat center right;
    width: 24px;
    height: 300px;
}

.font10 {
    font-size: 10px;
}

.font12 {
    font-size: 12px;
}

.font14 {
    font-size: 14px;
    line-height: 26px;
}

.font15 {
    font-size: 15px;
    line-height: 16px;
}

.font16 {
    font-size: 16px;
}

.font20 {
    font-size: 20px;
}

.font30 {
    font-size: 30px;
}

.font40 {
    font-size: 40px;
}

.font45 {
    font-size: 45px;
}

.lh55 {
    line-height: 55px;
}

.lh40 {
    line-height: 40px;
}

.lh0 {
    line-height: 0px;
}

.lh30 {
    line-height: 30px;
}

.smallcaps {
    font-variant: small-caps;
}

ol.circles-list, ol.circles-list-blue, ol.circles-list-purple {
    list-style-type: none;
    list-style-type: decimal !ie;
    margin: 0;
    padding: 0;
    counter-reset: li-counter;
}

ol.circles-list, ol.circles-list-blue, ol.circles-list-purple {
    list-style-type: none;
    list-style-type: decimal !ie;
    margin: 20px 0 0 20px;
    padding: 0;
    counter-reset: li-counter;
}

ol.circles-list>li, ol.circles-list-blue>li, ol.circles-list-purple>li {
    position: relative;
    margin-bottom: 20px;
    padding-left: 2.5em;
    padding-right: 2em;
    min-height: 3em;
}

ol.circles-list>li:before {
    position: absolute;
    top: 0;
    left: -0.7em;
    width: 1.2em;
    height: 1.2em;
    font-size: 2em;
    line-height: 1.13;
    text-align: center;
    color: #f5f5f5;
    border-radius: 50%;
    background-color: #592B5F;
    content: counter(li-counter);
    counter-increment: li-counter;
}

ol.circles-list-blue.restart-numbering {
    counter-reset: li-counter 5;
}

ol.circles-list-blue>li:before {
    position: absolute;
    top: -0.25em;
    left: -0.4em;
    width: 1.3em;
    height: 1.3em;
    font-size: 1.75em;
    line-height: 1.25;
    text-align: center;
    color: #f5f5f5;
    border-radius: 50%;
    background-color: #4B91BC;
    content: counter(li-counter, decimal);
    counter-increment: li-counter;
}

ol.circles-list-blue>li span {
    font-size: 20px;
    font-weight: bold;
}

ol.circles-list-purple>li:before {
    position: absolute;
    top: 0;
    left: -1.7em;
    width: 1.2em;
    height: 1.2em;
    font-size: 2em;
    line-height: 1.13;
    text-align: center;
    color: #f5f5f5;
    border-radius: 50%;
    background-color: #7e53a3;
    content: counter(li-counter);
    counter-increment: li-counter;
}

.arrow-down {
    width: 0;
    height: 0;
    opacity: .5;
    border-left: 100px solid transparent;
    border-right: 100px solid transparent;
    border-top: 60px solid #fff;
}

/* Positioning */
.flt_l {
    float: left
}

.flt_r {
    float: right
}

.clear_l {
    clear: left
}

.clear_r {
    clear: right
}

.clear_columns {
    display: block;
    clear: both;
    float: none;
    padding: 0;
    margin: 0;
    height: 0;
    line-height: 0;
    font-size: 0;
}

img {
    vertical-align: top
}

/*Heights*/
.h400 {
    min-height: 400px;
}

.h500 {
    min-height: 500px;
}

.h570 {
    min-height: 570px;
}

.h680 {
    min-height: 680px;
}

/*Widths*/
.width95 {
    margin-right: 5%;
}

.width90 {
    margin-right: 10%;
}

.width85 {
    margin-right: 15%;
}

.width75 {
    margin-right: 25%;
}

.width50 {
    margin-right: 50%;
}

.w33 {
    max-width: 33%;
    box-sizing: border-box
}

.w50 {
    max-width: 50%;
    box-sizing: border-box
}

.w66 {
    max-width: 66%;
    box-sizing: border-box
}

.w70 {
    max-width: 70%;
    box-sizing: border-box
}

.w46 {
    max-width: 46%;
    box-sizing: border-box
}

.w600 {
    max-width: 600px;
}

.w500 {
    max-width: 500px;
}

.w400 {
    max-width: 400px;
}

.w320 {
    max-width: 320px;
}

.w290 {
    max-width: 290px;
}

/*Margins*/
.tm10 {
    margin-top: 10px;
}

.tm20 {
    margin-top: 20px;
}

.tm30 {
    margin-top: 30px;
}

.tm40 {
    margin-top: 40px;
}

.tm50 {
    margin-top: 50px;
}

.tm60 {
    margin-top: 60px;
}

.tm80 {
    margin-top: 80px;
}

.tm100 {
    margin-top: 100px;
}

.tm120 {
    margin-top: 120px;
}

.tm215 {
    margin-top: 215px;
}

.tm0 {
    margin-top: 0px;
}

.tm-10 {
    margin-top: -10px;
}

.tm-15 {
    margin-top: -15px;
}

.tm-20 {
    margin-top: -20px;
}

.tm-30 {
    margin-top: -30px;
}

.tm-40 {
    margin-top: -40px;
}

.tm-50 {
    margin-top: -50px;
}

.tm-60 {
    margin-top: -60px;
}

.tm-70 {
    margin-top: -70px;
}

.tm-80 {
    margin-top: -80px;
}

.tm-85 {
    margin-top: -85px;
}

.tm-90 {
    margin-top: -90px;
}

.tm-100 {
    margin-top: -100px;
}

.tm-110 {
    margin-top: -110px;
}

.tm-120 {
    margin-top: -120px;
}

.tm-130 {
    margin-top: -130px;
}

.tm-140 {
    margin-top: -140px;
}

.tm-150 {
    margin-top: -150px;
}

.tm-160 {
    margin-top: -160px;
}

.tm-170 {
    margin-top: -170px;
}

.tm-180 {
    margin-top: -180px;
}

.tm-190 {
    margin-top: -190px;
}

.tm-200 {
    margin-top: -200px;
}

.tm-235 {
    margin-top: -235px;
}

.tm-380 {
    margin-top: -380px;
}

.rm10 {
    margin-right: 10px
}

.rm20 {
    margin-right: 20px
}

.rm-20 {
    margin-right: -20px
}

.rm-30 {
    margin-right: -30px
}

.rm30 {
    margin-right: 30px
}

.rm50 {
    margin-right: 50px
}

.rm70 {
    margin-right: 70px
}

.bm0 {
    margin-bottom: 0px;
}

.bm10 {
    margin-bottom: 10px
}

.bm20 {
    margin-bottom: 20px
}

.bm30 {
    margin-bottom: 30px
}

.bm40 {
    margin-bottom: 40px;
}

.bm70 {
    margin-bottom: 70px;
}

.bm170 {
    margin-bottom: 170px;
}

.bm-30 {
    margin-bottom: -30px;
}

.bm-50 {
    margin-bottom: -50px;
}

.lm10 {
    margin-left: 10px
}

.lm20 {
    margin-left: 20px
}

.lm25 {
    margin-left: 25px
}

.lm30 {
    margin-left: 30px
}

.lm40 {
    margin-left: 40px;
}

.lm50 {
    margin-left: 50px
}

.lm100 {
    margin-left: 100px
}

.lm-2 {
    margin-left: -2px;
}

.lm-20 {
    margin-left: -20px;
}

.lm-25 {
    margin-left: -25px;
}

.lm-15 {
    margin-left: -15px;
}

.lm-40 {
    margin-left: -40px;
}

.lm-280 {
    margin-left: -280px;
}

.lh27 {
    line-height: 27px;
}

.lh30 {
    line-height: 30px;
}

.pt5 {
    padding-top: 5px;
}

.padding_top50 {
    padding-top: 50px;
}

.padding0 {
    padding: 0px;
}

.padding20 {
    padding: 20px;
}

.pr15 {
    padding-right: 15px;
}

.pr10 {
    padding-right: 10px;
}

.pr25 {
    padding-right: 25px;
}

.pr30 {
    padding-right: 30px;
}

.pl15 {
    padding-left: 15px;
}

.pl30 {
    padding-left: 30px;
}

.pl40 {
    padding-left: 40px;
}

.pl25 {
    padding-left: 25px;
}

.pt40 {
    padding-top: 40px;
}

.pb15 {
    padding-bottom: 15px;
}

.margin0 {
    margin: 0 0 0 0;
}

.bdr_l_r {
    border-left: 1px #898989 solid;
    border-right: 1px #898989 solid;
}

/*Colors*/
.white {
    color: #FFF;
}

.blue {
    color: #0093B0;
}

.green {
    color: #499f24;
}

.blue_green {
    color: #24A680;
}

.success {
    color: #BF362F
}

.red, .error {
    color: #BF362F
}

.purple {
    color: rgba(120, 57, 128, 1);
}

.blue_block {
    color: rgba(0, 104, 167, 1);
}

.orange {
    color: #F78020;
}

.gray {
    color: gray;
}

.lt_gray {
    color: #d5d5d5;
}

.teal {
    color: #00a3af;
}

.navy {
    color: #02567f;
}

.chronoflex {
    color: #752fa4;
    font-weight: bold
}

.silicone {
    color: #000000;
    font-weight: bold
}

.groshong {
    color: #0063be;
    font-weight: bold
}

.powerpurple {
    color: #6A2D91;
}

.salineblue {
    color: #0079C2;
}

.black {
    color: #000000;
}

.android-green {
    color: #a4c639;
    font-weight: bold
}

.android-gray {
    color: #565656;
    font-weight: bold
}

.powerpurple-sub-header {
    color: #6A2D91;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 25px;
}

.salineblue-sub-header {
    color: #0079C2;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 25px;
}

.orange-sub-header {
    color: #F78020;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 25px;
}

.small_patient_orange {
    color: #F26854;
}

.small_patient_green {
    color: #00a79d;
}

.small_patient_purple {
    color: #7e53a3;
}

.small_patient_white {
    color: #FFF;
}

.sub_header {
    text-transform: uppercase;
    color: #FFF;
    text-align: center;
}

.sub_header.purple {
    background-color: rgba(126, 83, 163, 1);
}

.header-blue {
    color: rgba(0, 104, 167, 1);
    font-size: 30px;
    line-height: 32px;
}

.header-purple {
    color: rgba(126, 83, 163, 1);
    font-size: 30px;
    line-height: 32px;
}

.halcyon_citron {
    color: #D8E24A;
}

/*Replace all of this class with .semibold*/
.black-sub-header {}

/*img column*/
.image_left {
    float: left;
    margin: 0 30px 0 0;
    vertical-align: top;
    display: inline;
    clear: right;
}

.image_left img {
    margin: 0;
    vertical-align: top;
}

/* Columns */
#content_hldr.category_page #main .column {
    width: 290px;
    float: left;
    margin: 5px 17px 0 0;
    padding: 0;
    display: inline;
    clear: none;
}

#content_hldr.category_page #main .last {
    margin-right: 0;
}

#content_hldr.category_page #main .column img {
    border: 1px solid #4A5C58;
}

#content_hldr.category_page #main ul {
    padding: none;
}

/*Class determined by the first word of the category*/
#content_hldr.category_page #main div.implanted {}

#content_hldr.category_page #main div.booklets {}

/*Buttons*/
.submit-btn {
    display: inline-block;
    width: 120px;
    height: 28px;
    background: url(../img/buttons/submit.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.submit-btn:hover {
    background: url(../img/buttons/submit.gif) no-repeat 0 -28px;
}

.reset-btn {
    display: inline-block;
    width: 120px;
    height: 28px;
    background: url(../img/buttons/reset.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
}

.reset-btn:hover {
    background: url(../img/buttons/reset.gif) no-repeat 0 -28px;
}

.infobox input.search-btn, .search-btn {
    width: 120px;
    height: 28px;
    background: url(../img/buttons/search.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
}

.infobox input.search-btn:hover, .search-btn:hover {
    background: url(../img/buttons/search.gif) no-repeat 0 -28px;
}

.continue-btn {
    width: 120px;
    height: 28px;
    background: url(../img/buttons/continue.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.continue-btn:hover {
    background: url(../img/buttons/continue.gif) no-repeat 0 -28px;
}

.back-btn {
    width: 120px;
    height: 28px;
    background: url(../img/buttons/back.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: left;
}

.back-btn:hover {
    background: url(../img/buttons/back.gif) no-repeat 0 -28px;
}

.learn-btn {
    width: 120px;
    height: 24px;
    background: url(../img/buttons/learn_more.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.learn-btn:hover {
    background: url(../img/buttons/learn_more.gif) no-repeat 0 -24px;
}

.infobox input.install-btn, .install-btn {
    width: 120px;
    height: 28px;
    background: url(../img/buttons/install.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.infobox input.install-btn:hover, .install-btn:hover {
    background: url(../img/buttons/install.gif) no-repeat 0 -61px;
}

.infobox input.install-btn-device-manager, .install-btn-device-manager {
    background: url(../img/buttons/device-manager-btn.png) no-repeat;
    width: 245px;
    height: 61px;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.infobox input.install-btn-device-manager:hover, .install-btn-device-manager:hover {
    background: url(../img/buttons/device-manager-btn.png) no-repeat 0 -61px;
}

.infobox input.instructions-btn-device-manager, .instructions-btn-device-manager {
    background: url(../img/buttons/instructions-btn.png) no-repeat;
    width: 245px;
    height: 61px;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.infobox input.instructions-btn-device-manager:hover, .instructions-btn-device-manager:hover {
    background: url(../img/buttons/instructions-btn.png) no-repeat 0 -61px;
}

.btn {
    -moz-user-select: none;
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: normal;
    height: auto;
    line-height: 1.42857;
    margin: 0;
    padding: 6px 12px;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    background: #f7f9f9;
    /* Old browsers */
    background: -moz-linear-gradient(top, #f7f9f9 0%, #ccd9d9 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f7f9f9), color-stop(100%, #ccd9d9));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f7f9f9 0%, #ccd9d9 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f7f9f9 0%, #ccd9d9 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #f7f9f9 0%, #ccd9d9 100%);
    /* IE10+ */
    background: linear-gradient(to bottom, #f7f9f9 0%, #ccd9d9 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f7f9f9', endColorstr='#ccd9d9', GradientType=0);
    /* IE6-9 */
    cursor: pointer;
    border: solid 1.5px #b1c5c5;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    /* for Safari */
    -moz-border-radius: 3px;
    /* for Firefox */
    color: #648686;
    font-size: 13px;
    font-weight: normal;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    border: solid 1px #648686;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.25)
}

.btn.med {
    min-width: 160px;
}

.btn.wide {
    min-width: 200px;
}

/* Registration / Profile / Username Reset forms */
.row, .row_error {
    padding: 3px 0;
    display: inline-block;
}

.pdpform .row {
    width: 45%
}

.row_error {
    color: #FF0000;
    font-weight: bold
}

.cell, .cell_error {
    padding: 0 6px 0 0;
    color: #370615;
    margin: 0;
    float: left;
    display: inline;
    display: inline-block;
    display: inline-table;
}

.cell_error {
    color: #FF0000;
    font-weight: bold;
}

.req {
    color: #FF0000
}

.asterisk {
    font-size: 17px;
    margin: 0 3px 0 0;
    vertical-align: bottom;
}

#trademarks {
    padding: 0 0 0px 0;
}

#trademarks h2 {
    margin: 0 0 15px 0;
    padding: 0;
    font-size: 15px;
    height: 20px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px solid rgba(120, 57, 128, 1);
    text-transform: uppercase
}

/*Info Box*/
.infobox, #log {
    padding: 20px;
    margin: 0 0 20px 0;
    background: rgba(211, 237, 240, 1);
    border: 1px solid #dfdfdf;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    /* for Safari */
    -moz-border-radius: 5px;
    /* for Firefox */
}

.infobox {
    padding: 20px;
    margin: 0 0 20px 0;
    background: #f7f7f7;
    border: 1px solid #dfdfdf;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    /* for Safari */
    -moz-border-radius: 5px;
    /* for Firefox */
}

.infobox p {
    padding-bottom: 0;
    margin-bottom: 0;
}

.infobox hr {
    border: none;
    border-bottom: 1px solid #D0D0D0;
    margin: 30px 0;
}

.infobox h4 {
    margin: 5px 0 20px 0;
    padding: 0;
    font-size: 15px;
    height: 20px;
    font-weight: bold;
    color: #333;
    text-transform: uppercase
}

.infobox ul.category {
    list-style: none;
    display: inline-block;
    margin: 0;
    padding: 0;
}

.infobox ul.category li {
    list-style: none;
    padding: 7px 0 0 0;
    margin: 0;
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    width: 210px;
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
}

.infobox ul.columns li {
    margin: 0 15px 0 0;
    padding: 0 0 auto 0;
    width: 125px;
    float: left;
    display: inline;
}

.infobox .threecol {
    width: 550px;
}

.infobox .twocol {
    width: 330px;
}

.infobox .rcolumn {
    margin: 0 0 0 640px;
}

#category_nav #log {
    padding: 0;
    position: relative;
    margin: 0;
    width: 500px;
}

#log ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

#log li.error {
    background: url(../img/icons/warning_32.png) 0 center no-repeat;
    cursor: default;
    padding: 0 10px 0 44px;
    min-height: 40px
}

#log li.msg {
    background: url(../img/icons/tick_32.png) 0 center no-repeat;
    cursor: default;
    padding: 0 0 0 44px;
    min-height: 40px;
}

#log img {
    vertical-align: middle;
    margin-right: 12px;
    float: left;
}

.msg {
    color: green;
}

.image_columns {
    position: relative;
    clear: right;
    float: none;
    padding-bottom: 30px;
}

.image_columns img {
    margin-left: 50px;
    float: right;
}

/*QR Code*/
.QR_code a {
    color: rgba(0, 0, 0, .55);
    text-decoration: underline;
    font-weight: bold;
}

.QR_code a:visited:hover {
    color: rgba(120, 57, 128, 1)
}

.QR_code a:visited {
    color: rgba(0, 0, 0, .55);
}

.QR_code a:hover {
    color: rgba(120, 57, 128, 1)
}

.info_p {
    color: #2C6D76;
    font-size: 16px;
    margin-bottom: 30px;
}

#clicked-state {
    max-width: 280px;
    background-color: #ddd;
    padding: 30px;
    text-align: center;
    margin: 0 auto;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
}

.number p::first-letter {
    font-size: 800%;
    color: #783980;
    font-weight: bolder;
    float: left;
    padding: 5px 10px 15px 10px;
}

.icon {
    display: block;
}

.icon_mobile {
    display: none;
}

.fade_swap_holder {
    position: relative;
    margin: 0 auto;
    overflow: hidden;
}

.fade_swap {
    background: url(../img/products/accessories/supporting/AllPoints-Port-Access-System/reveal-bkgrnd.png) top left no-repeat;
    position: absolute;
    top: 0;
    right: 0;
    opacity: 1;
    /* Go back to default state at near full opacity faster than fade out*/
    -webkit-transition: opacity .1s ease-in, right .5s ease-in-out;
    -moz-transition: opacity .1s ease-in, right .5s ease-in-out;
    -o-transition: opacity .1s ease-in, right .5s ease-in-out;
    transition: opacity .1s ease-in, right .5s ease-in-out;
    height: 100%;
}

.fade_swap_holder:hover .fade_swap {
    opacity: 0;
    right: -550px;
    /*Fade out slow with gradual opacity change*/
    -webkit-transition: opacity 1.6s ease-in-out, right .5s ease-in-out;
    -moz-transition: opacity 1.6s ease-in-out, right .5s ease-in-out;
    -o-transition: opacity 1.6s ease-in-out, right .5s ease-in-out;
    transition: opacity 1.6s ease-in-out, right .5s ease-in-out;
}

.fade_swap p {
    padding: 70px 50px 70px 70px;
}
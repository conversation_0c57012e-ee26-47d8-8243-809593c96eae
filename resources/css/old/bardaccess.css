@charset "utf-8";

/* CORE CSS */
@font-face {
    font-family: 'Open Sans Light';
    src: url('../fonts/OpenSans/OpenSans-Light-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Light-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Light-webfont.ttf') format('truetype'), url('../fonts/OpenSans/OpenSans-Light-webfont.svg#open_sanslight') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Open Sans Light';
    src: url('../fonts/OpenSans/OpenSans-Semibold-webfont.eot');
    src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url('../fonts/OpenSans/OpenSans-Semibold-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Semibold-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Semibold-webfont.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'Open Sans Light';
    src: url('../fonts/OpenSans/OpenSans-Light-Italic-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Light-Italic-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Light-Italic-webfont.ttf') format('truetype'), url('../fonts/OpenSans/OpenSans-Light-Italic-webfont.svg#open_sanslight') format('svg');
    font-weight: normal;
    font-style: italic;
}

@font-face {
    font-family: 'Open Sans';
    src: url('../fonts/OpenSans/OpenSans-Regular-webfont.eot');
    src: local('Open Sans'), local('OpenSans'), url('../fonts/OpenSans/OpenSans-Regular-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Regular-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Regular-webfont.ttf') format('truetype'), url('../fonts/OpenSans/OpenSans-Regular-webfont.svg#open_sansregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'Open Sans Bold';
    src: url('../fonts/OpenSans/OpenSans-Bold-webfont.eot');
    src: local('Open Sans Bold'), local('OpenSans-Bold'), url('../fonts/OpenSans/OpenSans-Bold-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-Bold-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-Bold-webfont.ttf') format('truetype');
    font-style: normal;
}

@font-face {
    font-family: 'Open Sans ExtraBold';
    src: url('../fonts/OpenSans/OpenSans-ExtraBold-webfont.eot');
    src: local('Open Sans ExtraBold'), local('OpenSans-ExtraBold'), url('../fonts/OpenSans/OpenSans-ExtraBold-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans/OpenSans-ExtraBold-webfont.woff') format('woff'), url('../fonts/OpenSans/OpenSans-ExtraBold-webfont.ttf') format('truetype');
    font-style: normal;
}

body {
    font-family: 'Open Sans Light', sans-serif;
    line-height: 18px;
    color: #333;
    background: #FFFFFF;
    margin: 0;
    padding: 0;
}

body#contact_map {
    background: url(../css/images/bkgrounds/contact_map.gif) center 240px no-repeat;
}

#topnav_hldr {
    margin: 0;
    z-index: 100;
    position: relative;
}

#navigation {
    list-style: none;
    padding: 0;
    margin: 0;
    display: block;
    float: right;
}

#navigation li {
    display: inline;
    padding: 0 0 0 25px;
    margin: 0;
}

#navigation li a {
    margin: 0;
    padding: 0;
    text-decoration: none;
    color: #007079;
    font-size: 17px;
    text-transform: uppercase;
}

#navigation li a:hover {
    color: rgba(120, 57, 128, 1);
}

.navbar {
    display: block;
    min-height: 55px;
    margin: 0 0 -25px;
}

.navbar-default {
    background: none;
    border: none;
}

.navbar-brand {
    padding: 0;
    height: 33px;
    margin: -5px 0 0 0;
}

.navbar-brand>img {
    border: none;
}

.navbar-collapse {
    padding-right: 0;
}

/*main layout*/
#wrapper {
    display: block;
}

#container {
    max-width: 1250px;
    margin: 0 auto;
    padding: 0;
}

#content_hldr {
    background: rgb(0, 140, 155);
    /* Old browsers */
    background: -moz-linear-gradient(left, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(186, 210, 52, 1)), color-stop(25%, rgba(77, 182, 74, 1)), color-stop(50%, rgba(56, 171, 115, 1)), color-stop(75%, rgba(43, 167, 152, 1)), color-stop(100%, rgba(0, 140, 155, 1)));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(left, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(left, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(left, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
    /* IE10+ */
    background: linear-gradient(to right, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#bad234', endColorstr='#008c9b', GradientType=1);
    /* IE6-9 */
    position: relative;
    display: block;
    margin: 0;
    padding: 0 15px 15px 0;
    clear: both;
}

#content_hldr.fullpage #content {
    display: block;
    margin: 0;
    padding: 0;
}

#content_hldr.fullpage #content #pagecontent {
    margin: 0;
    padding: 50px
}

#content_hldr.fullpage #chip {
    background: url(../img/bkgrds/column_chip.png) 0 0 no-repeat;
    top: -15px;
    left: 0px;
    position: absolute;
    float: left;
    z-index: 1;
    height: 40px;
    width: 40px;
}

#content_hldr.fullpage #col_hldr {
    display: none;
}

#content_hldr_btm {
    clear: both;
    float: none;
    display: block;
    line-height: 0;
    font-size: 0;
}

#content {
    background: rgb(187, 226, 231);
    /* Old browsers */
    background: -moz-linear-gradient(left, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(236, 242, 202, 1)), color-stop(25%, rgba(209, 236, 206, 1)), color-stop(50%, rgba(197, 229, 214, 1)), color-stop(75%, rgba(193, 229, 225, 1)), color-stop(99%, rgba(187, 226, 231, 1)));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(left, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
    /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(left, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(left, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
    /* IE10+ */
    background: linear-gradient(to right, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ecf2ca', endColorstr='#bbe2e7', GradientType=1);
    /* IE6-9 */
    position: relative;
    display: block;
    padding: 0;
    margin: 0;
    color: rgba(0, 0, 0, 0.65);
}

#homepage_chip {
    position: absolute;
    z-index: 1;
    top: 0px;
    left: 0px;
    width: 56%
}

#homepage_chip img {
    width: 74%
}

#header {
    height: 60px;
    display: block;
    padding: 0;
    position: relative;
}

#baslogo {
    padding: 0;
    margin: 0;
    display: inline-block;
    position: relative;
    top: 52px;
    left: 0;
}

#baslogo img {
    border: none;
    padding: 0;
}

#ct_login {
    background: url(../img/icons/accessu.png) left center no-repeat;
    height: 20px;
    padding: 0 0 0 30px;
    margin: 0;
    display: inline-block;
    position: relative;
    top: 20px;
    float: right;
    font-size: 12px
}

#ct_login a {
    margin: 0px 7px 0px 7px;
    color: #007079
}

#ct_login a:visited:hover {
    color: rgba(120, 57, 128, 1)
}

#ct_login a:visited {
    color: #007079
}

#ct_login a:hover {
    color: rgba(120, 57, 128, 1);
}

#cat_title_hldr {
    background: #fff;
    display: block;
    margin: 0px -15px 15px -2px;
    padding: 30px 0 0 0;
}

#cat_title {
    background: none;
    height: 25px;
    display: inline-block;
    margin: 0
}

#cat_title h1 {
    font-size: 26px;
    font-weight: lighter;
    letter-spacing: 14px;
    white-space: nowrap;
    margin: 0 0 0 0;
    padding: 0;
    color: rgba(120, 57, 128, 1);
    text-transform: uppercase;
    text-decoration: none
}

#cat_title h1 a, #cat_title h1 a:visited {
    color: rgba(120, 57, 128, 1);
    text-decoration: none
}

#cat_title h1 a:hover, #cat_title h1 a:visited:hover {
    color: rgba(120, 57, 128, 1);
    text-decoration: none
}

ul.side-nav {
    padding: 0;
    margin: 0;
}

#col_hldr {
    background: url(../img/bkgrds/column_chip.png) 0 0 no-repeat;
    width: 250px;
    margin: -15px 0 0 0;
    position: relative;
    float: left;
    z-index: 1
}

#col {
    font-size: 16px;
    position: relative;
    margin: 0;
    padding: 25px 15px 0 15px;
    letter-spacing: .02em;
    color: #4a5c58;
}

#col .title {
    margin: 0;
    font-size: 12px;
    padding: 15px 10px 7px 7px;
    font-size: 18px;
    color: rgba(120, 57, 128, 1);
    text-transform: uppercase
}

#col a, #col a:link {
    padding: 4px 0 0 22px;
    line-height: 24px;
    text-decoration: none;
    color: rgba(120, 57, 128, 1);
}

#col a:visited:hover {
    color: rgba(120, 57, 128, 1);
}

#col a:visited {
    color: rgba(120, 57, 128, 1);
}

#col a:hover, #col a:hover {
    color: rgba(120, 57, 128, 1);
}

#col ul.select, #col ul.side-nav {
    margin: 5px 0 0 -25px;
    padding: 10px 0;
    list-style: none;
    text-transform: capitalize
}

#col .side-nav li, #col .select li {
    list-style: none;
    margin: 0 0 0 10px;
    cursor: pointer;
}

#col .expanded li {
    list-style: none;
    margin: 0;
    cursor: pointer;
}

#col .select li .link {
    text-transform: lowercase;
}

#col li.tab.selected {
    color: #bd4f48;
    cursor: default;
}

#col li.ui-tabs-active a, #col li.ui-tabs-active a:link, #col li.ui-tabs-active a:visited {
    background: url(../img/bullets/large_white_arrow.png) 0 2px no-repeat;
    color: rgba(120, 57, 128, 1);
    cursor: default;
    font-weight: bold;
}

#col .service a {
    font-size: 10px;
}

/*Main content/tabbed pages */
a, a:link {
    color: #00F
}

a:visited, a:visited:hover {}

#pagecontent {
    position: relative;
    display: block;
    background: #fff;
    font-size: 17px;
    line-height: 24px;
    padding: 25px;
    margin: 0 0 0 250px;
}

#pagecontent .purple-background {
    background: transparent radial-gradient(ellipse at 70% 40px, #7E53A3 0%, #4D1E75 100%) repeat scroll 0% 0%;
    padding: 0px;
}

#pagecontent.full {
    margin: 0;
    padding: 0;
}

.slogan {
    margin: 10px;
}

#main a {
    color: #007079;
}

#main {
    position: relative;
    padding: 0;
}

#return {
    background: url(../img/bkgrds/return_chip.png) bottom right no-repeat;
    height: 40px;
    width: 40px;
    margin: 0;
    padding: 0;
    display: inline-block;
    position: absolute;
    bottom: -15px;
    right: -15px;
    display: none;
    z-index: 3;
    cursor: pointer
}

#return a {
    font-size: 8px;
    color: #fff;
    text-decoration: none;
    position: absolute;
    bottom: 0px;
    right: 2px;
    display: none;
}

#footer_hldr {
    height: 150px;
    margin-top: 25px;
}

#footer {
    height: 150px;
    position: relative;
    display: block;
    margin: 0;
    padding: 0;
    clear: both;
    color: #4d4d4f;
    font-size: 12px;
}

#footer nav {
    position: relative;
    display: block;
    padding: 0;
}

#footer nav ul {
    position: relative;
    display: block;
    margin: 0;
    padding: 0;
}

#footer nav ul li {
    margin: 0 0 0 20px;
    padding: 0;
    list-style: none;
    display: inline-block;
}

#footer nav ul li:last-child {
    float: right;
    margin-right: 20px;
}

#footer img {
    border: none;
    vertical-align: bottom
}

#footer a, #footer a:link {
    margin: 0;
    padding: 0;
    text-decoration: none;
    text-transform: capitalize;
    color: #007079;
    font-size: 12px;
}

#footer a:visited:hover, #footer a:visited, #footer a:hover {
    color: #007079;
}

.consult_statement {
    font-size: 13px;
    margin: 10px 0 -5px 0;
    color: #BF362F;
    display: block;
    text-align: center;
}

#nav_container #footer .consult_statement {
    display: none;
}

li.copyright {
    text-align: right;
}

#date_modified {
    display: none;
}

.tagline {
    font-size: 13px;
    color: #007079;
    text-align: center;
}

/*search form/page */
input, form {
    margin: 0;
    padding: 0;
}

input .input {
    border-radius: 3px;
    -webkit-border-radius: 3px;
    /* for Safari */
    -moz-border-radius: 3px;
    /* for Firefox */
}

#search {
    text-align: left;
    padding-right: 15px;
    padding-left: 7px
}

#searchboxbtn {}

#search label {
    color: #AC302B;
    font-size: 12px;
    font-weight: bold;
    display: inline-block;
}

#search_box {
    display: inline-block;
    border: 1px solid #c58b8b;
    background: #ecf3f7;
    margin: 0;
    padding: 0 0 0 3px;
    width: 150px;
    height: 15px;
    font-size: 11px;
    color: #000;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    /* for Safari */
    -moz-border-radius: 3px;
    /* for Firefox */
}

#input_active, #search_box:focus, #search_box:hover {
    border: 1px solid #c58b8b;
    background: #ecf3f7;
    margin: 0;
    padding: 0 0 0 3px;
    width: 150px;
    height: 15px;
    font-size: 11px;
    background: #fff;
    color: #000;
}

.search {
    margin: 0;
    padding: 0 0 0 20px;
    _padding-top: 1px;
    width: 80px;
    height: 16px;
    font-size: 11px;
    text-transform: uppercase;
    color: #bd4f48;
    font-weight: bold;
    cursor: pointer;
    border: none;
}

.go {
    display: inline-block;
    margin: 0 0 0 2px;
    padding: 0;
    width: 20px;
    height: 18px;
    cursor: pointer;
    background: url(../img/buttons/search_arrow.gif) 0px center no-repeat;
    color: #fff;
    border: none;
    position: relative;
    vertical-align: middle;
}

/*** Main Layout - END***/
/*Basic Formatting Phase 2*/
.phase2 h3 {
    margin: 5px 0 20px 0;
    border-bottom: 1px solid #783980;
    font-weight: bold;
}

.phase2.pageLogo {
    margin: 20px 5px 10px 5px;
}

/*Basic Formatting*/
h1 {
    font-weight: bold;
    margin: 0 0 10px 0;
    font-size: 17px;
    color: #005221;
    text-transform: uppercase
}

h2 {
    margin: 0 0 40px 0;
    font-size: 20px;
    line-height: 20px;
    font-weight: normal;
    color: #222;
    text-transform: uppercase;
    position: relative;
}

h3 {
    margin: 5px 0 20px 0;
    padding: 0;
    font-size: 22px;
    line-height: 20px;
    font-weight: normal;
    color: #783980;
    border-bottom: medium none;
    text-transform: uppercase;
}

h4 {
    color: rgba(0, 0, 0, 0.55);
    font-size: 20px;
    margin-top: 35px;
    margin-bottom: 5px;
}

h5 {
    color: #000;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}

h6 {
    color: #374f5e;
    font-size: 18px;
    margin-top: 35px;
    margin-bottom: 5px;
}

img {
    display: block;
    height: auto;
    max-width: 100%;
}

.subtitles {
    color: #BF362F;
    font-weight: bold;
    font-size: 14px
}

.left {
    text-align: left;
}

.right {
    text-align: right;
}

.center {
    text-align: center;
    margin: 0 auto;
}

.extrabold {
    font-weight: bolder;
    font-family: 'Open Sans ExtraBold';
}

.bold {
    font-weight: bold;
}

.light {
    font-weight: lighter;
}

.italic {
    font-style: italic
}

.footnote {
    font-style: italic;
    font-size: 11px;
    margin-top: 30px;
    line-height: 16px;
}

.disclaimer {
    margin-top: 30px;
    font-style: italic;
}

.text-reference {
    font-size: 13px;
    line-height: 23px;
    color: #555
}

.directions {
    color: rgba(0, 0, 0, 0.55);
}

.headline {
    font-size: 18px;
    line-height: 24px;
}

.smallcaps {
    font-variant: small-caps;
}

.p-test {
    font-size: 14px;
    line-height: 20px;
}

.nowrap {
    white-space: nowrap;
}

.unbold {
    font-weight: normal
}

.rotate {
    /* Safari */
    -webkit-transform: rotate(90deg);
    /* Firefox */
    -moz-transform: rotate(90deg);
    /* IE */
    -ms-transform: rotate(90deg);
    /* Opera */
    -o-transform: rotate(90deg);
    /* Internet Explorer */
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
}

ol.numeral li {
    list-style: decimal
}

ol.alpha li {
    list-style: upper-alpha
}

ul.nobullet {
    list-style: none;
    padding-left: 0;
}

ul lo.nobullet {
    list-style: none;
    padding-left: 0;
}

ul.pdf {
    padding: 5px 8px 20px 40px;
    margin: 0;
    list-style: url(../img/blts/pdficon_small.gif);
}

.indent {
    margin-left: 30px
}

.underline {
    text-decoration: underline;
}

.no_underline {
    text-decoration: none;
    border: none;
}

.uppercase {
    text-transform: uppercase;
}

.lowercase {
    text-transform: lowercase;
}

.img_link {
    border: none;
}

.open-tab {
    cursor: pointer;
}

.line {
    background: url(../img/bkgrds/3px-line.png) no-repeat center right;
}

.border-right {
    background: url(../img/bkgrds/col-separator.jpg) no-repeat center right;
    width: 14px;
    height: 124px;
}

.border-right-300 {
    background: url(../img/bkgrds/col-separator-300.png) no-repeat center right;
    width: 24px;
    height: 300px;
}

.font10 {
    font-size: 10px;
}

.font12 {
    font-size: 12px;
}

.font14 {
    font-size: 14px;
    line-height: 26px;
}

.font16 {
    font-size: 16px;
}

.font20 {
    font-size: 20px;
}

.font30 {
    font-size: 30px;
}

.font45 {
    font-size: 45px;
}

.lh55 {
    line-height: 55px;
}

.lh40 {
    line-height: 40px;
}

.smallcaps {
    font-variant: small-caps;
}

ol.circles-list {
    list-style-type: none;
    list-style-type: decimal !ie;
    /*IE 7- hack*/
    margin: 0;
    margin-left: 3.7em;
    padding: 0;
    counter-reset: li-counter;
}

ol.circles-list>li {
    position: relative;
    margin-bottom: 20px;
    padding-left: 0.5em;
    min-height: 3em;
}

ol.circles-list>li:before {
    position: absolute;
    top: 0;
    left: -1.7em;
    width: 1.2em;
    height: 1.2em;
    font-size: 2em;
    line-height: 1.1;
    text-align: center;
    color: #f5f5f5;
    border-radius: 50%;
    background-color: #592B5F;
    content: counter(li-counter);
    counter-increment: li-counter;
}

.flt_l {
    float: left
}

.flt_r {
    float: right
}

.clear_l {
    clear: left
}

.clear_r {
    clear: right
}

.clear_columns {
    display: block;
    clear: both;
    float: none;
    padding: 0;
    margin: 0;
    height: 0;
    line-height: 0;
    font-size: 0;
}

img {
    vertical-align: top
}

/*Heights*/
.h400 {
    min-height: 400px;
}

.h500 {
    min-height: 500px;
}

.h570 {
    min-height: 570px;
}

.h680 {
    min-height: 680px;
}

/*Widths*/
.width95 {
    margin-right: 5%;
}

.width90 {
    margin-right: 10%;
}

.width85 {
    margin-right: 15%;
}

.width75 {
    margin-right: 25%;
}

.width50 {
    margin-right: 50%;
}

.w33 {
    max-width: 33%;
    box-sizing: border-box
}

.w50 {
    max-width: 50%;
    box-sizing: border-box
}

.w66 {
    max-width: 66%;
    box-sizing: border-box
}

.w70 {
    max-width: 70%;
    box-sizing: border-box
}

.w46 {
    max-width: 46%;
    box-sizing: border-box
}

.w600 {
    max-width: 600px;
}

.w500 {
    max-width: 500px;
}

.w400 {
    max-width: 400px;
}

.w320 {
    max-width: 320px;
}

.w290 {
    max-width: 290px;
}

/*Margins*/
.tm10 {
    margin-top: 10px;
}

.tm20 {
    margin-top: 20px;
}

.tm30 {
    margin-top: 30px;
}

.tm40 {
    margin-top: 40px;
}

.tm50 {
    margin-top: 50px;
}

.tm60 {
    margin-top: 60px;
}

.tm100 {
    margin-top: 100px;
}

.tm120 {
    margin-top: 120px;
}

.tm215 {
    margin-top: 215px;
}

.tm0 {
    margin-top: 0px;
}

.tm-10 {
    margin-top: -10px;
}

.tm-20 {
    margin-top: -20px;
}

.tm-30 {
    margin-top: -30px;
}

.tm-40 {
    margin-top: -40px;
}

.tm-50 {
    margin-top: -50px;
}

.tm-60 {
    margin-top: -60px;
}

.tm-70 {
    margin-top: -70px;
}

.tm-80 {
    margin-top: -80px;
}

.tm-85 {
    margin-top: -85px;
}

.tm-90 {
    margin-top: -90px;
}

.tm-100 {
    margin-top: -100px;
}

.tm-110 {
    margin-top: -110px;
}

.tm-120 {
    margin-top: -120px;
}

.tm-130 {
    margin-top: -130px;
}

.tm-140 {
    margin-top: -140px;
}

.tm-150 {
    margin-top: -150px;
}

.tm-160 {
    margin-top: -160px;
}

.tm-170 {
    margin-top: -170px;
}

.tm-180 {
    margin-top: -180px;
}

.tm-190 {
    margin-top: -190px;
}

.tm-200 {
    margin-top: -200px;
}

.tm-235 {
    margin-top: -235px;
}

.tm-380 {
    margin-top: -380px;
}

.rm10 {
    margin-right: 10px
}

.rm20 {
    margin-right: 20px
}

.rm-20 {
    margin-right: -20px
}

.rm-30 {
    margin-right: -30px
}

.rm30 {
    margin-right: 30px
}

.rm50 {
    margin-right: 50px
}

.bm0 {
    margin-bottom: 0px;
}

.bm10 {
    margin-bottom: 10px
}

.bm20 {
    margin-bottom: 20px
}

.bm30 {
    margin-bottom: 30px
}

.bm40 {
    margin-bottom: 40px;
}

.bm70 {
    margin-bottom: 70px;
}

.bm170 {
    margin-bottom: 170px;
}

.bm-50 {
    margin-bottom: -50px;
}

.lm10 {
    margin-left: 10px
}

.lm20 {
    margin-left: 20px
}

.lm25 {
    margin-left: 25px
}

.lm30 {
    margin-left: 30px
}

.lm40 {
    margin-left: 40px;
}

.lm50 {
    margin-left: 50px
}

.lm100 {
    margin-left: 100px
}

.lm-20 {
    margin-left: -20px;
}

.lm-25 {
    margin-left: -25px;
}

.lm-15 {
    margin-left: -15px;
}

.lm-40 {
    margin-left: -40px;
}

.lm-280 {
    margin-left: -280px;
}

.lh30 {
    line-height: 30px;
}

.pt5 {
    padding-top: 5px;
}

.padding_top50 {
    padding-top: 50px;
}

.padding0 {
    padding: 0px;
}

.padding20 {
    padding: 20px;
}

.pr15 {
    padding-right: 15px;
}

.pr30 {
    padding-right: 30px;
}

.pl30 {
    padding-left: 30px;
}

.margin0 {
    margin: 0 0 0 0;
}

/* Columns */
#trademarks .column {
    width: 25%;
    float: left;
    margin: 5px 60px 0 0;
    padding: 0;
    display: inline;
    clear: none;
}

#trademarks .last {
    margin-right: 0;
}

#trademarks .column img {
    border: 1px solid #4A5C58;
}

.col_l, .col_l_flush, .col_l_flush_nm, .fullpage .col_l_flush {
    float: left;
    position: relative;
}

.col_r, .col_r_flush, .col_r_flush_nm, .fullpage .col_r_flush {
    float: right;
    position: relative;
}

.col_full_flush, .fullpage .col_full_flush {
    position: relative;
}

.col_l {
    margin-right: 20px;
}

.col_r {
    margin-left: 20px
}

.col_l_flush {
    margin-right: 20px;
    margin-left: -25px
}

.col_r_flush {
    margin-right: -25px;
    margin-left: 20px
}

.col_l_flush_nm {
    margin-left: -25px
}

.col_r_flush_nm {
    margin-right: -25px;
}

.col_full_flush {
    margin-right: -25px;
    margin-left: 20px
}

.col_l_full_flush_extend {
    margin-right: -50px;
    margin-left: -25px
}

.col_r_full_flush_extend {
    margin-right: -25px;
    margin-left: -50px
}

.fullpage .col_l {
    margin-right: 50px;
}

.fullpage .col_r {
    margin-left: 50px
}

.fullpage .col_l_flush {
    margin-right: 50px;
    margin-left: -50px
}

.fullpage .col_r_flush {
    margin-right: -50px;
    margin-left: 50px
}

.fullpage .col_full_flush {
    margin-right: -50px;
    margin-left: -50px
}

.gutter>[class*='col-'] {
    padding-right: 10px;
    padding-left: 10px;
}

.top-band {
    background: url(../img/bkgrds/top_band_bkgrd.jpg) top center no-repeat;
    background-size: 100%;
    padding-top: 9px;
    margin-top: 40px;
}

.bottom-band {
    background: url(../img/bkgrds/bottom_band_bkgrd.jpg) bottom center no-repeat;
    background-size: 100%;
    padding-bottom: 9px;
    margin-bottom: 40px
}

.top-band_full {
    background: url(../img/bkgrds/top_band_full.jpg) top center no-repeat;
    background-size: 100%;
    padding-top: 9px;
    margin-top: 40px;
}

.bottom-band_full {
    background: url(../img/bkgrds/bottom_band_full.jpg) bottom center no-repeat;
    background-size: 100%;
    padding-bottom: 9px;
    margin-bottom: 40px
}

.bottom-line {
    background-image: url(../img/products/ultrasound/supporting/sapiens-line.png);
    background-size: 100%;
    background-repeat: no-repeat;
    margin-left: -15px;
    height: 10px;
    padding: 0;
}

/*Colors*/
.white {
    color: #FFF;
}

.blue {
    color: #0093B0;
}

.green {
    color: #499f24;
}

.blue_green {
    color: #24A680;
}

.success {
    color: #BF362F
}

.red, .error {
    color: #BF362F
}

.purple {
    color: rgba(120, 57, 128, 1);
}

.blue_block {
    color: rgba(0, 104, 167, 1);
}

.orange {
    color: #F78020;
}

.gray {
    color: gray;
}

.lt_gray {
    color: #d5d5d5;
}

.chronoflex {
    color: #752fa4;
    font-weight: bold
}

.silicone {
    color: #000000;
    font-weight: bold
}

.groshong {
    color: #0063be;
    font-weight: bold
}

.powerpurple {
    color: #6A2D91;
}

.salineblue {
    color: #0079C2;
}

.black {
    color: #000000;
}

.android-green {
    color: #a4c639;
    font-weight: bold
}

.android-gray {
    color: #565656;
    font-weight: bold
}

.powerpurple-sub-header {
    color: #6A2D91;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 25px;
}

.salineblue-sub-header {
    color: #0079C2;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 25px;
}

.orange-sub-header {
    color: #F78020;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 25px;
}

.sub_header {
    text-transform: uppercase;
    color: #FFF;
    text-align: center;
}

.sub_header.purple {
    background-color: rgba(126, 83, 163, 1);
}

/*Replace all of this class with .semibold*/
.black-sub-header {}

/*General Table Formatting*/
.striped {
    width: 100%;
    border-bottom: 3px solid #e1f2e;
}

.striped th {
    text-align: left;
    width: auto;
    padding: 5px 10px;
    font-weight: bold;
    font-size: 15px;
    line-height: 22px;
    border-bottom: 1px solid #e1f2e2;
    vertical-align: middle;
    white-space: nowrap;
}

.striped th.col_100 {
    width: 100px;
}

.striped td {
    padding: 3px 10px;
    text-align: cen;
    width: auto;
    font-size: 14px;
    line-height: 22px;
    color: #444;
    border-bottom: 1px solid #e1f2e2
}

.striped .font20 {
    font-size: 20px;
}

.striped tr:nth-of-type(odd) {
    background: #dff2e0;
}

.striped thead tr th {
    background: #FFF;
    border-bottom: 3px solid #e1f2e2;
}

.color_two {
    background: #dff2e0;
}

.width_autofill {
    width: auto;
    _width: auto;
}

/*PDF thumbnail images and text*/
ul.thumbnails {
    margin: 40px 5px 20px 0;
    padding: 0;
    float: none;
    list-style-position: inside;
    list-style: none;
    display: block;
    position: relative;
    padding-left: 0;
}

ul.thumbnails li {
    margin: 0 25px 10px 0;
    float: left;
    position: relative;
    display: inline;
    zoom: 1.0;
    width: 116px;
    height: 250px;
    line-height: 105%;
    font-size: 10px;
    vertical-align: middle;
}

ul.thumbnails.med li {
    width: 175px;
}

ul.thumbnails.wide li {
    width: 225px;
}

#resources ul.thumbnails li {
    margin: 0 25px 10px 0;
    float: left;
    position: relative;
    display: inline;
    width: 116px;
    height: 250px;
    line-height: 105%;
    font-size: 10px;
    vertical-align: middle;
}

#main ul.thumbnails li img {
    margin-bottom: 10px;
    margin-right: 20px;
    position: relative;
    clear: both
}

#main ul.thumbnails li a img {
    border: 1px solid #d6edf0;
    text-decoration: none;
}

#main ul.thumbnails li a:visited:hover img {
    border: 1px solid #007079;
    text-decoration: none;
}

#main ul.thumbnails li a:visited img {
    border: 1px solid #d6edf0;
    text-decoration: none;
}

#main ul.thumbnails li a:hover img {
    border: 1px solid #007079;
    text-decoration: none;
}

#main ul.thumbnails a {
    color: #007079;
    text-decoration: none;
}

#main ul.thumbnails a:visited:hover {
    color: rgba(120, 57, 128, 1);
    text-decoration: none
}

#main ul.thumbnails a:visited {
    color: #007079;
    text-decoration: none;
}

#main ul.thumbnails a:hover {
    color: rgba(120, 57, 128, 1);
    text-decoration: none
}

/*img column*/
.image_left {
    float: left;
    margin: 0 30px 0 0;
    vertical-align: top;
    display: inline;
    clear: right;
}

.image_left img {
    margin: 0;
    vertical-align: top;
}

/*2columns*/
ul.info-2col {
    list-style: none;
    margin: 0;
    padding: 0;
    float: none;
    display: block;
    position: relative;
}

ul.info-2col li {
    margin: 0;
    padding: 5px 20px 5px 20px;
    width: 310px;
    height: 100px;
    float: left;
    display: inline;
    border-right: #a2bd88 dotted 1px;
}

ul.info-2col li img {
    margin: -10px 10px 30px 0;
    padding: 0;
    float: left;
    display: inline;
}

ul.info-2col li.last {
    border: none
}

ul.info1-2col {
    list-style: none;
    margin: 0;
    padding: 0;
    float: none;
    display: block;
    position: relative;
}

ul.info1-2col li {
    margin: 0;
    padding: 5px 20px 5px 20px;
    width: 310px;
    float: left;
    display: inline;
}

ul.info1-2col li img {
    margin: -10px 10px 30px 0;
    padding: 0;
    float: left;
    display: inline;
}

ul.info1-2col li.last {
    border: none
}

/*3columns*/
ul.info-3col {
    list-style: none;
    margin: 0;
    padding: 0;
    float: none;
    display: block;
    position: relative;
}

ul.info-3col li {
    margin: 0;
    padding: 5px 15px 5px 15px;
    width: 218px;
    height: 125px;
    float: left;
    display: inline;
    border-right: #a2bd88 dotted 1px;
}

ul.info-3col li img {
    margin: 0 10px 50px 0;
    padding: 0;
    float: left;
    display: inline;
    vertical-align: middle
}

ul.info-3col li.last, .noborder li {
    border: none
}

ul.text-3col {
    list-style: none;
    margin: 0 auto;
    padding: 0;
    display: inline-block;
    position: relative;
    -webkit-columns: 3 175px;
    -moz-columns: 3 175px;
    columns: 3 175px;
    -webkit-column-gap: 60px;
    -moz-column-gap: 60px;
    column-gap: 60px;
}

ul.text-3col li {
    margin: 0;
    padding: 5px 0px;
    min-width: 175px;
    display: inline-block;
    vertical-align: top;
}

ul.text-3col li img {
    margin: 0 10px 50px 0;
    padding: 0;
    display: inline;
    vertical-align: middle
}

ul.text1-3col {
    list-style: none;
    margin: 20px 0 0 0;
    padding: 0;
    float: none;
    display: block;
    position: relative;
}

ul.text1-3col li {
    margin: 0;
    padding: 5px 10px 5px 10px;
    width: 213px;
    height: 100px;
    float: left;
    display: inline;
}

ul.text1-2col li img {
    margin: -10px 10px 30px 0;
    padding: 0;
    float: left;
    display: inline;
}

ol.text1-3col {
    margin: 20px 0 0 80px;
    padding: 0;
    float: none;
    display: block;
    position: relative;
}

ol.text1-3col li {
    margin: 0 0 20px 0;
    padding: 5px 10px 5px 10px;
    width: 30%;
    height: auto;
    float: left;
    display: inline;
}

ol.text1-3col li span {
    float: left;
    margin: 0 15px 50px 0;
}

.three-column-page {
    margin: 0;
    padding: 0;
    columns: 220px 3;
    -webkit-columns: 220px 3;
    /* Safari and Chrome */
    -moz-columns: 220px 3;
    /* Firefox */
    column-gap: 0;
    -moz-column-gap: 0;
    /* Firefox */
    -webkit-column-gap: 0;
    /* Safari and Chrome */
}

.three-column-page li {
    margin: 0;
    padding: 0 0 5px 0;
    width: 220px;
    list-style: none;
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
}

.three-column-full {
    margin: 0;
    padding: 0;
    columns: 250px 3;
    -webkit-columns: 250px 3;
    /* Safari and Chrome */
    -moz-columns: 250px 3;
    /* Firefox */
    column-gap: 0;
    -moz-column-gap: 0;
    /* Firefox */
    -webkit-column-gap: 0;
    /* Safari and Chrome */
}

.three-column-full li {
    margin: 0;
    padding: 0 0 5px 0;
    width: 250px;
    list-style: none;
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
}

.two-columns {
    columns: 225px 2;
    -webkit-columns: 225px 2;
    /* Safari and Chrome */
    -moz-columns: 225px 2;
    /* Firefox */
    column-gap: 0;
    -moz-column-gap: 0;
    /* Firefox */
    -webkit-column-gap: 0;
    /* Safari and Chrome */
}

.three-columns {
    columns: 225px 3;
    -webkit-columns: 225px 3;
    /* Safari and Chrome */
    -moz-columns: 225px 3;
    /* Firefox */
    column-gap: 0;
    -moz-column-gap: 0;
    /* Firefox */
    -webkit-column-gap: 0;
    /* Safari and Chrome */
}

/* CATEGORY CSS */
/* Category pages */
#content_hldr.category_page #content {
    display: block;
    margin: -15px 0 0 0;
    padding: 0;
}

#content_hldr.category_page #pagecontent {
    background: url(../img/bkgrds/category_chip.png) 0 0 no-repeat;
    margin: 0;
    padding: 50px;
}

#content_hldr.category_page #category_nav {
    margin: 0px 370px 0px 0px;
    color: #41514e;
    height: 225px;
    display: block;
}

#nav_container {
    background: url(../img/bkgrds/category_chip.png) 0 -50px no-repeat rgba(255, 255, 255, .5);
    margin: 0px -50px;
}

#product_nav {
    padding: 0;
    margin: 0 auto;
    display: block;
    text-align: center;
}

#product_nav ul {
    background: url(../img/nav/nav_arrow.png) left center repeat-x;
    padding: 0;
    margin: 50px auto;
    display: inline-block;
    text-decoration: none;
}

#product_nav li {
    background: url(../img/nav/placement_technologies.jpg) 10px 35px no-repeat rgba(36, 166, 128, 1);
    height: 240px;
    width: 220px;
    display: inline-block;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    position: relative;
    margin: 0 81px 0 0;
    padding: 5px 0 0 0;
}

#product_nav li:first-of-type {
    background: url(../img/nav/access_devices.jpg) 10px 35px no-repeat rgba(84, 183, 42, 1);
}

#product_nav li:last-of-type {
    background: url(../img/nav/care_and_maintenance.jpg) 10px 35px no-repeat rgba(6, 154, 175, 1);
    margin: 0
}

#product_nav li a {
    background: url(../img/nav/visit_chip.png) bottom right no-repeat rgba(36, 166, 128, .85);
    position: absolute;
    height: 198px;
    width: 195px;
    bottom: 0px;
    right: 0px;
    padding: 10px;
    color: #fff;
    font-size: 15px;
    font-weight: normal;
    text-align: left;
    line-height: 24px;
    text-decoration: none;
    transition: opacity .5s ease;
    opacity: 0;
}

#product_nav li:first-of-type a {
    background-color: rgba(84, 183, 42, .95);
}

#product_nav li:last-of-type a {
    background-color: rgba(6, 154, 175, .85);
}

#product_nav li:hover a {
    transition: opacity .5s ease;
    opacity: 1;
}

#resources_nav {
    padding: 0;
    margin: 0 auto;
    display: block;
    text-align: center;
}

#resources_nav ul {
    padding: 0;
    margin: 50px auto;
    display: inline-block;
    text-decoration: none;
}

#resources_nav li {
    background: url(../img/nav/mobile_apps.jpg) 10px 35px no-repeat rgba(36, 166, 128, 1);
    height: 240px;
    width: 220px;
    display: inline-block;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    position: relative;
    margin: 0 81px 0 0;
    padding: 5px 0 0 0;
}

#resources_nav li:first-of-type {
    background: url(../img/nav/literature.jpg) 10px 35px no-repeat rgba(84, 183, 42, 1);
}

#resources_nav li:last-of-type {
    background: url(../img/nav/programs.jpg) 10px 35px no-repeat rgba(6, 154, 175, 1);
    margin: 0
}

#resources_nav li a {
    background: url(../img/nav/visit_chip.png) bottom right no-repeat rgba(36, 166, 128, .85);
    position: absolute;
    height: 190px;
    width: 190px;
    bottom: 0px;
    right: 0px;
    padding: 10px;
    color: #fff;
    font-size: 16px;
    font-weight: normal;
    text-align: left;
    line-height: 20px;
    text-decoration: none;
    transition: opacity .5s ease;
    opacity: 0;
}

#resources_nav li:first-of-type a {
    background-color: rgba(84, 183, 42, .95);
}

#resources_nav li:last-of-type a {
    background-color: rgba(6, 154, 175, .85);
}

#resources_nav li:hover a {
    transition: opacity .5s ease;
    opacity: 1;
}

#nav_container h3 {
    padding: 30px 0 10px 0px;
    letter-spacing: 2px;
    color: rgba(84, 183, 42, 1);
    margin-top: 0;
    font-weight: light;
    text-align: center;
    font-size: 48px;
}

#nav_container.ultrasound {
    background: none;
    height: auto;
}

#nav_container.access_products {
    background: none;
    height: auto;
}

#nav_container.care_products {
    background: none;
    height: auto;
}

#nav_container #header_wrapper h3 {
    background: url(../img/bkgrds/category_chip.png) 0 -50px no-repeat rgba(255, 255, 255, .5);
    margin: 0px;
    padding: 6px 0 8px 108px;
    text-align: left
}

#nav_container.access_products h3 {
    color: rgba(36, 166, 128, 1);
}

#nav_container.access_products a {
    color: rgba(36, 166, 128, 1);
}

#nav_container h4 {
    color: rgba(36, 166, 128, 1);
    margin: 20px 0 -15px 110px;
}

#nav_container.care_products h3 {
    color: rgba(6, 154, 175, 1);
}

#nav_container.care_products a {
    color: rgba(6, 154, 175, 1);
}

#nav_container.care_products h4 {
    color: rgba(6, 154, 175, 1);
}

.nav_col {
    float: left;
    display: inline-block;
    list-style: none;
    min-width: 385px;
}

#product_selection h3 {
    background: none;
    color: rgba(84, 183, 42, .85);
    padding: 6px 0 23px 55px;
    font-size: 46px;
    letter-spacing: 2px;
    border: none;
    font-weight: normal;
    margin: 0 0 10px 55px;
    text-align: left;
}

#product_selection h3.tip_confirmation_location {
    font-size: 42px;
}

#product_selection ul.col {
    margin: 15px 0 20px 90px;
    padding: 0;
    min-height: 40px;
    list-style: none;
    display: block;
    line-height: 28px;
}

#product_selection ul.col li {
    margin: 0;
    padding: 0 0 0 20px;
    font-size: 18px;
}

/*Font is slightly darker than title so nurses can read easily */
#product_selection a {
    color: rgba(73, 158, 36, 1);
    text-decoration: none;
}

#product_selection a:hover {
    color: rgba(120, 57, 128, 1);
    background: url(../img/bullets/small_purple_arrow.png) 0 center no-repeat;
    display: inline-block;
    position: relative;
    margin-left: -10px;
    padding-left: 10px;
}

#sub_nav_container {
    margin: 0px 0 55px 0
}

#sub_nav_container ul {
    padding: 0px;
    margin: 0 auto;
    display: block;
    text-decoration: none;
    text-align: center;
}

#sub_nav_container li {
    margin: 0 45px 30px;
    width: 166px;
    display: inline-block;
    color: #fff;
    font-size: 16px;
    position: relative;
    text-align: center;
    line-height: 20px;
    vertical-align: top;
}

#sub_nav_container li a {
    text-decoration: none
}

#sub_nav_container li img {
    border: solid 8px rgba(84, 183, 42, 1);
    margin: -5px 10px 10px 0px;
    position: relative;
}

#sub_nav_container li span {
    display: table-cell;
    vertical-align: middle;
    position: relative;
    width: 155px;
    margin: 0;
    padding: 1px 6px;
    color: #fff;
    line-height: 14px;
    background-color: rgba(84, 183, 42, 1);
    height: 35px;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: bold
}

#sub_nav_container p {
    text-align: left;
    width: 70%;
    margin-left: 15%;
    margin-right: auto;
    padding-bottom: 10px;
}

#sub_nav_container.access li img {
    border: solid 8px rgba(36, 166, 128, 1);
}

#sub_nav_container.access h3 {
    color: rgba(36, 166, 128, 1);
}

#sub_nav_container.access li span {
    background-color: rgba(36, 166, 128, 1);
}

#sub_nav_container.care_maintenance li img {
    border: solid 8px rgba(6, 154, 175, 1);
}

#sub_nav_container.care_maintenance h3 {
    color: rgba(6, 154, 175, 1);
}

#sub_nav_container.care_maintenance li span {
    background-color: rgba(6, 154, 175, 1);
}

#secondary_nav {
    border-top: solid 1px rgba(120, 57, 128, 1.00);
    margin: 0px 50px 0px 60px;
}

#secondary_nav div {
    display: block;
    width: 100%;
}

#secondary_nav div:last-of-type {
    width: 100%;
}

#secondary_nav h4 {
    background: none;
    color: rgba(120, 57, 128, 1.00);
    text-transform: uppercase;
    padding: 0;
    font-size: 14px;
    letter-spacing: 4px;
    border: none;
    font-weight: bold;
    margin: 10px 0 0px 0px;
    width: 260px;
}

#secondary_nav .col {
    display: inline-block;
    float: left;
    list-style: none;
    padding: 0 0px 0 0px;
    color: rgba(89, 43, 95, 1);
    text-decoration: none;
    font-size: 14px;
    line-height: 18px;
    margin-top: -3px;
    width: 100%;
}

#secondary_nav a {
    color: rgba(120, 57, 128, 1.00);
    text-decoration: none;
}

#secondary_nav a:hover {
    color: rgba(173, 173, 173, 1);
    background: url(../img/bullets/small_purple_arrow.png) 0 center no-repeat;
    display: inline-block;
    position: relative;
    margin-left: -10px;
    padding-left: 10px;
}

#secondary_nav .col_2 {
    width: 385px;
    float: left;
}

#bpv {
    margin: 0px 0 0px 60px;
    color: rgba(36, 166, 128, 1);
}

#bpv p:first-of-type {
    font-size: 20px;
    font-style: normal;
    text-transform: uppercase;
    letter-spacing: 4px;
    margin-top: 20px;
    margin-bottom: -10px;
}

#bpv p {
    font-style: normal;
    font-size: 15px;
    line-height: 18px;
}

#bpv p:last-of-type {
    font-style: italic;
    margin-top: 20px;
    font-size: 14px;
}

#product_selection #bpv ul.col {
    margin: 0px 0 0px -20px;
    padding: 0;
    min-height: 40px;
    list-style: none;
    display: block;
    line-height: 28px;
}

#bpv a {
    color: rgba(36, 166, 128, 1);
}

#bpv a:hover {
    color: rgba(120, 57, 128, 1);
    text-decoration: none;
    display: inline-block;
    position: relative;
    margin-left: -10px;
    padding-left: 10px;
}

#bpv h4 {
    font-weight: bold;
    color: rgba(36, 166, 128, 1);
    margin-top: -5px;
}

#bpv span {
    font-style: normal;
    font-weight: bold;
    color: rgba(36, 166, 128, 1);
}

/* Base and Product category page*/
.cat-nav {
    margin: 0;
    padding: 0;
}

.cat-nav #submenu {
    padding: 0;
    margin: 0;
}

.cat-nav #submenu li {
    padding: 0;
    margin: 0;
}

.cat-nav li {
    width: 230px;
    list-style: none;
    display: -moz-inline-box;
    font-size: 15px;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
    _float: left;
}

.cat-nav a {
    padding: 0 0 0 11px;
    min-width: 225px;
    margin: 0;
    color: #007079;
    text-decoration: none
}

.cat-nav a:hover {
    color: rgba(120, 57, 128, 1);
    padding: 0 0 0 11px;
    text-decoration: none
}

.twocol-nav li {
    width: 325px;
    list-style: none;
    padding: 0 0 5px 0;
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
}

#content_hldr.category_page li.selected a {
    color: #582c5f;
    cursor: default;
    font-weight: bold;
}

#content_hldr.category_page #main h2 {
    color: #005e86;
    /*padding:0 0 0 11px;*/
    margin: 80px 50px 20px 0;
    font-size: 22px;
    font-weight: normal;
}

#content_hldr.category_page #main {
    min-height: 0px;
}

#content_hldr.category_page #main ul h4 {
    color: #4a5c58;
    margin: 0 0 5px 0;
    padding: 0 0 0 14px;
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
    border: none
}

#content_hldr.category_page #main ul.pdf {
    padding: 5px 8px 20px 40px;
    margin: 0;
    list-style: url(../img/blts/pdficon_small.gif);
}

#content_hldr.category_page #main ul.pdf li {
    padding: 0;
    _padding: 0 0 0 10px;
}

#content_hldr.category_page #main .infobox h4 {
    background: none;
    color: #374F5E;
    padding: 0;
    font-size: 14px;
    border: none;
    margin-bottom: 5px;
}

#content_hldr.category_page #main ul.last {
    margin: 0 0 20px 14px;
    padding: 23px 0 0 0;
}

#content_hldr.category_page #main ul.narrow {
    width: 275px;
}

#content_hldr.category_page #main ul.xnarrow {
    width: 250px;
}

#content_hldr.category_page #main ul.xxnarrow {
    width: 225px;
}

#content_hldr.category_page #main ul.wide {
    width: 350px;
}

#content_hldr.category_page #main ul.xwide {
    width: 375px;
}

#content_hldr.category_page #main ul.xxwide {
    width: 400px;
}

.ui-tabs-vertical .ui-tabs-nav {
    padding: .2em .1em .2em .2em;
}

.ui-tabs-vertical .ui-tabs-nav li {
    clear: left;
    width: 100%;
    margin: 0 -1px .2em 0;
}

.ui-tabs-vertical .ui-tabs-nav li a {
    display: block;
}

.ui-tabs-vertical .ui-tabs-nav li.ui-tabs-active {
    padding-bottom: 0;
    padding-right: .1em;
    border-right-width: 1px;
    border-right-width: 1px;
}

.ui-tabs-vertical .ui-tabs-panel {
    padding: 1em;
}

/* section adjustments */
/* About Us page */
#content_hldr.category_page.about_cat {}

#content_hldr.category_page.about_cat #content {}

#content_hldr.category_page.about_cat #content #category_nav {
    margin: 20px 0 0 21px;
    color: #41514e;
    height: 150px;
    display: block;
}

#content_hldr.category_page.about_cat #content #category_nav .cat-nav {
    width: 210px;
}

#content_hldr.category_page.about_cat .yui-nav li {
    width: 225px;
    list-style: none;
    padding: 0 0 5px 0;
    display: block;
}

#content_hldr.category_page.about_cat #content #main.infobox {
    padding: 20px;
    margin: 0 0 20px 0;
    background: #edf9ff;
    border: 1px solid #dfdfdf;
}

#content_hldr.category_page.about_cat #content #main.infobox p {
    padding-bottom: 0;
    margin-bottom: 0;
    clear: none
}

#content_hldr.category_page.about_cat #content #main.infobox ul {
    list-style: none;
    float: left;
    margin: 0;
    padding: 0;
}

#content_hldr.category_page.about_cat #content #main.infobox ul li {
    margin: 0;
    padding: 0;
    width: 125px;
}

#content_hldr.category_page.about_cat #content #main.infobox ul.columns li {
    margin: 0 15px 0 0;
    padding: 0;
    width: 125px;
    float: left;
    display: inline;
}

#about_us h2, #home_office h2, #customer_service h2, #clinical_support h2, #contact_hr h2 {
    margin: 40px 0 0px 0;
    padding: 0;
    font-size: 20px;
    font-weight: bold;
    color: rgba(120, 57, 128, 1);
    border-bottom: 1px solid rgba(120, 57, 128, 1);
    text-transform: uppercase
}

.two_column {
    -webkit-column-count: 2;
    /* Chrome, Safari, Opera */
    -moz-column-count: 2;
    /* Firefox */
    column-count: 2;
    -webkit-column-gap: 20px;
    /* Chrome, Safari, Opera */
    -moz-column-gap: 20px;
    /* Firefox */
    column-gap: 20px;
    display: inline-block;
}

.three_column {
    -webkit-column-count: 3;
    /* Chrome, Safari, Opera */
    -moz-column-count: 3;
    /* Firefox */
    column-count: 3;
    -webkit-column-gap: 20px;
    /* Chrome, Safari, Opera */
    -moz-column-gap: 20px;
    /* Firefox */
    column-gap: 20px;
    display: inline-block;
}

.two_column p, .three_column p, .three_column div {
    margin-top: 0px;
    -webkit-column-break-inside: avoid;
    page-break-inside: avoid;
    break-inside: avoid;
}

.three_column div img {
    width: 100%;
}

/* Resource page */
#content_hldr.category_page.resource_cat {}

#content_hldr.category_page.resource_cat #content {}

#content_hldr.category_page.resource_cat #content #category_nav {
    margin: 20px 0 0 21px;
    color: #41514e;
    height: 150px;
    display: block;
}

#content_hldr.category_page.resource_cat #content #category_nav .cat-nav {
    width: 210px;
}

#content_hldr.category_page.resource_cat #content #main .row {
    display: block;
}

#content_hldr.category_page.resource_cat #content #main h3 {
    margin: 0 0 10px 0;
}

#content_hldr.category_page.resource_cat #content #main h3 .showstate img {
    padding: 7px 8px 2px 0;
    _padding: 4px 8px 2px 0;
}

#content_hldr.category_page.resource_cat #content #main .title {
    margin: 0 0 10px 35px;
}

#content_hldr.category_page.resource_cat #content #main .title .showstate img {
    padding: 7px 8px 2px 0;
    _padding: 4px 8px 2px 0;
    margin: 0 0 0 30px;
}

/*Resouce Literature page*/
#resource_container {
    margin: 0 0 0 75px;
}

#resource_container ul {
    margin: 10px 0 20px 0;
}

#resource_container ul li {
    margin: 0 0 0 -15px;
}

#resource_container li.pdf {
    list-style: url(../img/blts/pdficon_small.gif);
}

#resource_container h3 {
    margin: 0 0 10px 75px;
    font-size: 36px;
}

#resource_container h4 {
    padding: 25px 0 10px 0px;
    color: gray;
    margin: 0;
    text-align: left;
    font-size: 20px;
    font-weight: bold;
}

#resource_container .product_title {
    color: rgba(84, 183, 42, 1);
    margin: 0;
    font-weight: bold;
    text-align: left;
    font-size: 18px;
    font-weight: lighter;
}

#resource_container a:link {
    text-decoration: none;
    color: gray;
}

#resource_container a {
    text-decoration: none;
    color: purple;
}

#resource_container a:hover {
    text-decoration: none;
    color: purple;
}

/* Patient info page */
#content_hldr.category_page.patient_info #content {}

#content_hldr.category_page.patient_info #content #category_nav {
    margin: 20px 0 0 21px;
    color: #41514e;
    height: 150px;
    display: block;
}

#content_hldr.category_page.patient_info #content #category_nav .cat-nav {
    width: 210px;
}

#content_hldr.category_page.patient_info .yui-nav li {
    width: 255px;
    list-style: none;
    padding: 0 0 5px 0;
    display: block;
}

#content_hldr.category_page.patient_info #content #main .row {
    display: block;
}

#content_hldr.category_page.patient_info #content #main h3 {
    margin: 0 0 10px 0;
}

#content_hldr.category_page.patient_info #content #main h3 .showstate img {
    padding: 7px 8px 2px 0;
    _padding: 4px 8px 2px 0;
}

#content_hldr.category_page.patient_info #content #main .title {
    margin: 0 0 10px 0;
}

#content_hldr.category_page.patient_info #content #main .title .showstate img {
    padding: 7px 8px 2px 0;
    _padding: 4px 8px 2px 0;
    margin: 0 0 0 30px;
}

#content_hldr.category_page.patient_info #content #main .product_resources {
    margin: 0 0 0 0px;
}

#content_hldr.category_page.patient_info #content #main .product_resources ul {
    margin: 10px 0 20px 0;
}

#content_hldr.category_page.patient_info #content #main .product_resources ul .pdf {
    padding: 0;
    margin: 0;
    list-style: url(../img/blts/pdficon_small.gif);
}

#content_hldr.category_page.patient_info #content #main .product_resources h3 {
    margin: 0 0 0 75px;
}

/* RSS page */
/*#content_hldr.category_page.rss_cat{} */
#content_hldr.category_page.rss_cat #content {}

#content_hldr.category_page.rss_cat #content #category_nav {
    margin: 10px 0 0 245px;
    color: #d6edf0;
    height: 360px;
    display: block;
}

#content_hldr.category_page.rss_cat #content #category_nav p {
    margin: 0 20px 15px 0;
}

#content_hldr.category_page.rss_cat #content #category_nav h3 {
    color: #FFF;
}

#content_hldr.category_page .rss-nav {
    margin: 0;
    padding: 0;
}

#content_hldr.category_page .rss-nav li {
    width: 235px;
    list-style: none;
    padding: 0 0 5px 0;
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
}

#content_hldr.category_page .rss-nav a {
    background: url(../img/icons/rss.gif) 0 0 no-repeat;
    padding: 0 0 0 22px;
    min-width: 205px;
    margin: 0;
    color: #FFF;
    text-decoration: none
}

#content_hldr.category_page .rss-nav li.selected a {
    color: #582c5f;
    background: url(../img/icons/rss.gif) 0 4px no-repeat;
    cursor: default;
}

#content_hldr.category_page .rss-nav li.rss-header {
    width: auto;
    display: block;
    clear: right;
    margin-bottom: 5px;
    padding-bottom: 5px;
}

/* Catalog page */
#content_hldr.category_page .catalog_cat {}

#content_hldr.category_page.catalog_cat .cat-nav li {
    width: 250px;
}

/* Contact Us page */
#content_hldr.category_page.contact_cat {}

#content_hldr.category_page.contact_cat #content {}

#content_hldr.category_page.contact_cat #content #category_nav {
    margin: 20px 0 0 21px;
    color: #41514e;
    height: 180px;
    display: block;
}

#content_hldr.category_page.contact_cat .yui-nav li {
    width: 225px;
    list-style: none;
    padding: 0 0 5px 0;
    display: block;
}

#content_hldr.category_page.contact_cat #content #main div.infobox {
    padding: 20px;
    margin: 0 0 20px 0;
    background: #edf9ff;
    border: 1px solid #dfdfdf;
}

#content_hldr.category_page.contact_cat #content #main div.infobox p {
    padding-bottom: 0;
    margin-bottom: 0;
    clear: none
}

#content_hldr.category_page.contact_cat #content #main div.infobox ul {
    list-style: none;
    float: left;
    margin: 0;
    padding: 0;
}

#content_hldr.category_page.contact_cat #content #main div.infobox ul li {
    margin: 0;
    padding: 0;
    width: 125px;
}

#content_hldr.category_page.contact_cat #content #main div.infobox ul.columns li {
    margin: 0 15px 0 0;
    padding: 0;
    width: 125px;
    float: left;
    display: inline;
}

#content_hldr.category_page #main #contact_hldr {
    padding: 0;
    margin: 0
}

#content_hldr.category_page #main #contact_hldr #location_map {
    float: right;
    padding: 0;
    margin: -20px -20px 0 30px;
}

#contact_hldr h2 {
    display: none;
}

/* Columns */
#content_hldr.category_page #main .column {
    width: 290px;
    float: left;
    margin: 5px 17px 0 0;
    padding: 0;
    display: inline;
    clear: none;
}

#content_hldr.category_page #main .last {
    margin-right: 0;
}

#content_hldr.category_page #main .column img {
    border: 1px solid #4A5C58;
}

#content_hldr.category_page #main ul {
    padding: none;
}

/*Class determined by the first word of the category*/
#content_hldr.category_page #main div.implanted {}

#content_hldr.category_page #main div.booklets {}

/*Related Items*/
#related_wrapper {
    display: block;
    margin: 0;
    min-height: 75px;
    position: relative;
    text-align: right;
}

#related_hldr {
    padding: 0;
}

#related {
    position: relative;
}

#related ul {
    list-style: none;
    margin: 0;
    padding: 0;
    position: relative;
    right: 15px;
}

#related ul li {
    margin: 0 0 0 3px;
    vertical-align: top;
    padding: 0;
    width: 200px;
    height: 75px;
    position: relative;
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF, Opera, Safari */
    zoom: 1.0;
    *display: inline;
    /*IE*/
}

#related ul li h4 {
    background: rgba(36, 166, 128, 1);
    color: #fff;
    font-size: 12px;
    line-height: 15px;
    padding: 0 0 0 10px;
    margin: -15px 0 0 -3px;
    border-left: 3px solid white;
    width: 190px;
    height: 15px;
    text-align: left;
}

#related ul li.catalog h4 {
    border-left: medium none;
    margin: -15px 0px 0px 0px;
    background: rgba(84, 183, 42, 1);
}

#related ul li.catalog p a {
    background: rgba(84, 183, 42, .85);
}

/*Related - homepage*/
#related.home ul li:first-of-type {
    display: none;
}

#related ul li:first-of-type h4 {
    background: rgba(84, 183, 42, 1);
    border: medium none;
    margin: -15px 0 0 0;
}

#related ul li:last-of-type h4 {
    background: rgba(6, 154, 175, 1);
}

#related ul li h4 a {
    color: #fff;
    text-decoration: none;
    border: none;
    text-transform: uppercase;
}

#related ul li h4 a:visited:hover, #related ul li h4 a:visited, related ul li h4 a:hover {
    color: #fff;
}

#related ul li p {
    display: block;
    font-size: 16px;
    text-transform: lowercase;
    font-style: italic;
    line-height: 18px;
    color: #7f7f7f;
    margin: 0px;
}

#related ul li p a {
    background: url(../img/related/nav_chip_related.png) bottom right no-repeat;
    background-color: rgba(36, 166, 128, .85);
    color: #fff;
    display: table-cell;
    vertical-align: middle;
    width: 180px;
    height: 75px;
    padding: 0 10px;
    text-align: center;
    text-decoration: none;
    line-height: none;
    overflow: hidden;
    transition: opacity .5s ease;
    opacity: 0;
}

#related ul li:first-of-type p a {
    background-color: rgba(84, 183, 42, .85);
}

#related ul li:last-of-type p a {
    background-color: rgba(6, 154, 175, .85);
}

#related ul li p a:visited:hover, #related ul li p a:visited {
    color: #fff
}

#related ul li p a:hover {
    color: #fff;
    transition: opacity .5s ease;
    opacity: 1;
}

#related ul li img.icon {
    float: left;
    display: inline;
    margin: 0 15px 80px 15px;
}

/*Related Items- Imaging*/
#related ul li.prevue {
    background: url(../img/related/prevue_related.jpg) 0px bottom no-repeat;
}

#related ul li.prevue_green {
    background: url(../img/related/prevue_green_related.jpg) 0px bottom no-repeat;
}

#related ul li.prevue_bluegreen {
    background: url(../img/related/prevue_bluegreen_related.jpg) 0px bottom no-repeat;
}

#related ul li.sherlock3cg {
    background: url(../img/related/sherlock_3cg_related.jpg) 0px bottom no-repeat;
}

#related ul li.sherlock3cg_bluegreen {
    background: url(../img/related/sherlock_3cg_bluegreen_related.jpg) 0px bottom no-repeat;
}

#related ul li.visionII_bluegreen {
    background: url(../img/related/vision_II_bluegreen.jpg) 0px bottom no-repeat;
}

#related ul li.visionII {
    background: url(../img/related/vision_II.jpg) 0px bottom no-repeat;
}

#related ul li.sherlock {
    background: url(../img/related/sherlock_related.jpg) 0px bottom no-repeat;
}

/*Related Items-Access Devices*/
#related ul li.powerglide {
    background: url(../img/related/powerglide_related.jpg) 0px bottom no-repeat;
}

#related ul li.powerpicc {
    background: url(../img/related/powerpicc.png) 0px bottom no-repeat;
}

#related ul li.powerpicc_green {
    background: url(../img/related/powerpicc_green.jpg) 0px bottom no-repeat;
}

#related ul li.powerpicchf_green {
    background: url(../img/related/powerpicchf_green.jpg) 0px bottom no-repeat;
}

#related ul li.powerpiccft_green {
    background: url(../img/related/powerpiccft_green.jpg) 0px bottom no-repeat;
}

#related ul li.powerpiccsv {
    background: url(../img/related/powerpiccsv.jpg) 0px bottom no-repeat;
}

#related ul li.powerhohn_map {
    background: url(../img/related/powerhohn-map.jpg) 0px 0 no-repeat;
}

#related ul li.powerhohn_petition {
    background: url(../img/related/powerhohn-nurse.jpg) 0px 0 no-repeat;
}

#related ul li.powerhohn_syllabus {
    background: url(../img/related/powerhohn-syllabus.jpg) 0px 15px no-repeat;
}

/*Related Items-Care and Maintenance*/
#related ul li.maxbarrier {
    background: url(../img/related/maximal_barrier.jpg) 0px 15px no-repeat;
}

#related ul li.guardiva {
    background: url(../img/related/guardiva_related.jpg) 0px no-repeat;
}

#related ul li.sitescrub {
    background: url(../img/related/site_scrub_related.jpg) right bottom no-repeat;
}

#related ul li.sitescrub_bluegreen {
    background: url(../img/related/site_scrub_related_bluegreen.jpg) right bottom no-repeat;
}

#related ul li.sitescrub_blue {
    background: url(../img/related/site_scrub_related_blue.jpg) right bottom no-repeat;
}

#related ul li.introducers {
    background: url(../img/related/introducers_related.jpg) right bottom no-repeat;
}

#related ul li.port_access_kit {
    background: url(../img/related/port_access_kit_related.jpg) bottom right no-repeat;
}

#related ul li.port_access_kit_bluegreen {
    background: url(../img/related/port_access_kit_bluegreen.jpg) bottom right no-repeat;
}

/*Related Items-Statlock*/
#related ul li.statlock {
    background: url(../img/related/statlock.jpg) right bottom no-repeat;
}

#related ul li.statlock_dressing_blue {
    background: url(../img/related/statlock_dressing_blue.jpg) right bottom no-repeat;
}

/*Related Items-Needles*/
#related ul li.microez {
    background: url(../img/related/microez_related.jpg) 0px bottom no-repeat;
}

#related ul li.winged {
    background: url(../img/related/winged_related.jpg) bottom right no-repeat;
}

#related ul li.huberplus {
    background: url(../img/related/huberplus_related.jpg) bottom right no-repeat;
}

#related ul li.miniloc {
    background: url(../img/related/miniloc_related.jpg) bottom right no-repeat;
}

#related ul li.liftloc {
    background: url(../img/related/liftloc_related.jpg) bottom right no-repeat;
}

#related ul li.safestep {
    background: url(../img/related/safestep_related.jpg) 0px bottom no-repeat;
}

#related ul li.powerloc {
    background: url(../img/related/powerloc_related.jpg) 0px bottom no-repeat;
}

#related ul li.powerloc_max {
    background: url(../img/related/powerloc_max_related.jpg) 0px bottom no-repeat;
}

#related ul li.powerloc_max_blue {
    background: url(../img/related/powerloc_max_related_blue.jpg) 0px bottom no-repeat;
}

#related ul li.powerloc_blue {
    background: url(../img/related/powerloc_blue.jpg) 0px bottom no-repeat;
}

/*Related Items-Company*/
#related ul li.criticalchoice_site {
    background: url(../img/related/criticalchoice_website.jpg) 0px bottom no-repeat;
}

#related ul li.faqs {
    background: url(../img/related/FAQs-related.jpg) 0px bottom no-repeat;
}

#related ul li.catalog {
    background: url(../img/related/product_catalog.jpg) 0px bottom no-repeat;
}

#related ul li.sales {
    background: url(../img/bkgrds/related_sales.jpg) bottom right no-repeat;
}

#related ul li.careers {
    background: url(../img/related/careers_related.jpg) bottom right no-repeat;
}

#related ul li.careers_alt {
    background: url(../img/related/careers_alt_related.jpg) bottom right no-repeat;
}

#related ul li.tradeshow {
    background: url(../img/related/tradeshow_related.jpg) bottom right no-repeat;
}

#related ul li.customer_service_girl {
    background: url(../img/related/customer_service_girl_related.jpg) bottom right no-repeat;
}

#related ul li.customer_service_boy {
    background: url(../img/related/customer_service_boy_related.jpg) bottom right no-repeat;
}

#related ul li.programs {
    background: url(../img/bkgrds/related_programs.jpg) bottom right no-repeat;
}

#related ul li.education {
    background: url(../img/related/education_related.jpg) bottom right no-repeat;
}

#related ul li.iapp {
    background: url(../img/related/iapp_related.jpg) bottom right no-repeat;
}

#related ul li.powerports {
    background: url(../img/related/powerports_related.jpg) right bottom no-repeat;
}

#related ul li.nonpowerports {
    background: url(../img/related/nonpowerports_related.jpg) right bottom no-repeat;
}

#related ul li:nth-of-type(even) {
    background-color: rgba(209, 236, 231, 1);
}

#related ul li:first-of-type {
    background-color: rgba(202, 240, 212, 1);
}

#related ul li:last-of-type {
    background-color: rgba(220, 235, 240, 1);
}

#promo {
    margin: 0;
    padding: 10px 0 0 0;
    display: block;
    background: url(../img/bkgrds/promo_bkgrd.gif) bottom left no-repeat #E1ECF2;
}

#promo ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: block;
    position: relative;
}

#promo ul li {
    margin: 0 1px 0 0;
    _margin: 0 6px 0 0;
    padding: 0;
    border: none;
    background: none;
    width: 243px;
    height: 120px;
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
}

#promo ul li.last {
    margin: 0;
}

#promo ul li a {
    height: 90%;
    display: block;
    margin: 0 0 0 10px;
    padding: 0 0 0 25px;
    text-decoration: none;
    color: #FFF;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
}

#content_hldr.maintenance_page {
    background: #FFF;
}

#content_hldr.maintenance_page #cat_title {
    height: 45px;
}

#content_hldr.maintenance_page #content {
    background: url(../img/bkgrds/maintenance_bkgrd.jpg) top left no-repeat;
    display: block;
    margin: 0;
    padding: 20px;
    max-width: 990px;
    border-radius: 0 0 10px 10px;
    -webkit-border-radius: 0 0 10px 10px;
    /* for Safari */
    -moz-border-radius: 0 0 10px 10px;
    /* for Firefox */
}

#content_hldr.maintenance_page #col_hldr, #content_hldr.maintenance_page #pagecontent h2.page_title, #content_hldr.maintenance_page #return {
    display: none;
}

#content_hldr.maintenance_page #main {
    margin: 0 0 0 125px;
    color: #41514e;
    height: 250px;
    display: block;
}

#content_hldr.maintenance_page #main h2 {
    padding-top: 50px
}

/* PAGE CSS */
/*Overview*/
#overview .tagline {
    margin: 0 0 20px 0;
    font-size: 14px;
    font-weight: bold;
    width: 200px;
    display: inline-block;
}

#overview img {
    float: right;
    position: relative;
}

#overview img.nologo {
    float: right;
    position: relative;
    margin-top: 0;
    margin-bottom: 10px;
}

#overview ul {
    margin: 0 20px 30px 0;
    padding: 0 0 0 40px;
    z-index: 1;
    position: relative;
    min-height: 305px;
}

#overview ul.nologo {
    margin: 0 20px 0 0;
    padding: 40px 0 0 0;
}

#overview ul li {
    list-style: none;
    margin: 0;
    padding: 0 0 15px 0;
    line-height: 20px;
}

#overview ul.nobullet li {
    line-height: 30px;
}

/*Overview P2 transition*/
#overview p:first-of-type {
    font-size: 25px;
    font-style: italic;
    line-height: 30px;
    padding: 50px 0 0 40px;
    color: white;
}

#overview .sizes {
    margin: 0;
    padding: 7px 0 0 0;
    _padding: 3px 0 0 0;
    border-top: 1px solid #582c5f;
    clear: right;
}

#overview .bottom_box {
    margin: 10px -40px -40px;
    padding: 15px 80px;
    _padding: 15px 80px;
    clear: right;
    background-color: #FFFFFF;
    opacity: 0.3;
    color: black;
}

#overview .sizes span {
    margin-right: 8px;
}

#overview p.values {
    font-size: 24px;
    color: rgba(120, 57, 128, 1);
    letter-spacing: 3px;
    padding: 0px;
    text-align: center;
    width: 100%;
}

#overview h3 {
    font-size: 26px;
    color: white;
}

#overview p.about_statement {
    padding: 20px;
    font-size: 24px;
    color: white;
    font-weight: lighter;
    font-style: italic;
    text-align: center;
    line-height: 30px;
    background: rgb(226, 238, 184);
    /* Old browsers */
    background: -moz-linear-gradient(left, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(226, 238, 184, 1)), color-stop(25%, rgba(136, 211, 154, 1)), color-stop(50%, rgba(142, 208, 176, 1)), color-stop(75%, rgba(133, 202, 191, 1)), color-stop(100%, rgba(110, 190, 196, 1)));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(left, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(left, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(left, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
    /* IE10+ */
    background: linear-gradient(to right, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e2eeb8', endColorstr='#6ebec4', GradientType=1);
    /* IE6-8 */
}

#overview div.two_column p {
    font-size: 16px;
    color: #4d4d4f;
    font-style: normal;
    line-height: normal;
    padding: 0;
}

.background-purple {
    padding: 40px;
    margin: -25px;
    background: rgba(126, 83, 163, 1);
    /* Old Browsers */
    background: -moz-radial-gradient(70% 40px, ellipse cover, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(radial, 70% 40px, 0px, center center, 100%, color-stop(0%, rgba(126, 83, 163, 1)), color-stop(100%, rgba(77, 30, 117, 1)));
    /* Chrome, Safari4+ */
    background: -webkit-radial-gradient(70% 40px, ellipse cover, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-radial-gradient(70% 40px, ellipse cover, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-radial-gradient(70% 40px, ellipse cover, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
    /* IE 10+ */
    background: radial-gradient(ellipse at 70% 40px, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#7e53a3', endColorstr='#4d1e75', GradientType=1);
    /* IE6-9 fallback on horizontal gradient */
}

.background-blue {
    padding: 40px;
    margin: -25px;
    background: rgba(0, 104, 167, 1);
    /* Old Browsers */
    background: -moz-radial-gradient(70% 40px, ellipse cover, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(radial, 70% 40px, 0px, center center, 100%, color-stop(0%, rgba(55, 156, 219, 1)), color-stop(100%, rgba(0, 104, 167, 1)));
    /* Chrome, Safari4+ */
    background: -webkit-radial-gradient(70% 40px, ellipse cover, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-radial-gradient(70% 40px, ellipse cover, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-radial-gradient(70% 40px, ellipse cover, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
    /* IE 10+ */
    background: radial-gradient(ellipse at 70% 40px, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#379CDB', endColorstr='#0068a7', GradientType=1);
    /* IE6-9 fallback on horizontal gradient */
    ;
}

.background-orange {
    padding: 40px;
    margin: -25px;
    background: rgba(230, 106, 32, 1);
    /* Old Browsers */
    background: -moz-radial-gradient(70% 40px, ellipse cover, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
    /* FF3.6+ */
    background: -webkit-gradient(radial, 70% 40px, 0px, center center, 100%, color-stop(0%, rgba(255, 136, 45, 1)), color-stop(100%, rgba(230, 106, 32, 1)));
    /* Chrome, Safari4+ */
    background: -webkit-radial-gradient(70% 40px, ellipse cover, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-radial-gradient(70% 40px, ellipse cover, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
    /* Opera 11.10+ */
    background: -ms-radial-gradient(70% 40px, ellipse cover, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
    /* IE 10+ */
    background: radial-gradient(ellipse at 70% 40px, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FF882D', endColorstr='#E66A20', GradientType=1);
    /* IE6-9 fallback on horizontal gradient */
    ;
}

.subtitle_block_blue {
    padding: 20px 25px;
    margin-left: -25px;
    background-color: rgba(15, 118, 181, 1);
    clear: both;
    display: inline-block;
    float: left;
}

.content_block_blue {
    padding: 20px 30px;
    margin-right: -25px;
    width: 60%;
    background-color: rgba(15, 118, 181, 1);
    color: rgba(255, 255, 255, 1);
}

.content_block_blue h4 {
    color: rgba(255, 255, 255, 1);
}

/*Features, Training tab, Pinpoint*/
#features, #training, #pinpoint {
    margin: 0 0 25px 0;
}

#features ul.inline-list, #training ul.inline-list, #pinpoint ul.inline-list {
    margin: 0;
    padding: 0;
}

#features ul.inline-list li, #training ul.inline-list li, #pinpoint ul.inline-list li {
    display: inline;
    padding-right: 20px;
}

#features ul li, #training ul li, #pinpoint ul li {
    margin-bottom: 20px;
}

#features ul.single-space li, #training ul.single-space li, #pinpoint ul.single-space li {
    margin-bottom: 0;
}

#specifications, #kit_components {
    overflow: scroll
}

#features .headline_section, #sr1 .headline_section, #sr2 .headline_section {
    background-image: url(../img/bkgrds/feature_triangles.png) no-repeat;
    padding: 5px 25px;
    margin: 45px -25px 10px 0px;
}

#features .headline_section p, #sr1 .headline_section p, #sr2 .headline_section p {
    color: rgba(255, 255, 255, 1)
}

#features .headline_section.purple, #sr1 .headline_section.purple, #sr2 .headline_section.purple {
    background-color: rgba(126, 83, 163, 1)
}

.feature_headline {
    font-size: 30px;
    font-style: italic;
    line-height: 35px;
    margin-top: 40px;
    color: white;
    font-weight: lighter;
}

/*Product Specs*/
#specs.tabcontent {
    padding: 0 0 40px 0
}

#specs h4 {
    margin: 30px 0 10px 0;
    padding: 0;
    color: #374f5e;
}

#specs .sub_header {
    margin: 0;
    padding: 0;
}

#specs .text-reference {
    margin-left: 20px;
}

#specs .note {
    margin-left: 5px;
    font-style: italic;
    font-size: 8pt
}

#product_specs .note {
    margin: -15px 0 30px 20px;
}

.jtubel {
    background-color: #FFFFFF;
    border-left: 3px solid #666666;
    vertical-align: middle;
}

.jtuber {
    background-color: #FFFFFF;
    vertical-align: middle;
}

#specs .wide {
    width: 420px;
}

/*Kit Components*/
#kit_components tr td {
    font-size: 40px;
    text-align: center;
    padding: 5px 0
}

#kit_components tr td:first-of-type {
    padding-left: 11px;
    font-size: 14px;
    text-align: left;
}

#kit_components .text tr td:nth-child(n+2) {
    font-size: 14px;
}

/*Comparison*/
#comparison {
    overflow: scroll
}

#comparison.tabcontent {
    padding: 0 0 40px 0
}

#comparison h4 {
    margin: 10px 0 0 0;
}

#comparison .text tr td {
    font-size: 14px;
    text-align: center;
}

#comparison tr td {
    font-size: 40px;
    text-align: center;
    padding: 5px 0
}

#comparison tr td:first-of-type {
    padding-left: 11px;
    font-size: 14px;
    text-align: left;
}

/*Resources tab*/
#literature ul#product_literature li {
    list-style: url(../img/blts/pdficon_small.gif);
    line-height: 32px;
}

#literature ul#product_literature li.video {
    list-style: url(../img/blts/media.gif);
}

#literature ul#product_literature li.plain {
    list-style: disc;
    list-style-image: none;
}

/*Videos tab*/
#video {}

#vid_player {
    margin: 20px 0 20px 5px;
    width: 711px;
    background: #d6d6d6;
    padding: 0 0 10px 0;
    line-height: 0;
    border: 1px solid #838282;
}

/*FAQ tab*/
#expandable_faq h4 {
    border: none;
    margin: 45px 0 5px 0;
}

#expandable_faq #expand {
    background: #dff2e0;
    border: 1px solid #dfdfdf;
    padding: 4px 7px;
    float: right;
}

#expandable_faq .faq_box {
    padding: 5px 20px 15px 20px;
    margin-bottom: -1px;
    background-color: #dff2e0;
    border: 1px solid #DFDFDF
}

#expandable_faq .faq, #expandable_faq .switchcontent {
    margin-left: 20px;
}

#expandable_faq .showstate {
    margin: 0 5px 0 -25px;
}

#expandable_faq .showstate img {
    padding: 3px 0 0 0;
}

/* Search Results page*/
#results {
    background: url(../img/bkgrds/bkgrd_border_line.gif) 630px 0 repeat-y;
    margin: 0 0 30px 0;
    display: block;
    min-height: 250px;
    _height: 250px;
}

.search_results .page_results {
    float: left;
    padding-left: 0px;
    padding-right: 0px;
}

.search_results .page_results table {
    width: 85%;
}

.search_results .catalog_results {
    float: left;
    padding-left: 0px;
    padding-right: 0px;
}

.search_results_inside {
    padding-right: 20px;
}

#results i {
    color: #FF4800;
    margin-top: 10px;
}

#results td {
    padding-bottom: 15px;
}

.search_results ul#product_literature {
    margin: 4px 20px 4px 0;
    padding: 0;
}

.search_results ul#product_literature li {
    display: inline;
    white-space: nowrap;
    list-style: none;
    line-height: 23px;
}

.search_results ul#product_literature a {
    padding: 3px 5px;
    font-size: 9px;
    font-family: 'Lato', Verdana, sans-serif;
    font-weight: bold;
    background: #edf9ff;
    border: 1px solid #dfdfdf;
    color: #50748c;
    text-decoration: none;
}

.search_results ul#product_literature a:hover {
    background: #6595B4;
    color: #FFFFFF
}

/*Search page*/
#adv_search_tab {
    background: url(../img/bkgrds/adv_search_tab_bkgrd.jpg) left 0 no-repeat;
    font-size: 14px;
    margin: 0 0 -4px 0;
    display: block;
    text-align: left;
    padding: 4px 0 0 20px;
    color: #325081;
    height: 28px;
    cursor: hand;
    cursor: pointer;
    z-index: 100;
}

#advanced_search {}

.result_hdr {
    color: #FFF;
    font-size: 8pt;
    background-color: #6595B4;
    padding: 2px 5px;
    margin-top: 0;
    display: block;
    margin-bottom: 15px;
    border-radius: 0 0 5px 5px;
    -webkit-border-radius: 0 0 5px 5px;
    /* for Safari */
    -moz-border-radius: 0 0 5px 5px;
    /* for Firefox */
}

.result_hdr p {
    margin: 0;
}

/*Sitemap bullet lists - use same list items from product sub-nav with a header of .hdr class*/
#sitemap {
    padding: 0;
}

#sitemap a, #sitemap a:link {
    color: rgba(0, 0, 0, .55);
    white-space: nowrap
}

#sitemap a:visited:hover {
    color: rgba(120, 57, 128, 1);
}

#sitemap a:visited {
    color: rgba(0, 0, 0, .55);
}

#sitemap a:hover {
    color: rgba(120, 57, 128, 1);
}

#sitemap h2 {
    margin: 64px 0 0px 0;
    padding: 0;
    font-size: 20px;
    font-weight: bold;
    color: rgba(120, 57, 128, 1);
    border-bottom: 1px solid rgba(120, 57, 128, 1);
    text-transform: uppercase
}

#sitemap h2:first-of-type {
    margin: 0;
}

#sitemap h2 a, #sitemap h2 a:link, #sitemap h2 a:visited:hover, #sitemap h2 a:visited, #sitemap h2 a:hover {
    text-decoration: none;
    color: rgba(120, 57, 128, 1);
}

#sitemap h3 {
    font-size: 17px;
    margin-bottom: 6px;
    margin-left: 10px;
    color: rgba(120, 57, 128, 1);
    border-bottom: 1px solid rgba(120, 57, 128, 1);
}

#sitemap ul {
    margin-top: 4px;
    margin-bottom: 4px;
}

#sitemap ul li {
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
}

#sitemap ul li a {
    padding: 0 5px 0 10px;
    margin-right: 5px;
    background: url(../img/bullets/small_purple_arrow.png) 0 center no-repeat;
    text-decoration: none;
}

#sitemap ul li.hdr {
    padding: 2px 0 2px 10px;
    font-size: 18px;
    font-weight: bold;
    color: rgba(0, 0, 0, .55);
    margin: 32px 0 16px -10px;
    white-space: normal;
    display: block;
    list-style: none;
}

#sitemap ul li.hdr a {
    padding-left: 0;
    background: none;
    color: rgba(0, 0, 0, .55);
}

#sitemap ul ul {
    margin: 0 0 0 10px;
    _margin: 0 0 0 23px;
    padding: 0;
}

#sitemap ul ul li.hdr {
    margin-top: 8px;
    padding: 2px 0 2px 13px;
    background: none;
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, .55);
    margin-left: -33px;
    white-space: normal;
    display: block;
    list-style: none;
}

#sitemap ul ul li.hdr a, #sitemap ul ul li.hdr a:link {
    padding-left: 10px;
    background: none;
    color: rgba(0, 0, 0, .55);
    text-decoration: none;
}

#sitemap ul ul li.hdr a:hover {
    color: rgba(120, 57, 128, 1);
}

#sitemap ul ul li.hdr a:visited {
    padding-left: 10px;
    color: rgba(0, 0, 0, .55);
}

#sitemap ul ul li.hdr a:hover {
    color: rgba(120, 57, 128, 1);
}

#sitemap ul ul li {
    font-weight: normal;
    white-space: nowrap;
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
}

#sitemap ul ul li a, #sitemap ul ul li a:link {
    padding: 0 5px 0 10px;
    background: url(../img/bullets/small_purple_arrow.png) 0 center no-repeat;
    text-decoration: none;
    color: rgba(0, 0, 0, .55);
}

#sitemap ul ul li a:visited:hover {
    color: rgba(120, 57, 128, 1);
}

#sitemap ul ul li a:visited {
    color: rgba(0, 0, 0, .55);
}

#sitemap ul ul li a:hover {
    color: rgba(120, 57, 128, 1);
}

#sitemap ul ul .row {
    display: block;
    padding-left: 0;
}

#sitemap .row {
    display: block;
    padding-left: 40px;
}

#sitemap h3.hdr {
    padding: 2px 0 2px 10px;
    font-size: 18px;
    font-weight: bold;
    color: rgba(0, 0, 0, .55);
    margin: 40px 0 25px -10px;
    white-space: normal;
    display: block;
    list-style: none;
    border: none;
}

#sitemap .title {
    margin-top: 8px;
    padding: 2px 0 2px 10px;
    background: none;
    font-size: 15px;
    font-weight: bold;
    color: rgba(0, 0, 0, .55);
    white-space: normal;
    display: block;
    list-style: none;
}

#sitemap .title a, #sitemap .title a:link {
    background: none;
    color: rgba(0, 0, 0, .55);
    text-decoration: none;
}

#salescontact h2 {
    margin: 0 0 20px 0;
    display: inline-block;
    _display: inline;
}

#salescontact h3 {
    font-size: 18px;
    margin: 30px 0 10px 0;
}

#salescontact .info_content {
    padding: 20px;
    margin: 0 0 30px 0;
    background: #f7f7f7;
    border: 1px solid #dfdfdf;
}

#salescontact .info_content td {
    padding: 3px;
}

#salescontact #left_col {
    padding: 10px 0 20px 0;
    vertical-align: top;
    line-height: 20px;
    width: 350px;
    font-size: 11px
}

#salescontact #right_col {
    padding: 10px 0 20px 0;
    vertical-align: top;
    line-height: 20px;
    width: 350px;
    font-size: 11px
}

#salescontact textarea:focus, #salescontact textarea:active {
    width: 655px;
    height: 250px;
}

.submit-btn {
    display: inline-block;
    width: 120px;
    height: 28px;
    background: url(../img/buttons/submit.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.submit-btn:hover {
    background: url(../img/buttons/submit.gif) no-repeat 0 -28px;
}

.reset-btn {
    display: inline-block;
    width: 120px;
    height: 28px;
    background: url(../img/buttons/reset.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
}

.reset-btn:hover {
    background: url(../img/buttons/reset.gif) no-repeat 0 -28px;
}

.infobox input.search-btn, .search-btn {
    width: 120px;
    height: 28px;
    background: url(../img/buttons/search.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
}

.infobox input.search-btn:hover, .search-btn:hover {
    background: url(../img/buttons/search.gif) no-repeat 0 -28px;
}

.continue-btn {
    width: 120px;
    height: 28px;
    background: url(../img/buttons/continue.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.continue-btn:hover {
    background: url(../img/buttons/continue.gif) no-repeat 0 -28px;
}

.back-btn {
    width: 120px;
    height: 28px;
    background: url(../img/buttons/back.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: left;
}

.back-btn:hover {
    background: url(../img/buttons/back.gif) no-repeat 0 -28px;
}

.learn-btn {
    width: 120px;
    height: 24px;
    background: url(../img/buttons/learn_more.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.learn-btn:hover {
    background: url(../img/buttons/learn_more.gif) no-repeat 0 -24px;
}

.infobox input.install-btn, .install-btn {
    width: 120px;
    height: 28px;
    background: url(../img/buttons/install.gif) no-repeat;
    outline: none;
    vertical-align: top;
    border: 0;
    margin: 5px 0 0 0;
    float: right;
}

.infobox input.install-btn:hover, .install-btn:hover {
    background: url(../img/buttons/install.gif) no-repeat 0 -28px;
}

.btn {
    -moz-user-select: none;
    border: 1px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: normal;
    height: auto;
    line-height: 1.42857;
    margin: 0;
    padding: 6px 12px;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    background: #f7f9f9;
    /* Old browsers */
    background: -moz-linear-gradient(top, #f7f9f9 0%, #ccd9d9 100%);
    /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #f7f9f9), color-stop(100%, #ccd9d9));
    /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f7f9f9 0%, #ccd9d9 100%);
    /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f7f9f9 0%, #ccd9d9 100%);
    /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #f7f9f9 0%, #ccd9d9 100%);
    /* IE10+ */
    background: linear-gradient(to bottom, #f7f9f9 0%, #ccd9d9 100%);
    /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f7f9f9', endColorstr='#ccd9d9', GradientType=0);
    /* IE6-9 */
    cursor: pointer;
    border: solid 1.5px #b1c5c5;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    /* for Safari */
    -moz-border-radius: 3px;
    /* for Firefox */
    color: #648686;
    font-size: 13px;
    font-weight: normal;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    border: solid 1px #648686;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.25)
}

.btn.med {
    min-width: 160px;
}

.btn.wide {
    min-width: 200px;
}

/* Registration / Profile / Username Reset forms */
.row, .row_error {
    padding: 3px 0;
    display: inline-block;
}

.pdpform .row {
    width: 45%
}

.row_error {
    color: #FF0000;
    font-weight: bold
}

.cell, .cell_error {
    padding: 0 6px 0 0;
    color: #370615;
    margin: 0;
    float: left;
    display: inline;
    display: inline-block;
    display: inline-table;
}

.cell_error {
    color: #FF0000;
    font-weight: bold;
}

.req {
    color: #FF0000
}

.asterisk {
    font-size: 17px;
    margin: 0 3px 0 0;
    vertical-align: bottom;
}

#trademarks {
    padding: 0 0 0px 0;
}

#trademarks h2 {
    margin: 0 0 15px 0;
    padding: 0;
    font-size: 15px;
    height: 20px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px solid rgba(120, 57, 128, 1);
    text-transform: uppercase
}

.infobox, #log {
    padding: 20px;
    margin: 0 0 20px 0;
    background: rgba(211, 237, 240, 1);
    border: 1px solid #dfdfdf;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    /* for Safari */
    -moz-border-radius: 5px;
    /* for Firefox */
}

.infobox {
    padding: 20px;
    margin: 0 0 20px 0;
    background: #f7f7f7;
    border: 1px solid #dfdfdf;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    /* for Safari */
    -moz-border-radius: 5px;
    /* for Firefox */
}

.infobox p {
    padding-bottom: 0;
    margin-bottom: 0;
}

.infobox hr {
    border: none;
    border-bottom: 1px solid #D0D0D0;
    margin: 30px 0;
}

.infobox h4 {
    margin: 5px 0 20px 0;
    padding: 0;
    font-size: 15px;
    height: 20px;
    font-weight: bold;
    color: #333;
    text-transform: uppercase
}

.infobox ul.category {
    list-style: none;
    display: inline-block;
    margin: 0;
    padding: 0;
}

.infobox ul.category li {
    list-style: none;
    padding: 7px 0 0 0;
    margin: 0;
    display: -moz-inline-box;
    /* Firefox 2 and under*/
    display: inline-block;
    /* FF3, Opera, Safari */
    width: 210px;
    zoom: 1.0;
    *display: inline;
    /*IE 6 and 7*/
}

/* contact us page*/
.infobox ul.columns li {
    margin: 0 15px 0 0;
    padding: 0 0 auto 0;
    width: 125px;
    float: left;
    display: inline;
}

.infobox .threecol {
    width: 550px;
}

.infobox .twocol {
    width: 330px;
}

.infobox .rcolumn {
    margin: 0 0 0 640px;
}

#category_nav #log {
    padding: 0;
    position: relative;
    margin: 0;
    width: 500px;
}

#log ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

#log li.error {
    background: url(../img/icons/warning_32.png) 0 center no-repeat;
    cursor: default;
    padding: 0 10px 0 44px;
    min-height: 40px
}

#log li.msg {
    background: url(../img/icons/tick_32.png) 0 center no-repeat;
    cursor: default;
    padding: 0 0 0 44px;
    min-height: 40px;
}

#log img {
    vertical-align: middle;
    margin-right: 12px;
    float: left;
}

.msg {
    color: green;
}

.image_columns {
    position: relative;
    clear: right;
    float: none;
    padding-bottom: 30px;
}

.image_columns img {
    margin-left: 50px;
    float: right;
}

/*PowerPICC Solo page layout*/
#power span, .power {
    color: #6A2D91;
    font-weight: bold;
    font-size: 14px;
}

#saline span, .saline {
    color: #0079C2;
    font-weight: bold;
    font-size: 14px;
}

#one span {
    color: #000;
    font-weight: bold;
    font-size: 14px;
}

#power li {
    list-style: url(../css/images/blts/power_bullet.gif);
    margin-bottom: 20px;
}

#saline li {
    list-style: url(../css/images/blts/saline_bullet.gif);
    margin-bottom: 20px;
}

#one li {
    list-style: url(../css/images/blts/one_bullet.gif);
    margin-bottom: 20px;
}

.solo2 {
    vertical-align: baseline;
    position: relative;
    bottom: 0.22em;
}

/*Product Catalog*/
#product_catalog {
    margin: 0 0 25px 0;
    display: block;
    position: relative;
    color: rgba(0, 0, 0, 1);
}

#csinfo {
    background: #edf9ff;
    border: 1px solid #dfdfdf;
    padding: 20px;
    margin: 0 0 45px 0;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    /* for Safari */
    -moz-border-radius: 5px;
    /* for Firefox */
}

.catalog-ordering-info {
    cursor: hand;
    float: right;
    margin: -40px 0px 0px 0px;
    cursor: pointer;
    color: #1F5B7F;
}

#product_catalog .infotext {
    font-size: 13px;
}

#product_catalog #reset {
    text-align: right;
}

#product_catalog .catalog_hdr {
    position: relative;
    font-weight: bold;
    color: #FFF;
    font-size: 14px;
    text-transform: uppercase;
    background: #006271;
    padding: 3px 0 3px 15px;
}

#product_catalog .catalog_hdr .r_hdr {
    position: absolute;
    top: 3px;
    right: 10px;
    display: inline;
}

#product_catalog #selection {
    width: 60%;
}

#product_catalog #selection span {
    display: inline-block;
    margin: 0 3px 25px 0
}

#product_catalog select {
    display: block;
    width: 100%;
    font-size: 14px;
    border: 1px solid #dfdfdf;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    /* for Safari */
    -moz-border-radius: 5px;
    /* for Firefox */
}

#product_catalog select option:hover {
    background: #eee;
}

#product_catalog #selection .title, #product_catalog #selection .subtitle {
    color: #374f5e;
    font-size: 14px;
    font-weight: bold
}

#product_catalog #results {
    position: absolute;
    top: 105px;
    right: 0px;
    bottom: 0px;
    width: 35%;
    overflow: auto;
    padding: 0;
    color: #374f5e;
    font-size: 14px;
}

#product_catalog .full {
    width: 100%;
}

#product_catalog .half {
    width: 49%;
}

#product_catalog .matches {
    color: #374f5e;
    font-size: 14px;
    padding: 8px 10px 20px 0;
    font-weight: bold;
}

/*Critical Choice*/
.wave-back {
    background: url(../img/products/ultrasound/supporting/sapien-wave.jpg) center top repeat-x;
}

.3cg-back {
    background: url(../img/products/ultrasound/supporting/Sherlock-3CG-watermark.jpg) center top no-repeat;
}

.col_2 {
    width: 50%;
    display: block;
    float: left;
}

.col_2_right {
    width: 50%;
    display: block;
    float: right;
}

/* The lens overlay effect */
#lens {
    border: 2px solid white;
    /* Positioned absolutely, so we can move it around */
    position: absolute;
    height: 180px;
    width: 180px;
    /* Hidden by default */
    display: none;
    /* A blank cursor, notice the default fallback */
    cursor: url('img/blank.cur'), default;
    /* CSS3 Box Shadow */
    -moz-box-shadow: 0 0 5px #777, 0 0 10px #aaa inset;
    -webkit-box-shadow: 0 0 5px #777;
    box-shadow: 0 0 5px #777, 0 0 10px #aaa inset;
    /* CSS3 rounded corners */
    -moz-border-radius: 90px;
    -webkit-border-radius: 90px;
    border-radius: 90px;
}

#lens.chrome {
    /* A special chrome version of the cursor */
    cursor: url('img/blank_google_chrome.cur'), default;
}

#overlay_hldr {
    /* The main div */
    margin: 0 auto;
    position: relative;
}

.QR_code a {
    color: rgba(0, 0, 0, .55);
    text-decoration: underline;
    font-weight: bold;
}

.QR_code a:visited:hover {
    color: rgba(120, 57, 128, 1)
}

.QR_code a:visited {
    color: rgba(0, 0, 0, .55);
}

.QR_code a:hover {
    color: rgba(120, 57, 128, 1)
}

.info_p {
    color: #2C6D76;
    font-size: 16px;
    margin-bottom: 30px;
}

#clicked-state {
    max-width: 280px;
    background-color: #ddd;
    padding: 30px;
    text-align: center;
    margin: 0 auto;
    -webkit-border-radius: 10px;
    -moz-border-radius: 10px;
    border-radius: 10px;
}

.number p::first-letter {
    font-size: 800%;
    color: #783980;
    font-weight: bolder;
    float: left;
    padding: 5px 10px 15px 10px;
}

.accucath_hero {
    margin: 80px -40px 0px 0px
}

.accucath-list li span {
    font-size: 18px;
    line-height: 24px;
    color: rgba(120, 57, 128, 1);
    font-weight: bold;
}

.accucath-full-color-icon {
    display: none;
}

.accucath-white-icon {
    display: block;
}

.allpoints_dck_hero {
    margin: 25px 0px 0 50px;
    opacity: 0.3;
}

.allpoints_dck_hero_m {
    display: none;
}

.nautilus_hero {
    margin: 100px -20px 90px 30px
}

.nautilus_feature_hero {
    margin: 100px 0px 0px 30px
}

.nautilus figure img {
    margin: 0 auto;
}

.nautilus-adapters {
    font-size: 20px;
    font-style: italic;
    margin-top: 10px;
    color: gray;
    font-weight: lighter;
}

.powerglidepro_hero {
    margin: 80px -20px 20px 20px;
}

.powerglidepro_hand {
    margin: -165px -25px -100px 0;
    position: relative;
}

.powerglidepro {
    min-height: 265px;
}

#site-rite-8 h3 {
    margin: 120px 0 -10px 0;
}

#site-rite-8 p:first-of-type {
    padding: 50px 0 0px 0px;
}

.site_rite_8_product {
    margin: -300px -25px -165px 0;
}

.site-rite-8-hero {
    margin: 50px -40px 0 12px;
}

.site_rite_8_box {
    z-index: 1;
    width: 100%;
    position: relative;
}

.site_rite_8_visualize {
    font-size: 2100%;
    font-family: 'Open Sans ExtraBold';
    z-index: 0;
    color: rgba(213, 213, 213, .35);
    padding: 0;
    margin: 0;
    top: 900px;
    left: -786px;
    position: absolute;
}

.site_rite_8_virtual {
    font-size: 1780%;
    font-family: 'Open Sans ExtraBold';
    z-index: 0;
    color: rgba(213, 213, 213, .35);
    padding: 0;
    margin: 0;
    top: 1100px;
    left: -540px;
    position: absolute;
}

.visualize_right {
    border-top: 3px purple solid;
    border-left: 3px purple solid;
    width: 45%;
    float: right;
    padding: 20px;
}

.visualize_right_img {
    float: left;
    padding-right: 30px;
}

.visualize_left {
    border-top: 3px purple solid;
    border-right: 3px purple solid;
    width: 45%;
    float: left;
    padding: 20px;
}

.visualize_left_img {
    float: right;
    padding-left: 30px;
}

.y_sensor_diamond {
    margin-top: -380px;
}

.visual_tip_confirmation {
    margin-top: 120px;
}

.left_box_flush_green {
    background-color: #499f24;
    float: left;
    margin-left: -25px;
    color: #FFFFFF;
}

.sr8-pinpoint-needle {
    margin-top: -100px;
    float: left;
}

.powermidline_hero {
    margin: 0px -40px 0px 20px;
}

.powerglidepro_blood_control {
    margin: -95px 0px -70px 20px;
    z-index: 2;
}

.powerglidepro_needle_guidewire {
    margin: 0px 0px -50px -25px;
}

.powerglidepro_body_softening {
    margin: -60px 0px 0px -35px;
}
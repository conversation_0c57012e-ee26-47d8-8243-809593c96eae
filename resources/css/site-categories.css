@charset "utf-8";

/* CATEGORY CSS */
/* Category pages */
#content_hldr.category_page #content {
  display: block;
  margin: -15px 0 0 0;
  padding: 0;
}

#content_hldr.category_page #pagecontent {
  background: url(../img/bkgrds/category_chip.png) 0 0 no-repeat;
  margin: 0;
  padding: 50px;
}

#content_hldr.category_page #category_nav {
  margin: 0px 370px 0px 0px;
  color: #41514e;
  height: 225px;
  display: block;
}

#cat_title_hldr {
  background: #fff;
  display: block;
  margin: 0px -15px 15px -2px;
  padding: 30px 0 0 0;
}

#cat_title {
  background: none;
  height: 25px;
  display: inline-block;
  margin: 0
}

#cat_title h1 {
  font-size: 26px;
  font-weight: lighter;
  letter-spacing: 14px;
  white-space: nowrap;
  margin: 0 0 0 0;
  padding: 0;
  color: rgba(120, 57, 128, 1);
  text-transform: uppercase;
  text-decoration: none
}

#cat_title h1 a, #cat_title h1 a:visited {
  color: rgba(120, 57, 128, 1);
  text-decoration: none
}

#cat_title h1 a:hover, #cat_title h1 a:visited:hover {
  color: rgba(120, 57, 128, 1);
  text-decoration: none
}

/* About Us page */
#about_us h2, #home_office h2, #customer_service h2, #clinical_support h2, #contact_hr h2 {
  margin: 40px 0 0px 0;
  padding: 0;
  font-size: 20px;
  font-weight: bold;
  color: rgba(120, 57, 128, 1);
  border-bottom: 1px solid rgba(120, 57, 128, 1);
  text-transform: uppercase
}

.two_column {
  -webkit-column-count: 2;
  /* Chrome, Safari, Opera */
  -moz-column-count: 2;
  /* Firefox */
  column-count: 2;
  -webkit-column-gap: 20px;
  /* Chrome, Safari, Opera */
  -moz-column-gap: 20px;
  /* Firefox */
  column-gap: 20px;
  display: inline-block;
}

.three_column {
  -webkit-column-count: 3;
  /* Chrome, Safari, Opera */
  -moz-column-count: 3;
  /* Firefox */
  column-count: 3;
  -webkit-column-gap: 20px;
  /* Chrome, Safari, Opera */
  -moz-column-gap: 20px;
  /* Firefox */
  column-gap: 20px;
  display: inline-block;
}

.two_column p, .three_column p, .three_column div {
  margin-top: 0px;
  -webkit-column-break-inside: avoid;
  page-break-inside: avoid;
  break-inside: avoid;
}

.three_column div img {
  width: 100%;
}

/*Resouce Literature page*/
#resource_container {
  margin: 0 0 0 75px;
}

#resource_container ul {
  margin: 10px 0 20px 0;
}

#resource_container ul li {
  margin: 0 0 0 -15px;
}

#resource_container li.pdf {
  list-style: url(../img/blts/pdficon_small.gif);
}

#resource_container h3 {
  margin: 0 0 10px 75px;
  font-size: 36px;
}

#resource_container h4 {
  padding: 25px 0 10px 0px;
  color: gray;
  margin: 0;
  text-align: left;
  font-size: 20px;
  font-weight: bold;
}

#resource_container .product_title {
  color: rgba(84, 183, 42, 1);
  margin: 0;
  font-weight: bold;
  text-align: left;
  font-size: 18px;
  font-weight: lighter;
}

#resource_container a:link {
  text-decoration: none;
  color: gray;
}

#resource_container a {
  text-decoration: none;
  color: purple;
}

#resource_container a:hover {
  text-decoration: none;
  color: purple;
}

/* Catalog page */
#content_hldr.category_page .catalog_cat {}

#content_hldr.category_page.catalog_cat .cat-nav li {
  width: 250px;
}

/*Product Catalog*/
#product_catalog {
  margin: 0 0 25px 0;
  display: block;
  position: relative;
  color: rgba(0, 0, 0, 1);
}

#csinfo {
  background: #edf9ff;
  border: 1px solid #dfdfdf;
  padding: 20px;
  margin: 0 0 45px 0;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  /* for Safari */
  -moz-border-radius: 5px;
  /* for Firefox */
}

.catalog-ordering-info {
  cursor: hand;
  float: right;
  margin: -40px 0px 0px 0px;
  cursor: pointer;
  color: #1F5B7F;
}

#product_catalog .infotext {
  font-size: 13px;
}

#product_catalog #reset {
  text-align: right;
}

#product_catalog .catalog_hdr {
  position: relative;
  font-weight: bold;
  color: #FFF;
  font-size: 14px;
  text-transform: uppercase;
  background: #006271;
  padding: 3px 0 3px 15px;
}

#product_catalog .catalog_hdr .r_hdr {
  position: absolute;
  top: 3px;
  right: 10px;
  display: inline;
}

#product_catalog #selection {
  width: 60%;
}

#product_catalog #selection span {
  display: inline-block;
  margin: 0 3px 25px 0
}

#product_catalog select {
  display: block;
  width: 100%;
  font-size: 14px;
  border: 1px solid #dfdfdf;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  /* for Safari */
  -moz-border-radius: 5px;
  /* for Firefox */
}

#product_catalog select option:hover {
  background: #eee;
}

#product_catalog #selection .title, #product_catalog #selection .subtitle {
  color: #374f5e;
  font-size: 14px;
  font-weight: bold
}

#product_catalog #results {
  position: absolute;
  top: 105px;
  right: 0px;
  bottom: 0px;
  width: 35%;
  overflow: auto;
  padding: 0;
  color: #374f5e;
  font-size: 14px;
}

#product_catalog .full {
  width: 100%;
}

#product_catalog .half {
  width: 49%;
}

#product_catalog .matches {
  color: #374f5e;
  font-size: 14px;
  padding: 8px 10px 20px 0;
  font-weight: bold;
}

/*Sitemap bullet lists - use same list items from product sub-nav with a header of .hdr class*/
#sitemap {
  padding: 0;
}

#sitemap a, #sitemap a:link {
  color: rgba(0, 0, 0, .55);
  white-space: nowrap
}

#sitemap a:visited:hover {
  color: rgba(120, 57, 128, 1);
}

#sitemap a:visited {
  color: rgba(0, 0, 0, .55);
}

#sitemap a:hover {
  color: rgba(120, 57, 128, 1);
}

#sitemap h2 {
  margin: 64px 0 0px 0;
  padding: 0;
  font-size: 20px;
  font-weight: bold;
  color: rgba(120, 57, 128, 1);
  border-bottom: 1px solid rgba(120, 57, 128, 1);
  text-transform: uppercase
}

#sitemap h2:first-of-type {
  margin: 0;
}

#sitemap h2 a, #sitemap h2 a:link, #sitemap h2 a:visited:hover, #sitemap h2 a:visited, #sitemap h2 a:hover {
  text-decoration: none;
  color: rgba(120, 57, 128, 1);
}

#sitemap h3 {
  font-size: 17px;
  margin-bottom: 6px;
  margin-left: 10px;
  color: rgba(120, 57, 128, 1);
  border-bottom: 1px solid rgba(120, 57, 128, 1);
}

#sitemap ul {
  margin-top: 4px;
  margin-bottom: 4px;
}

#sitemap ul li {
  display: -moz-inline-box;
  /* Firefox 2 and under*/
  display: inline-block;
  /* FF3, Opera, Safari */
  zoom: 1.0;
  *display: inline;
  /*IE 6 and 7*/
}

#sitemap ul li a {
  padding: 0 5px 0 10px;
  margin-right: 5px;
  background: url(../img/bullets/small_purple_arrow.png) 0 center no-repeat;
  text-decoration: none;
}

#sitemap ul li.hdr {
  padding: 2px 0 2px 10px;
  font-size: 18px;
  font-weight: bold;
  color: rgba(0, 0, 0, .55);
  margin: 32px 0 16px -10px;
  white-space: normal;
  display: block;
  list-style: none;
}

#sitemap ul li.hdr a {
  padding-left: 0;
  background: none;
  color: rgba(0, 0, 0, .55);
}

#sitemap ul ul {
  margin: 0 0 0 10px;
  _margin: 0 0 0 23px;
  padding: 0;
}

#sitemap ul ul li.hdr {
  margin-top: 8px;
  padding: 2px 0 2px 13px;
  background: none;
  font-size: 16px;
  font-weight: bold;
  color: rgba(0, 0, 0, .55);
  margin-left: -33px;
  white-space: normal;
  display: block;
  list-style: none;
}

#sitemap ul ul li.hdr a, #sitemap ul ul li.hdr a:link {
  padding-left: 10px;
  background: none;
  color: rgba(0, 0, 0, .55);
  text-decoration: none;
}

#sitemap ul ul li.hdr a:hover {
  color: rgba(120, 57, 128, 1);
}

#sitemap ul ul li.hdr a:visited {
  padding-left: 10px;
  color: rgba(0, 0, 0, .55);
}

#sitemap ul ul li.hdr a:hover {
  color: rgba(120, 57, 128, 1);
}

#sitemap ul ul li {
  font-weight: normal;
  white-space: nowrap;
  display: -moz-inline-box;
  /* Firefox 2 and under*/
  display: inline-block;
  /* FF3, Opera, Safari */
  zoom: 1.0;
  *display: inline;
  /*IE 6 and 7*/
}

#sitemap ul ul li a, #sitemap ul ul li a:link {
  padding: 0 5px 0 10px;
  background: url(../img/bullets/small_purple_arrow.png) 0 center no-repeat;
  text-decoration: none;
  color: rgba(0, 0, 0, .55);
}

#sitemap ul ul li a:visited:hover {
  color: rgba(120, 57, 128, 1);
}

#sitemap ul ul li a:visited {
  color: rgba(0, 0, 0, .55);
}

#sitemap ul ul li a:hover {
  color: rgba(120, 57, 128, 1);
}

#sitemap ul ul .row {
  display: block;
  padding-left: 0;
}

#sitemap .row {
  display: block;
  padding-left: 40px;
}

#sitemap h3.hdr {
  padding: 2px 0 2px 10px;
  font-size: 18px;
  font-weight: bold;
  color: rgba(0, 0, 0, .55);
  margin: 40px 0 25px -10px;
  white-space: normal;
  display: block;
  list-style: none;
  border: none;
}

#sitemap .title {
  margin-top: 8px;
  padding: 2px 0 2px 10px;
  background: none;
  font-size: 15px;
  font-weight: bold;
  color: rgba(0, 0, 0, .55);
  white-space: normal;
  display: block;
  list-style: none;
}

#sitemap .title a, #sitemap .title a:link {
  background: none;
  color: rgba(0, 0, 0, .55);
  text-decoration: none;
}

/* Search Results page*/
#results {
  background: url(../img/bkgrds/bkgrd_border_line.gif) 630px 0 repeat-y;
  margin: 0 0 30px 0;
  display: block;
  min-height: 250px;
  _height: 250px;
}

.search_results .page_results {
  float: left;
  padding-left: 0px;
  padding-right: 0px;
}

.search_results .page_results table {
  width: 85%;
}

.search_results .catalog_results {
  float: left;
  padding-left: 0px;
  padding-right: 0px;
}

.search_results_inside {
  padding-right: 20px;
}

#results i {
  color: #FF4800;
  margin-top: 10px;
}

#results td {
  padding-bottom: 15px;
}

.search_results ul#product_literature {
  margin: 4px 20px 4px 0;
  padding: 0;
}

.search_results ul#product_literature li {
  display: inline;
  white-space: nowrap;
  list-style: none;
  line-height: 23px;
}

.search_results ul#product_literature a {
  padding: 3px 5px;
  font-size: 9px;
  font-family: 'Lato', Verdana, sans-serif;
  font-weight: bold;
  background: #edf9ff;
  border: 1px solid #dfdfdf;
  color: #50748c;
  text-decoration: none;
}

.search_results ul#product_literature a:hover {
  background: #6595B4;
  color: #FFFFFF
}

/*Search page*/
#adv_search_tab {
  background: url(../img/bkgrds/adv_search_tab_bkgrd.jpg) left 0 no-repeat;
  font-size: 14px;
  margin: 0 0 -4px 0;
  display: block;
  text-align: left;
  padding: 4px 0 0 20px;
  color: #325081;
  height: 28px;
  cursor: hand;
  cursor: pointer;
  z-index: 100;
}

.result_hdr {
  color: #FFF;
  font-size: 8pt;
  background-color: #6595B4;
  padding: 2px 5px;
  margin-top: 0;
  display: block;
  margin-bottom: 15px;
  border-radius: 0 0 5px 5px;
  -webkit-border-radius: 0 0 5px 5px;
  /* for Safari */
  -moz-border-radius: 0 0 5px 5px;
  /* for Firefox */
}

.result_hdr p {
  margin: 0;
}

/*Sales Contact*/
#salescontact h2 {
  margin: 0 0 20px 0;
  display: inline-block;
  _display: inline;
}

#salescontact h3 {
  font-size: 18px;
  margin: 30px 0 10px 0;
}

#salescontact .info_content {
  padding: 20px;
  margin: 0 0 30px 0;
  background: #f7f7f7;
  border: 1px solid #dfdfdf;
}

#salescontact .info_content td {
  padding: 3px;
}

#salescontact #left_col {
  padding: 10px 0 20px 0;
  vertical-align: top;
  line-height: 20px;
  width: 350px;
  font-size: 11px
}

#salescontact #right_col {
  padding: 10px 0 20px 0;
  vertical-align: top;
  line-height: 20px;
  width: 350px;
  font-size: 11px
}

#salescontact textarea:focus, #salescontact textarea:active {
  width: 655px;
  height: 250px;
}
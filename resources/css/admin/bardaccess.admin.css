@charset "utf-8";

/* CSS Document */
.navbar-default {
    background: transparent;
    border-radius: 0px;
    box-shadow: none;
}

.login-link {
    background: transparent none repeat scroll 0% 0%;
    margin-left: 10px !important;
    padding: 0px !important;
    float: left;
    text-decoration: none;
}

.login-nav>li>a:hover, .login-nav>li>a:focus {
    background: transparent !important;
}

.login-nav ul {
    margin-top: 33px;
    right: 0px;
    font-size: 12px;
    box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.076);
    border-radius: 0px;
}

.dropdown-menu>li>a {
    text-decoration: none;
}

.dropdown-menu>li>a:focus, .dropdown-menu>li>a:hover {
    background-color: transparent !important;
}

.dropdown-menu>li>a:hover, .dropdown-menu>li>a:focus {
    background-color: #E8E8E8 !important;
    background-image: none;
    background-repeat: no-repeat;
}

.field_title {
    width: 155px;
    display: inline-block;
    _display: inline;
    text-align: right;
    padding-right: 2px;
    font-size: 14px;
    color: #4A5C58
}

.required .field_title {
    font-size: 11px;
    color: #9E0000
}

input, select, textarea {
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    font-size: 16px;
    height: 35px;
    line-height: 1.42857;
    padding: 6px 0px 6px 10px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
    min-width: 230px !important;
    -webkit-box-sizing: border-box;
    /* Safari/Chrome, other WebKit */
    -moz-box-sizing: border-box;
    /* Firefox, other Gecko */
    box-sizing: border-box;
    /* Opera/IE 8+ */
}

input[type=radio], input[type=checkbox] {
    min-width: 20px !important;
}

input {
    height: 32px;
    line-height: 32px;
    vertical-align: middle;
    font-size: 16px
}

select {
    height: auto;
    line-height: 32px;
    vertical-align: middle;
    font-size: 14px;
    padding: 5px 0px 5px 10px;
}

.text_field {
    width: 495px;
}

textarea.input {
    height: 90px;
}

input:hover, select:hover, textarea:hover {
    background: #fff;
}

input:active, input:focus, select:active, select:focus, textarea:active, textarea:focus {
    border: 1px solid #6D233F;
    background: #fff;
}

input.checkbox {
    height: 12px;
    margin-right: 5px;
}

input.error, .infobox textarea.error {
    border: 1px solid #9E0000;
    background: #f5e5e5
}

input.required, .infobox textarea.required {
    color: #000
}

/*  Registration / Profile / Username Reset forms */
.row, .mceEditor {
    padding: 0px 0px 15px 0px;
    clear: both;
    display: block;
    min-height: 22px;
}

.row_error {
    color: #FF0000;
    font-weight: bold
}

.cell, .cell_error {
    padding: 0px 6px 0px 0px;
    color: #370615;
    margin: 0px;
    float: left;
    display: inline;
    display: inline-block;
    display: inline-table;
}

.req {
    color: #FF0000
}

.asterisk {
    font-size: 17px;
    margin: 0px 3px 0px 0px;
    vertical-align: bottom;
}

.row .icon {
    color: inherit;
    vertical-align: middle;
    display: inline
}

.row .filesize {
    position: absolute;
    clear: right;
    right: 10px;
    _right: 10px;
    display: inline;
    color: #628BAF;
}

.admin_options {
    float: left;
    margin-left: 25px;
    margin-top: 25px;
    padding: 0 10px 10px 10px;
    border: solid #777 1px;
    line-height: 140%
}

.error {
    color: #FF0000;
    font-weight: bold
}

.success {
    color: #158628;
    font-weight: bold
}

.error_box {
    background-color: #FDA0A3;
    padding: 10px;
    margin-bottom: 15px;
    width: 300px;
    border: 1px solid #FF0000;
    font-weight: bold;
    font-style: italic;
}

.success_box {
    background-color: #B8D9BC;
    padding: 10px;
    margin-bottom: 15px;
    width: 300px;
    border: 1px solid #158628;
    font-weight: bold;
    font-style: italic;
}

.response_box {
    padding: 5px;
    border: solid #B8D9BC;
    border-width: 0 0 0 3px;
    width: 500px;
}

/*Product Catalog Pages*/
.catalog_table {
    font-size: 12px;
    width: 100%;
    border: 1px solid #dfdfdf;
    border-radius: 5px;
    -webkit-border-radius: 5px;
    /* for Safari */
    -moz-border-radius: 5px;
    /* for Firefox */
}

.catalog_table td {
    padding: 15px;
    text-align: left;
    width: auto;
    line-height: 15px;
    border-top: 1px solid #dfdfdf;
    border-left: 1px solid #dfdfdf;
}

.catalog_table tr:first-of-type td {
    border-top: none;
}

.catalog_table tr td:first-of-type {
    border-left: none;
}

.catalog_table ul {
    margin: 0;
    padding: 12px;
}

body.edit_catalog {
    background: #E1ECF2;
}

body.edit_catalog #outer_wrapper {
    background: #E1ECF2;
    display: block;
}

body.edit_catalog #wrapper {
    background: none;
    display: block;
}

body.edit_catalog #container {
    background: none;
    width: 625px;
    margin: 0 auto;
    padding: 0px 0px;
}

body.edit_catalog header {
    display: none;
}

body.edit_catalog #topnav_hldr {
    display: none;
}

body.edit_catalog #cat_title_hldr {
    background: url(../images/bkgrds/cat_tab_hdr_white_bkgrd_625w.gif) top left repeat-x;
    margin: 10px 0px 0px 0px;
}

body.edit_catalog #footer_hldr {
    display: none;
}

.product_form input {
    width: 890px;
}

#myTable_wrapper, #datatables_container, .dataTables_wrapper, .dataTables_wrapper::after {
    clear: none;
}

#myTable, table.dataTable {
    clear: none;
}

table.dataTable thead td, table.dataTable thead th {
    padding: 10px;
    border-bottom: 1px solid #111;
    text-align: left;
}
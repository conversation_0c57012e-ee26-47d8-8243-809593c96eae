@charset "utf-8";

/* CSS Document */
img {
    border: none;
}

ul.display {
    float: left;
    width: 900px;
    margin: 0;
    padding: 0px;
    list-style: none;
}

ul.display li {
    float: left;
    width: 900px;
    padding: 0px 0 10px 0;
    margin: 0 -1px -1px 0;
    border-top: 1px solid #DFDFDF;
    border-left: 1px solid #DFDFDF;
    border-bottom: 1px solid #DFDFDF;
    border-right: 1px solid #DFDFDF;
}

ul.display li a {
    color: #e7ff61;
    text-decoration: none;
}

ul.display li .content_block {
    padding: 0 0 0 5px;
}

ul.display li .content_block h2 {
    margin: 0;
    padding: 5px;
    font-weight: normal;
    font-size: 1.7em;
}

ul.display li .content_block p {
    margin: 5px 20px 0px 0px;
    font-size: 1.2em;
}

ul.display li .content_block a img {
    padding: 3px;
    border: 2px solid #ccc;
    background: #fff;
    margin: 0 15px 0 0;
    float: left;
}

ul.thumb_view li {
    width: 235px;
    height: 245px
}

ul.thumb_view li h2 {
    display: inline;
    font-size: 12px;
}

ul.thumb_view li p {
    display: none;
}

ul.thumb_view li .content_block a img {
    margin: 0 0 10px;
}

a.switch_thumb {
    width: 200px;
    height: 28px;
    line-height: 26px;
    padding: 0;
    margin: 10px 0;
    display: block;
    background: url(../images/buttons/switch.gif) no-repeat;
    outline: none;
    text-indent: -9999px;
}

a:hover.switch_thumb {
    filter: alpha(opacity=75);
    opacity: .75;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=75)";
}

a.swap {
    background-position: left bottom;
}

h3 {
    margin: 10px 0 0;
    padding: 0px 5px 0px 0px;
    font-size: 1em;
}

.icons {
    margin: 6px 6px -3px 6px
}

#datatables_container {
    margin-top: 60px;
    margin-bottom: 30px;
}

.status_icon {
    color: #4A759D;
}

.edit_icon {
    color: #F0AD4E;
}

.upload_icon {
    color: #449D44;
}

.picture_icon {
    color: #4A759D;
}

.literature_icon {
    color: #4A759D;
}

.video_icon {
    color: #35AA7D;
}

.trash_icon {
    color: #d9534f;
}

.status-btn {
    color: #4A759D;
    padding: 6px;
    border: 1px solid #e9e9e9;
    font-size: 13px !important;
    min-width: 13px;
    cursor: pointer;
}

.active {
    color: #4A759D;
}

.inactive {
    color: #ccc !important;
    cursor: default !important;
    ;
}

.edit-btn {
    color: #F0AD4E;
    padding: 6px;
    border: 1px solid #e9e9e9;
    font-size: 13px !important;
    min-width: 13px;
    cursor: pointer;
}

.key-btn {
    color: #449D44;
    padding: 6px;
    border: 1px solid #e9e9e9;
    font-size: 13px !important;
    min-width: 13px;
    cursor: pointer;
}

.upload-btn {
    color: #449D44;
    padding: 6px;
    border: 1px solid #e9e9e9;
    font-size: 13px !important;
    min-width: 13px;
    cursor: pointer;
}

.download-btn {
    color: #4A759D;
    padding: 6px;
    border: 1px solid #e9e9e9;
    font-size: 13px !important;
    min-width: 13px;
    cursor: pointer;
}

.picture-btn {
    color: #4A759D;
    padding: 6px;
    border: 1px solid #e9e9e9;
    font-size: 13px !important;
    min-width: 13px;
    cursor: pointer;
}

.literature-btn {
    color: #4A759D;
    padding: 6px;
    border: 1px solid #e9e9e9;
    font-size: 13px !important;
    min-width: 13px;
    cursor: pointer;
}

.video-btn {
    color: #35AA7D;
    padding: 6px;
    border: 1px solid #e9e9e9;
    font-size: 13px !important;
    min-width: 13px;
    cursor: pointer;
}

.trash-btn {
    color: #d9534f;
    padding: 6px;
    border: 1px solid #e9e9e9;
    font-size: 13px !important;
    min-width: 13px;
    cursor: pointer;
}

.initial-btn {
    padding: 3px 8px 2px 8px;
    border: 1px solid #e9e9e9;
    font-size: 14px !important;
    min-width: 14px;
    cursor: pointer;
}

.dataTable tr {
    color: #4A759D;
}

.dataTable tr.inactive * {
    color: #ccc !important;
}

.dataTable .label {
    border: medium none;
    border: 1px solid #ececec;
    color: #888;
}

.dataTable .label-danger {
    background: transparent;
    color: #F07E1D;
}

.dataTable a {
    text-decoration: none;
    font-size: 14px;
}

.dataTables_wrapper .dataTables_filter input {
    width: 270px;
}

.error {
    color: #FF0000;
    font-weight: bold
}

.success {
    color: #158628;
    font-weight: bold
}

.error_box {
    background-color: #EF8686;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #9E0000;
    font-weight: bold;
    font-style: italic;
    clear: right;
}

.success_box {
    background: #EDF9FF;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #D0D0D0;
    font-weight: bold;
    font-style: italic;
}

.response_box {
    padding: 5px;
    border: solid #B8D9BC;
    border-width: 0 0 0 3px;
    width: 500px;
}

/* Elabeling Language Mangement */
.languages_table td {
    padding-top: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgb(236, 236, 236);
    height: 20px;
}

.languages_table .aligncenter {
    text-align: center;
}

.languages_table .alignleft {
    text-align: left;
}

/* ADMIN STYLES */
#pagecontent {
    font-size: 12px;
    min-height: 625px;
}

#myTable_filter input {
    height: 32px;
    line-height: 32px;
    vertical-align: middle;
    font-size: 16px;
    border: 1px solid #99b3b4;
    background: #e7ebec;
    padding: 0px 0px 0px 10px;
    border-radius: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
}
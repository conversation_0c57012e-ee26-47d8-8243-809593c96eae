@charset "utf-8";

/* CSS Document */
.eifu_background {
    background: url(../img/bkgrds/eifu_collage.jpg) right bottom no-repeat;
    margin: 0px -40px -81px 0px;
    padding: 20px 0 0 0;
    border-radius: 0 0 10px 10px;
    -webkit-border-radius: 0 0 10px 10px;
    /* for Safari */
    -moz-border-radius: 0 0 10px 10px;
    /* for Firefox */
}

.field_title {
    font-weight: bold;
}

#resource_select_box {
    max-width: 268px;
}

#form_part_number {
    max-width: 250px;
}

#part_number {
    width: 8%;
}

@media (max-width:1260px) {
    .navbar-brand {
        margin-top: 0px;
    }

    .navbar-toggle {
        display: block;
        float: right;
        position: absolute;
        right: 0;
        margin-top: -2px;
    }

    .navbar-default .navbar-collapse, .navbar-default .navbar-form {
        display: none;
        border-color: transparent;
    }

    .navbar-default .collapsing {
        display: block;
    }

    .navbar-default .in {
        display: block;
        border-color: transparent;
    }

    #cat_title h1 {
        font-size: 22px;
        letter-spacing: 2px;
        white-space: normal;
    }
}

/* FULL SIZE */
@media (min-width: 1000px) {
    #container {
        width: 1000px;
    }

    .table_icon {
        box-sizing: border-box;
        display: table-cell;
        padding: 0 2px 0;
    }

    #form_part_number {
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_number {
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_title {
        width: 57%;
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_actions {
        width: 15%;
        height: 34px;
        line-height: 25px;
        text-align: right;
        padding: 6px 8px 6px 8px
    }

    #btn_cont {
        width: 175px;
        float: left;
        margin-left: 20px;
    }

    .btn-block {
        width: auto !important;
    }

    #buttons_container {
        float: right;
        margin-top: -45px;
    }
}

/* TABLET */
@media (max-width: 768px) {
    #container {
        width: 100%;
    }

    #content_hldr.navcolumnless #content {
        padding: 20px;
    }

    #form_part_number {
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_number {
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_title {
        width: 57%;
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_actions {
        width: 15%;
        height: 34px;
        line-height: 25px;
        text-align: right;
        padding: 6px 8px 6px 8px
    }

    #btn_cont {
        width: 100%;
        margin-top: 10px;
    }

    #buttons_container {
        margin-right: 20px
    }

    .navbar-brand>img {
        padding-top: 0px;
    }

    #cat_title_hldr {
        padding-left: 10px;
    }

    .navbar-nav {
        display: none;
    }
}

/* IN BETWEEN TABLET AND FULL SIZE */
@media (min-width: 768px) {
    .navbar-collapse.collapse {
        display: block;
    }

    #form_part_number {
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_number {
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_title {
        width: 57%;
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_actions {
        width: 15%;
        height: 34px;
        line-height: 25px;
        text-align: right;
        padding: 6px 8px 6px 8px
    }

    #btn_cont {
        width: 175px;
        float: left;
        margin-left: 20px;
    }

    .btn-block {
        width: auto !important;
    }

    #buttons_container {
        float: right;
        margin-top: -12px
    }
}

/* PHONE */
@media (max-width: 400px) {
    #container {
        width: 100%;
    }

    #content_hldr.navcolumnless #content {
        padding: 10px;
    }

    #part_title {
        width: 57%;
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_actions {
        width: 15%;
        height: 34px;
        line-height: 25px;
        text-align: right;
        padding: 6px 8px 6px 8px
    }

    #btn_cont {
        width: 100%;
        margin-top: 10px;
    }

    #buttons_container {
        margin-right: 20px
    }

    table.dataTable.row-border tbody th, table.dataTable.row-border tbody td, table.dataTable.display tbody th, table.dataTable.display tbody td {
        border-top: 1px solid #ddd;
        overflow: auto !important;
        text-overflow: none !important;
        white-space: inherit !important;
    }

    .navbar-brand>img {
        padding-top: 15px;
        padding-left: 10px;
    }

    .navbar-toggle {
        margin-top: 13px
    }

    .navbar-header {
        height: 50px;
    }

    #cat_title_hldr {
        padding-left: 10px;
        max-width: 100%;
        overflow: hidden;
    }
}

/* FULL SIZE */
@media (min-width: 1260px) {
    #container {
        width: 1260px;
    }

    #form_part_number {
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_number {
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_title {
        width: 57%;
        height: 34px;
        line-height: 25px;
        padding: 6px 8px 6px 8px
    }

    #part_actions {
        width: 15%;
        height: 34px;
        line-height: 25px;
        text-align: right;
        padding: 6px 8px 6px 8px
    }

    #btn_cont {
        width: 175px;
        float: left;
        margin-left: 20px;
    }

    .btn-block {
        width: auto !important;
    }

    #buttons_container {
        float: right;
        margin-top: -45px;
    }
}

/* FULL SIZE */
@media (min-width: 1420px) {
    #container {
        width: 1450px;
        max-width: 1450px;
    }

    .quote-div {
        width: 42%;
        text-align: right;
    }

    .quote {
        font-style: italic;
        font-size: 21px;
        padding-top: 50px;
    }
}
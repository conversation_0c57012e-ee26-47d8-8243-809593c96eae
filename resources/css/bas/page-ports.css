@charset "utf-8";
#lens {
  border: 2px solid white;
  /* Positioned absolutely, so we can move it around */
  position: absolute;
  height: 180px;
  width: 180px;
  /* Hidden by default */
  display: none;
  /* A blank cursor, notice the default fallback */
  cursor: url('img/blank.cur'), default;
  /* CSS3 Box Shadow */
  -moz-box-shadow: 0 0 5px #777, 0 0 10px #aaa inset;
  -webkit-box-shadow: 0 0 5px #777;
  box-shadow: 0 0 5px #777, 0 0 10px #aaa inset;
  /* CSS3 rounded corners */
  -moz-border-radius: 90px;
  -webkit-border-radius: 90px;
  border-radius: 90px;
}

#lens.chrome {
  /* A special chrome version of the cursor */
  cursor: url('img/blank_google_chrome.cur'), default;
}

#overlay_hldr {
  /* The main div */
  margin: 0 auto;
  position: relative;
}
@charset "utf-8";

/* Accucath*/
.accucath_hero {
  margin: 80px -40px 0px 0px
}

.accucath-list li span {
  font-size: 18px;
  line-height: 24px;
  color: rgba(120, 57, 128, 1);
  font-weight: bold;
}

.accucath-full-color-icon {
  display: none;
}

.accucath-white-icon {
  display: block;
}

/* PowerGlide Pro*/
.powerglidepro_hero {
  margin: 80px -20px 20px 20px;
}

.powerglidepro_hand {
  margin: 15px -25px -100px 0;
  position: relative;
}

.powerglidepro {
  min-height: 265px;
}

.powerglidepro_blood_control {
  margin: -95px 0px -70px 20px;
  z-index: 2;
}

.powerglidepro_needle_guidewire {
  margin: 0px 0px -50px -25px;
}

.powerglidepro_body_softening {
  margin: 20px 0px 30px -25px;
}

/* PowerGlide ST*/
#powerglide-st p.summary {
  padding: 50px 0px 0 0px;
}

.powerglide-st_hero {
  margin: 80px -20px 50px 40px;
}

.powerglide-st {
  min-height: 315px;
}

.powerglide-st-introducers {
  margin: -220px 0 -45px 0;
  position: relative;
}

/* PowerMidline*/
.powermidline_hero {
  margin: 0px -40px 0px 20px;
}

.powermidline_product {
  margin: -80px -40px 0px 20px;
}

.powermidline {
  min-height: px;
}

.powermidline_subhead p span {
  font-size: 19px;
  font-weight: bold;
  color: #004593;
}

.power_midline th {
  text-align: center;
  width: 290px;
}

.provena_midline_hero {
  margin: 0px -40px 0px 20px;
}

.provena_midline_product {
  margin: 150px -40px 0px 20px;
}
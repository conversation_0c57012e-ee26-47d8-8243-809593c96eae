@charset "utf-8";

#page header>div>.links span {
  color: #fff;
}

#page.scrolled header>div>.links span {
  color: #004593;
}

#content_hldr {
  max-width: 1250px;
  margin: 0 auto;
  padding: 0;
  background: #F7F7F7;
  position: relative;
  display: block;
  clear: both;
}

#content_hldr.fullpage #content {
  display: block;
  margin: 0;
  padding: 0;
}

#content_hldr.fullpage #content #pagecontent {
  margin: 0;
  padding: 50px
}

#content_hldr.fullpage #chip {
  display: none;
}

#content_hldr.fullpage #col_hldr {
  display: none;
}

#content_hldr_btm {
  clear: both;
  float: none;
  display: block;
  line-height: 0;
  font-size: 0;
}

#content {
  position: relative;
  display: block;
  padding: 0;
  margin: 0;
  color: rgba(0, 0, 0, 0.65);
  border: 2px solid #E1E1E2;
}

section {
  padding: 45px 15px 30px 15px;
}

section.background-image {
  min-height: 450px;
  position: relative;
  margin: -45px -15px 0px -15px;
}

section.background-image .container-fluid {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

form.mobile-search {
  margin-top: 15px;
}

form.mobile-search button, form.mobile-search button:hover {
  background: transparent;
  bottom: 0;
  color: #004593;
  font-size: 14px;
  height: 30px;
  min-width: 0;
  padding: 0;
  position: absolute;
  right: 20px;
  text-align: center;
  top: 2px;
  width: 30px;
}

ul.side-nav {
  padding: 0;
  margin: 0;
}

#col_hldr {
  width: 250px;
  margin: 0;
  position: relative;
  float: left;
  z-index: 1
}

#col {
  font-size: 16px;
  position: relative;
  margin: 0;
  padding: 25px 15px 0 15px;
  letter-spacing: .02em;
  color: #4a5c58;
}

#col .title {
  margin: 0;
  font-size: 12px;
  padding: 15px 10px 7px 7px;
  font-size: 18px;
  color: #004593;
  text-transform: uppercase
}

#col a, #col a:link {
  padding: 4px 0 0 22px;
  line-height: 24px;
  text-decoration: none;
  color: #333;
}

#col a:visited:hover {
  color: #333;
}

#col a:visited {
  color: #333;
}

#col a:hover, #col a:hover {
  color: #333;
}

#col ul.select, #col ul.side-nav {
  margin: 5px 0 0 -25px;
  padding: 10px 0;
  list-style: none;
  text-transform: capitalize
}

#col .side-nav li, #col .select li {
  list-style: none;
  margin: 0 0 0 10px;
  cursor: pointer;
}

#col .expanded li {
  list-style: none;
  margin: 0;
  cursor: pointer;
}

#col .select li .link {
  text-transform: lowercase;
}

#col li.tab.selected {
  color: #004593;
  cursor: default;
  opacity: 1;
}

#col li.ui-tabs-active a, #col li.ui-tabs-active a:link, #col li.ui-tabs-active a:visited {
  /* background: url(../img/bullets/large_white_arrow.png) 0 2px no-repeat; */
  color: #004593;
  cursor: default;
  opacity: 1;
}

#col .service a {
  font-size: 14px;
}

#col img {
  display: block;
  height: auto;
  max-width: 100%;
}

/*Main content/tabbed pages */
.slogan {
  margin: 10px;
}

#main a {
  color: #004593;
}

#main {
  position: relative;
  padding: 0;
}

#return {
  display: none;
}

#top {
  background: rgba(0, 0, 0, .3);
  border-radius: 5px;
  bottom: 115px;
  color: #fff;
  display: none;
  font-size: 26px;
  height: 50px;
  line-height: 50px;
  position: fixed;
  right: 5px;
  text-align: center;
  width: 50px;
  z-index: 9;
  padding-top: 10px;
}

.consult_statement {
  font-size: 13px;
  margin: 10px 0 -5px 0;
  color: #BF362F;
  display: block;
  text-align: center;
}

#nav_container #footer .consult_statement {
  display: none;
}

/*** Main Layout - END***/
/* Columns */
#trademarks .column {
  width: 25%;
  float: left;
  margin: 5px 60px 0 0;
  padding: 0;
  display: inline;
  clear: none;
}

#trademarks .last {
  margin-right: 0;
}

#trademarks .column img {
  border: 1px solid #4A5C58;
}

.col_l, .col_l_flush, .col_l_flush_nm, .fullpage .col_l_flush {
  float: left;
  position: relative;
}

.col_r, .col_r_flush, .col_r_flush_nm, .fullpage .col_r_flush {
  float: right;
  position: relative;
}

.col_full_flush, .fullpage .col_full_flush {
  position: relative;
}

.col_l {
  margin-right: 20px;
}

.col_r {
  margin-left: 20px
}

.col_l_flush {
  margin-right: 20px;
  margin-left: -25px
}

.col_r_flush {
  margin-right: -25px;
  margin-left: 20px
}

.row .col_r_flush {
  margin-right: -40px;
  margin-left: 20px
}

.col_l_flush_nm {
  margin-left: -5px
}

.col_r_flush_nm {
  margin-right: -5px;
}

.col_full_flush {
  margin-right: -5px;
  margin-left: 20px
}

.col_l_full_flush_extend {
  margin-right: -50px;
  margin-left: -25px
}

.col_r_full_flush_extend {
  margin-right: -25px;
  margin-left: -50px
}

.fullpage .col_l {
  margin-right: 50px;
}

.fullpage .col_r {
  margin-left: 50px
}

.fullpage .col_l_flush {
  margin-right: 50px;
  margin-left: -50px
}

.fullpage .col_r_flush {
  margin-right: -50px;
  margin-left: 50px
}

.fullpage .col_full_flush {
  margin-right: -50px;
  margin-left: -50px
}

.gutter>[class*='col-'] {
  padding-right: 10px;
  padding-left: 10px;
}

.top-band {
  background: url(../img/bkgrds/top_band_bkgrd.jpg) top center no-repeat;
  background-size: 100%;
  padding-top: 9px;
  margin-top: 40px;
}

.bottom-band {
  background: url(../img/bkgrds/bottom_band_bkgrd.jpg) bottom center no-repeat;
  background-size: 100%;
  padding-bottom: 13px;
  margin-bottom: 40px
}

.top-band_full {
  background: url(../img/bkgrds/top_band_full.jpg) top center no-repeat;
  background-size: 100%;
  padding-top: 9px;
  margin-top: 40px;
}

.bottom-band_full {
  background: url(../img/bkgrds/bottom_band_full.jpg) bottom center no-repeat;
  background-size: 100%;
  padding-bottom: 9px;
  margin-bottom: 40px
}

.bottom-line {
  background-image: url(../img/products/ultrasound/supporting/sapiens-line.png);
  background-size: 100%;
  background-repeat: no-repeat;
  margin-left: -15px;
  height: 10px;
  padding: 0;
}

/*General Table Formatting*/
.striped {
  width: 100%;
  border: 1px solid #e6e6e6;
}

.striped th {
  text-align: left;
  width: auto;
  padding: 5px 10px;
  font-weight: bold;
  font-size: 15px;
  line-height: 22px;
  border: 1px solid #e6e6e6;
  vertical-align: middle;
  white-space: nowrap;
}

.striped th.center {
  text-align: center;
}

.striped th.col_100 {
  width: 100px;
}

.striped td {
  padding: 3px 10px;
  text-align: cen;
  width: auto;
  font-size: 14px;
  line-height: 22px;
  color: #444;
  border: none;
  border: 1px solid #e6e6e6;
}

.striped .font20 {
  font-size: 20px;
}

.striped tr:nth-of-type(odd) {
  background: #bfd0e4;
}

.striped tr:nth-of-type(even) {
  background: #fff;
}

.striped thead tr th {
  background: #FFF;
}

.striped td.bigbullet {
  font-size: 40px;
  text-align: center;
  padding: 5px 0
}

.striped ul {
  padding: 0 0 0 20px;
}

.width_autofill {
  width: auto;
  _width: auto;
}

/*PDF thumbnail images and text*/
ul.thumbnails {
  margin: 40px 5px 20px 0;
  padding: 0;
  float: none;
  list-style-position: inside;
  list-style: none;
  display: block;
  position: relative;
  padding-left: 0;
}

ul.thumbnails li {
  margin: 0 25px 10px 0;
  float: left;
  position: relative;
  display: inline;
  zoom: 1.0;
  width: 116px;
  height: 250px;
  line-height: 105%;
  font-size: 10px;
  vertical-align: middle;
}

ul.thumbnails.med li {
  width: 175px;
}

ul.thumbnails.wide li {
  width: 225px;
}

#resources ul.thumbnails li {
  margin: 0 25px 10px 0;
  float: left;
  position: relative;
  display: inline;
  width: 116px;
  height: 250px;
  line-height: 105%;
  font-size: 10px;
  vertical-align: middle;
}

#main ul.thumbnails li img {
  margin-bottom: 10px;
  margin-right: 20px;
  position: relative;
  clear: both
}

#main ul.thumbnails li a img {
  border: 1px solid #d6edf0;
  text-decoration: none;
}

#main ul.thumbnails li a:visited:hover img {
  border: 1px solid #007079;
  text-decoration: none;
}

#main ul.thumbnails li a:visited img {
  border: 1px solid #d6edf0;
  text-decoration: none;
}

#main ul.thumbnails li a:hover img {
  border: 1px solid #007079;
  text-decoration: none;
}

#main ul.thumbnails a {
  color: #007079;
  text-decoration: none;
}

#main ul.thumbnails a:visited:hover {
  color: rgba(120, 57, 128, 1);
  text-decoration: none
}

#main ul.thumbnails a:visited {
  color: #007079;
  text-decoration: none;
}

#main ul.thumbnails a:hover {
  color: rgba(120, 57, 128, 1);
  text-decoration: none
}

/*2columns*/
ul.info-2col {
  list-style: none;
  margin: 0;
  padding: 0;
  float: none;
  display: block;
  position: relative;
}

ul.info-2col li {
  margin: 0;
  padding: 5px 20px 5px 20px;
  width: 310px;
  height: 100px;
  float: left;
  display: inline;
  border-right: #a2bd88 dotted 1px;
}

ul.info-2col li img {
  margin: -10px 10px 30px 0;
  padding: 0;
  float: left;
  display: inline;
}

ul.info-2col li.last {
  border: none
}

ul.info1-2col {
  list-style: none;
  margin: 0 auto;
  padding: 0;
  float: none;
  list-style: none;
  margin: 0 auto;
  padding: 0;
  display: inline-block;
  position: relative;
}

ul.info1-2col li {
  margin: 0;
  padding: 5px 20px 5px 20px;
  width: 310px;
  float: left;
  display: inline;
}

ul.info1-2col li img {
  margin: -10px 10px 30px 0;
  padding: 0;
  float: left;
  display: inline;
}

ul.info1-2col li.last {
  border: none
}

/*3columns*/
ul.info-3col {
  list-style: none;
  margin: 0;
  padding: 0;
  float: none;
  display: block;
  position: relative;
}

ul.info-3col li {
  margin: 0;
  padding: 5px 15px 5px 15px;
  width: 218px;
  height: 125px;
  float: left;
  display: inline;
  border-right: #a2bd88 dotted 1px;
}

ul.info-3col li img {
  margin: 0 10px 50px 0;
  padding: 0;
  float: left;
  display: inline;
  vertical-align: middle
}

ul.info-3col li.last, .noborder li {
  border: none
}

ul.text-3col {
  list-style: none;
  margin: 0 auto;
  padding: 0;
  display: inline-block;
  position: relative;
  -webkit-columns: 3 175px;
  -moz-columns: 3 175px;
  columns: 3 175px;
  -webkit-column-gap: 60px;
  -moz-column-gap: 60px;
  column-gap: 60px;
}

ul.text-3col li {
  margin: 0;
  padding: 5px 0px;
  min-width: 175px;
  display: inline-block;
  vertical-align: top;
}

ul.text-3col li img {
  margin: 0 10px 50px 0;
  padding: 0;
  display: inline;
  vertical-align: middle
}

ul.text1-3col {
  list-style: none;
  margin: 20px 0 0 0;
  padding: 0;
  float: none;
  display: block;
  position: relative;
}

ul.text1-3col li {
  margin: 0;
  padding: 5px 10px 5px 10px;
  width: 213px;
  height: 100px;
  float: left;
  display: inline;
}

ul.text1-2col li img {
  margin: -10px 10px 30px 0;
  padding: 0;
  float: left;
  display: inline;
}

ol.col-3 {
  counter-reset: li-counter;
}

ol.col-3 li {
  padding: 0 0 0 50px;
  list-style-type: none;
}

ol.col-3 li::before {
  position: absolute;
  top: 0;
  left: 0em;
  width: 1.5em;
  font-size: 1.5em;
  line-height: 1;
  font-weight: bold;
  text-align: right;
  color: #004593;
  content: counter(li-counter)".";
  counter-increment: li-counter;
}

.three-column-page {
  margin: 0;
  padding: 0;
  columns: 220px 3;
  -webkit-columns: 220px 3;
  /* Safari and Chrome */
  -moz-columns: 220px 3;
  /* Firefox */
  column-gap: 0;
  -moz-column-gap: 0;
  /* Firefox */
  -webkit-column-gap: 0;
  /* Safari and Chrome */
}

.three-column-page li {
  margin: 0;
  padding: 0 0 5px 0;
  width: 220px;
  list-style: none;
  display: -moz-inline-box;
  /* Firefox 2 and under*/
  display: inline-block;
  /* FF3, Opera, Safari */
  zoom: 1.0;
  *display: inline;
  /*IE 6 and 7*/
}

.three-column-full {
  margin: 0;
  padding: 0;
  columns: 250px 3;
  -webkit-columns: 250px 3;
  /* Safari and Chrome */
  -moz-columns: 250px 3;
  /* Firefox */
  column-gap: 0;
  -moz-column-gap: 0;
  /* Firefox */
  -webkit-column-gap: 0;
  /* Safari and Chrome */
}

.three-column-full li {
  margin: 0;
  padding: 0 0 5px 0;
  width: 250px;
  list-style: none;
  display: -moz-inline-box;
  /* Firefox 2 and under*/
  display: inline-block;
  /* FF3, Opera, Safari */
  zoom: 1.0;
  *display: inline;
  /*IE 6 and 7*/
}

.two-columns {
  columns: 225px 2;
  -webkit-columns: 225px 2;
  /* Safari and Chrome */
  -moz-columns: 225px 2;
  /* Firefox */
  column-gap: 0;
  -moz-column-gap: 0;
  /* Firefox */
  -webkit-column-gap: 0;
  /* Safari and Chrome */
}

.three-columns {
  columns: 225px 3;
  -webkit-columns: 225px 3;
  /* Safari and Chrome */
  -moz-columns: 225px 3;
  /* Firefox */
  column-gap: 0;
  -moz-column-gap: 0;
  /* Firefox */
  -webkit-column-gap: 0;
  /* Safari and Chrome */
}
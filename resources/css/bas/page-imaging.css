@charset "utf-8";

/* Site-Rite 8 */
#site-rite-8 h3 {
  margin: 120px 0 -10px 0;
}

#site-rite-8 p:first-of-type {
  padding: 20px 0 0px 0px;
}

.site_rite_8_product {
  margin: -300px -25px -165px 0;
}

.site-rite-8-hero {
  margin: 50px -40px 0 12px;
}

.site_rite_8_box {
  z-index: 1;
  width: 100%;
  position: relative;
}

.site_rite_8_visualize {
  font-size: 2100%;
  font-family: 'Open Sans ExtraBold';
  z-index: 0;
  color: rgba(213, 213, 213, .35);
  padding: 0;
  margin: 0;
  top: 980px;
  left: -835px;
  position: absolute;
}

.site_rite_8_virtual {
  font-size: 1780%;
  font-family: 'Open Sans ExtraBold';
  z-index: 0;
  color: rgba(213, 213, 213, .35);
  padding: 0;
  margin: 0;
  top: 1100px;
  left: -540px;
  position: absolute;
}

.visual {
  font-size: 45px;
}

.visualize_right {
  border-top: 3px #004593 solid;
  border-left: 3px #004593 solid;
  width: 45%;
  float: right;
  padding: 20px;
}

.visualize_right_img {
  float: left;
  padding-right: 30px;
}

.visualize_left {
  border-top: 3px #004593 solid;
  border-right: 3px #004593 solid;
  width: 45%;
  float: left;
  padding: 20px;
}

.visualize_left_img {
  float: right;
  padding-left: 30px;
}

.y_sensor_diamond {
  margin-top: -380px;
}

.visual_tip_confirmation {
  margin-top: 120px;
}

.diamond {
  float: right;
  margin-top: 20px;
  margin-bottom: 30px;
}

.diamond-heart {
  margin-top: -70px;
}

.TCS_system {
  margin-top: 100px;
}

.pinpoint {
  font-size: 30px;
}

.sr8-pinpoint-needle {
  margin-top: -100px;
  float: left;
}

/* Sherlock 3CG */
.diamond_hero {
  margin: 0 -40px 0 100px
}

.wave-back {
  background: url(../img/products/ultrasound/supporting/sapien-wave.jpg) center top repeat-x;
}

/* Nautilus */
#nautilus h3 {
  margin: 55px 0 -10px 0;
  line-height: 30px;
}

#nautilus p:first-of-type {
  padding: 20px 0 0px 0px;
}

.nautilus_hero {
  margin: 140px -20px 90px 30px
}

.nautilus_feature_hero {
  margin: 100px 0px 0px 30px
}

.nautilus figure img {
  margin: 0 auto;
}

.nautilus_child {
  margin: 147px -20px -12px 0px
}

.tip_confirmation p {
  margin: 60px -20px 0 35px
}

.nautilus-adapters {
  font-size: 20px;
  font-style: italic;
  margin-top: 10px;
  color: gray;
  font-weight: lighter;
}

/* Halycon */
#halcyon {
  margin: 125px 0 0px 0;
  color: white;
}

#halcyon p:first-of-type {
  padding: 20px 0 0px 0px;
}

#halcyon ul {
  margin: 20px 0 0px 0;
}

#halcyon ul li {
  margin: 0px 0 0px -20px;
}

.halcyon_hero {
  margin: 85px 0 50px 60px;
}

.halcyon_headline {
  font-size: 32px;
  font-weight: bold;
  margin: 40px 0 30px;
  color: #D8E24A;
  font-family: Segoe, "Segoe UI", "DejaVu Sans", "Trebuchet MS", Verdana, "sans-serif";
}

.halcyon_3cg {
  margin: 85px 0 0px 0px;
}

.halcyon_maxp {
  margin: 85px 0 0px 0px;
}

/* Cue */
.cue_hero {
  margin: 15px 0 50px 60px;
}
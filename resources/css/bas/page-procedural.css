/*AllPoints DCK Page*/
.allpoints td:nth-child(n+2) {
  text-align: center;
}

.allpoints th:first-child {
  text-align: center;
  font-size: 36px;
  line-height: 40px;
}

#allpoints h3, #allpoints_pak h3 {
  margin: 50px 0 -10px 0;
  line-height: 25px;
}

#allpoints p:first-of-type, #allpoints_pak p:first-of-type {
  padding: 50px 0 30px 0px;
}

#evidence-based_guidelines .icon_guardiva {
  padding: 40px 25px 40px 0px;
  width: 90px
}

#evidence-based_guidelines .icon_statlock {
  padding: 50px 25px 65px 0px;
  width: 90px
}

#evidence-based_guidelines .icon_purell {
  padding: 30px 25px 40px 0px;
  width: 90px
}

.guidelines td {
  padding: 10px 5px;
  background-color: white;
  border-bottom: 1px solid #1279B8;
}

.guidelines .separator td {
  border-top: 5px solid #1279B8;
  width: 200px;
}

.guidelines .separator td:first-of-type {
  background: #fff
}

h4 .guidelines {
  font-size: 26px;
  text-transform: uppercase;
  color: #1279B8;
  font-weight: bold;
}

.guidelines th {
  background-color: #1279B8;
  color: white;
  border-bottom: none;
  text-align: center;
}

.guidelines td:last-of-type {
  text-align: center;
}

.allpoints_dck_hero {
  margin: 25px 0px 0 50px;
  opacity: 0.3;
}

.allpoints_dck_hero_m {
  display: none;
}

.visual_guide_checklist {
  display: none;
}

/* AllPoints Training Program */
#allpoints {
  margin: 25px 0;
}

#allpoints .cell {
  margin: 20px 0 0px 0;
  font-size: 14px;
  line-height: 17px;
}

#allpoints .btn {
  float: right;
  background-color: #97c848;
  border: none;
  font-weight: bold;
  margin-top: 20px;
}

#allpoints .btn:hover {
  background-color: #009d51;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.25)
}

/*AllPoints DCK Page*/
#allpoints_pak {
  margin: 55px 0;
}

/*Sentrinex Page*/
#sentrinex h3 {
  margin: 55px 0 -20px 45px;
}

#sentrinex p {
  padding: 0px;
}

/*Securis Page*/
.securis_hero {
  margin: 30px 0px 0 50px;
}

.securis_arm {
  margin: -101px 0 -227px
}

.securis {
  margin-top: -100px;
}

.white_securis {
  color: white;
}

.securis-list li span {
  font-size: 18px;
  line-height: 24px;
  color: #004593;
  font-weight: bold;
}

.comfort_pad {
  margin: -15px 5px 0px -12px;
}

.securis_comparison {
  margin: -25px;
}

.securis_header h4 {
  color: white;
  font-size: 40px;
  background-color: 004593;
  padding: 10px 0 10px 0;
  margin: 0 -25px;
  font-weight: lighter;
}

.sterility_btn {
  color: white;
  padding: 15px;
  background-color: rgba(102, 184, 60, 1);
  letter-spacing: 7px;
  margin-right: 15px;
}

.sterility_btn.gray {
  background-color: #898989;
}

.securis_check {
  padding: 20px 20px 40px 20px;
}

.securis_comparison_header h4 {
  color: white;
  font-size: 35px;
  background-color: #004593;
  padding: 45px;
  margin: -25px -25px 0px -25px;
  text-align: center;
}

.securis_comparison_bkgrnd {
  background: url(../img/products/accessories/supporting/Securis/securis-comparisons-background.png) no-repeat;
  background-size: 100%;
  height: 290px;
  margin: 0 -25px;
  position: relative;
}

.securis_overflow_container {
  height: 500px;
  overflow-y: scroll;
  margin: 0px;
  overflow-x: hidden;
}

.securis_tape {
  display: none;
}

.securis_centurion {
  display: none;
}

.securis_tegaderm {
  display: none;
}
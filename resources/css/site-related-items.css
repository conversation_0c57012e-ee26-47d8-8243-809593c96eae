@charset "utf-8";
/*Related Items*/

#related_wrapper {
  display: block;
  margin: 0;
  min-height: 75px;
  position: relative;
  text-align: right;
}

#related_hldr {
  padding: 0;
}

#related {
  position: relative;
}

#related ul {
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  right: 15px;
}

#related ul li {
  margin: 0 0 0 3px;
  vertical-align: top;
  padding: 0;
  width: 200px;
  height: 75px;
  position: relative;
  display: -moz-inline-box;
  /* Firefox 2 and under*/
  display: inline-block;
  /* FF, Opera, Safari */
  zoom: 1.0;
  *display: inline;
  /*IE*/
}

#related ul li h4 {
  background: rgba(36, 166, 128, 1);
  color: #fff;
  font-size: 12px;
  line-height: 15px;
  padding: 0 0 0 10px;
  margin: -15px 0 0 -3px;
  border-left: 3px solid white;
  width: 190px;
  height: 15px;
  text-align: left;
}

#related ul li.catalog h4 {
  border-left: medium none;
  margin: -15px 0px 0px 0px;
  /* background: rgba(84, 183, 42, 1); */
}

#related ul li.catalog p a {
  /* background: rgba(84, 183, 42, .85); */
}

/*Related - homepage*/

/* #related.home ul li:first-of-type {
  display: none;
} */

#related ul li:first-of-type h4 {
  background: rgba(84, 183, 42, 1);
  border: medium none;
  margin: -15px 0 0 0;
}

#related ul li:last-of-type h4 {
  background: rgba(6, 154, 175, 1);
}

#related ul li h4 a {
  color: #fff;
  text-decoration: none;
  border: none;
  text-transform: uppercase;
}

#related ul li h4 a:visited:hover, #related ul li h4 a:visited, related ul li h4 a:hover {
  color: #fff;
}

#related ul li p {
  display: block;
  font-size: 16px;
  font-style: italic;
  line-height: 18px;
  color: #7f7f7f;
  margin: 0px;
}

#related ul li p a {
  background: url(../img/related/nav-chip-related.png) bottom right no-repeat;
  background-color: rgba(36, 166, 128, .85);
  color: #fff;
  display: table-cell;
  vertical-align: middle;
  width: 180px;
  height: 75px;
  padding: 0 10px;
  text-align: center;
  text-decoration: none;
  line-height: none;
  overflow: hidden;
  transition: opacity .5s ease;
  opacity: 0;
}

#related ul li:first-of-type p a {
  background-color: rgba(84, 183, 42, .85);
}

#related ul li:last-of-type p a {
  background-color: rgba(6, 154, 175, .85);
}

#related ul li p a:visited:hover, #related ul li p a:visited {
  color: #fff
}

#related ul li p a:hover {
  color: #fff;
  transition: opacity .5s ease;
  opacity: 1;
}

#related ul li img.icon {
  float: left;
  display: inline;
  margin: 0 15px 80px 15px;
}

/*Related Items- Imaging*/

#related ul li.prevue {
  background: url(../img/related/prevue-related.jpg) 0px bottom no-repeat;
}

#related ul li.prevue_green {
  background: url(../img/related/prevue-green-related.jpg) 0px bottom no-repeat;
}

#related ul li.prevue_bluegreen {
  background: url(../img/related/prevue-bluegreen-related.jpg) 0px bottom no-repeat;
}

#related ul li.siterite8_green {
  background: url(../img/related/siterite8-green-related.jpg) 0px bottom no-repeat;
}

#related ul li.siterite8_bluegreen {
  background: url(../img/related/siterite8-bluegreen-related.jpg) 0px bottom no-repeat;
}

#related ul li.siterite8_blue {
  background: url(../img/related/siterite8-blue-related.jpg) 0px bottom no-repeat;
}

#related ul li.sherlock3cg {
  background: url(../img/related/sherlock-3cg-related.jpg) 0px bottom no-repeat;
}

#related ul li.sherlock3cg_bluegreen {
  background: url(../img/related/sherlock-3cg-bluegreen-related.jpg) 0px bottom no-repeat;
}

#related ul li.visionII_bluegreen {
  background: url(../img/related/vision-II-bluegreen.jpg) 0px bottom no-repeat;
}

#related ul li.visionII {
  background: url(../img/related/vision-II.jpg) 0px bottom no-repeat;
}

#related ul li.sherlock {
  background: url(../img/related/sherlock-related.jpg) 0px bottom no-repeat;
}

/*Related Items-Access Devices*/

#related ul li.powerglide {
  background: url(../img/related/powerglide-related.jpg) 0px bottom no-repeat;
}

#related ul li.powerpicc {
  background: url(../img/related/powerpicc.png) 0px bottom no-repeat;
}

#related ul li.powerpicc_green {
  background: url(../img/related/powerpicc-green.jpg) 0px bottom no-repeat;
}

#related ul li.powerpicchf_green {
  background: url(../img/related/powerpicchf-green.jpg) 0px bottom no-repeat;
}

#related ul li.powerpiccft_green {
  background: url(../img/related/powerpiccft-green.jpg) 0px bottom no-repeat;
}

#related ul li.powerpiccsv {
  background: url(../img/related/powerpiccsv.jpg) 0px bottom no-repeat;
}

#related ul li.powerhohn_green {
  background: url(../img/related/powerhohn-green.jpg) 0px 0 no-repeat;
}

#related ul li.powerhohn_bluegreen {
  background: url(../img/related/powerhohn-bluegreen.jpg) 0px 0 no-repeat;
}

#related ul li.powerhohn_blue {
  background: url(../img/related/powerhohn-blue.jpg) 0px 0 no-repeat;
}

#related ul li.powerhohn_map {
  background: url(../img/related/powerhohn-map.jpg) 0px 0 no-repeat;
}

#related ul li.powerhohn_petition {
  background: url(../img/related/powerhohn-nurse.jpg) 0px 0 no-repeat;
}

#related ul li.powerhohn_syllabus {
  background: url(../img/related/powerhohn-syllabus.jpg) 0px 15px no-repeat;
}

/*Related Items-Care and Maintenance*/

#related ul li.maxbarrier {
  background: url(../img/related/maximal-barrier.jpg) 0px 15px no-repeat;
}

#related ul li.guardiva {
  background: url(../img/related/guardiva-related.jpg) 0px no-repeat;
}

#related ul li.guardiva_green {
  background: url(../img/related/guardiva-green-related.jpg) 0px no-repeat;
}

#related ul li.sitescrub {
  background: url(../img/related/site-scrub-related.jpg) right bottom no-repeat;
}

#related ul li.sitescrub_bluegreen {
  background: url(../img/related/site-scrub-related-bluegreen.jpg) right bottom no-repeat;
}

#related ul li.sitescrub_blue {
  background: url(../img/related/site-scrub-related-blue.jpg) right bottom no-repeat;
}

#related ul li.introducers {
  background: url(../img/related/introducers-related.jpg) right bottom no-repeat;
}

#related ul li.port_access_kit {
  background: url(../img/related/port-access-kit-related.jpg) bottom right no-repeat;
}

#related ul li.port_access_kit_bluegreen {
  background: url(../img/related/port-access-kit-bluegreen.jpg) bottom right no-repeat;
}

/*Related Items-Statlock*/

#related ul li.statlock {
  background: url(../img/related/statlock.jpg) right bottom no-repeat;
}

#related ul li.statlock_dressing_blue {
  background: url(../img/related/statlock-dressing-blue.jpg) right bottom no-repeat;
}

/*Related Items-Needles*/

#related ul li.microez {
  background: url(../img/related/microez-related.jpg) 0px bottom no-repeat;
}

#related ul li.winged {
  background: url(../img/related/winged-related.jpg) bottom right no-repeat;
}

#related ul li.huberplus {
  background: url(../img/related/huberplus-related.jpg) bottom right no-repeat;
}

#related ul li.miniloc {
  background: url(../img/related/miniloc-related.jpg) bottom right no-repeat;
}

#related ul li.liftloc {
  background: url(../img/related/liftloc-related.jpg) bottom right no-repeat;
}

#related ul li.safestep {
  background: url(../img/related/safestep-related.jpg) 0px bottom no-repeat;
}

#related ul li.powerloc {
  background: url(../img/related/powerloc-related.jpg) 0px bottom no-repeat;
}

#related ul li.powerloc_max {
  background: url(../img/related/powerloc-max-related.jpg) 0px bottom no-repeat;
}

#related ul li.powerloc_max_blue {
  background: url(../img/related/powerloc-max-related-blue.jpg) 0px bottom no-repeat;
}

#related ul li.powerloc_blue {
  background: url(../img/related/powerloc-blue.jpg) 0px bottom no-repeat;
}

/*Related Items-Company*/

#related ul li.catalog {
  background: url(../img/related/product-catalog.jpg) 0px bottom no-repeat;
}

#related ul li.bd-products {
  background: url(../img/related/infection-prevention.png) 0px bottom no-repeat;
}

#related ul li.tradeshow {
  background: url(../img/related/tradeshow-related.jpg) bottom right no-repeat;
}

#related ul li.education {
  background: url(../img/related/education-related.jpg) bottom right no-repeat;
}

#related ul li:nth-of-type(even) {
  background-color: rgba(209, 236, 231, 1);
}

#related ul li:first-of-type {
  background-color: rgba(202, 240, 212, 1);
}

#related ul li:last-of-type {
  background-color: rgba(220, 235, 240, 1);
}
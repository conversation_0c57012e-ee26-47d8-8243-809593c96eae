@font-face{
	font-family:'FS Albert Pro';
	src:	url('../fonts/FSAlbertWeb-Regular.eot');
	src:	url('../fonts/FSAlbertWeb-Regular.eot?#iefix') format('embedded-opentype'),
			url('../fonts/FSAlbertWeb-Regular.woff2') format('woff2'),
			url('../fonts/FSAlbertWeb-Regular.woff') format('woff'),
			url('../fonts/FSAlbertWeb-Regular.ttf') format('truetype'),
			url('../fonts/FSAlbertWeb-Regular.svg') format('svg');
}

@font-face{
	font-family:'FS Albert Pro Light';
	src:	url('../fonts/FSAlbertWeb-Light.eot');
	src:	url('../fonts/FSAlbertWeb-Light.eot?#iefix') format('embedded-opentype'),
			url('../fonts/FSAlbertWeb-Light.woff2') format('woff2'),
			url('../fonts/FSAlbertWeb-Light.woff') format('woff'),
			url('../fonts/FSAlbertWeb-Light.ttf') format('truetype'),
			url('../fonts/FSAlbertWeb-Light.svg') format('svg');
}

@font-face{
	font-family:'FS Albert Pro Bold';
	src:	url('../fonts/FSAlbertWeb-Bold.eot');
	src:	url('../fonts/FSAlbertWeb-Bold.eot?#iefix') format('embedded-opentype'),
			url('../fonts/FSAlbertWeb-Bold.woff2') format('woff2'),
			url('../fonts/FSAlbertWeb-Bold.woff') format('woff'),
			url('../fonts/FSAlbertWeb-Bold.ttf') format('truetype'),
			url('../fonts/FSAlbertWeb-Bold.svg') format('svg');
}
/*
 * jQuery FlexSlider v2.6.3
 * http://www.woothemes.com/flexslider/
 *
 * Copyright 2012 WooThemes
 * Free to use under the GPLv2 and later license.
 * http://www.gnu.org/licenses/gpl-2.0.html
 *
 * Contributing author: <PERSON> (@mbmufffin)
 *
 */
/* ====================================================================================================================
 * RESETS
 * ====================================================================================================================*/
.flex-container a:hover,
.flex-slider a:hover {
  outline: none;
}
.slides,
.slides > li,
.flex-control-nav,
.flex-direction-nav {
  margin: 0;
  padding: 0;
  list-style: none;
}
.flex-pauseplay span {
  text-transform: capitalize;
}
.flexslider .slides > li:first-child{display:block;-webkit-backface-visibility:visible;}
.flexslider .slides > li{display:none;-webkit-backface-visibility:hidden;}
.flexslider .slides > div:first-child{display:block;-webkit-backface-visibility:visible;}
.flexslider .slides > div{display:none;-webkit-backface-visibility:hidden;}
.flexslider .hero-slides > div:first-child{display:block;-webkit-backface-visibility:visible;}
.flexslider .hero-slides > div{display:none;-webkit-backface-visibility:hidden;}
.flexslider .quote-slides > div:first-child{display:block;-webkit-backface-visibility:visible;}
.flexslider .quote-slides > div{display:none;-webkit-backface-visibility:hidden;}
.full-slider .flexslider.slider .flex-viewport{height:0;}
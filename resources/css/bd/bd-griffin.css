html {
  -ms-overflow-style: scrollbar;
}

body {
  background: #004593;
  color: #6f6c6c;
  font-size: 14px;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  line-height: 20px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.h1, h1 {
  color: #444444;
  font-size: 36px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-weight: normal;
  line-height: 40px;
  margin: 0 0 30px;
}

.h1.xl, h1.xl {
  font-size: 50px;
  line-height: 50px;
}

.h1 a, h1 a {
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
}

.h1.xl .trademark, h1.xl .trademark {
  font-size: 14px;
}

.h1.xl sup, h1.xl sup, .h1 sup, h1 sup {
  font-size: 50%;
  top: -0.8em;
}

.h2, h2 {
  clear: right;
  color: #444444;
  font-size: 32px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-weight: normal;
  line-height: 38px;
  margin: 0 0 30px;
}

.h2.border-bottom, h2.border-bottom {
  border-bottom: 2px solid #a9dcd6;
  padding-bottom: 10px;
}

.h3, h3 {
  color: #444444;
  font-size: 28px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-weight: normal;
  line-height: 36px;
  margin: 0 0 15px;
}

.h3.border, h3.border {
  border-left: none;
  border-right: none;
  color: #8b8b8b;
  padding: 5px 0;
}

.h4, h4 {
  color: #444444;
  font-size: 18px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-weight: normal;
  line-height: 24px;
  margin: 0 0 15px;
}

.h4.border-bottom, h4.border-bottom {
  padding-bottom: 10px;
}

.h5, h5 {
  color: #444444;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
  font-weight: normal;
  letter-spacing: .05em;
  margin: 0 0 10px;
}

.h6, h6 {
  color: #444444;
  font-size: 12px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-weight: normal;
  margin: 0 0 10px;
}

strong {
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-weight: normal;
}

hr {
  background: #e5e5e5;
  border: none;
  height: 1px;
  margin: 0 0 30px;
}

blockquote {
  font-size: 28px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  line-height: 34px;
  margin: 0 0 30px;
  padding: 30px;
}

blockquote .quote {
  display: block;
  margin: 0 0 15px;
  position: relative;
}

blockquote .quote:before {
  color: #00B8B0;
  content: "\f10d";
  font-family: FontAwesome;
  font-size: 20px;
  left: -25px;
  position: absolute;
  top: -5px;
}

blockquote .quote:after {
  color: #00B8B0;
  content: "\f10e";
  font-family: FontAwesome;
  font-size: 20px;
  margin-left: 10px;
  position: relative;
  top: -5px;
}

blockquote cite {
  display: block;
  font-size: 16px;
  line-height: 20px;
  opacity: 0.6;
}

p {
  margin: 0 0 30px;
}

ol {
  margin: 0 0 30px 20px;
  padding: 0;
}

ol li {
  margin: 0 0 10px;
}

ol ol {
  list-style-type: lower-alpha;
  margin-bottom: 20px;
  margin-top: 20px;
}

ul {
  margin: 0 0 30px 30px;
  padding: 0;
}

ul li {
  margin: 0 0 10px;
}

ul ul {
  margin-bottom: 20px;
  margin-top: 20px;
}

figure {
  margin: 0 0 30px;
}

figure img {
  display: block;
  max-width: 100%;
}

figure figcaption {
  display: block;
  font-size: 14px;
  line-height: 20px;
  margin: 10px 0 0 0;
  padding: 0;
}

form label {
  display: block;
}

form label span.label-text {
  display: block;
  margin: 0 0 5px;
}

form div.radios {
  color: #444;
  margin: 0 0 15px;
}

form div.radios label {
  padding-left: 25px;
  position: relative;
}

form div.radios label input {
  left: 0;
  position: absolute;
  top: 3px;
}

form div.checkboxes {
  color: #444;
  margin: 0 0 15px;
}

form div.checkboxes label {
  padding-left: 25px;
  position: relative;
}

form div.checkboxes label input {
  left: 0;
  position: absolute;
  top: 3px;
}

form p.forgot {
  margin: 5px 0;
}

input[type=password],
input[type=text],
select,
textarea {
  background: #fff;
  border: 1px solid #e5e5e5;
  color: #444444;
  font-family: Arial, Helvetica, Verdana, sans-serif;
  display: block;
  font-size: 12px;
  height: 36px;
  line-height: 20px;
  outline: none;
  padding: 8px;
  width: 100%;
}

textarea {
  height: 112px;
  resize: vertical;
}

textarea.short {
  height: 87px;
}

input[type=password]:focus,
input[type=text]:focus,
select:focus,
textarea:focus {
  border-color: #004593;
}

input[type=password].error,
input[type=text].error,
select.error,
textarea.error {
  border-color: #8D2A90;
}

input[type=password],
input[type=email],
textarea,
button[type=submit],
input[type=submit],
button[type=reset],
input[type=reset] {
  -webkit-appearance: none;
}

button[type=submit],
button[type=reset],
input[type=submit],
input[type=reset] {
  background: #004593;
  border: none;
  color: #ffffff;
  display: inline-block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
  line-height: 12px;
  min-width: 100px;
  padding: 10px 15px;
  text-decoration: none;
  white-space: nowrap;
}

button[type=submit].large,
button[type=reset].large,
input[type=submit].large,
input[type=reset].large {
  font-size: 14px;
  line-height: 20px;
  padding: 15px;
}

button[type=reset],
input[type=reset] {
  background: #8b8b8b;
  color: #ffffff;
}

button[type=submit]:hover,
button[type=submit]:focus,
button[type=reset]:hover,
button[type=reset]:focus,
input[type=submit]:hover,
input[type=submit]:focus,
input[type=reset]:hover,
input[type=reset]:focus {
  color: #fff;
  opacity: 0.8;
}

a {
  color: #004593;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  text-decoration: none;
}

a:hover,
a:focus {
  opacity: 0.6;
  text-decoration: none;
}

::selection {
  background: #e5e5e5;
  color: #444444;
}

::-moz-selection {
  background: #e5e5e5;
  color: #444444;
}

/* Colors
=======================================================================================================*/
/* Grays
	Black 		#000000
	Dark Gray	#444444
	Gray 		#8b8b8b
	Light Gray	#e5e5e5
	Off White	#f7f7f7
	White		#ffffff
*/
.black {
  background: #000 !important;
}

.black-text {
  color: #000 !important;
}

.gray {
  background: #8b8b8b !important;
}

.gray-text {
  color: #8b8b8b !important;
}

.dark-gray {
  background: #444444 !important;
}

.dark-gray-text {
  color: #444444 !important;
}

.light-gray {
  background: #e5e5e5 !important;
}

.light-gray-text {
  color: #e5e5e5 !important;
}

.off-white {
  background: #f7f7f7 !important;
}

.off-white-text {
  color: #f7f7f7 !important;
}

.white {
  background: #fff !important;
}

.white-text {
  color: #fff !important;
}

/* Colors
	Primary 		#004593
	Primary Dark 	#023970
	Primary Light 	#8cc6ec
	Accent 1  		#8d2a90
	Accent 1 Dark 	#712886
	Accent 1 Light 	#f4c9df
	Accent 2  		#00bce8
	Accent 2 Dark 	#0090b5
	Accent 2 Light 	#addfeb
	Accent 3  		#00b8b0
	Accent 3 Dark 	#009988
	Accent 3 Light 	#a9dcd6
	Accent 4		#F27707
	Accent 5		#FDB913
	Accent 6 		#FEF48F
	Accent 7 		#8fcf4f
	Accent 8 		#e546a7
	Accent 9 		#d10018
	Accent 10		##00a651
*/
.primary {
  background: #004593 !important;
}

.primary-text {
  color: #004593 !important;
}

.primary-dark {
  background: #023970 !important;
}

.primary-dark-text {
  color: #023970 !important;
}

.primary-light {
  background: #8cc6ec !important;
}

.primary-light-text {
  color: #8cc6ec !important;
}

.accent-1 {
  background: #8d2a90 !important;
}

.accent-1-text {
  color: #8d2a90 !important;
}

.accent-1-dark {
  background: #712886 !important;
}

.accent-1-dark-text {
  color: #712886 !important;
}

.accent-1-light {
  background: #f4c9df !important;
}

.accent-1-light-text {
  color: #f4c9df !important;
}

.accent-2 {
  background: #00bce8 !important;
}

.accent-2-text {
  color: #00bce8 !important;
}

.accent-2-dark {
  background: #0090b5 !important;
}

.accent-2-dark-text {
  color: #0090b5 !important;
}

.accent-2-light {
  background: #addfeb !important;
}

.accent-2-light-text {
  color: #addfeb !important;
}

.accent-3 {
  background: #00b8b0 !important;
}

.accent-3-text {
  color: #00b8b0 !important;
}

.accent-3-dark {
  background: #009988 !important;
}

.accent-3-dark-text {
  color: #009988 !important;
}

.accent-3-light {
  background: #a9dcd6 !important;
}

.accent-3-light-text {
  color: #a9dcd6 !important;
}

.accent-4 {
  background: #F27707 !important;
}

.accent-4-text {
  color: #F27707 !important;
}

.accent-4-light {
  background: #fef1e6 !important;
}

.accent-4-light-text {
  color: #fef1e6 !important;
}

.accent-5 {
  background: #FDB913 !important;
}

.accent-5-text {
  color: #FDB913 !important;
}

.accent-6 {
  background: #FEF48F !important;
}

.accent-6-text {
  color: #FEF48F !important;
}

.accent-7 {
  background: #8fcf4f !important;
}

.accent-7-text {
  color: #8fcf4f !important;
}

.accent-8 {
  background: #e546a7 !important;
}

.accent-8-text {
  color: #e546a7 !important;
}

.accent-9 {
  background: #d10018 !important;
}

.accent-9-text {
  color: #d10018 !important;
}

.accent-10 {
  background: #00a651 !important;
}

.accent-10-text {
  color: #00a651 !important;
}

.gradient {
  background: #f7f7f7;
  background: -moz-radial-gradient(center, ellipse cover, rgba(247, 247, 247, 1) 0%, rgba(229, 229, 229, 1) 100%);
  background: -webkit-radial-gradient(center, ellipse cover, rgba(247, 247, 247, 1) 0%, rgba(229, 229, 229, 1) 100%);
  background: radial-gradient(ellipse at center, rgba(247, 247, 247, 1) 0%, rgba(229, 229, 229, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f7f7f7', endColorstr='#e5e5e5', GradientType=1);
}

/* Re-Use
=======================================================================================================*/
.align-center {
  text-align: center;
}

.align-left {
  text-align: left;
}

.align-right {
  text-align: right;
}

.border {
  border: 1px solid #e5e5e5;
  position: relative;
}

.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

.border-left {
  border-left: 1px solid #e5e5e5;
}

.border-right {
  border-right: 1px solid #e5e5e5;
}

.border-top {
  border-top: 1px solid #e5e5e5;
}

.border-none {
  border: none !important;
}

.block {
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  position: relative;
}

.block.badge {
  min-height: 228px;
}

.block.accent-top:before {
  background: #00b8b0;
  content: " ";
  display: block;
  height: 4px;
  left: -1px;
  position: absolute;
  top: -1px;
  right: -1px;
}

.block.accent-bottom:after {
  background: #f7f7f7;
  bottom: 0;
  content: " ";
  display: block;
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

.card {
  background: #fff;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  padding: 20px;
}

.clear {
  clear: both;
}

.container {
  max-width: 970px
}

.container-fluid {
  max-width: 1900px
}

.error {
  display: block;
  color: #8D2A90;
}

.fade {
  opacity: 0.8;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.large {
  font-size: 18px;
  line-height: 26px;
}

.large-xl {
  font-size: 22px;
  line-height: 26px;
}

.nowrap {
  white-space: nowrap;
}

.normal {
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
}

.margin-full {
  margin: 0 0 30px !important;
}

.margin-half {
  margin: 0 0 15px !important;
}

.margin-third {
  margin: 0 0 10px !important;
}

.margin-none {
  margin: 0 !important;
}

.padding {
  padding: 30px;
}

.padding-sm {
  padding: 15px;
}

.padding-none {
  padding: 0 !important;
}

.pipe {
  display: inline-block;
  margin: 0 7px;
}

.scroll-content {
  max-height: 270px;
  overflow: auto;
}

.side-padding {
  padding: 0 30px;
}

.small {
  font-size: 12px;
  line-height: 18px;
  margin: 0 0 15px;
}

.small h4 {
  margin: 0 0 10px;
}

.small p {
  margin: 0 0 15px;
}

.small p.img-wrap {
  margin: 0 0 10px;
}

.small ul {
  margin-bottom: 15px;
}

.small hr {
  margin: 0 0 15px;
}

.trademark {
  font-size: 12px;
  vertical-align: super;
}

.spacer {
  height: 30px;
}

.spacer.half {
  height: 15px;
}

.stat {
  font-size: 120px;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  line-height: 126px;
  margin: 0 0 15px;
}

.transparent {
  background-color: transparent;
}

.uppercase {
  text-transform: uppercase;
}

.warning {
  font-size: 60px;
  line-height: 60px;
  margin: 0 0 15px;
}

.no-bull {
  list-style: none;
  margin: 0 0 30px;
  padding: 0;
}

.no-bull .pipe {
  margin: 0 3px;
}

.icon-list {
  list-style: none;
  margin: 0 0 30px;
  padding: 0 0 0 20px;
}

.icon-list li {
  position: relative;
}

.icon-list i {
  left: -20px;
  position: absolute;
  top: 3px;
}

.display-block {
  display: block;
  width: 100%;
}

.inline-block {
  display: inline-block;
  min-width: 120px;
}

.inline {
  margin-left: 0;
}

.inline li {
  display: inline;
  margin: 0 20px 0 0;
}

.word-break {
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}

.vertical-wrap {
  display: table;
}

.vertical-wrap .vertical-middle {
  display: table-cell;
  vertical-align: middle;
}

.center-center {
  background-position: center center !important;
}

.center-top {
  background-position: center top !important;
}

.center-bottom {
  background-position: center bottom !important;
}

.left-center {
  background-position: left center !important;
}

.left-top {
  background-position: left top !important;
}

.left-bottom {
  background-position: left bottom !important;
}

.right-center {
  background-position: right center !important;
}

.right-top {
  background-position: right top !important;
}

.right-bottom {
  background-position: right bottom !important;
}

.row.no-column-padding>div {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.button-group {
  margin: 0 -5px 20px;
  overflow: hidden;
}

.button-group .button {
  float: left;
  margin: 0 5px 10px;
}

.button {
  background: #004593;
  color: #ffffff;
  display: inline-block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
  line-height: 16px;
  padding: 10px 15px;
  text-decoration: none;
  white-space: nowrap;
}

.button.full-width {
  width: 100%;
}

.button.large {
  font-size: 14px;
  line-height: 20px;
  padding: 15px;
}

.button:hover,
.button:focus {
  color: #fff;
  opacity: 0.8;
}

.button.disabled {
  background: #dcdcdc;
  cursor: default;
}

.button.disabled:hover,
.button.disabled:focus {
  opacity: 1;
}

.button.add {
  background: #dcdcdc;
  color: #444;
  font-size: 18px;
  line-height: 20px;
  padding: 8px;
  text-align: center;
  width: 44px;
}

.button.added {
  font-size: 18px;
  line-height: 20px;
  padding: 8px;
  text-align: center;
  width: 44px;
}

.label-button {
  background: #fff;
  border: 1px solid #e5e5e5;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
  margin: 0 10px 0 0;
  padding: 2px 15px;
}

.label-button:hover,
.label-button:focus {
  background: #004593;
  color: #fff;
  opacity: 1;
}

.switch {
  margin: 0 0 10px;
  overflow: hidden;
}

.switch a {
  border: 1px solid #e5e5e5;
  color: #8b8b8b;
  display: block;
  float: left;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  overflow: hidden;
  text-align: center;
  padding: 4px 0;
  position: relative;
  text-transform: uppercase;
  width: 60px;
}

.switch a:after {
  background: #f7f7f7;
  bottom: 0;
  content: " ";
  display: block;
  height: 4px;
  position: absolute;
  width: 100%;
}

.switch a.active {
  background: #004593;
  border: 1px solid #023970;
  color: #fff;
}

.switch a.active:after {
  background: #004593;
}

.tag {
  color: #fff;
  display: inline-block;
  font-size: 10px;
  line-height: 18px;
  margin-right: 5px;
  text-align: center;
  text-transform: uppercase;
  width: 36px;
}

.tag:hover,
.tag:focus {
  color: #fff;
}

.tag.as {
  background: #98d2d9;
}

.tag.cl {
  background: #0466bb;
}

.tag.dc {
  background: #d7d6d1;
}

.tag.di {
  background: #fd5712;
}

.tag.hs {
  background: #fd5712;
}

.tag.io {
  background: #a1175b;
}

.tag.ip {
  background: #0466bb;
}

.tag.le {
  background: #ffb478;
}

.tag.mm {
  background: #78c342;
}

.tag.ms {
  background: #e7c530;
}

.tag.or {
  background: #610533;
}

.tag.rc {
  background: #bf9c6a;
}

.tag.st {
  background: #a1175b;
}

.tag.th {
  background: #e7c530;
}

.tag.wh {
  background: #01b49d;
}

.product-details {
  list-style: none;
  margin: 0 0 30px;
}

.product-details li {
  padding-left: 150px;
  position: relative;
}

.product-details li .label-text {
  color: #444;
  left: 0;
  position: absolute;
  top: 0;
}

.product-details li input[type=text] {
  display: inline-block;
  font-size: 12px;
  height: auto;
  line-height: 18px;
  max-width: 40px;
  padding: 0 5px;
  position: relative;
  top: -1px;
}

.square {
  list-style: square;
}

.checkmark {
  list-style: none;
  margin: 0 0 30px;
}

.checkmark li {
  padding-left: 30px;
  position: relative;
}

.checkmark li:before {
  color: #00b8b0;
  content: "\f00c";
  font-family: FontAwesome;
  font-size: 20px;
  left: 0;
  position: absolute;
  top: 0;
}

/* Components
=======================================================================================================*/
.table-wrap {
  border: 1px solid #d3d3d3;
  margin: 0 0 30px;
  overflow: auto;
}

.table-wrap::-webkit-scrollbar {
  -webkit-appearance: none;
}

.table-wrap::-webkit-scrollbar:vertical {
  width: 11px;
}

.table-wrap::-webkit-scrollbar:horizontal {
  height: 11px;
}

.table-wrap::-webkit-scrollbar-thumb {
  border-radius: 8px;
  border: 2px solid #fff;
  background-color: #e5e5e5;
}

.table-wrap::-webkit-scrollbar-track {
  background-color: #fff;
  border-radius: 8px;
}

.table-scroll {
  position: relative;
}

.table-scroll .table-pager {
  display: none;
  position: absolute;
  right: 10px;
  top: 10px;
}

.table-wrap.solutions {
  border: none;
  margin: 0 -15px 30px;
  text-align: center;
}

.table-wrap.solutions table h3 {
  font-size: 18px;
  line-height: 20px;
  margin: 0 0 5px;
}

.table-wrap.solutions table h4 {
  margin: 0 0 5px;
}

.table-wrap.solutions table thead th:first-child {
  background: transparent;
  color: inherit;
  max-width: 100%;
  min-width: 250px;
}

.table-wrap.solutions table thead th {
  background: #009988;
  background: -moz-linear-gradient(left, #009988 0%, #00b8b0 100%);
  background: -webkit-linear-gradient(left, #009988 0%, #00b8b0 100%);
  background: linear-gradient(to right, #009988 0%, #00b8b0 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#009988', endColorstr='#00b8b0', GradientType=1);
  border: none;
  color: #fff;
  min-width: 150px;
  max-width: 150px;
  padding: 10px;
  position: relative;
  text-align: center;
}

.table-wrap.solutions table thead th .plus {
  background: #fff;
  border-radius: 100%;
  color: #009988;
  display: inline-block;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-size: 22px;
  line-height: 16px;
  height: 20px;
  margin-top: -10px;
  position: absolute;
  right: -10px;
  top: 50%;
  width: 20px;
  z-index: 1;
}

.table-wrap.solutions table thead th h4 {
  color: #fff;
}

.table-wrap.solutions table tbody tr td:first-child {
  border-left: none;
  text-align: left;
  vertical-align: top;
}

.table-wrap.solutions table tbody tr td:last-child {
  border-right: none;
}

.table-wrap.solutions table tbody tr td {
  border: 1px solid #D3D3D3;
  vertical-align: middle;
}

.table-wrap.solutions table tbody tr .check {
  background: #fff;
  border: 1px solid #00B8B0;
  border-radius: 100%;
  color: #fff;
  display: inline-block;
  height: 36px;
  line-height: 36px;
  width: 36px;
}

.table-wrap.solutions table tbody tr .check .border {
  background: #00B8B0;
  border-radius: 100%;
  display: inline-block;
  height: 30px;
  line-height: 30px;
  width: 30px;
}

.more-button {
  padding-left: 40px;
  position: relative;
}

.more-button:hover,
.more-button:focus {
  opacity: 1;
}

.more-button .plus-minus {
  background: #00B8B0;
  border-radius: 100%;
  color: #fff;
  display: block;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-size: 30px;
  height: 30px;
  left: 0;
  line-height: 26px;
  position: absolute;
  top: -7px;
  width: 30px;
}

.more-button .minus {
  display: none;
}

.more-button .less-text {
  display: none;
}

.more-button.less .plus {
  display: none;
}

.more-button.less .more-text {
  display: none;
}

.more-button.less .minus {
  display: inline;
}

.more-button.less .less-text {
  display: inline;
}

.table-wrap table th[role=columnheader]:not(.no-sort) {
  cursor: pointer;
}

.table-wrap table th[role=columnheader]:not(.no-sort):after {
  content: "\f0d7";
  font-family: FontAwesome;
  margin-left: 10px;
  -ms-user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.table-wrap table th[aria-sort=ascending]:not(.no-sort):after {
  content: "\f0d8";
}

table {
  border-collapse: collapse;
  font-size: 12px;
  line-height: 18px;
  width: 100%;
}

table thead tr.header {
  border-bottom: 4px solid #d3d3d3;
  border-top: 2px solid #e5e5e5;
}

table thead tr.header th {
  background: #fff;
  font-size: 14px;
  padding: 10px;
}

table thead tr.header.accent-3 {
  border: none;
}

table thead tr.header.accent-3 th {
  background: #00b8b0;
  border-color: #009988;
  color: #fff;
}

table thead th:first-child {
  border-left: 0;
}

table thead th {
  border-bottom: 1px solid #d3d3d3;
  border-left: 1px solid #d3d3d3;
  background: #f7f7f7;
  color: #444;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-weight: normal;
  text-align: left;
  padding: 5px 10px;
  vertical-align: middle;
}

table thead th a {
  display: block;
  text-transform: uppercase;
}

table tbody.reverse tr:nth-child(odd) {
  background: #f7f7f7;
}

table tbody tr:nth-child(even) {
  background: #f7f7f7;
}

table tbody td:first-child {
  border-left: 0;
}

table tbody td {
  border-left: 1px solid #d3d3d3;
  font-size: 12px;
  padding: 10px;
  vertical-align: top;
}

table tbody td i {
  font-size: 14px;
}

table tbody td ul {
  margin: 0 0 0 15px;
  padding: 0;
}

table tbody tr.divider td {
  background: #f7f7f7;
  border-bottom: 1px solid #d3d3d3;
  border-top: 1px solid #d3d3d3;
  color: #444;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
}

table tbody tr.on {
  background: rgba(140, 198, 236, 0.2);
  box-shadow: inset 0 1px 0 0px #004593, inset 0 -1px 0 0px #004593;
}

table.hover tbody tr:hover {
  background: rgba(140, 198, 236, 0.2);
  cursor: pointer;
}

form .error-message {
  background: #004593;
  color: #fff;
  margin: 0 0 30px;
  padding: 10px;
}

form .error-message p {
  margin: 0 0 5px;
}

form p {
  margin: 0 0 15px;
}

form hr {
  margin: 0 0 15px;
}

form.search.border {
  border: 1px solid #e5e5e5 !important;
}

form.search {
  background: #fff;
  border: 1px solid #e5e5e5;
  width: 100%;
}

form.search .pad {
  padding: 2px;
  padding-right: 32px;
  position: relative;
}

form.search input[type=text] {
  border: none;
  height: 30px;
  padding: 5px 8px;
}

form.search button {
  background: transparent;
  bottom: 0;
  color: #004593;
  font-size: 14px;
  height: 30px;
  min-width: 0;
  padding: 0;
  position: absolute;
  right: 2px;
  text-align: center;
  top: 2px;
  width: 30px;
}

form.search button:hover,
form.search button:focus {
  color: #004593;
}

form.promo {
  background: #fff;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  padding-right: 50px;
  position: relative;
  width: 100%;
}

form.promo input[type=text] {
  border: none;
  height: 30px;
  padding: 5px 8px;
}

form.promo button {
  bottom: -1px;
  height: 32px;
  min-width: 0;
  padding: 0;
  position: absolute;
  right: 0;
  text-align: center;
  top: -1px;
  width: 50px;
}

form.promo button:hover,
form.promo button:focus {
  color: #004593;
}

form.search.text-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  padding-right: 100px;
}

form.search.text-button input[type=text] {
  background: transparent;
  color: #444;
  height: 40px;
}

form.search.text-button input[type=text]:focus {
  background: #fff;
}

form.search.text-button button {
  background: #004593;
  height: 100%;
  color: #fff;
  right: 0;
  top: 0;
  width: 100px;
}

.suggestion-wrap {
  margin: 0 auto;
  max-width: 600px;
  position: relative;
}

.search-type-examples .header-search-type {
  display: block;
  left: auto;
  margin: 0 0 30px;
  overflow: hidden;
  position: relative;
  right: auto;
  top: auto;
  width: 100%;
}

.header-search-type {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  display: none;
  font-size: 14px;
  position: absolute;
  right: 0;
  top: 60px;
  width: 100%;
  z-index: 2;
}

.header-search-type .pad {
  padding: 15px 15px 5px;
}

.header-search-type .side-right {
  padding: 15px 15px 0;
}

.header-search-type .side-left {
  padding: 15px 15px 0;
}

.header-search-type .message {
  border-bottom: 1px solid #e5e5e5;
  margin: -15px -15px 20px;
  padding: 10px 15px;
  text-align: center;
}

.header-search-type h4 {
  margin: 0 0 10px;
}

.header-search-type h4.heading {
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 3px;
}

.header-search-type h4.heading .right-text {
  float: right;
  font-size: 12px;
  margin: 0 0 0 10px;
}

.header-search-type p {
  margin: 0 0 15px;
}

.header-search-type p.found-text {
  font-size: 12px;
  margin: -5px 0 15px;
  text-align: right;
}

.header-search-type p.type-text {
  font-size: 12px;
  font-weight: 600;
  margin: -5px 0 0;
  text-align: right;
}

.header-search-type p.results-text {
  font-size: 12px;
  margin: -15px 0 20px;
  text-align: right;
}

.header-search-type ul {
  list-style: none;
  margin: 0 0 20px;
}

.header-search-type ul li.thumb {
  min-height: 60px;
  padding-left: 90px;
  position: relative;
}

.header-search-type ul li.thumb .img {
  left: 0;
  position: absolute;
  top: 0;
}

.header-search-type ul li.thumb .img img {
  max-height: 60px;
  max-width: 80px;
}

.header-search-type ul.support li {
  margin: 0 0 5px;
}

.header-search-type ul.suggestion-list {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  font-size: 12px;
  margin: -15px -15px 10px;
  padding: 10px;
}

.header-search-type ul.suggestion-list li {
  margin: 0 0 5px;
}

.header-search-type .description {
  display: block;
  font-size: 12px;
}

.search-type-examples .search-type {
  display: block;
  left: auto;
  margin: 0 0 30px;
  overflow: hidden;
  position: relative;
  right: auto;
  top: auto;
}

.search-type {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  display: none;
  font-size: 14px;
  left: 0;
  padding: 15px 15px 0;
  position: absolute;
  right: 100px;
  top: 100%;
  z-index: 2;
}

.search-type .message {
  border-bottom: 1px solid #e5e5e5;
  margin: -15px -15px 20px;
  padding: 10px 15px;
  text-align: center;
}

.search-type h4 {
  margin: 0 0 10px;
}

.search-type h4.heading {
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 3px;
}

.search-type h4.heading .right-text {
  float: right;
  font-size: 12px;
  margin: 0 0 0 10px;
}

.search-type p {
  margin: 0 0 15px;
}

.search-type p.found-text {
  font-size: 12px;
  font-weight: 600;
  margin: -5px 0 10px;
  text-align: right;
}

.search-type p.type-text {
  font-size: 12px;
  font-weight: 600;
  margin: -5px 0 0;
  text-align: right;
}

.search-type p.results-text {
  font-size: 12px;
  margin: -15px 0 20px;
  text-align: right;
}

.search-type ul {
  list-style: none;
  margin: 0 0 20px;
}

.search-type ul li.thumb {
  min-height: 60px;
  padding-left: 90px;
  position: relative;
}

.search-type ul li.thumb .img {
  left: 0;
  position: absolute;
  top: 0;
}

.search-type ul li.thumb .img img {
  max-height: 60px;
  max-width: 80px;
}

.search-type ul.support li {
  margin: 0 0 5px;
}

.search-type ul.suggestion-list {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  font-size: 12px;
  margin: -15px -15px 10px;
  padding: 10px;
}

.search-type ul.suggestion-list li {
  margin: 0 0 5px;
}

.search-type .description {
  display: block;
  font-size: 12px;
}

.search-type .bar {
  background: #00b8b0;
  height: 4px;
  margin: 0 -15px 20px;
}

.search-suggestions {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  display: none;
  font-size: 12px;
  left: 0;
  margin-top: 10px;
  padding: 15px 15px 0;
  position: absolute;
  top: 100%;
  width: 100%;
  z-index: 2;
}

.search-suggestions span.results {
  color: #8b8b8b;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  text-transform: none;
}

.search-suggestions ul {
  list-style: none;
  margin: 0 0 20px;
}

.search-suggestions ul li a {
  color: #8b8b8b;
}

.search-suggestions p {
  list-style: none;
  margin: 0 0 15px;
}

.search-suggestions a.close {
  color: #8b8b8b;
  font-size: 30px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  position: absolute;
  top: 0;
  right: 5px;
}

.checks-list {
  list-style: none;
  margin-left: 0;
}

.checks-list li {
  padding-left: 25px;
  position: relative;
}

.checks-list li:before {
  color: #00B8B0;
  content: "\f00c";
  font-family: FontAwesome;
  font-size: 18px;
  left: 0;
  position: absolute;
  top: -2px;
}

.faq-list {
  list-style: none;
  margin-left: 0;
}

.faq-list li {
  padding-left: 25px;
  position: relative;
}

.faq-list li:before {
  color: #00B8B0;
  content: "Q.";
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 14px;
  left: 0;
  position: absolute;
  top: 0;
}

.thumbs-list {
  list-style: none;
  margin-left: 0;
}

.thumbs-list li {
  margin: 25px 0;
  padding-left: 40px;
  position: relative;
}

.thumbs-list li img {
  left: 0;
  position: absolute;
  top: -9px;
}

.share {
  margin: 0 0 30px;
}

.share img {
  margin: 5px 7px 0 0;
}

.banner {
  margin: 0 0 30px;
  max-width: 100%;
  position: relative;
}

.banner img {
  max-width: 100%;
}

.banner .caption {
  background: rgba(0, 0, 0, 0.4);
  bottom: 0;
  color: #fff;
  left: 0;
  margin: 0;
  padding: 10px 20px;
  position: absolute;
  width: 100%;
}

.banner .caption h4 {
  color: #fff;
  margin: 0;
}

.hero {
  min-height: 250px;
  overflow: hidden;
  position: relative;
}

.hero img {
  left: 50%;
  margin-left: -470px;
  min-height: 250px;
  position: absolute;
  top: 0;
  width: 940px;
}

.hero .caption {
  background: rgba(0, 0, 0, 0.4);
  bottom: 0;
  color: #fff;
  left: 0;
  margin: 0;
  padding: 10px 60px 10px 20px;
  position: absolute;
  width: 100%;
}

.hero .caption h4 {
  border-left: 6px solid #00B8B0;
  color: #fff;
  margin-left: -20px;
  padding-left: 14px;
}

.hero .caption p {
  max-width: 316px;
}

.tint-overlay {
  bottom: 0;
  left: 0;
  opacity: 0.7;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 0;
}

.square-img-wrap {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.hero-img-wrap {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}

.hero-img-wrap .bg-img {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  bottom: 0;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
}

.float-img-wrap {
  overflow: hidden;
  position: relative;
}

.float-img {
  text-align: center;
}

.float-img img {
  max-width: 100%;
}

.banner-img {
  margin: 0 0 30px;
  position: relative;
}

.banner-img .content h2 {
  margin: 0 0 15px;
}

.banner-img .content p {
  margin: 0 0 15px;
}

.img-box {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  margin: 0 0 30px;
  position: relative;
}

.img-box .content {
  color: #fff;
  padding: 45px 30px 30px;
  position: relative;
  z-index: 1;
}

.img-box .content h5 {
  color: #fff;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
}

.img-box .content h4 {
  color: #fff;
  font-size: 20px;
}

.img-box .content a {
  color: #fff;
}

.icon-box {
  margin: 0 auto 30px;
  max-width: 170px;
  overflow: hidden;
}

.icon-box .img-wrap {
  margin: 0 0 10px;
}

.icon-box .img-wrap img {
  max-width: 100%;
  width: auto;
}

.icon-box p {
  margin: 0 0 10px;
}

.details-box {
  padding: 0 30px;
}

.details-box ul {
  border-top: 5px solid #e5e5e5;
  list-style: none;
  margin: 0;
  padding: 30px 0;
}

.details-box ul li {
  margin: 0 0 20px;
  position: relative;
  padding-left: 120px;
}

.details-box ul li strong {
  left: 0;
  position: absolute;
  top: 0;
}

.icon-section {
  margin: 0 auto;
  max-width: 800px;
  position: relative;
}

.icon-section:after,
.icon-section:before {
  border-top: 3px dotted #fff;
  content: " ";
  display: none;
  height: 1px;
  left: 50%;
  position: absolute;
  top: 60px;
  width: 60px;
}

.icon-section:after {
  margin-left: 40px;
  -ms-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg)
}

.icon-section:before {
  margin-left: -100px;
  -ms-transform: rotate(-45deg);
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg)
}

.icon-section .icon {
  margin: 0 0 5px;
}

.icon-section h4 {
  margin: 0 0 5px;
}

.img-link-wrap {
  font-size: 12px;
  line-height: 18px;
  overflow: hidden;
  position: relative;
}

.img-link-wrap a:hover img,
.img-link-wrap a:focus img {
  opacity: 0.8;
}

.img-link-wrap .img {
  background: #e5e5e5;
  display: block;
  position: relative;
  text-align: center;
}

.img-link-wrap .img img {
  max-width: 100%;
}

.img-link-wrap .text-wrap {
  display: block;
  margin-top: 10px;
  min-height: 54px;
}

.img-link-wrap .default-text-wrap {
  display: block;
  font-size: 14px;
  line-height: 20px;
  margin-top: 10px;
}

.img-link-wrap .img.play:after {
  background: url(../img/button-play.png) no-repeat;
  content: "\a0";
  height: 31px;
  left: 50%;
  margin: -16px 0 0 -16px;
  position: absolute;
  top: 50%;
  width: 31px;
}

.img-wrap.float-left {
  margin-right: 15px;
}

.img-wrap.float-right {
  margin-left: 15px;
}

.img-wrap {
  overflow: hidden;
  position: relative;
}

.img-wrap img {
  max-width: 100%;
  width: 100%;
}

.img-wrap .caption {
  display: block;
  margin: 5px 0;
}

.img-wrap.no-stretch img {
  width: auto;
}

.img-wrap a {
  background: transparent;
  display: block;
  position: relative;
}

.img-wrap a:hover,
.img-wrap a:focus {
  background: #e5e5e5;
  opacity: 1;
}

.img-wrap a:hover img,
.img-wrap a:focus img {
  opacity: 0.8;
}

.img-wrap a:after {
  content: "\a0";
  left: 50%;
  position: absolute;
  top: 50%;
}

.img-wrap.interactive a:after {
  background: url(../img/button-interactive.png) no-repeat;
  height: 38px;
  margin: -19px 0 0 -19px;
  width: 38px;
}

.img-wrap.play a:after {
  background: url(../img/button-play.png) no-repeat;
  height: 31px;
  margin: -16px 0 0 -16px;
  width: 31px;
}

.img-wrap.podcast a:after {
  background: url(../img/button-play.png) no-repeat;
  height: 31px;
  margin: -16px 0 0 -16px;
  width: 31px;
}

.img-wrap.tour span.tour {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 5px;
  color: #444;
  display: block;
  left: 50%;
  line-height: 15px;
  margin: -20px 0 0 -25px;
  padding: 5px 8px;
  position: absolute;
  text-align: center;
  text-transform: uppercase;
  top: 50%;
  width: 50px;
  z-index: 9;
}

.img-wrap span.tour span.degree {
  display: block;
  font-size: 17px;
}

.img-wrap a span.open {
  bottom: 15px;
  font-size: 24px;
  position: absolute;
  right: 15px;
}

.img-wrap .gradient-overlay {
  background: -moz-linear-gradient(top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  background: -webkit-linear-gradient(top, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  background: linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#00ffffff', GradientType=0);
  left: 0;
  padding: 30px 15px 60px;
  position: absolute;
  text-align: center;
  top: 0;
  width: 100%;
}

.download-wrap a {
  color: #fff;
  font-size: 30px;
  display: block;
  padding: 50px 30px;
  text-align: center;
}

.icon-row {
  margin: 0 0 30px;
  text-align: center;
}

.icon-row .icon {
  display: inline-block;
  margin: 0 5px 10px;
  width: 90px;
}

.icon-row .icon img {
  margin: 0 0 5px;
}

.background-box {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  display: table;
  margin: 0 auto 10px;
  max-width: 700px;
  min-height: 210px;
  position: relative;
  padding: 49px 30px;
  width: 100%;
}

.background-box .content {
  color: #fff;
  display: table-cell;
  position: relative;
  text-shadow: 0 2px 1px rgba(0, 0, 0, 0.4);
  vertical-align: middle;
  z-index: 1;
}

.background-box .content h4 {
  color: #fff;
}

.background-box .content p {
  margin: 0 0 10px;
}

.cta-wrap {
  background-color: #004593;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  display: table;
  margin: 0 0 30px;
  position: relative;
  text-align: center;
  width: 100%;
}

.cta-wrap a {
  color: #fff;
  display: table-cell;
  font-size: 32px;
  line-height: 38px;
  height: 300px;
  padding: 20px;
  position: relative;
  vertical-align: middle;
  z-index: 1;
}

.table-boxes.five-wide {
  margin: 0 -15px;
}

.table-boxes.five-wide .table-box {
  float: left;
  padding: 0 15px;
  width: 50%;
}

.table-boxes.five-wide .table-box:last-child {
  margin-left: 25%;
}

.table-boxes .table-box {
  display: table;
  margin: 0 0 30px;
}

.table-boxes .table-box .cell {
  display: table-cell;
  padding: 15px;
  text-align: center;
  vertical-align: middle;
}

.table-boxes .table-box:last-child {
  margin-left: 25%;
}

.content-box {
  margin: 0 0 30px;
  padding: 30px;
  position: relative;
}

.content-box p {
  margin: 0 0 10px;
}

.content-box.gradient {
  background: inherit;
  position: relative;
}

.content-box.gradient:before {
  background: -moz-linear-gradient(left, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 100%);
  background: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 100%);
  background: linear-gradient(to right, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#4d000000', endColorstr='#00000000', GradientType=1);
  content: " ";
  position: absolute;
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 0;
}

.content-box.gradient * {
  position: relative;
  z-index: 1;
}

.content-box .plus {
  background: #fff;
  border-radius: 100%;
  color: #009988;
  display: inline-block;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-size: 22px;
  line-height: 16px;
  height: 20px;
  margin-top: -10px;
  position: absolute;
  right: -10px;
  text-align: center;
  top: 50%;
  width: 20px;
  z-index: 1;
}

.content-box .plus i {
  font-size: 12px;
  left: 1px;
  position: relative;
  top: -1px;
}

.section-box {
  margin: 0 auto 30px;
  max-width: 400px;
  padding: 30px;
}

.section-box .content {
  position: relative;
  z-index: 1;
}

.section-box h4 {
  margin: 0 0 10px;
}

.section-box p {
  margin: 0 0 10px;
}

.section-modal-box {
  cursor: pointer;
  margin: 0 auto 30px;
  max-width: 400px;
  padding: 30px;
}

.section-modal-box .content {
  position: relative;
  z-index: 1;
}

.section-modal-box h4 {
  margin: 0 0 10px;
}

.section-modal-box p {
  margin: 0 0 10px;
}

.plus-box {
  line-height: 30px;
  margin: 0 0 30px;
  min-height: 30px;
  position: relative;
}

.plus-box .plus {
  background: #fff;
  border-radius: 100%;
  color: #00B8B0;
  display: block;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-size: 30px;
  height: 30px;
  left: 50%;
  margin: -15px 0 0 -15px;
  line-height: 26px;
  position: absolute;
  top: 50%;
  width: 30px;
}

.pillar-box {
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
}

.pillar-box a {
  background: #fff;
  color: #444444;
  display: block;
  padding: 30px 30px 60px;
  position: relative;
  text-align: center;
  transition: background .5s ease-in-out;
}

.pillar-box a img {
  margin: 0 0 10px;
}

.pillar-box a img.hover {
  display: none;
}

.pillar-box a span {
  display: block;
}

.pillar-box a .arrow {
  bottom: 20px;
  font-size: 30px;
  left: 0;
  position: absolute;
  width: 100%;
}

.pillar-box a:hover,
.pillar-box a:focus {
  opacity: 1;
  color: #fff;
}

.pillar-box a:hover img.hover,
.pillar-box a:focus img.hover {
  display: inline;
}

.pillar-box a:hover img.default,
.pillar-box a:focus img.default {
  display: none;
}

.pillar-box a:hover span,
.pillar-box a:focus span {
  color: #fff;
}

.pillar-box a.primary-hover:hover,
.pillar-box a.primary-hover:focus {
  background: #004593;
}

.pillar-box a.accent-3-dark-hover:hover,
.pillar-box a.accent-3-dark-hover:focus {
  background: #009988;
}

.pillar-box a.accent-4-hover:hover,
.pillar-box a.accent-4-hover:focus {
  background: #F27707;
}

.pillar-box a.accent-5-hover:hover,
.pillar-box a.accent-5-hover:focus {
  background: #FDB913;
}

.pillar-box a.accent-6-hover:hover,
.pillar-box a.accent-6-hover:focus {
  background: #FEF48F;
}

.num-wrap {
  position: relative;
}

.num-wrap span.num {
  background: #004593;
  color: #fff;
  display: inline-block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 11px;
  left: 10px;
  padding: 0 5px;
  position: absolute;
  top: -8px;
}

.map-wrap {
  height: auto;
  overflow: hidden;
  padding-bottom: 56.25%;
  position: relative;
  width: 100%;
}

.map-wrap iframe {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.iframe-wrap {
  height: auto;
  overflow: hidden;
  padding-bottom: 56.25%;
  position: relative;
  width: 100%;
}

.iframe-wrap iframe {
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.vid-swap-wrap {
  display: none;
}

.vid-wrap {
  height: auto;
  max-width: 100%;
  overflow: hidden;
  padding-bottom: 56.25%;
  position: relative;
}

.vid-wrap iframe,
/* .vid-wrap video {
  bottom: 0;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
} */
.alert {
  margin: 0 auto 30px;
}

.alert a {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  padding: 10px;
  padding-right: 30px;
  position: relative;
}

.alert a:hover,
.alert a:focus {
  border-color: #023970;
  background: #004593;
  color: #fff;
  opacity: 1;
}

.alert a span.right-icon i {
  position: absolute;
  right: 10px;
  top: 13px;
}

.alert a strong i {
  margin-right: 5px;
}

.career-button {
  background: #fff;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  text-align: center;
}

.career-button .copy {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  padding: 15px;
}

.career-button>a {
  display: block;
  padding: 10px;
}

.careers-button {
  background: #fff;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
}

.careers-button .copy {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  font-size: 16px;
  padding: 15px;
}

.careers-button .button-wrap {
  padding: 15px;
  padding-right: 100px;
  position: relative;
}

.careers-button .button-wrap p {
  margin: 0 0 5px;
}

.careers-button .button-wrap a.button {
  bottom: 18px;
  position: absolute;
  right: 15px;
}

.support-button {
  background: #fff;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
}

.support-button .copy {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  font-size: 16px;
  padding: 15px;
}

.support-button .button-wrap {
  padding: 15px;
}

.support-button .button-wrap p {
  margin: 0 0 5px;
}

.accent {
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  position: relative;
}

.accent .num {
  background: #004593;
  color: #fff;
  display: inline-block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 11px;
  left: 10px;
  padding: 0 5px;
  position: absolute;
  top: -8px;
}

.accent a.num {
  background: #004593;
  opacity: 1;
}

.accent .copy {
  border-top: 4px solid #00B8B0;
  border-bottom: 4px solid #f7f7f7;
  line-height: 20px;
  margin: 0;
  overflow: hidden;
  padding: 20px 20px 10px;
}

.accent .copy h3 {
  margin: 0 0 10px;
}

.accent .copy p {
  margin: 0 0 10px;
}

.accent .copy hr {
  margin: 0 0 15px;
}

.accent .copy ul {
  margin-bottom: 10px;
}

.accent .copy .copy {
  border: none;
}

.resource-block {
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  position: relative;
}

.resource-block:before {
  background: #00b8b0;
  display: block;
  content: " ";
  height: 4px;
  left: -1px;
  position: absolute;
  top: -1px;
  right: -1px;
}

.resource-block:after {
  background: #f7f7f7;
  bottom: 0;
  content: " ";
  display: block;
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

.resource-block a {
  color: #8b8b8b;
  display: block;
  padding: 10px;
  position: relative;
}

.resource-block.icon a {
  padding-right: 40px;
}

.resource-block.icon i {
  color: #004593;
  font-size: 20px;
  position: absolute;
  right: 10px;
  top: 12px;
}

.ticker-bar {
  border: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  margin: 0 0 30px;
  position: relative;
}

.ticker-bar .logo {
  background: #c6c6c6;
  display: block;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 70px;
}

.ticker-bar .logo img {
  left: 50%;
  margin: -15px 0 0 -15px;
  position: absolute;
  top: 50%;
}

.ticker-bar .inner {
  margin: 0 60px 0 70px;
}

.ticker-bar .inner .copy {
  padding: 10px 15px;
}

.ticker-bar .inner .copy p {
  font-size: 14px;
  margin: 0;
}

.ticker-bar a.info-prev,
.ticker-bar a.info-next {
  background: #f7f7f7;
  border-left: 1px solid #e5e5e5;
  display: block;
  color: #8b8b8b;
  font-size: 20px;
  height: 100%;
  line-height: 100%;
  position: absolute;
  text-align: center;
  top: 0;
  width: 30px;
}

.ticker-bar a.info-prev {
  right: 30px;
}

.ticker-bar a.info-next {
  right: 0;
}

.ticker-bar a.info-next i,
.ticker-bar a.info-prev i {
  margin: -10px 0 0 -3px;
  position: absolute;
  left: 50%;
  top: 50%;
}

.ticker-bar a.info-prev:hover,
.ticker-bar a.info-next:hover,
.ticker-bar a.info-prev:focus,
.ticker-bar a.info-next:focus {
  color: #004593;
}

.stock-box {
  background: #004593;
  color: #fff;
  margin: 0 0 30px;
  padding: 15px;
  position: relative;
}

.stock-box h4 {
  color: #fff;
  margin: 0;
}

.stock-box a {
  color: #8cc6ec;
}

.stock-box .change {
  margin: 20px 0;
  font-size: 20px;
}

.stock-box .change .price {
  font-size: 40px;
}

.stock-box .pos {
  color: #00AF05;
}

.stock-box .neg {
  color: #e1151b;
}

.stock-box canvas {
  color: #fff;
}

.stock-box .footnotes {
  font-size: 11px;
  margin: 10px 0 0;
}

.stock-bar {
  background: #fff;
  border: 1px solid #e5e5e5;
  border-bottom-width: 4px;
  margin: 0 0 30px;
}

.stock-bar .inner {
  font-size: 18px;
  position: relative;
}

.stock-bar .inner img {
  background: #c6c6c6;
  border-right: 1px solid #e5e5e5;
  left: 0;
  top: 0;
  padding: 10px 15px;
  position: absolute;
}

.stock-bar .inner .company {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  line-height: 30px;
  padding: 10px 30px 10px 80px;
}

.stock-bar .inner .stock {
  line-height: 38px;
  overflow: hidden;
  padding: 10px 30px;
}

.stock-bar .inner .stock .change.neg {
  color: #e1151b;
  float: right;
}

.stock-bar .inner .stock .change.pos {
  color: #00AF05;
  float: right;
}

.partner-bar {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  font-size: 16px;
  margin: 0 0 30px;
  overflow: hidden;
  padding: 20px 10px;
  position: relative;
  text-align: center;
  text-transform: uppercase;
}

.partner-bar img {
  display: block;
  margin: 15px auto 0;
  max-width: 100%;
}

.callout-block {
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  padding: 10px;
  position: relative;
}

.callout-block:after {
  background: #f7f7f7;
  bottom: 0;
  content: " ";
  display: block;
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

.profile-block {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  margin: 0 0 30px;
  min-height: 160px;
  padding-left: 40%;
  position: relative;
}

.profile-block>.profile-img {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  display: block;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 40%;
}

.profile-block .text-wrap {
  padding: 10px;
}

.profile-block .text-wrap p {
  margin: 0 0 10px;
}

.person-block {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  overflow: hidden;
  position: relative;
}

.person-block .img-wrap {
  display: block;
}

.person-block .img-bg {
  background: #e5e5e5;
  display: block;
}

.person-block .text-wrap {
  border-bottom: 4px solid #e5e5e5;
  min-height: 110px;
  padding: 5px 10px;
  padding-right: 30px;
  position: relative;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}

.person-block .text-wrap h4 {
  font-size: 16px;
  line-height: 20px;
  margin: 0;
}

.person-block .text-wrap p {
  font-size: 12px;
  line-height: 18px;
  margin: 0;
}

.person-block .text-wrap a.go-to-link {
  font-size: 16px;
  position: absolute;
  right: 10px;
  top: 5px;
}

.person-block:hover .img-wrap img,
.person-block:focus .img-wrap img {
  opacity: 0.8;
}

.person-block:hover .text-wrap,
.person-block:focus .text-wrap {
  background: #004593;
  border-bottom: 4px solid #023970;
  color: #fff;
}

.person-block:hover .text-wrap h4,
.person-block:focus .text-wrap h4 {
  color: #fff;
}

.person-block:hover .text-wrap a,
.person-block:focus .text-wrap a {
  color: #fff;
}

.person-block .hover {
  background: #004593;
  color: #fff;
  left: 0;
  height: 100%;
  padding: 10px;
  position: absolute;
  top: 100%;
  width: 100%;
  transition: top 500ms;
  -webkit-transition: top 500ms;
}

.person-block .hover h4 {
  color: #fff;
}

.person-block .hover a {
  color: #fff;
}

.person-block .hover ul {
  list-style: none;
  margin: 0;
  padding: 0;
  1
}

.person-block:hover .hover {
  top: 0;
}

.map-block {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  margin: 0 0 30px;
}

.map-block .text-wrap {
  padding: 20px;
}

.map-block .text-wrap p {
  margin: 0 0 10px;
}

.browse-block {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  overflow: hidden;
  position: relative;
}

.browse-block .img-wrap {
  display: block;
}

.browse-block .text-wrap {
  border-bottom: 4px solid #e5e5e5;
  min-height: 114px;
  padding: 5px 10px;
  padding-right: 30px;
  position: relative;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}

.browse-block .text-wrap h4 {
  font-size: 14px;
  line-height: 20px;
  margin: 0;
}

.browse-block .text-wrap p {
  font-size: 12px;
  line-height: 18px;
  margin: 0;
}

.browse-block .text-wrap a.go-to-link {
  font-size: 16px;
  position: absolute;
  right: 10px;
  top: 5px;
}

.browse-block .text-wrap span.go-to-span {
  font-size: 16px;
  position: absolute;
  right: 10px;
  top: 5px;
}

.browse-block:hover .hover {
  top: 0;
}

.browse-block:hover .img-wrap,
.browse-block:focus .img-wrap {
  background: #e5e5e5;
}

.browse-block:hover .img-wrap img,
.browse-block:focus .img-wrap img {
  opacity: 0.8;
}

.browse-block:hover .text-wrap,
.browse-block:focus .text-wrap {
  background: #004593;
  border-bottom: 4px solid #023970;
  color: #fff;
}

.browse-block:hover .text-wrap h4,
.browse-block:focus .text-wrap h4 {
  color: #fff;
}

.browse-block:hover .text-wrap a,
.browse-block:focus .text-wrap a {
  color: #fff;
}

.browse-block .hover {
  background: #004593;
  border-bottom: 4px solid #e5e5e5;
  color: #fff;
  left: 0;
  height: 100%;
  position: absolute;
  top: 100%;
  width: 100%;
  transition: top 500ms;
  -webkit-transition: top 500ms;
}

.browse-block .hover .more {
  font-size: 12px;
  line-height: 18px;
  padding: 10px;
}

.browse-block .hover .more h4 {
  color: #fff;
  font-size: 16px;
  line-height: 18px;
}

.browse-block .hover>h4 {
  background: #004593;
  border-bottom: 4px solid #023970;
  bottom: -4px;
  color: #fff;
  font-size: 14px;
  left: 0;
  margin: 0;
  padding: 5px 30px 5px 10px;
  position: absolute;
  width: 100%;
}

.browse-block .hover>h4 i {
  position: absolute;
  right: 10px;
  top: 10px;
}

.browse-block .hover>h4 a {
  color: #fff;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
}

.media-block {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  margin: 0 0 30px;
  overflow: hidden;
  position: relative;
}

.media-block .img-wrap {
  background: #e5e5e5;
  display: block;
}

.media-block .text-wrap {
  min-height: 74px;
  padding: 5px 10px;
  position: relative;
}

.media-block .text-wrap h4 {
  font-size: 14px;
  line-height: 20px;
  margin: 0;
}

.media-block .hover {
  background: #004593;
  left: 0;
  height: 100%;
  padding: 10px;
  position: absolute;
  top: 100%;
  width: 100%;
  transition: top 500ms;
  -webkit-transition: top 500ms;
}

.media-block .hover a {
  color: #fff;
}

.media-block .hover ul {
  list-style: none;
  margin: 0;
  padding: 0;
  1
}

.media-block:hover .hover {
  top: 0;
}

.media-block:hover .img-wrap img,
.media-block:focus .img-wrap img {
  opacity: 0.8;
}

.grid-block {
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  overflow: hidden;
  position: relative;
}

.grid-block .text-wrap {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  padding: 15px;
}

.grid-block .text-wrap h4 {
  margin: 0 0 5px;
}

.grid-block .text-wrap p {
  margin: 0 0 5px;
}

.grid-block .text-wrap p.category {
  font-size: 12px;
  opacity: 0.7;
  text-transform: uppercase;
}

.grid-block .buttons a {
  background: #fff;
  border-bottom: 4px solid #f7f7f7;
  display: block;
  font-size: 12px;
  padding: 10px 0;
  text-align: center;
  text-transform: uppercase;
}

.grid-block .buttons a:hover,
.grid-block .buttons a:focus {
  background: #004593;
  border-bottom: 4px solid #023970;
  color: #fff;
  opacity: 1;
}

.product-landing-block {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  overflow: hidden;
  position: relative;
}

.product-landing-block .img-wrap {
  display: block;
}

.product-landing-block .img-bg {
  background: #e5e5e5;
  display: block;
}

.product-landing-block .text-wrap {
  border-bottom: 4px solid #e5e5e5;
  display: table;
  height: 60px;
  padding: 5px 10px;
  position: relative;
  width: 100%;
}

.product-landing-block .text-wrap h4 {
  display: table-cell;
  font-size: 16px;
  line-height: 20px;
  margin: 0;
  vertical-align: middle;
}

.product-landing-block:hover .img-wrap img,
.product-landing-block:focus .img-wrap img {
  opacity: 0.8;
}

.product-landing-block:hover .text-wrap,
.product-landing-block:focus .text-wrap {
  background: #004593;
  border-bottom: 4px solid #023970;
  color: #fff;
}

.product-landing-block:hover .text-wrap h4,
.product-landing-block:focus .text-wrap h4 {
  color: #fff;
}

.product-landing-block:hover .text-wrap a,
.product-landing-block:focus .text-wrap a {
  color: #fff;
}

.product-block {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  overflow: hidden;
  position: relative;
}

.product-block .img-wrap {
  display: block;
}

.product-block .img-bg {
  background: #e5e5e5;
  display: block;
  overflow: hidden;
  text-align: center;
}

.product-block .text-wrap {
  border-bottom: 4px solid #e5e5e5;
  min-height: 122px;
  padding: 5px 10px;
  padding-right: 25px;
  position: relative;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}

.product-block .text-wrap.tagged {
  padding-right: 50px;
}

.product-block .text-wrap h4 {
  font-size: 16px;
  line-height: 20px;
  margin: 0;
}

.product-block .text-wrap p {
  font-size: 12px;
  line-height: 18px;
  margin: 0;
}

.product-block .text-wrap a.go-to-link {
  font-size: 16px;
  position: absolute;
  right: 10px;
  top: 5px;
}

.product-block:hover .hover {
  top: 0;
}

.product-block:hover .img-wrap img,
.product-block:focus .img-wrap img {
  opacity: 0.8;
}

.product-block:hover .text-wrap,
.product-block:focus .text-wrap {
  background: #004593;
  border-bottom: 4px solid #023970;
  color: #fff;
}

.product-block:hover .text-wrap h4,
.product-block:focus .text-wrap h4 {
  color: #fff;
}

.product-block:hover .text-wrap a,
.product-block:focus .text-wrap a {
  color: #fff;
}

.product-block .hover {
  background: #F7F7F7;
  border-bottom: 4px solid #e5e5e5;
  left: 0;
  height: 100%;
  position: absolute;
  top: 100%;
  width: 100%;
  transition: top 500ms;
  -webkit-transition: top 500ms;
}

.product-block .hover .more {
  font-size: 12px;
  line-height: 18px;
  padding: 10px;
}

.product-block .hover h4 {
  background: #004593;
  color: #fff;
  font-size: 14px;
  margin: 0;
  padding: 5px 30px 5px 10px;
  position: relative;
}

.product-block .hover h4 i {
  position: absolute;
  right: 10px;
  top: 10px;
}

.link-block {
  border: 1px solid #e5e5e5;
  display: table;
  margin: 0 0 30px;
  width: 100%;
}

.link-block a {
  border-bottom: 4px solid #f7f7f7;
  border-top: 2px solid #00b8b0;
  color: #8b8b8b;
  display: table-cell;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  height: 40px;
  line-height: 20px;
  overflow: hidden;
  padding: 10px;
  padding-right: 30px;
  position: relative;
  vertical-align: middle;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}

.link-block a:after {
  color: #004593;
  content: "\f101";
  font-family: FontAwesome;
  font-size: 18px;
  position: absolute;
  right: 8px;
  top: 10px;
}

.link-block a:hover,
.link-block a:focus {
  color: #004593;
}

.quotes-block {
  margin: 0 0 30px;
  position: relative;
}

.quotes-block h4 {
  padding-right: 50px
}

.quotes-block .quote {
  position: relative;
  z-index: 9;
}

.quotes-block .quote p:first-child {
  font-size: 12px;
  line-height: 18px;
}

.quotes-block .quote p {
  margin: 0 0 10px;
}

.quotes-block .quote p.cite {
  margin: 0 0 15px;
}

.quotes-block .quotes {
  position: relative;
}

.quotes-block .quotes:before {
  color: #f7f7f7;
  content: "\201C";
  font-family: Georgia;
  font-size: 90px;
  left: -5px;
  position: absolute;
  top: 20px;
  -ms-transform: rotate(-20deg);
  -webkit-transform: rotate(-20deg);
  transform: rotate(-20deg)
}

.quotes-block .quotes-nav {
  position: absolute;
  right: 0;
  top: 3px;
}

.quotes-block .quotes-nav a {
  color: #e5e5e5;
  display: inline-block;
  font-size: 16px;
  overflow: hidden;
  text-align: center;
  width: 20px;
}

.quotes-block .quotes-nav a:hover,
.quotes-block .quotes-nav a:focus {
  color: #004593;
}

.info-block {
  margin: 0 0 30px;
}

.info-block .info {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  font-size: 12px;
  line-height: 18px;
  margin: 0 0 15px;
  padding: 10px 20px;
  position: relative;
}

.info-block .info p {
  margin: 0;
}

.events-block {
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
}

.events-block .inner {
  border-bottom: 4px solid #f7f7f7;
  border-top: 4px solid #00b8b0;
}

.events-block .event {
  border-top: 1px solid #e5e5e5;
  padding: 5px;
  padding-left: 50px;
  position: relative;
}

.events-block .event a {
  border-left: 1px solid #e5e5e5;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  min-height: 40px;
  padding-left: 10px;
}

.events-block .event .date {
  left: 0;
  line-height: 15px;
  position: absolute;
  text-align: center;
  top: 10px;
}

.events-block .event .date span {
  display: block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  width: 50px;
}

.section-nav {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  padding-bottom: 4px;
  position: relative;
}

.section-nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.section-nav ul li {
  margin: 0;
}

.section-nav ul li:first-child a {
  border-top: none;
}

.section-nav ul li.first a {
  border-top: 4px solid #00B8B0;
}

.section-nav ul li a {
  background: #fff;
  border-top: 1px solid #e5e5e5;
  color: #8b8b8b;
  display: block;
  padding: 10px 7px;
  padding-right: 30px;
  position: relative;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}

.section-nav ul li span.on {
  border-top: 4px solid #00B8B0;
  color: #8b8b8b;
  display: block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  padding: 10px 7px;
}

.section-nav ul li a:after {
  content: "\f101";
  font-family: FontAwesome;
  font-size: 18px;
  position: absolute;
  right: 7px;
  top: 10px;
}

.page-nav a {
  background: rgba(255, 255, 255, 0.6);
  border: 4px solid #8B8B8B;
  border-radius: 100%;
  color: #8B8B8B;
  display: none;
  font-size: 35px;
  height: 50px;
  line-height: 42px;
  overflow: hidden;
  position: fixed;
  text-align: center;
  top: 280px;
  width: 50px;
  z-index: 1;
}

.page-nav a.prev {
  left: 15px;
}

.page-nav a.next {
  right: 15px;
}

.page-nav a.prev i {
  position: relative;
  left: -3px;
}

.page-nav a.next i {
  position: relative;
  right: -3px;
}

.popular-links {
  margin: 0 0 30px;
}

.popular-links h4 {
  border-top: 4px solid #f7f7f7;
  margin: 0;
  padding: 7px 10px;
}

.popular-links ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.popular-links ul li {
  margin: 0;
}

.popular-links ul li a {
  border-top: 1px solid #e7e7e7;
  display: block;
  padding: 12px 10px;
  padding-right: 30px;
  position: relative;
}

.popular-links ul li a:hover,
.popular-links ul li a:focus {
  background: #004593;
  color: #fff;
}

.popular-links ul li a i {
  font-size: 20px;
  position: absolute;
  right: 10px;
  top: 12px;
}

.popular-links ul.authors li a {
  min-height: 100px;
  overflow: hidden;
  padding: 10px 30px 10px 110px;
}

.popular-links ul.authors li a img {
  left: 0;
  position: absolute;
  top: 0;
}

.popular-links ul.authors li a span.name {
  color: #444;
}

.popular-links ul.authors li a span.meta {
  color: #8b8b8b;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
}

.popular-links ul.authors li a:hover,
.popular-links ul.authors li a:focus {
  background: inherit;
  color: inherit;
}

.all-group {
  bottom: 0;
  display: table;
  left: 0;
  overflow: hidden;
  position: absolute;
}

.all-group .all {
  border-left: 1px solid #e5e5e5;
  display: table-cell;
  width: 1%;
}

.all-group .all:first-child {
  border-left: none;
}

.all {
  background: #fff;
  border-top: 1px solid #e5e5e5;
  border-bottom: 4px solid #f7f7f7;
  display: block;
  padding: 12px 10px;
  text-align: center;
}

.all:hover,
.all:focus {
  background: #004593;
  border-bottom: 4px solid #023970;
  color: #fff;
  opacity: 1;
}

.accordions {
  margin: 0 0 30px;
}

.accordion {
  margin: 0 0 -1px;
  position: relative;
}

.accordion .top {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  padding: 15px 30px 15px 0;
  position: relative;
}

.accordion .top p {
  margin: 0 0 10px;
}

.accordion .toggle {
  cursor: pointer;
}

.accordion .toggle:after {
  content: "\f078";
  font-family: FontAwesome;
  font-size: 16px;
  position: absolute;
  right: 0;
  top: 15px;
}

.accordion .toggle.opened:after {
  content: "\f077";
}

.accordion .toggle:hover:after,
.accordion .toggle:focus:after {
  color: #004593;
}

.accordion .copy.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

.accordion .copy {
  background: #f7f7f7;
  border-bottom: 4px solid #00B8B0;
  padding: 10px 20px;
  position: relative;
}

.accordion .copy.blank {
  background: transparent;
  border-bottom: none;
  padding: 0;
  top: -1px;
}

.accordion .copy p {
  margin: 0 0 15px;
}

.accordion .copy hr {
  margin: 0 0 15px;
}

.accordion .copy .link-list {
  background: #fff;
  padding: 10px;
}

.accordion .copy .link-list ul {
  margin-bottom: 0;
}

.accordion .copy ul.flags {
  list-style: none;
  margin: 0;
  padding: 0;
}

.accordion .copy ul.flags li a {
  color: #8b8b8b;
  padding-left: 25px;
  position: relative;
}

.accordion .copy ul.flags li img {
  left: 0;
  position: absolute;
  top: 2px;
}

.accordion .copy .bar {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: 0 -20px 10px;
  padding: 10px 0;
}

.accordion.locked .top {
  opacity: 0.5;
}

.accordion.locked .icon {
  border: 2px solid #004593;
  border-radius: 100%;
  color: #004593;
  display: block;
  font-size: 20px;
  height: 30px;
  line-height: 26px;
  position: absolute;
  right: 0;
  text-align: center;
  top: 15px;
  width: 30px;
}

.nested .faqs {
  border-top: none;
  margin: 0;
}

.nested .faqs .question {
  padding: 20px 65px 10px 55px;
}

.nested .faqs .question:before {
  left: 17px;
}

.nested .faqs .question:after {
  right: 20px;
}

.faqs {
  border-top: 1px solid #e5e5e5;
  margin: 0 0 30px;
}

.faqs p {
  margin: 0 0 10px;
}

.faqs .question {
  border-bottom: 1px solid #e5e5e5;
  font-size: 19px;
  line-height: 24px;
  padding: 20px 50px 10px 40px;
  position: relative;
}

.faqs .question:before {
  content: "\ Q. ";
  left: 2px;
  position: absolute;
  top: 20px;
}

.faqs .question:after {
  color: #8b8b8b;
  content: "\f078";
  font-family: FontAwesome;
  font-size: 16px;
  position: absolute;
  right: 5px;
  top: 20px;
}

.faqs .question.opened:after {
  color: #004593;
  content: "\f077";
}

.faqs .question:hover:after,
.faqs .question:focus:after {
  color: #004593;
}

.faqs .answer {
  background: #f7f7f7;
  border-bottom: 4px solid #00b8b0;
  font-size: 14px;
  line-height: 20px;
  overflow: hidden;
  padding: 20px 60px 10px;
  position: relative;
}

.faqs .answer:before {
  content: "\ A. ";
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  left: 30px;
  position: absolute;
  top: 20px;
}

.listing.border {
  border: none;
  border-top: 1px solid #e5e5e5;
  margin: 0 0 30px;
  padding: 15px 0 0 0;
}

.listing.border .item {
  border-bottom: 1px solid #e5e5e5;
  margin: 0 0 15px;
  padding: 0 0 15px;
}

.listing .item-top h3 {
  font-size: 20px;
  line-height: 24px;
}

.listing .item-top .avatar {
  font-size: 12px;
  margin: 0 0 15px;
  padding-left: 40px;
  position: relative;
}

.listing .item-top .avatar img {
  left: 0;
  position: absolute;
  top: -5px;
}

.listing .item {
  margin: 0 0 30px;
  line-height: 18px;
  padding: 0;
  position: relative;
}

.listing .item h3 {
  font-size: 20px;
  line-height: 24px;
}

.listing .item h4 {
  font-size: 16px;
  margin: 0 0 5px;
}

.listing .item p {
  font-size: 12px;
  margin: 0 0 5px;
}

.listing .item ul {
  font-size: 12px;
  margin-bottom: 0;
}

.listing .item.thumb {
  min-height: 90px;
  padding-left: 80px;
  position: relative;
}

.listing .item.thumb.small {
  min-height: 50px;
  padding-left: 50px;
}

.listing .item.thumb.small p {
  margin: 0;
}

.listing .item.thumb.wide {
  min-height: 90px;
  padding-left: 116px;
}

.listing .item.thumb .thumb {
  border: 1px solid #e5e5e5;
  left: 0;
  max-height: 70px;
  max-width: 100px;
  overflow: hidden;
  position: absolute;
  top: 3px;
}

.listing .item.thumb .thumb a {
  display: block;
}

.listing .item.thumb .thumb a:hover,
.listing .item.thumb .thumb a:focus {
  background: #e5e5e5;
  opacity: 1;
}

.listing .item.thumb .thumb a:hover img,
.listing .item.thumb .thumb a:focus img {
  opacity: 0.8;
}

.listing .item.thumb .thumb a:hover:after,
.listing .item.thumb .thumb a:focus:after {
  display: block;
}

.listing .item.thumb .thumb a:after {
  color: #fff;
  content: "\f00e";
  display: none;
  font-family: FontAwesome;
  font-size: 22px;
  height: 30px;
  left: 50%;
  line-height: 30px;
  margin: -15px 0 0 -15px;
  position: absolute;
  text-align: center;
  top: 50%;
  width: 30px;
}

.listing .item.thumb .thumb.no-hover-img a:after {
  content: none;
}

.listing .item.thumb .thumb.play a:after {
  background: url(../img/button-play.png) no-repeat;
  content: " ";
  display: block;
  height: 31px;
  margin: -16px 0 0 -16px;
  width: 31px;
}

.listing .item.thumb.small .thumb.play a:after {
  background-size: 100%;
  height: 20px;
  margin: -10px 0 0 -10px;
  width: 20px;
}

.listing .item.thumb .thumb.no-icon a:after {
  display: none;
}

.listing .item.event {
  margin: 0 0 15px;
}

.listing .item.event {
  padding-left: 50px;
  position: relative;
}

.listing .item.event p {
  margin: 0;
}

.listing .item.event .date {
  left: 0;
  position: absolute;
  text-align: center;
  top: 0;
}

.listing .item.event .date span {
  display: block;
  margin: 0 0 3px;
}

.listing .item.event .date span.day {
  color: #444444;
  font-size: 26px;
}

.listing.product-catalog .item {
  padding-right: 60px;
}

.listing.product-catalog .item .button {
  position: absolute;
  right: 0;
  top: 0;
}

.listing.product-catalog .item .price p {
  font-size: 14px;
}

.listing-schedule {
  margin: 0 0 30px;
}

.listing-schedule .item {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  font-size: 16px;
  line-height: 20px;
  margin: 0 0 -1px;
  padding: 15px 15px 10px;
}

.listing-schedule .item p {
  margin: 0 0 5px;
}

form.comment {
  margin: 0 0 30px;
}

.comments .comment {
  margin: 0 0 30px;
}

.comments .comment p.large {
  margin: 0 0 5px;
}

.comments .comment p {
  margin: 0 0 10px;
}

.comments .comment.nested {
  margin: 0 0 0 30px;
}

.comments .comment.nested div.text-wrap {
  border-left: 1px solid #e5e5e5;
  padding: 0 0 0 15px;
}

.comments .comment.nested p.action {
  padding: 0 0 0 15px;
}

.pager {
  margin: 0 0 15px 0;
  padding-right: 68px;
  position: relative;
  text-align: right;
}

.pager a {
  color: #8b8b8b;
  display: inline-block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  margin: 0 5px;
}

.pager .off {
  opacity: 0.5;
  pointer-events: none;
}

.pager .on {
  color: #004593;
}

.pager .pages {
  position: absolute;
  right: 0;
  top: -3px;
  text-align: left;
  width: 58px;
}

.pager .pages select {
  height: 24px;
  padding: 0;
}

.pager .pages .SelectBox {
  padding: 2px 5px;
}

.pager .pages .SumoSelect.open>.optWrapper {
  top: 30px;
}

.way-arrow-wrap {
  margin: 0 0 30px;
  padding: 100px;
}

.way-arrow {
  position: relative;
}

.way-arrow .line {
  background: #8b8b8b;
  height: 1px;
  position: relative;
}

.way-arrow .line:after {
  border-bottom: 5px solid transparent;
  border-left: 10px solid #8b8b8b;
  border-top: 5px solid transparent;
  content: " ";
  position: absolute;
  right: 0;
  top: -5px;
}

.way-arrow .spot-wrap {
  font-size: 13px;
  left: 0;
  line-height: 16px;
  margin-left: -35px;
  margin-top: -20px;
  position: absolute;
  text-align: center;
  top: 0;
  width: 70px;
}

.way-arrow .spot-wrap .spot {
  background: #addfeb;
  border: 2px solid #8b8b8b;
  border-radius: 100%;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  height: 40px;
  margin: 0 auto 10px;
  overflow: hidden;
  position: relative;
  width: 40px;
  z-index: 0;
}

.way-arrow .spot-wrap .spot:hover,
.way-arrow .spot-wrap .spot:focus {
  opacity: 1;
}

.way-arrow .spot-wrap .spot .content {
  display: block;
  opacity: 0;
  padding: 40px 20px;
  transition: opacity 600ms ease;
}

.way-arrow .spot-wrap .spot .content .title {
  display: block;
  font-weight: 600;
  margin: 0 0 5px;
}

.way-arrow .spot-wrap .spot .content .text {
  opacity: 0.7;
}

.way-arrow .spot-wrap.opened {
  margin-left: -100px;
  margin-top: -100px;
  width: 200px;
}

.way-arrow .spot-wrap.opened .spot {
  background: #23527c;
  border-color: #00bce8;
  color: #fff;
  height: 200px;
  position: relative;
  width: 200px;
  z-index: 1;
}

.way-arrow .spot-wrap.opened .spot .content {
  opacity: 1;
}

.way-arrow .spot-wrap.opened .label {
  color: #444444;
}

.wheel-rotator-wrap {
  margin: 0 -15px;
  overflow: hidden;
  padding-top: 40px;
}

.wheel-rotator {
  margin: 0 auto;
  max-width: 680px;
  position: relative;
}

.wheel-wrap {
  color: #fff;
  margin: 0 auto 70px;
  max-width: 240px;
  position: relative;
}

.wheel-wrap a {
  color: #fff;
  text-transform: uppercase;
}

.wheel-wrap a:hover,
.wheel-wrap a:focus {
  opacity: 1;
}

.wheel-wrap.center .hub {
  background: #00B8B0;
}

/* .wheel-wrap.center .text{opacity:0;} */
.wheel-wrap.center .hub .text {
  opacity: 1;
}

.wheel-wrap.center .wheel .circle.on {
  background: #004593;
}

.wheel-wrap.center .right-spot {
  display: block;
}

.wheel-wrap .hub {
  background: #004593;
  border: 2px solid #0782D6;
  border-radius: 100%;
  display: block;
  height: 120px;
  left: 60px;
  padding-top: 90px;
  position: absolute;
  text-align: center;
  top: 60px;
  width: 120px;
  z-index: 0;
}

.wheel-wrap .hub img {
  left: 25px;
  position: absolute;
  top: 10px;
  max-width: 80px;
  z-index: 1;
}

.wheel-wrap .hub .text {
  text-transform: uppercase;
}

.wheel-wrap .hub .spoke {
  background: #0782D6;
  height: 2px;
  position: absolute;
  width: 20px;
}

.wheel-wrap .hub .spoke-1 {
  left: 12px;
  top: -2px;
  -ms-transform: rotate(60deg);
  -webkit-transform: rotate(60deg);
  transform: rotate(60deg)
}

.wheel-wrap .hub .spoke-2 {
  right: -20px;
  top: 59px;
}

.wheel-wrap .hub .spoke-3 {
  bottom: -2px;
  left: 12px;
  -ms-transform: rotate(-60deg);
  -webkit-transform: rotate(-60deg);
  transform: rotate(-60deg)
}

.wheel-wrap .wheel {
  border: 2px solid #fff;
  border-radius: 100%;
  height: 240px;
  position: relative;
  width: 240px;
  transform: rotate(0deg);
  transition: transform 600ms linear;
}

.wheel-wrap .wheel .circle.on {
  background: #00b8b0;
}

/* .wheel-wrap .wheel .circle.on .text{display:none !important;} */
.wheel-wrap .wheel .circle.reverse .text {
  bottom: -80px;
  top: auto;
}

.wheel-wrap .wheel .circle {
  background: #004593;
  border: 2px solid #fff;
  border-radius: 100%;
  display: block;
  height: 80px;
  text-align: center;
  text-transform: uppercase;
  position: relative;
  width: 80px;
  transform: rotate(0deg);
  transition: transform 600ms linear;
}

.wheel-wrap .wheel .circle img {
  left: 14px;
  position: absolute;
  top: 14px;
}

.wheel-wrap .wheel .circle .text {
  display: block;
  left: 50%;
  margin-left: -50px;
  position: relative;
  top: -25px;
  width: 100px;
}

.wheel-wrap .wheel .spot-1 {
  left: 21px;
  position: absolute;
  top: -21px;
}

.wheel-wrap .wheel .spot-2 {
  position: absolute;
  right: -36px;
  top: 78px;
}

.wheel-wrap .wheel .spot-3 {
  bottom: -21px;
  left: 21px;
  position: absolute;
}

.wheel-wrap .turn-right {
  border-radius: 100%;
  display: block;
  height: 80px;
  left: 23px;
  outline: none;
  position: absolute;
  top: -19px;
  width: 80px;
}

.wheel-wrap .turn-left {
  border-radius: 100%;
  bottom: -19px;
  display: block;
  height: 80px;
  left: 23px;
  outline: none;
  position: absolute;
  width: 80px;
}

.wheel-wrap .right-spot {
  border-radius: 100%;
  display: none;
  height: 80px;
  outline: none;
  position: absolute;
  right: -34px;
  top: 80px;
  width: 80px;
}

.wheel-wrap .center-spot {
  border-radius: 100%;
  display: block;
  left: 60px;
  height: 120px;
  outline: none;
  position: absolute;
  top: 60px;
  width: 120px;
}

.wheel-rotator .line {
  background: #0782D6;
  bottom: -40px;
  display: none;
  left: 300px;
  position: absolute;
  top: -40px;
  width: 2px;
}

.wheel-rotator .wheel-content {
  color: #fff;
  padding: 0 15px;
}

.wheel-rotator .wheel-content .content {
  display: none;
}

.wheel-rotator .wheel-content .content.on {
  display: block;
}

.wheel-rotator .wheel-content .content h3 {
  color: #fff;
}

.wheel-rotator .wheel-content .content p:first-of-type {
  font-size: 16px;
}

.wheel-rotator .wheel-content .content p {
  margin: 0 0 15px;
}

.select-tab {
  margin: -25px auto 30px;
  position: relative;
  width: 300px;
}

.select-tab .toggle {
  color: #444;
  cursor: pointer;
  display: inline-block;
  font-size: 30px;
  line-height: 30px;
  padding: 10px 25px 10px 0;
  position: relative;
  white-space: nowrap;
}

.select-tab .toggle:after {
  content: "\f107";
  font-family: FontAwesome;
  right: 0;
  position: absolute;
  top: 10px;
}

.select-tab .toggle.opened:after {
  content: "\f106";
}

.select-tab .options {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  border: 1px solid #e5e5e5;
  display: none;
  font-size: 18px;
  left: 0;
  line-height: 20px;
  position: absolute;
  top: 105%;
  width: 100%;
  z-index: 2;
}

.select-tab .options a {
  border-bottom: 1px solid #e5e5e5;
  color: #8b8b8b;
  display: block;
  padding: 10px;
  text-align: left;
}

.select-tab .options a:hover,
.select-tab .options a:focus {
  background: #f7f7f7;
  color: #444;
  opacity: 1;
}

.content-tab-nav {
  margin: 0 auto 30px;
  max-width: 300px;
  position: relative;
}

.content-tab-nav .toggle {
  border: 1px solid #e5e5e5;
  color: #444;
  cursor: pointer;
  display: block;
  font-size: 20px;
  line-height: 30px;
  padding: 5px 10px;
  position: relative;
  white-space: nowrap;
}

.content-tab-nav .toggle:after {
  content: "\f107";
  font-family: FontAwesome;
  right: 10px;
  position: absolute;
  top: 5px;
}

.content-tab-nav .toggle.opened:after {
  content: "\f106";
}

.content-tab-nav .options {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  border: 1px solid #e5e5e5;
  display: none;
  font-size: 18px;
  left: 0;
  line-height: 20px;
  position: absolute;
  top: 105%;
  width: 100%;
  z-index: 2;
}

.content-tab-nav .options a {
  border-bottom: 1px solid #e5e5e5;
  color: #8b8b8b;
  display: block;
  padding: 10px;
  text-align: left;
}

.content-tab-nav .options a:hover,
.content-tab-nav .options a:focus {
  background: #f7f7f7;
  color: #444;
  opacity: 1;
}

.tabs .tab-nav {
  display: table;
  position: relative;
}

.tabs .tab-nav a {
  background: #f3f3f3;
  border: 1px solid #fff;
  border-bottom: 1px solid #e5e5e5;
  color: #8b8b8b;
  display: table-cell;
  font-size: 13px;
  line-height: 18px;
  padding: 10px;
  text-align: center;
  vertical-align: middle;
  width: 150px;
}

.tabs .tab-nav a.on {
  background: #fff;
  border: 1px solid #e5e5e5;
  border-bottom: 1px solid #fff;
  color: #00b8b0;
  position: relative;
}

.tabs .tab-nav a.on:hover,
.tabs .tab-nav a.on:focus {
  opacity: 1;
}

.tabs .tab-nav a.on:after {
  background: #00b8b0;
  content: " ";
  height: 4px;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.tabs .tab-wrap {
  border-top: 1px solid #e5e5e5;
  margin-top: -1px;
}

.tabs .tab-wrap .tab {
  margin: 0 0 30px;
  padding: 30px 0 0 0;
}

.tabs.support .tab-nav {
  width: 100%;
}

.tabs.support .tab-nav a {
  color: #004593;
  width: 1%;
}

.tabs.support .tab-nav a.on {
  color: #444444;
}

.tabs.support .tab-nav a.on:hover,
.tabs.support .tab-nav a.on:focus {
  opacity: 1;
}

.tabs.support .tab-nav a.on:after {
  display: none;
}

.tabs.support .tab-wrap {
  border: 1px solid #e5e5e5;
  margin: -1px 0 30px;
}

.tabs.support .tab-wrap .tab {
  margin: 0;
  padding: 30px 30px 15px 30px;
}

.home-tabs .tab-nav {
  display: block;
  text-align: center;
}

.home-tabs .tab-nav a {
  border-bottom: 4px solid #fff;
  color: #8b8b8b;
  display: inline-block;
  font-size: 13px;
  line-height: 18px;
  margin: 0 10px;
  padding: 5px 2px;
  text-align: center;
}

.home-tabs .tab-nav a.on {
  border: none;
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

.home-tabs .tab-nav a.on:after {
  display: none;
}

.home-tabs .tab-wrap {
  border-top: none;
  margin-top: 0;
}

.home-tabs .tab-wrap .tab {
  margin: 0 0 30px;
  padding: 30px 0 0 0;
  overflow: hidden;
}

.search-tabs {
  margin: 0 0 15px;
  overflow: auto;
}

.search-tabs-wrap {
  min-width: 540px;
  position: relative;
}

.search-tabs .tab-nav {
  margin: 0 0 15px;
  padding-right: 50px;
  position: relative;
}

.search-tabs .tab-nav a {
  border-bottom: 4px solid #fff;
  color: #8b8b8b;
  display: inline-block;
  font-size: 13px;
  line-height: 18px;
  margin: 0 20px 0 0;
  padding: 5px 2px;
  text-align: center;
}

.search-tabs .tab-nav a:last-child {
  margin: 0;
}

.search-tabs .tab-nav a.on {
  border: none;
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

.search-tabs .tab-nav a.on:after {
  display: none;
}

.search-tabs .view {
  position: absolute;
  right: 15px;
  top: 0;
}

.search-tabs .view a {
  font-size: 20px;
  margin-left: 10px;
}

.basic-tabs {
  margin: 0 0 15px;
}

.basic-tabs-wrap {
  min-width: 540px;
  position: relative;
}

.basic-tabs .tab-nav {
  margin: 0 0 15px;
  position: relative;
}

.basic-tabs .tab-nav a {
  border-bottom: 4px solid #fff;
  color: #8b8b8b;
  display: inline-block;
  font-size: 13px;
  line-height: 18px;
  margin: 0 10px;
  padding: 5px 2px;
  text-align: center;
}

.basic-tabs .tab-nav a.on {
  border: none;
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

.background-color-tabs .tab-nav {
  display: table;
  width: 100%;
}

.background-color-tabs .tab-nav a {
  color: #fff;
  display: table-cell;
  font-size: 16px;
  line-height: 20px;
  padding: 30px 5px;
  text-align: center;
  width: 1%;
}

.background-color-tabs .tab-nav a:hover,
.background-color-tabs .tab-nav a:focus {
  opacity: 1;
}

.background-color-tabs .tab-nav a img {
  display: block;
  margin: 0 auto 5px;
  max-width: 70px;
}

.background-tabs .tab-nav {
  margin: -45px 0 45px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.background-tabs .tab-nav a {
  background: rgba(256, 256, 256, 0.9);
  color: #444444;
  display: block;
  float: left;
  font-size: 12px;
  line-height: 18px;
  padding: 30px 3px;
  text-align: center;
  width: 25%;
}

.background-tabs .tab-nav a.on {
  background: rgba(256, 256, 256, 0);
  color: #fff;
}

.background-tabs .tab-nav.wide-5 a {
  font-size: 12px;
  line-height: 18px;
  width: 20%;
}

.background-tabs .content-tabs {
  margin: -30px -15px;
  position: relative;
}

.background-tabs .content-tabs .tab {
  min-height: 0;
}

.section-tabs.marketing .tab-nav {
  margin: -15px 0 0;
}

.section-tabs.marketing .tab-nav a.on:after {
  margin-top: 25px;
}

.section-tabs.marketing .tab-nav img {
  margin: 20px 0 0;
}

.section-tabs section:first-child {
  position: relative;
  z-index: 1;
}

.section-tabs section:last-child {
  position: relative;
  z-index: 0;
}

.section-tabs .tab-nav {
  margin: 30px 0 -30px;
}

.section-tabs .tab-nav a {
  color: #fff;
  font-size: 16px;
  height: 120px;
  display: inline-block;
  opacity: 1;
  position: relative;
  width: 90px;
}

.section-tabs .tab-nav a.on:after {
  color: #004593;
  content: "\f0d7";
  font-size: 100px;
  font-family: FontAwesome;
  left: 0;
  margin-top: -2px;
  position: absolute;
  top: 100%;
  width: 100%;
}

.section-tabs .tab-nav img {
  margin: 0 0 5px;
}

.section-tabs .tab-nav span.dot {
  border: 2px solid #fff;
  border-radius: 100%;
  display: block;
  height: 64px;
  margin: 0 auto 5px;
  width: 64px;
}

.section-tabs .tab-nav .on span.dot {
  background: #fff;
}

.section-tabs .tab-nav span.text {
  display: block;
}

.tab-block {
  position: relative;
}

.tab-block .tab-nav {
  margin: 0;
  padding: 0;
  width: 200px;
}

.tab-block .tab-nav li {
  display: table;
  margin: 0;
  width: 100%;
}

.tab-block .tab-nav li a {
  background: #f7f7f7;
  border-right: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  color: #8b8b8b;
  display: table-cell;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  height: 70px;
  padding: 0 15px;
  vertical-align: middle;
}

.tab-block .tab-nav li:first-child a {
  border-top: none;
}

.tab-block .tab-nav li.on a {
  background: #fff;
  border-left: 4px solid #00b8b0;
  border-right: 1px solid #fff;
  padding-left: 11px;
}

.tab-block .tab-nav li a:hover,
.tab-block .tab-nav li a:focus {
  border-left: 4px solid #00b8b0;
  color: #444;
  padding-left: 11px;
}

.tab-block .tab-wrap .tab {
  padding: 15px;
}

.tab-explore {
  position: relative;
}

.tab-explore .tab-nav {
  margin: 0;
  padding: 0;
  width: 160px;
}

.tab-explore .tab-nav li {
  display: table;
  margin: 0;
  width: 100%;
}

.tab-explore .tab-nav li a {
  background: #f7f7f7;
  border-right: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  color: #8b8b8b;
  display: table-cell;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  height: 50px;
  padding: 0 15px;
  vertical-align: middle;
}

.tab-explore .tab-nav li:first-child a {
  border-top: none;
}

.tab-explore .tab-nav li.on a {
  background: #fff;
  border-left: 4px solid #00b8b0;
  border-right: 1px solid #fff;
  padding-left: 11px;
}

.tab-explore .tab-nav li a:hover,
.tab-explore .tab-nav li a:focus {
  border-left: 4px solid #00b8b0;
  color: #444;
  padding-left: 11px;
}

.tab-explore .tab-wrap .tab {
  padding: 15px;
}

.tab-explore .tab-nav li.first a {
  background: #f7f7f7 url(../img/wheels/wheel-10/active-home.png) no-repeat center center;
  border-left: none;
  padding: 0 10px;
  height: 170px;
}

.tab-explore .tab-nav li.first.on a {
  background: url(../img/wheels/wheel-10/active-home.png) no-repeat center center;
  border-left: none;
}

.tab-explore .tab-nav li.first a:hover,
.tab-explore .tab-nav li.first a:focus {
  background: #fff url(../img/wheels/wheel-10/active-home.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-2] a {
  background: url(../img/wheels/wheel-10/active-1.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-3] a {
  background: url(../img/wheels/wheel-10/active-2.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-4] a {
  background: url(../img/wheels/wheel-10/active-3.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-5] a {
  background: url(../img/wheels/wheel-10/active-4.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-6] a {
  background: url(../img/wheels/wheel-10/active-5.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-7] a {
  background: url(../img/wheels/wheel-10/active-6.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-8] a {
  background: url(../img/wheels/wheel-10/active-7.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-9] a {
  background: url(../img/wheels/wheel-10/active-8.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-10] a {
  background: url(../img/wheels/wheel-10/active-9.png) no-repeat center center;
}

.tab-explore .tab-nav li[rel=tab-explore-11] a {
  background: url(../img/wheels/wheel-10/active-10.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li.first a {
  background: #f7f7f7 url(../img/wheels/wheel-9/active-home.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li.first.on a {
  background: url(../img/wheels/wheel-9/active-home.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li.first a:hover,
.tab-explore.wheel-9 .tab-nav li.first a:focus {
  background: #fff url(../img/wheels/wheel-9/active-home.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li[rel=tab-explore-2] a {
  background: url(../img/wheels/wheel-9/active-1.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li[rel=tab-explore-3] a {
  background: url(../img/wheels/wheel-9/active-2.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li[rel=tab-explore-4] a {
  background: url(../img/wheels/wheel-9/active-3.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li[rel=tab-explore-5] a {
  background: url(../img/wheels/wheel-9/active-4.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li[rel=tab-explore-6] a {
  background: url(../img/wheels/wheel-9/active-5.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li[rel=tab-explore-7] a {
  background: url(../img/wheels/wheel-9/active-6.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li[rel=tab-explore-8] a {
  background: url(../img/wheels/wheel-9/active-7.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li[rel=tab-explore-9] a {
  background: url(../img/wheels/wheel-9/active-8.png) no-repeat center center;
}

.tab-explore.wheel-9 .tab-nav li[rel=tab-explore-10] a {
  background: url(../img/wheels/wheel-9/active-9.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li.first a {
  background: #f7f7f7 url(../img/wheels/wheel-8/active-home.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li.first.on a {
  background: url(../img/wheels/wheel-8/active-home.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li.first a:hover,
.tab-explore.wheel-8 .tab-nav li.first a:focus {
  background: #fff url(../img/wheels/wheel-8/active-home.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li[rel=tab-explore-2] a {
  background: url(../img/wheels/wheel-8/active-1.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li[rel=tab-explore-3] a {
  background: url(../img/wheels/wheel-8/active-2.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li[rel=tab-explore-4] a {
  background: url(../img/wheels/wheel-8/active-3.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li[rel=tab-explore-5] a {
  background: url(../img/wheels/wheel-8/active-4.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li[rel=tab-explore-6] a {
  background: url(../img/wheels/wheel-8/active-5.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li[rel=tab-explore-7] a {
  background: url(../img/wheels/wheel-8/active-6.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li[rel=tab-explore-8] a {
  background: url(../img/wheels/wheel-8/active-7.png) no-repeat center center;
}

.tab-explore.wheel-8 .tab-nav li[rel=tab-explore-9] a {
  background: url(../img/wheels/wheel-8/active-8.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li.first a {
  background: #f7f7f7 url(../img/wheels/wheel-7/active-home.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li.first.on a {
  background: url(../img/wheels/wheel-7/active-home.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li.first a:hover,
.tab-explore.wheel-7 .tab-nav li.first a:focus {
  background: #fff url(../img/wheels/wheel-7/active-home.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li[rel=tab-explore-2] a {
  background: url(../img/wheels/wheel-7/active-1.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li[rel=tab-explore-3] a {
  background: url(../img/wheels/wheel-7/active-2.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li[rel=tab-explore-4] a {
  background: url(../img/wheels/wheel-7/active-3.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li[rel=tab-explore-5] a {
  background: url(../img/wheels/wheel-7/active-4.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li[rel=tab-explore-6] a {
  background: url(../img/wheels/wheel-7/active-5.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li[rel=tab-explore-7] a {
  background: url(../img/wheels/wheel-7/active-6.png) no-repeat center center;
}

.tab-explore.wheel-7 .tab-nav li[rel=tab-explore-8] a {
  background: url(../img/wheels/wheel-7/active-7.png) no-repeat center center;
}

.tab-explore.wheel-6 .tab-nav li.first a {
  background: #f7f7f7 url(../img/wheels/wheel-6/active-home.png) no-repeat center center;
}

.tab-explore.wheel-6 .tab-nav li.first.on a {
  background: url(../img/wheels/wheel-6/active-home.png) no-repeat center center;
}

.tab-explore.wheel-6 .tab-nav li.first a:hover,
.tab-explore.wheel-6 .tab-nav li.first a:focus {
  background: #fff url(../img/wheels/wheel-6/active-home.png) no-repeat center center;
}

.tab-explore.wheel-6 .tab-nav li[rel=tab-explore-2] a {
  background: url(../img/wheels/wheel-6/active-1.png) no-repeat center center;
}

.tab-explore.wheel-6 .tab-nav li[rel=tab-explore-3] a {
  background: url(../img/wheels/wheel-6/active-2.png) no-repeat center center;
}

.tab-explore.wheel-6 .tab-nav li[rel=tab-explore-4] a {
  background: url(../img/wheels/wheel-6/active-3.png) no-repeat center center;
}

.tab-explore.wheel-6 .tab-nav li[rel=tab-explore-5] a {
  background: url(../img/wheels/wheel-6/active-4.png) no-repeat center center;
}

.tab-explore.wheel-6 .tab-nav li[rel=tab-explore-6] a {
  background: url(../img/wheels/wheel-6/active-5.png) no-repeat center center;
}

.tab-explore.wheel-6 .tab-nav li[rel=tab-explore-7] a {
  background: url(../img/wheels/wheel-6/active-6.png) no-repeat center center;
}

.tab-explore.wheel-5 .tab-nav li.first a {
  background: #f7f7f7 url(../img/wheels/wheel-5/active-home.png) no-repeat center center;
}

.tab-explore.wheel-5 .tab-nav li.first.on a {
  background: url(../img/wheels/wheel-5/active-home.png) no-repeat center center;
}

.tab-explore.wheel-5 .tab-nav li.first a:hover,
.tab-explore.wheel-5 .tab-nav li.first a:focus {
  background: #fff url(../img/wheels/wheel-5/active-home.png) no-repeat center center;
}

.tab-explore.wheel-5 .tab-nav li[rel=tab-explore-2] a {
  background: url(../img/wheels/wheel-5/active-1.png) no-repeat center center;
}

.tab-explore.wheel-5 .tab-nav li[rel=tab-explore-3] a {
  background: url(../img/wheels/wheel-5/active-2.png) no-repeat center center;
}

.tab-explore.wheel-5 .tab-nav li[rel=tab-explore-4] a {
  background: url(../img/wheels/wheel-5/active-3.png) no-repeat center center;
}

.tab-explore.wheel-5 .tab-nav li[rel=tab-explore-5] a {
  background: url(../img/wheels/wheel-5/active-4.png) no-repeat center center;
}

.tab-explore.wheel-5 .tab-nav li[rel=tab-explore-6] a {
  background: url(../img/wheels/wheel-5/active-5.png) no-repeat center center;
}

.tab-explore.wheel-4 .tab-nav li.first a {
  background: #f7f7f7 url(../img/wheels/wheel-4/active-home.png) no-repeat center center;
}

.tab-explore.wheel-4 .tab-nav li.first.on a {
  background: url(../img/wheels/wheel-4/active-home.png) no-repeat center center;
}

.tab-explore.wheel-4 .tab-nav li.first a:hover,
.tab-explore.wheel-4 .tab-nav li.first a:focus {
  background: #fff url(../img/wheels/wheel-4/active-home.png) no-repeat center center;
}

.tab-explore.wheel-4 .tab-nav li[rel=tab-explore-2] a {
  background: url(../img/wheels/wheel-4/active-1.png) no-repeat center center;
}

.tab-explore.wheel-4 .tab-nav li[rel=tab-explore-3] a {
  background: url(../img/wheels/wheel-4/active-2.png) no-repeat center center;
}

.tab-explore.wheel-4 .tab-nav li[rel=tab-explore-4] a {
  background: url(../img/wheels/wheel-4/active-3.png) no-repeat center center;
}

.tab-explore.wheel-4 .tab-nav li[rel=tab-explore-5] a {
  background: url(../img/wheels/wheel-4/active-4.png) no-repeat center center;
}

.tour-tabs .tour-tab-nav {
  list-style: none;
  margin: 0 0 20px;
  padding: 0;
  overflow: hidden;
}

.tour-tabs .tour-tab-nav li {
  float: left;
  margin: 0;
  width: 20%;
}

.tour-tabs .tour-tab-nav li:first-child a {
  border-left: none;
}

.tour-tabs .tour-tab-nav li a {
  background: #004593;
  border-left: 1px solid #e5e5e5;
  color: #fff;
  display: block;
  padding: 5px;
  text-align: center;
}

.tour-tabs .tour-tab-nav li a img {
  display: block;
  margin: 5px auto;
}

.tour-tabs .tour-tab-nav li.on a {
  background: #00B8B0;
}

.tour-tabs .tab-wrap .tab {
  display: none;
}

.tour-tabs .tab-wrap .tab:first-child {
  display: block;
}

.modal-tabs .modal-tab-nav {
  list-style: none;
  margin: 0 0 20px;
  padding: 0;
  overflow: hidden;
}

.modal-tabs .modal-tab-nav li {
  float: left;
  margin: 0;
  width: 50%;
}

.modal-tabs .modal-tab-nav li:first-child a {
  border-left: none;
}

.modal-tabs .modal-tab-nav li a {
  background: #f7f7f7;
  border-left: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
  color: #444;
  display: block;
  padding: 12px;
  text-align: center;
}

.modal-tabs .modal-tab-nav li.on a {
  background: #fff;
  color: #004593;
}

.modal-tabs .tab-wrap .tab {
  display: none;
}

.modal-tabs .tab-wrap .tab:first-child {
  display: block;
}

.hot-spots {
  position: relative;
}

.hot-spots .hot-spot-img {
  position: relative;
}

.hot-spots .hot-spot-img .spot-link {
  display: block;
  border: 3px solid #00b8b0;
  border-radius: 100%;
  height: 20px;
  left: 0;
  position: absolute;
  top: 0;
  width: 20px;
}

.hot-spots .hot-spot-img .spot-link.on {
  transform: scale(1.5);
}

.hot-spots .hot-spot-content .spot {
  display: none;
}

.hot-spots .hot-spot-content h3 {
  margin: 0 0 10px;
}

.hot-spots .hot-spot-content p {
  margin: 0 0 15px;
}

.button-drop {
  position: relative;
}

.button-drop>a {
  border: 1px solid #e5e5e5;
  color: #8b8b8b;
  display: block;
  font-size: 14px;
  overflow: hidden;
  padding: 10px;
  position: relative;
  z-index: 1;
}

.button-drop>a:before {
  background: #f7f7f7;
  bottom: 0;
  content: "\a0";
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

.button-drop>a:after {
  color: #8b8b8b;
  content: "\f107";
  font-family: FontAwesome;
  font-size: 18px;
  position: absolute;
  top: 10px;
  right: 10px;
}

.button-drop>a.opened:before {
  background: #00B8B0;
  bottom: auto;
  top: 0;
}

.button-drop>a.opened:after {
  content: "\f106";
}

.button-drop ul {
  background: #fff;
  border: 1px solid #e5e5e5;
  border-top: none;
  display: none;
  left: 0;
  margin: 0;
  overflow: hidden;
  padding: 0;
  position: absolute;
  top: 100%;
  width: 100%;
  z-index: 9;
}

.button-drop ul li {
  margin: 0;
}

.button-drop ul li:first-child a {
  border-top: none;
}

.button-drop ul li a {
  border-top: 1px solid #e5e5e5;
  color: #8b8b8b;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  padding: 7px 15px;
}

.button-drop ul li a:hover,
.button-drop ul li a:focus {
  color: #004593;
  opacity: 1;
}

.button-drop ul li.all-link a {
  background: #f7f7f7;
  color: #004593;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
}

.button-drop ul li.all-link a:hover,
.button-drop ul li.all-link a:focus {
  background: #004593;
  color: #fff;
}

.slide-select-list {
  border: 1px solid #e5e5e5;
  position: relative;
}

.slide-select-list .toggle {
  border-bottom: 4px solid #f7f7f7;
  border-top: 2px solid #fff;
  color: #8b8b8b;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  overflow: hidden;
  padding: 10px;
  position: relative;
  white-space: nowrap;
}

.slide-select-list .toggle:before {
  background: -moz-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 60%, rgba(255, 255, 255, 1) 100%);
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 60%, rgba(255, 255, 255, 1) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 60%, rgba(255, 255, 255, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00ffffff', endColorstr='#ffffff', GradientType=1);
  content: " ";
  display: block;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 60px;
}

.slide-select-list .toggle:after {
  color: #8b8b8b;
  content: "\f107";
  font-family: FontAwesome;
  font-size: 18px;
  position: absolute;
  right: 8px;
  top: 10px;
}

.slide-select-list .toggle:hover,
.slide-select-list .toggle:focus {
  color: #004593;
}

.slide-select-list ul {
  display: none;
  list-style: none;
  margin: 0;
  padding: 10px 0;
}

.slide-select-list ul li {
  margin: 0;
}

.slide-select-list ul li a {
  color: #8b8b8b;
  display: block;
  font-size: 12px;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  line-height: 18px;
  overflow: hidden;
  padding: 5px 10px;
  position: relative;
}

.slide-select-list .toggle.opened {
  border-top: 2px solid #00b8b0;
}

.slide-select-list .toggle.opened:after {
  content: "\f106";
}

/* Sidebar Navs */
.mobile-side-nav-toggle {
  border-bottom: 1px solid #e5e5e5;
  padding: 0 50px 10px 0;
  position: relative;
}

.mobile-side-nav-toggle a {
  color: #8b8b8b;
}

.mobile-side-nav-toggle span {
  cursor: pointer;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  height: 32px;
  line-height: 32px;
  margin: 0;
  position: absolute;
  right: 0;
  text-align: right;
  top: -3px;
  width: 32px;
}

.mobile-side-nav-toggle span:after {
  color: #004593;
  content: "\f0c9";
  font-family: FontAwesome;
  font-size: 20px;
}

.mobile-side-nav-toggle span.opened:after {
  content: "\f00d";
}

.side-nav {
  display: none;
  margin: 0 0 30px;
}

.side-nav ul {
  font-size: 13px;
  line-height: 20px;
  margin: 0;
  padding: 0;
}

.side-nav ul li {
  list-style: none;
  margin: 0;
  padding: 0;
}

.side-nav ul li a {
  border-left: 5px solid #fff;
  color: #8b8b8b;
  display: block;
  padding: 5px 10px;
}

.side-nav ul li.on>a {
  border-left: 5px solid #00b8b0;
  color: #004593;
}

.side-nav ul li a:hover,
.side-nav ul li a:focus {
  border-left: 5px solid #004593;
  color: #004593;
  opacity: 1;
}

.side-nav ul ul {
  margin: 0;
  padding: 0;
}

.side-nav ul ul li a {
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  margin: 0;
  margin-left: 25px;
}

.search-nav {
  margin: 0 0 30px;
}

.search-nav ul {
  font-size: 13px;
  line-height: 20px;
  margin: 0;
  padding: 0;
}

.search-nav ul li {
  list-style: none;
  margin: 0;
  padding: 0;
}

.search-nav ul li a {
  border-left: 5px solid #f7f7f7;
  display: block;
  padding: 5px 10px;
}

.search-nav ul li a:hover,
.search-nav ul li a:focus {
  border-left: 5px solid #004593;
  color: #004593;
  opacity: 1;
}

.search-nav ul ul {
  margin: 0;
  padding: 0;
}

.search-nav ul ul li a {
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  margin: 0;
  margin-left: 25px;
}

.selections {
  margin: 0 0 30px;
}

.selections p {
  margin: 0 0 10px;
}

.selections ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.selections ul li {
  margin: 0;
  padding: 0;
}

.selections ul li a {
  background: #00b8b0;
  color: #fff;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  margin: 2px 0;
  padding: 5px 30px 5px 10px;
  position: relative;
}

.selections ul li a:after {
  content: "\00D7";
  font-size: 20px;
  position: absolute;
  right: 10px;
  top: 5px;
}

.side-nav-toggle {
  margin: 0 0 15px;
}

.side-nav-toggle h5 a {
  color: #444;
  font-size: 15px;
  text-transform: none;
  padding-left: 15px;
  position: relative;
}

.side-nav-toggle h5 a:before {
  content: "\f107";
  font-family: FontAwesome;
  left: 0;
  position: absolute;
  top: 0;
}

.side-nav-toggle h5 a.opened:before {
  content: "\f106";
}

.side-nav-toggle ul {
  list-style: none;
  margin: 0 0 0 15px;
  padding: 0;
}

.side-nav-toggle ul li {
  margin: 0;
  padding: 0;
}

.side-nav-toggle ul li a {
  color: #8b8b8b;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  padding: 4px 0;
}

.side-nav-toggle ul li a:hover,
.side-nav-toggle ul li a:focus {
  color: #004593;
}

.side-nav-toggle.topics ul li a {
  padding-left: 45px;
  position: relative;
}

.side-nav-toggle.topics ul li a span.tag {
  left: 0;
  position: absolute;
  top: 5px;
}

.side-nav-toggle.checklist ul li a {
  padding-left: 20px;
  position: relative;
}

.side-nav-toggle.checklist ul li a:before {
  content: "\f096";
  font-family: FontAwesome;
  left: 0;
  position: absolute;
  top: 5px;
}

.side-nav-toggle.checklist ul li a.checked:before {
  content: "\f046";
}

.mobile-side-nav {
  margin: 0 0 30px;
}

.mobile-side-nav select {
  margin: 0 0 15px;
}

/* Aside widget styles */
aside {
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
}

aside>h4>.label-text {
  color: #6f6c6c;
  display: block;
  font-size: 11px;
  text-transform: uppercase;
}

aside>h4 {
  line-height: 20px;
  margin: 0;
  padding: 10px;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}

aside p {
  margin: 0 0 10px;
}

aside .no-bull i {
  margin-right: 5px;
}

aside .copy {
  font-size: 12px;
  line-height: 18px;
  padding: 0 10px;
}

aside .copy h3 {
  font-size: 20px;
  line-height: 25px;
}

aside .copy .icon-wrap, aside .icon-wrap {
  margin: 15px 0;
}

aside .link {
  background: #f7f7f7;
  border-top: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  display: block;
  padding: 10px;
}

aside .link:hover,
aside .link:focus {
  border-bottom-color: #00B8B0;
}

aside .listing .item .large {
  font-size: 14px;
  line-height: 20px;
}

aside .listing .item {
  border-top: 1px solid #e5e5e5;
  margin: 0;
  padding: 10px;
}

aside .listing .avatar {
  min-height: 36px;
  padding-left: 60px;
  position: relative;
}

aside .listing .avatar img {
  left: 10px;
  position: absolute;
  top: 13px;
}

aside form {
  background: #f7f7f7;
  border-top: 1px solid #e5e5e5;
  padding: 10px;
}

aside form .error-message {
  margin: -10px -10px 10px;
}

aside form input[type=text] {
  height: 26px;
  padding: 5px;
}

aside form input[type=submit] {
  width: 100%;
}

aside form textarea {
  height: 82px;
}

aside .custom {
  color: #444;
  font-size: 30px;
  line-height: 35px;
  padding: 15px;
}

aside .custom span {
  color: #00B8B0;
}

aside .downloads {
  font-size: 12px;
  line-height: 18px;
  position: relative;
}

aside .downloads .type:first-child {
  border-top: none;
}

aside .downloads .type {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: 0;
  padding: 10px;
  text-transform: uppercase;
}

aside .downloads .item {
  background: #fff;
  margin: 0;
  padding: 5px 10px;
}

aside .downloads .item a {
  display: block;
}

aside .downloads .item.file {
  padding-left: 30px;
  position: relative;
}

aside .downloads .item.file a:after {
  font-family: FontAwesome;
  font-size: 14px;
  left: 10px;
  position: absolute;
  top: 5px;
}

aside .downloads .item.pdf a:after {
  content: "\f1c1";
}

aside .downloads .item.word a:after {
  content: "\f1c2";
}

aside .downloads .item.excel a:after {
  content: "\f1c3";
}

aside .downloads .item.powerpoint a:after {
  content: "\f1c4";
}

aside .downloads .item.zip a:after {
  content: "\f1c6";
}

aside.contact {
  border: none;
  padding: 0;
}

aside.contact h4 {
  margin: 0;
  padding: 0;
}

aside.contact h4 a {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  padding: 15px 10px;
}

aside.contact h4 a:hover,
aside.contact h4 a:focus {
  border-bottom-color: #00B8B0;
}

aside.contact h4 a span {
  display: block;
}

aside.contact h4 a span.text-wrap {
  color: #8b8b8b;
  border-top: 1px solid #e5e5e5;
  font-size: 12px;
  line-height: 18px;
  margin: 10px -10px 0;
  padding: 10px 10px 0;
}

aside.order {
  background: #fff;
  padding: 20px 10px 0;
}

aside.order ul {
  list-style: none;
  margin: 0 0 15px;
}

aside.order ul li.sizes {
  padding-left: 45px;
  position: relative;
  text-align: left;
}

aside.order ul li.sizes .label {
  left: 0;
  position: absolute;
  top: 9px;
}

aside.order ul li .label {
  white-space: nowrap;
}

aside.order ul li .number {
  float: right;
  white-space: nowrap;
}

aside.order ul li .number input {
  display: inline-block;
  font-size: 12px;
  height: auto;
  line-height: 18px;
  max-width: 40px;
  padding: 0 5px;
  text-align: center;
  position: relative;
  top: -2px;
}

aside.order .button {
  display: block;
  margin: 0 -10px;
  padding: 15px;
  text-align: center;
}

.promotion {
  background: #addfeb;
  border: 1px solid #0090b5;
  border-radius: 7px;
  color: #444;
  margin: 0 0 30px;
  padding: 10px;
  position: relative;
}

.promotion p {
  margin: 0 0 5px;
}

.promotion h4 {
  font-size: 16px;
  margin: 0 0 5px;
}

.promotion .close {
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-size: 30px;
  position: absolute;
  right: 10px;
  top: 7px;
}

.home-blocks aside {
  background: #f7f7f7;
  padding-bottom: 50px;
  position: relative;
}

.home-blocks aside>h4 {
  background: #fff;
  margin: 0;
}

.home-blocks aside .all {
  bottom: 0;
  left: 0;
  position: absolute;
  width: 100%;
}

.tool-tip {
  position: relative;
}

.tool-tip .tip {
  background: #e5e5e5;
  border: 2px solid #8b8b8b;
  color: #444;
  display: none;
  left: 50%;
  margin-left: -120px;
  position: absolute;
  padding: 10px 10px 0;
  text-align: left;
  text-transform: none;
  top: 30px;
  width: 240px;
  z-index: 1;
}

.tool-tip .tip .title {
  display: block;
  font-size: 13px;
  margin: 0 0 5px;
}

.tool-tip .tip .text {
  display: block;
  font-size: 12px;
  line-height: 16px;
  margin: 0 0 10px;
}

.tool-tip:hover .tip,
.tool-tip:focus .tip {
  display: block;
}

.tool-tip .tip:before {
  color: #8b8b8b;
  content: "\F0D8";
  font-family: "FontAwesome";
  font-size: 40px;
  height: auto;
  left: 50%;
  margin: -18px 0 0 -12px;
  position: absolute;
  top: 0;
  width: auto;
}

.tool-tip .tip:after {
  color: #e5e5e5;
  content: "\F0D8";
  font-family: "FontAwesome";
  font-size: 40px;
  height: auto;
  left: 50%;
  margin: -15px 0 0 -12px;
  position: absolute;
  top: 0;
  width: auto;
}

.tool-tip .tip ::selection {
  background: #8b8b8b;
  color: #fff;
}

.tool-tip .tip ::-moz-selection {
  background: #8b8b8b;
  color: #fff;
}

.orders-filter {
  margin: 0 0 15px;
}

.orders-filter .label-text {
  display: block;
  margin: 0 0 5px;
}

.orders-filter .select-wrap {
  margin: 0 0 10px;
}

.track-orders {
  background: #fff;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  position: relative;
  text-align: center;
}

.track-orders .header {
  border-bottom: 1px solid #e5e5e5;
}

.track-orders .header h4 {
  font-size: 24px;
  margin: 0;
  padding: 15px;
}

.track-orders .track-item {
  border-bottom: 1px solid #e5e5e5;
  padding: 30px 15px 20px;
}

.track-orders .track-item h4 {
  font-size: 24px;
}

.track-orders .track-item h5 {
  font-size: 16px;
}

.track-orders .track-item .track-bar {
  background: #e5e5e5;
  height: 15px;
  margin: 15px 0;
  position: relative;
}

.track-orders .track-item .track-bar .track-line {
  background: #004593;
  display: block;
  height: 15px;
  left: 0;
  position: absolute;
  top: 0;
}

.track-orders .track-item .track-bar.percent-10 .track-line {
  width: 10%;
}

.track-orders .track-item .track-bar.percent-20 .track-line {
  width: 20%;
}

.track-orders .track-item .track-bar.percent-30 .track-line {
  width: 30%;
}

.track-orders .track-item .track-bar.percent-40 .track-line {
  width: 40%;
}

.track-orders .track-item .track-bar.percent-50 .track-line {
  width: 50%;
}

.track-orders .track-item .track-bar.percent-60 .track-line {
  width: 60%;
}

.track-orders .track-item .track-bar.percent-70 .track-line {
  width: 70%;
}

.track-orders .track-item .track-bar.percent-80 .track-line {
  width: 80%;
}

.track-orders .track-item .track-bar.percent-90 .track-line {
  width: 90%;
}

.track-orders .track-item .track-bar.percent-100 .track-line {
  width: 100%;
}

.track-orders .track-item p {
  margin: 0 0 10px;
  text-transform: uppercase;
}

.scroll-filters {
  margin: 0 -15px 30px;
  max-height: 320px;
  overflow: auto;
  padding: 0 0 0 15px;
}

.scroll-quickshop {
  margin: 0 -15px 30px;
  max-height: 275px;
  overflow: auto;
  padding: 0 0 0 15px;
}

.mini-cart p {
  color: #444;
  font-size: 15px;
  font-weight: 600;
  margin: 0 0 10px;
}

.mini-cart .scroll-cart {
  max-height: 580px;
  overflow: auto;
}

.mini-cart .cart-item {
  border-bottom: 1px solid #e5e5e5;
  min-height: 130px;
  padding: 20px 115px 20px 165px;
  position: relative;
}

.mini-cart .cart-item .thumb {
  left: 30px;
  position: absolute;
  text-align: center;
  top: 15px;
  width: 120px;
}

.mini-cart .cart-item .thumb img {
  max-height: 100px;
  max-width: 120px;
}

.mini-cart .cart-item .quantity {
  position: absolute;
  right: 30px;
  text-align: right;
  top: 15px;
}

.mini-cart .total {
  padding: 15px 30px;
}

.mini-cart .total p {
  margin: 0;
}

.mini-cart .buttons {
  padding: 0 30px;
}

.mini-cart .buttons p {
  font-weight: 400;
  margin: 0 0 30px;
}

.mini-cart .buttons p .button {
  min-width: 90%;
}

.cart p {
  margin: 0 0 5px;
}

.cart .cart-item {
  border-bottom: 1px solid #e5e5e5;
  min-height: 104px;
  padding: 20px 25px 20px 80px;
  position: relative;
}

.cart .cart-item .thumb {
  left: 0;
  position: absolute;
  text-align: center;
  top: 20px;
}

.cart .cart-item .thumb img {
  max-height: 64px;
  max-width: 64px;
}

.cart .cart-item .quantity {
  font-size: 16px;
  white-space: nowrap;
}

.cart .cart-item .quantity span.label-text {
  display: inline-block;
  margin-right: 5px;
}

.cart .cart-item .quantity input[type=text] {
  display: inline-block;
  max-width: 60px;
  min-width: 45px;
  position: relative;
  top: -1px;
}

.cart .cart-item .price {
  margin-top: 6px;
  text-align: right;
  white-space: nowrap;
}

.cart .pos {
  color: #00AF05;
}

.cart .neg {
  color: #e1151b;
}

.cart .remove {
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-size: 30px;
  position: absolute;
  right: 0;
  top: 25px;
}

.cart .details {
  font-size: 16px;
  line-height: 24px;
  margin: 15px 0 30px;
}

.cart .details p {
  margin: 0 0 15px;
}

.cart .details span.label {
  display: inline-block;
  min-width: 120px;
  text-align: left;
  white-space: nowrap;
}

.cart .details span.number {
  display: inline-block;
  min-width: 100px;
  white-space: nowrap;
}

.cart.saved {
  margin: 0 0 30px;
}

.cart.saved .cart-item {
  min-height: 0;
  padding-left: 0;
  padding-right: 95px;
}

.cart.saved .cart-item .price {
  text-align: left;
}

.cart.saved .add {
  position: absolute;
  right: 30px;
  top: 20px;
}

.cart-table .cart-item {
  min-height: 64px;
  padding: 0 25px 0 80px;
  position: relative;
}

.cart-table .cart-item .thumb {
  left: 0;
  position: absolute;
  text-align: center;
  top: 0;
}

.cart-table .cart-item .thumb img {
  max-height: 64px;
  max-width: 64px;
}

.cart-table .quantity {
  white-space: nowrap;
}

.cart-table .quantity span.label-text {
  display: inline-block;
  margin-left: 5px;
}

.cart-table .quantity input[type=text] {
  display: inline-block;
  font-size: 12px;
  height: auto;
  line-height: 18px;
  max-width: 40px;
  padding: 0 5px;
  position: relative;
  top: -1px;
}

.checkout p {
  margin: 0 0 5px;
}

.checkout .checkout-item {
  border-bottom: 1px solid #e5e5e5;
  min-height: 104px;
  padding: 20px 0 20px 80px;
  position: relative;
}

.checkout .checkout-item .thumb {
  left: 0;
  position: absolute;
  text-align: center;
  top: 20px;
}

.checkout .checkout-item .thumb img {
  max-height: 64px;
  max-width: 64px;
}

.checkout .checkout-item .quantity {
  font-size: 16px;
  white-space: nowrap;
}

.checkout .checkout-item .price {
  text-align: right;
  white-space: nowrap;
}

.checkout .details {
  font-size: 16px;
  line-height: 24px;
  margin: 15px 0 30px;
}

.checkout .details p {
  margin: 0 0 15px;
}

.checkout .details span.label {
  display: inline-block;
  min-width: 120px;
  text-align: left;
  white-space: nowrap;
}

.checkout .details .totals.wide span.label {
  min-width: 160px;
}

.checkout .details span.number {
  display: inline-block;
  min-width: 100px;
  white-space: nowrap;
}

.checkout .buttons {
  padding: 0 30px;
}

.checkout .buttons .button {
  min-width: 90%;
}

.register-login {
  background: #fff;
  border: 1px solid #e5e5e5;
  padding: 45px 30px;
}

.register-login form {
  margin: 0 auto;
  max-width: 360px;
}

.register-login form p.icon {
  padding-left: 30px;
  position: relative;
}

.register-login form p.icon i {
  font-size: 20px;
  left: 0;
  opacity: 0.6;
  position: absolute;
  top: 6px;
}

.register-login form button {
  min-width: 140px;
}

aside.register {
  background: #fff;
}

aside.register h4 {
  background: #0090b5;
  color: #fff;
  padding: 20px 5px;
  text-align: center;
}

aside.register .content {
  padding: 15px;
}

aside.register .content ul {
  list-style: none;
  margin: 0 -15px;
  padding: 0;
}

aside.register .content ul li {
  margin: 0;
  padding: 10px 15px;
}

aside.register .content ul li.on {
  background: #00bce8;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  color: #fff;
}

.register-main {
  background: #fff;
  border: 1px solid #e5e5e5;
}

.register-main .header {
  background: #f7f7f7;
  padding: 22px 15px 22px 110px;
  overflow: hidden;
  position: relative;
}

.register-main .header .step-place {
  background: #00bce8;
  color: #fff;
  height: 100%;
  left: 0;
  padding: 8px;
  position: absolute;
  text-align: center;
  top: 0;
  width: 80px;
}

.register-main .header .step-place .step-text {
  display: block;
  text-transform: uppercase;
}

.register-main .header .step-place .step-number {
  font-size: 20px;
}

.register-main .header h2 {
  color: #00bce8;
  font-size: 20px;
  line-height: 20px;
  margin: 0;
  text-transform: uppercase;
}

.register-main .header .step-place:before {
  background: #00bce8;
  display: block;
  bottom: 0;
  content: " ";
  position: absolute;
  right: -15px;
  transform: rotate(18deg);
  top: -15px;
  width: 30px;
}

.register-main .content {
  padding: 30px;
}

.register-main .content h4 {
  color: #00bce8;
}

.register-main .content .yes-no {
  list-style: none;
  margin: 0 0 30px;
  padding: 0;
}

.register-main .content .yes-no li {
  padding-left: 25px;
  position: relative;
}

.register-main .content .yes-no li:before {
  color: #009988;
  content: "\f05d";
  font-family: FontAwesome;
  font-size: 20px;
  left: 0;
  position: absolute;
  top: -1px;
}

.register-main .content .yes-no li.no:before {
  color: #8b8b8b;
  content: "\f05c";
  opacity: 0.6;
}

.register-main .content .button {
  min-width: 140px;
  text-align: center;
}

.register-main .content .buttons {
  margin: 0 -5px;
}

.register-main .content .buttons .button {
  margin: 5px;
}

.arrow-list {
  margin: 0 0 5px;
  overflow: hidden;
  padding-left: 20px;
  position: relative;
}

.arrow-list:before {
  border-bottom: 30px solid transparent;
  border-left: 15px solid rgba(0, 0, 0, 0);
  border-right: 0;
  border-top: 30px solid transparent;
  content: ' ';
  left: 0;
  position: absolute;
  top: 0;
}

.arrow-list:after {
  border-bottom: 25px solid rgba(0, 0, 0, 0);
  border-left: 15px solid transparent;
  border-right: 0;
  border-top: 25px solid rgba(0, 0, 0, 0);
  content: ' ';
  position: absolute;
  right: 0;
  top: 0;
}

.arrow-list li {
  background: #004593;
  color: #fff;
  display: table;
  float: left;
  font-size: 12px;
  height: 60px;
  line-height: 20px;
  list-style-type: none;
  margin-right: 6%;
  min-width: 80px;
  padding: 10px 0 10px 7px;
  position: relative;
  text-align: center;
  width: 94%;
}

.arrow-list li::before {
  border-bottom: 30px solid #004593;
  border-left: 15px solid transparent;
  border-right: 0;
  border-top: 30px solid #004593;
  content: ' ';
  left: -15px;
  position: absolute;
  top: 0;
}

.arrow-list li::after {
  border-bottom: 30px solid transparent;
  border-left: 15px solid #004593;
  border-right: 0;
  border-top: 30px solid transparent;
  content: ' ';
  position: absolute;
  right: -15px;
  top: 0;
}

.arrow-list li span {
  display: table-cell;
  vertical-align: middle;
}

.table-list {
  list-style: none;
  margin: 0;
  width: 100%;
}

.table-list li {
  position: relative;
}

.table-list li span.label {
  background: #e5e5e5;
  display: block;
  line-height: 20px;
  margin: 0;
  padding: 10px 40px 10px 20px;
  position: relative;
  text-align: center;
}

.table-list li.transparent {
  opacity: 0;
}

.table-list a.tip-toggle {
  left: 0;
  height: 100%;
  position: absolute;
  top: 0;
  width: 100%;
}

.table-list a.tip-toggle i {
  bottom: 5px;
  font-size: 20px;
  position: absolute;
  right: 10px;
}

.table-list a.tip-toggle i:last-child {
  display: none;
}

.table-list a.tip-toggle.opened i:first-child {
  display: none;
}

.table-list a.tip-toggle.opened i:last-child {
  display: inline;
}

.table-list .tip {
  background: #e5e5e5;
  border-top: 1px solid #8b8b8b;
  color: #444;
  display: none;
  padding: 10px 20px;
  text-align: left;
  text-transform: none;
  width: 100%;
}

.table-list .tip .title {
  display: block;
  font-size: 13px;
  margin: 0 0 5px;
}

.table-list .tip .text {
  display: block;
  font-size: 12px;
  line-height: 16px;
  margin: 0 0 10px;
  padding: 0 0 0 10px;
  position: relative;
}

.table-list .tip .text:before {
  content: "\2022";
  left: 0;
  position: absolute;
  top: -1px;
}

.stage-bar-wrap {
  margin: 30px 0;
}

.stage-bar-wrap .stage-labels {
  font-size: 16px;
  margin: 0 0 10px;
  overflow: hidden;
  text-align: center;
}

.stage-bar-wrap .stage-labels>div {
  float: left;
  text-align: center;
  width: 25%;
}

.stage-bar-wrap .stage-bar {
  overflow: hidden;
  position: relative;
}

.stage-bar-wrap .bar-wrap {
  position: relative;
}

.stage-bar-wrap .stage-bar>div {
  float: left;
  height: 40px;
  text-align: center;
  width: 25%;
}

.stage-bar-wrap .stage-bar>div:nth-child(2) {
  background: rgba(0, 0, 0, 0.1);
}

.stage-bar-wrap .stage-bar>div:nth-child(3) {
  background: rgba(0, 0, 0, 0.3);
}

.stage-bar-wrap .stage-bar>div:nth-child(4) {
  background: rgba(0, 0, 0, 0.5);
}

.stage-bar-wrap .star {
  height: 40px;
  line-height: 20px;
  margin-left: -20px;
  position: absolute;
  top: 0;
  width: 40px;
}

.stage-bar-wrap .star>a {
  color: #fff;
  display: block;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  opacity: 1;
  position: relative;
  width: 40px;
  z-index: 1;
}

.stage-bar-wrap .star:after {
  background: #004593;
  border-radius: 100%;
  content: " ";
  height: 30px;
  left: 5px;
  position: absolute;
  top: 5px;
  width: 30px;
  z-index: 0;
}

.stage-bar-wrap .star.opened>a {
  font-size: 18px;
}

.stage-bar-wrap .star.opened:after {
  background: #00b8b0;
  height: 40px;
  left: 0;
  top: 0;
  width: 40px;
}

.stage-bar-wrap .star .info {
  background: #e5e5e5;
  border: 2px solid #8b8b8b;
  color: #444;
  display: none;
  left: 50%;
  margin-left: -120px;
  position: absolute;
  padding: 10px 10px 0;
  text-align: left;
  text-transform: none;
  top: 45px;
  width: 240px;
  z-index: 2;
}

.stage-bar-wrap .star p {
  font-size: 13px;
  margin: 0 0 5px;
}

.stage-bar-wrap .star ul {
  font-size: 12px;
  line-height: 16px;
  margin: 0 0 10px 20px;
}

.stage-bar-wrap .star ul li {
  margin: 0 0 5px;
}

.stage-bar-wrap .star .info:before {
  color: #8b8b8b;
  content: "\F0D8";
  font-family: "FontAwesome";
  font-size: 40px;
  height: auto;
  left: 50%;
  margin: -18px 0 0 -12px;
  position: absolute;
  top: 0;
  width: auto;
}

.stage-bar-wrap .star .info:after {
  color: #e5e5e5;
  content: "\F0D8";
  font-family: "FontAwesome";
  font-size: 40px;
  height: auto;
  left: 50%;
  margin: -15px 0 0 -12px;
  position: absolute;
  top: 0;
  width: auto;
}

.stage-bar-wrap .star .info ::selection {
  background: #8b8b8b;
  color: #fff;
}

.stage-bar-wrap .star .info ::-moz-selection {
  background: #8b8b8b;
  color: #fff;
}

.color-circles {
  margin: 0 auto 30px;
  max-width: 430px;
  text-align: center;
}

.color-circles a {
  border-radius: 100%;
  display: inline-block;
  height: 50px;
  margin: 5px;
  position: relative;
  width: 50px;
}

.color-circles a.none {
  border: 1px solid #8b8b8b;
}

.color-circles a.none:after {
  background: #8b8b8b;
  content: " ";
  height: 2px;
  left: 0;
  position: absolute;
  transform: rotate(-45deg);
  top: 50%;
  width: 100%;
}

.color-circles a.white {
  border: 1px solid #8b8b8b;
}

.color-circles a.on {
  border: 3px solid #00bce8
}

/* Plugins
=======================================================================================================*/
.section-slider {
  position: relative;
}

.section-slider .h1 {
  margin: 0 0 15px;
}

.section-slider h2 {
  margin: 0 0 15px;
}

.section-slider .img-group img {
  margin: 0 10px 10px 0;
  max-width: 120px;
}

.section-slider .flex-direction-nav {
  display: none;
}

.section-slider .flex-direction-nav li {
  margin: 0;
}

.section-slider .flex-direction-nav li a {
  border: 4px solid #ffffff;
  border-radius: 100%;
  color: #fff;
  display: block;
  font-family: FontAwesome;
  font-size: 35px;
  height: 50px;
  line-height: 44px;
  margin-top: -25px;
  overflow: hidden;
  position: absolute;
  text-align: center;
  top: 50%;
  width: 50px;
  z-index: 1;
}

.section-slider .flex-direction-nav li a.flex-prev {
  left: 15px;
}

.section-slider .flex-direction-nav li a.flex-next {
  right: 15px;
}

.section-slider .flex-direction-nav li a.flex-prev:after {
  content: "\f0d9";
  position: relative;
  right: 3px;
}

.section-slider .flex-direction-nav li a.flex-next:after {
  content: "\f0da";
  position: relative;
  right: -3px;
}

.section-slider .flex-control-nav {
  margin: -50px 0 0 0;
  padding: 15px;
  position: relative;
  text-align: center;
  z-index: 1;
}

.section-slider .flex-control-nav li {
  display: inline;
  margin: 0;
  padding: 0;
}

.section-slider .flex-control-nav li a.flex-active {
  background: #004593;
}

.section-slider .flex-control-nav li a {
  background: #fff;
  border-radius: 100%;
  display: inline-block;
  height: 12px;
  margin: 0 5px;
  overflow: hidden;
  text-indent: -999px;
  width: 12px;
}

.timeline-slider .slides .slide {
  display: none;
}

.timeline-slider .slides .slide:first-child {
  display: block;
}

.timeline-slider .slides .slide .img-wrap img {
  width: auto;
}

.timeline-slider .flex-direction-nav {
  display: none;
}

.timeline-slider .flex-direction-nav li a {
  border: 4px solid #E5E5E5;
  border-radius: 100%;
  color: #E5E5E5;
  display: block;
  font-family: FontAwesome;
  font-size: 35px;
  height: 50px;
  line-height: 44px;
  overflow: hidden;
  position: absolute;
  text-align: center;
  top: 30%;
  width: 50px;
  z-index: 1;
}

.timeline-slider .flex-direction-nav li a.flex-prev {
  left: 0;
}

.timeline-slider .flex-direction-nav li a.flex-next {
  right: 0;
}

.timeline-slider .flex-direction-nav li a.flex-prev:after {
  content: "\f0d9";
  position: relative;
  right: 3px;
}

.timeline-slider .flex-direction-nav li a.flex-next:after {
  content: "\f0da";
  position: relative;
  right: -3px;
}

.timeline-slider .flex-direction-nav .flex-disabled {
  display: none;
}

.timeline-slider-controls {
  margin: 0 auto 30px;
  position: relative;
}

.timeline-slider-controls .line {
  background: #e5e5e5;
  height: 5px;
  left: 10%;
  position: absolute;
  right: 10%;
  top: 22px;
  z-index: 0;
}

.timeline-slider-controls .line .highlight {
  background: #00B8B0;
  display: block;
  height: 5px;
  left: 0;
  position: absolute;
  top: 0;
  width: 0;
  transition: width 200ms;
  -webkit-transition: width 200ms;
}

.timeline-slider-controls.active-slide-2 .line .highlight {
  width: 25%;
}

.timeline-slider-controls.active-slide-3 .line .highlight {
  width: 50%;
}

.timeline-slider-controls.active-slide-4 .line .highlight {
  width: 75%;
}

.timeline-slider-controls.active-slide-5 .line .highlight {
  width: 100%;
}

.timeline-slider-controls.active-slide-2 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.timeline-slider-controls.active-slide-3 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.timeline-slider-controls.active-slide-3 ol li.active-slide-2 .icon {
  background: #00B8B0;
}

.timeline-slider-controls.active-slide-4 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.timeline-slider-controls.active-slide-4 ol li.active-slide-2 .icon {
  background: #00B8B0;
}

.timeline-slider-controls.active-slide-4 ol li.active-slide-3 .icon {
  background: #00B8B0;
}

.timeline-slider-controls.active-slide-5 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.timeline-slider-controls.active-slide-5 ol li.active-slide-2 .icon {
  background: #00B8B0;
}

.timeline-slider-controls.active-slide-5 ol li.active-slide-3 .icon {
  background: #00B8B0;
}

.timeline-slider-controls.active-slide-5 ol li.active-slide-4 .icon {
  background: #00B8B0;
}

.timeline-slider-controls ol {
  list-style: none;
  margin: 0;
  overflow: hidden;
  padding: 0;
  position: relative;
  text-align: center;
  width: 100%;
  z-index: 1;
}

.timeline-slider-controls ol li {
  cursor: pointer;
  display: block;
  float: left;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  margin: 0;
  padding-top: 55px;
  position: relative;
  vertical-align: top;
  width: 20%;
}

.timeline-slider-controls ol li .icon {
  background: #004593;
  border-radius: 100%;
  display: block;
  height: 24px;
  left: 50%;
  line-height: 22px;
  margin-left: -12px;
  overflow: hidden;
  position: absolute;
  text-align: center;
  top: 12px;
  width: 24px;
}

.timeline-slider-controls ol li .icon img {
  max-width: 12px;
}

.timeline-slider-controls ol li .text {
  display: none;
}

.timeline-slider-controls ol .flex-active .text {
  display: block;
}

.timeline-slider-controls ol .flex-active .icon {
  background: #00B8B0;
  height: 48px;
  line-height: 46px;
  margin-left: -24px;
  top: 0;
  width: 48px;
}

.timeline-slider-controls ol .flex-active .icon img {
  max-width: 24px;
}

.timeline-slider-controls.wide-4 .line {
  left: 12%;
  right: 12%;
}

.timeline-slider-controls.wide-4 ol li {
  width: 25%;
}

.timeline-slider-controls.wide-4.active-slide-2 .line .highlight {
  width: 34%;
}

.timeline-slider-controls.wide-4.active-slide-3 .line .highlight {
  width: 67%;
}

.timeline-slider-controls.wide-4.active-slide-4 .line .highlight {
  width: 100%;
}

.timeline-slider-controls.wide-3 .line {
  left: 16%;
  right: 16%;
}

.timeline-slider-controls.wide-3 ol li {
  width: 33.3333%;
}

.timeline-slider-controls.wide-3.active-slide-2 .line .highlight {
  width: 50%;
}

.timeline-slider-controls.wide-3.active-slide-3 .line .highlight {
  width: 100%;
}

.step-slider-wrap>.container {
  position: static !important;
}

.step-slider .slides .slide {
  display: none;
}

.step-slider .slides .slide:first-child {
  display: block;
}

.step-slider .slides .slide .img-wrap img {
  width: auto;
}

.step-slider .slides .slide .row .h1.xl {
  font-size: 20px;
  line-height: 20px;
}

.step-slider .slides .slide .h1.xl {
  margin: 5px 0 15px;
}

.step-slider .slides .slide .h1.xl .icon {
  position: relative;
  top: -5px;
}

.step-slider .slides .slide p {
  margin: 0 0 15px;
}

.step-slider .slides .slide .big {
  font-size: 10px;
  line-height: 18px;
  margin: 0 0 15px;
}

.step-slider .slides .slide .times,
.step-slider .slides .slide .equals {
  font-size: 50px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  line-height: 50px;
  position: absolute;
  top: 30px;
}

.step-slider .slides .slide .times {
  left: -13px;
}

.step-slider .slides .slide .equals {
  right: -13px;
}

.step-slider .slides .slide h5 {
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  margin-top: 10px;
}

.step-slider .slides .slide .border-right {
  border-right: 1px solid #0077ff;
}

.step-slider .flex-direction-nav {
  display: none;
}

.step-slider .flex-direction-nav li a {
  border: 4px solid #E5E5E5;
  border-radius: 100%;
  color: #E5E5E5;
  display: block;
  font-family: FontAwesome;
  font-size: 35px;
  height: 50px;
  line-height: 44px;
  margin-top: -5px;
  overflow: hidden;
  position: absolute;
  text-align: center;
  top: 53%;
  width: 50px;
  z-index: 1;
}

.step-slider .flex-direction-nav li a.flex-prev {
  left: 30px;
}

.step-slider .flex-direction-nav li a.flex-next {
  right: 30px;
}

.step-slider .flex-direction-nav li a.flex-prev:after {
  content: "\f0d9";
  position: relative;
  right: 3px;
}

.step-slider .flex-direction-nav li a.flex-next:after {
  content: "\f0da";
  position: relative;
  right: -3px;
}

.step-slider .flex-direction-nav .flex-disabled {
  display: none;
}

.step-slider-controls {
  margin: 0 auto 30px;
  position: relative;
}

.step-slider-controls .line {
  background: #0077ff;
  height: 5px;
  left: 8%;
  position: absolute;
  right: 8%;
  top: 22px;
  z-index: 0;
}

.step-slider-controls .line .highlight {
  background: #00B8B0;
  display: block;
  height: 5px;
  left: 0;
  position: absolute;
  top: 0;
  width: 0;
  transition: width 200ms;
  -webkit-transition: width 200ms;
}

.step-slider-controls.active-slide-2 .line .highlight {
  width: 17%;
}

.step-slider-controls.active-slide-3 .line .highlight {
  width: 34%;
}

.step-slider-controls.active-slide-4 .line .highlight {
  width: 50%;
}

.step-slider-controls.active-slide-5 .line .highlight {
  width: 67%;
}

.step-slider-controls.active-slide-6 .line .highlight {
  width: 84%;
}

.step-slider-controls.active-slide-7 .line .highlight {
  width: 100%;
}

.step-slider-controls.active-slide-2 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-3 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-3 ol li.active-slide-2 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-4 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-4 ol li.active-slide-2 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-4 ol li.active-slide-3 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-5 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-5 ol li.active-slide-2 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-5 ol li.active-slide-3 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-5 ol li.active-slide-4 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-6 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-6 ol li.active-slide-2 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-6 ol li.active-slide-3 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-6 ol li.active-slide-4 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-6 ol li.active-slide-5 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-7 ol li.active-slide-1 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-7 ol li.active-slide-2 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-7 ol li.active-slide-3 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-7 ol li.active-slide-4 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-7 ol li.active-slide-5 .icon {
  background: #00B8B0;
}

.step-slider-controls.active-slide-7 ol li.active-slide-6 .icon {
  background: #00B8B0;
}

.step-slider-controls ol {
  list-style: none;
  margin: 0;
  overflow: hidden;
  padding: 0;
  position: relative;
  text-align: center;
  width: 100%;
  z-index: 1;
}

.step-slider-controls ol li {
  cursor: pointer;
  display: block;
  float: left;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  margin: 0;
  padding-top: 55px;
  position: relative;
  width: 14.28%;
}

.step-slider-controls ol li .icon {
  background: #004593;
  border-radius: 100%;
  display: block;
  height: 24px;
  left: 50%;
  line-height: 22px;
  margin-left: -12px;
  overflow: hidden;
  position: absolute;
  text-align: center;
  top: 12px;
  width: 24px;
}

.step-slider-controls ol li .icon img {
  max-width: 12px;
}

.step-slider-controls ol li .text {
  color: #fff;
  display: none;
}

.step-slider-controls ol .flex-active .text {
  display: block;
  opacity: 0;
}

.step-slider-controls ol .flex-active .icon {
  background: #00B8B0;
  height: 48px;
  line-height: 46px;
  margin-left: -24px;
  top: 0;
  width: 48px;
}

.step-slider-controls ol .flex-active .icon img {
  max-width: 24px;
}

.careers-slider {
  margin: 0 0 30px;
}

.careers-slider .flex-control-nav {
  background: #fff;
  margin: 0;
  padding: 15px 0;
  text-align: center;
}

.careers-slider .flex-control-nav li {
  display: inline;
  margin: 0;
  padding: 0;
}

.careers-slider .flex-control-nav li a.flex-active {
  background: #004593;
}

.careers-slider .flex-control-nav li a {
  background: #e5e5e5;
  border-radius: 100%;
  display: inline-block;
  height: 12px;
  margin: 0 5px;
  overflow: hidden;
  text-indent: -999px;
  width: 12px;
}

.careers-slider .carousel .slides img {
  border-bottom: 4px solid #e5e5e5;
}

.careers-slider .carousel .slides li {
  opacity: 0.8;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.careers-slider .carousel .slides .flex-active-slide {
  opacity: 1;
}

.careers-slider .carousel .slides .flex-active-slide img {
  border-bottom: 4px solid #00B8B0;
}

.flexslider.hero {
  margin: 0 0 30px;
  position: relative;
}

.flexslider.hero ul.flex-direction-nav li {
  margin: 0;
}

.flexslider.hero ul.flex-direction-nav li a {
  background: rgba(0, 0, 0, 0.4);
  bottom: 0;
  color: #fff;
  display: block;
  font-family: FontAwesome;
  font-size: 16px;
  height: 50px;
  line-height: 50px;
  overflow: hidden;
  position: absolute;
  right: 0;
  text-align: center;
  width: 50px;
  z-index: 1;
}

.flexslider.hero ul.flex-direction-nav li a.flex-prev {
  bottom: 50px;
}

.flexslider.hero ul.flex-direction-nav li a.flex-prev:after {
  content: "\f077";
}

.flexslider.hero ul.flex-direction-nav li a.flex-next:after {
  content: "\f078";
}

.blog-slider {
  background: #fff;
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  padding: 30px 30px 0;
  position: relative;
}

.blog-slider .slide {
  position: relative;
}

.blog-slider .product-block {
  background: #fff;
}

.blog-slider .flex-direction-nav li {
  margin: 0;
  padding: 0;
}

.blog-slider a.flex-prev,
.blog-slider a.flex-next {
  bottom: 9px;
  color: #8b8b8b;
  height: 30px;
  display: block;
  font-size: 18px;
  line-height: 30px;
  position: absolute;
  text-align: center;
  width: 30px;
}

.blog-slider a.flex-prev {
  left: 0;
}

.blog-slider a.flex-next {
  right: 0;
}

.blog-slider a.flex-prev:after {
  content: '\f053';
  font-family: FontAwesome;
}

.blog-slider a.flex-next:after {
  content: '\f054';
  font-family: FontAwesome;
}

.blog-slider a.flex-prev:hover,
.blog-slider a.flex-next:hover {
  color: #004593;
  opacity: 1;
}

.blog-slider .flex-control-nav {
  background: #f7f7f7;
  border-top: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  margin: 0 -30px;
  padding: 10px;
  text-align: center;
}

.blog-slider .flex-control-nav li {
  display: inline;
  margin: 0;
  padding: 0;
}

.blog-slider .flex-control-nav li a.flex-active {
  background: #004593;
}

.blog-slider .flex-control-nav li a {
  background: #e5e5e5;
  border-radius: 100%;
  display: inline-block;
  height: 12px;
  margin: 0 5px;
  overflow: hidden;
  text-indent: -999px;
  width: 12px;
}

.flexslider.accent-block .slide h4 {
  padding-right: 40px;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}

.flexslider.accent-block ul.flex-direction-nav {
  margin: 0;
  padding: 0;
  position: absolute;
  right: 15px;
  text-align: right;
  top: 20px;
  width: 60px;
  z-index: 2;
}

.flexslider.accent-block ul.flex-direction-nav li {
  display: inline;
  margin: 0;
  padding: 0;
}

.flexslider.accent-block ul.flex-direction-nav li a {
  color: #8b8b8b;
  display: inline-block;
  font-family: FontAwesome;
  font-size: 16px;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-align: right;
  width: 20px;
}

.flexslider.accent-block ul.flex-direction-nav li a:hover {
  color: #004593;
}

.flexslider.accent-block ul.flex-direction-nav li a.flex-prev:after {
  content: "\f053";
}

.flexslider.accent-block ul.flex-direction-nav li a.flex-next:after {
  content: "\f054";
}

.full-slider {
  margin: 0 0 60px;
  position: relative;
}

.full-slider .slider {
  background: #f7f7f7;
  background: -moz-radial-gradient(center, ellipse cover, rgba(247, 247, 247, 1) 0%, rgba(229, 229, 229, 1) 100%);
  background: -webkit-radial-gradient(center, ellipse cover, rgba(247, 247, 247, 1) 0%, rgba(229, 229, 229, 1) 100%);
  background: radial-gradient(ellipse at center, rgba(247, 247, 247, 1) 0%, rgba(229, 229, 229, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f7f7f7', endColorstr='#e5e5e5', GradientType=1);
  position: relative;
}

.full-slider .slider:after {
  background: #fff;
  content: " ";
  display: block;
  height: 100%;
  right: 0;
  position: absolute;
  top: 0;
  width: 1px;
}

.full-slider .img-wrap {
  display: block;
}

.full-slider .custom img {
  display: block;
  margin: 0 auto 50px;
  width: auto;
}

.full-slider .flex-control-nav {
  text-align: center;
  margin: 15px 0 -30px;
}

.full-slider .flex-control-nav li {
  display: inline;
  margin: 0;
  padding: 0;
}

.full-slider .flex-control-nav li a.flex-active {
  background: #004593;
}

.full-slider .flex-control-nav li a {
  background: #e5e5e5;
  border-radius: 100%;
  display: inline-block;
  height: 12px;
  margin: 0 5px;
  overflow: hidden;
  text-indent: -999px;
  width: 12px;
}

.full-slider .flex-nav a.flex-prev,
.full-slider .flex-nav a.flex-next {
  background: #00b8b0;
  border-radius: 100%;
  color: #fff;
  height: 30px;
  display: none;
  font-size: 18px;
  line-height: 30px;
  position: absolute;
  text-align: center;
  top: 50%;
  width: 30px;
}

.full-slider .flex-nav a.flex-prev {
  left: -60px;
  text-indent: -2px;
}

.full-slider .flex-nav a.flex-next {
  right: -60px;
  text-indent: 2px;
}

.full-slider .flex-nav a.flex-prev:hover,
.full-slider .flex-nav a.flex-next:focus {
  opacity: 1;
}

.full-slider .flex-nav a.flex-disabled {
  cursor: default;
  opacity: 0.2;
}

.full-slider .carousel.wide-4 {
  margin-left: -117px;
  width: 233px;
}

.full-slider .carousel.wide-3 {
  margin-left: -97px;
  width: 193px;
}

.full-slider .carousel.wide-2 {
  margin-left: -78px;
  width: 156px;
}

.full-slider .carousel {
  background: #fff;
  border: 1px solid #e5e5e5;
  left: 50%;
  margin: -24px 0 0 -135px;
  position: absolute;
  padding: 5px 36px;
  top: 100%;
  width: 270px;
}

.full-slider .carousel img {
  max-height: 36px;
  max-width: 36px;
}

.full-slider .carousel .flex-active-slide {
  position: relative;
}

.full-slider .carousel .flex-active-slide:after {
  border: 4px solid #004593;
  content: " ";
  display: block;
  left: 0;
  height: 36px;
  position: absolute;
  top: 0;
  width: 36px;
}

.full-slider .carousel ul.slides li:first-child {
  display: block;
}

.full-slider .carousel ul.slides li {
  display: none;
  margin: 0;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.full-slider .carousel .flex-direction-nav li {
  margin: 0;
  padding: 0;
  position: static;
}

.full-slider .carousel a.flex-prev,
.full-slider .carousel a.flex-next {
  color: #8b8b8b;
  height: 30px;
  display: block;
  font-size: 18px;
  line-height: 30px;
  position: absolute;
  text-align: center;
  top: 9px;
  width: 30px;
}

.full-slider .carousel a.flex-prev {
  left: 0;
}

.full-slider .carousel a.flex-next {
  right: 0;
}

.full-slider .carousel a.flex-prev:after {
  content: '\f053';
  font-family: FontAwesome;
}

.full-slider .carousel a.flex-next:after {
  content: '\f054';
  font-family: FontAwesome;
}

.full-slider .carousel a.flex-prev:hover,
.full-slider .carousel a.flex-next:focus {
  color: #004593;
  opacity: 1;
}

.full-slider .carousel a.flex-disabled {
  cursor: default;
  opacity: 0.2;
}

.full-slider .carousel a.flex-disabled:hover,
.full-slider .carousel a.flex-disabled:focus {
  color: #8b8b8b;
}

.excellence-slider {
  border: 1px solid #e5e5e5;
  margin: 0 0 30px;
  position: relative;
}

.excellence-slider .slide {
  position: relative;
}

.excellence-slider .caption {
  padding: 10px;
}

.excellence-slider .img-wrap {
  display: block;
  text-align: center;
}

.excellence-slider .flex-direction-nav li {
  margin: 0;
  padding: 0;
}

.excellence-slider a.flex-prev,
.excellence-slider a.flex-next {
  bottom: 9px;
  color: #8b8b8b;
  height: 30px;
  display: block;
  font-size: 18px;
  line-height: 30px;
  position: absolute;
  text-align: center;
  width: 30px;
}

.excellence-slider a.flex-prev {
  left: 0;
}

.excellence-slider a.flex-next {
  right: 0;
}

.excellence-slider a.flex-prev:after {
  content: '\f053';
  font-family: FontAwesome;
}

.excellence-slider a.flex-next:after {
  content: '\f054';
  font-family: FontAwesome;
}

.excellence-slider a.flex-prev:hover,
.excellence-slider a.flex-next:hover {
  color: #004593;
  opacity: 1;
}

.excellence-slider .flex-control-nav {
  background: #f7f7f7;
  border-top: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  margin: 0;
  padding: 10px;
  text-align: center;
}

.excellence-slider .flex-control-nav li {
  display: inline;
  margin: 0;
  padding: 0;
}

.excellence-slider .flex-control-nav li a.flex-active {
  background: #004593;
}

.excellence-slider .flex-control-nav li a {
  background: #e5e5e5;
  border-radius: 100%;
  display: inline-block;
  height: 12px;
  margin: 0 5px;
  overflow: hidden;
  text-indent: -999px;
  width: 12px;
}

.info-block .info .flex-direction-nav {
  margin: 0;
  padding: 0;
}

.info-block .info .flex-direction-nav li {
  margin: 0;
  padding: 0;
}

.info-block .info .flex-direction-nav li a {
  color: #8b8b8b;
  display: block;
  font-family: FontAwesome;
  font-size: 16px;
  height: 20px;
  line-height: 20px;
  margin-top: -10px;
  overflow: hidden;
  position: absolute;
  top: 50%;
  width: 20px;
}

.info-block .info .flex-direction-nav li a:hover,
.info-block .info .flex-direction-nav li a:focus {
  color: #004593;
}

.info-block .info .flex-direction-nav li a.flex-prev {
  left: 0;
}

.info-block .info .flex-direction-nav li a.flex-next {
  right: 0;
  text-align: right;
}

.info-block .info .flex-direction-nav li a.flex-prev:after {
  content: "\f053";
}

.info-block .info .flex-direction-nav li a.flex-next:after {
  content: "\f054";
}

.flexslider.mini .flex-control-nav {
  margin: 15px 0;
  text-align: center;
}

.flexslider.mini .flex-control-nav li {
  display: inline;
  margin: 0;
  padding: 0;
}

.flexslider.mini .flex-control-nav li a.flex-active {
  background: #004593;
}

.flexslider.mini .flex-control-nav li a {
  background: #e5e5e5;
  border-radius: 100%;
  display: inline-block;
  height: 12px;
  margin: 0 5px;
  overflow: hidden;
  text-indent: -999px;
  width: 12px;
}

.quotes-block .flex-control-nav {
  margin: 0;
  text-align: center;
}

.quotes-block .flex-control-nav li {
  display: inline;
  margin: 0;
  padding: 0;
}

.quotes-block .flex-control-nav li a.flex-active {
  background: #004593;
}

.quotes-block .flex-control-nav li a {
  background: #e5e5e5;
  border-radius: 100%;
  display: inline-block;
  height: 12px;
  margin: 0 5px;
  overflow: hidden;
  text-indent: -999px;
  width: 12px;
}

.box-strip-block {
  margin: 0;
  padding: 0 30px;
  position: relative;
}

.box-strip-block .slide {
  padding: 0 10px;
  width: 296px !important;
}

.box-strip-block .slide>div {
  min-height: 280px;
}

.box-strip-block .flx-prev,
.box-strip-block .flx-next {
  display: block;
  color: #8b8b8b;
  height: 100%;
  font-size: 30px;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 30px;
  z-index: 1;
}

.box-strip-block .flx-prev i {
  left: 25%;
  position: absolute;
  top: 48%;
}

.box-strip-block .flx-next i {
  position: absolute;
  right: 25%;
  top: 48%;
}

.box-strip-block .flx-prev {
  left: 0;
}

.box-strip-block .flx-next {
  right: 0;
}

.short-box-strip-block {
  margin: 0 auto 30px;
  max-width: 642px;
  padding: 0 30px;
  position: relative;
}

.short-box-strip-block .slide {
  padding: 0 10px;
  width: 296px !important;
}

.short-box-strip-block .slide .content {
  border-top: 1px solid #e5e5e5;
  border-bottom: 4px solid #e5e5e5;
  min-height: 75px;
  padding: 15px;
  text-align: center;
}

.short-box-strip-block .slide .content p {
  margin: 0;
}

.short-box-strip-block .flx-prev,
.short-box-strip-block .flx-next {
  display: block;
  color: #8b8b8b;
  height: 100%;
  font-size: 30px;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 30px;
  z-index: 1;
}

.short-box-strip-block .flx-prev i {
  left: 25%;
  position: absolute;
  top: 48%;
}

.short-box-strip-block .flx-next i {
  position: absolute;
  right: 25%;
  top: 48%;
}

.short-box-strip-block .flx-prev {
  left: 0;
}

.short-box-strip-block .flx-next {
  right: 0;
}

.carousel-strip-block {
  padding: 12px 30px
}

.carousel-strip-block .slide {
  padding: 0 10px;
  width: 200px !important;
}

.carousel-strip-block .product-block {
  margin: 0;
  max-width: 195px;
}

.carousel-strip-block .product-block img {
  max-height: 100px;
}

.carousel-strip-block .product-block .text-wrap {
  min-height: 100px;
}

.carousel-strip-block .flx-prev,
.carousel-strip-block .flx-next {
  background: #f7f7f7;
  display: block;
  color: #8b8b8b;
  height: 100%;
  font-size: 30px;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 30px;
  z-index: 1;
}

.carousel-strip-block .flx-prev i {
  left: 25%;
  position: absolute;
  top: 48%;
}

.carousel-strip-block .flx-next i {
  position: absolute;
  right: 25%;
  top: 48%;
}

.carousel-strip-block .flx-prev {
  border-right: 1px solid #e5e5e5;
  left: 0;
}

.carousel-strip-block .flx-next {
  border-left: 1px solid #e5e5e5;
  right: 0;
}

.modal-slider .flexslider .img-wrap {
  display: block;
}

.modal-slider .flexslider .img-wrap img {
  display: block;
  margin: 0 auto;
}

.modal-slider .flexslider .flex-caption {
  display: none;
}

.modal-slider a.modal-slider-prev,
.modal-slider a.modal-slider-next {
  background: #f7f7f7;
  border-left: 1px solid #e5e5e5;
  display: block;
  color: #8b8b8b;
  font-size: 20px;
  height: 100%;
  line-height: 100%;
  position: absolute;
  text-align: center;
  top: 0;
  width: 30px;
}

.modal-slider a.modal-slider-prev {
  right: 30px;
}

.modal-slider a.modal-slider-next {
  right: 0;
}

.modal-slider a.modal-slider-next i,
.modal-slider a.modal-slider-prev i {
  margin: -10px 0 0 -3px;
  position: absolute;
  left: 50%;
  top: 50%;
}

.modal-slider a.modal-slider-prev:hover,
.modal-slider a.modal-slider-next:hover,
.modal-slider a.modal-slider-prev:focus,
.modal-slider a.modal-slider-next:focus {
  color: #004593;
}

.modal-slider .sub-footer {
  padding-right: 150px;
  position: relative;
}

.modal-slider .sub-footer .slide-count {
  font-size: 12px;
  margin-top: -10px;
  position: absolute;
  right: 70px;
  top: 50%;
}

/* Timeline */
#timeline-embed {
  height: 600px;
}

.tl-timenav .tl-attribution {
  display: none;
}

/* Magnific Popup */
.white-popup {
  background: #fff;
  border: 4px solid #222;
  margin: 40px auto;
  max-width: 940px;
  position: relative;
}

.white-popup .mini-cart {
  padding: 15px 0;
}

.white-popup .mini-cart .scroll-cart {
  max-height: 520px;
  overflow: auto;
}

.white-popup .mfp-close {
  color: #444;
  height: 40px;
  line-height: 40px;
  font-size: 27px;
  opacity: 1;
  position: absolute;
  padding: 0;
  right: 0;
  text-align: center;
  top: 0;
  width: 40px;
}

.white-popup .header {
  border-bottom: 1px solid #e5e5e5;
  display: block;
  font-size: 20px;
  margin: 0;
  padding: 10px 30px;
  position: relative;
}

.white-popup .caption {
  border-top: 4px solid #00B8B0;
  padding: 15px 30px;
}

.white-popup .header .open-modal {
  left: 30px;
  position: absolute;
  top: 10px;
}

.white-popup .content {
  padding: 20px 30px;
  position: relative;
}

.white-popup hr.divider {
  margin: 20px -30px;
}

.sub-footer {
  border-top: 4px solid #00B8B0;
  padding: 15px 30px;
}

.white-popup .sales-support {
  overflow: hidden;
  text-align: center;
}

.white-popup .sales-support .content:first-child {
  border-bottom: 1px solid #e5e5e5;
}

.white-popup .sales-support .content {
  padding: 60px 30px 30px;
}

.white-popup .sales-support .content .img-wrap {
  margin: 0 0 15px;
}

.white-popup .sales-support .content .img-wrap img {
  max-width: 100%;
  width: auto;
}

.white-popup .sales-support .content h3 {
  margin: 0 0 15px;
}

.mfp-gallery .mfp-content {
  background: #fff;
  border: 4px solid #222;
  margin: 40px auto;
  max-width: 960px;
  min-width: 300px;
  position: relative;
  width: auto;
}

.mfp-gallery .mfp-content .mfp-close {
  color: #444;
  height: 40px;
  line-height: 40px;
  font-size: 27px;
  opacity: 1;
  position: absolute;
  padding: 0;
  right: 0;
  text-align: center;
  top: 0;
  width: 40px;
}

.mfp-gallery .mfp-content .header {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  display: block;
  font-size: 20px;
  margin: 0;
  padding: 10px 30px;
}

.mfp-gallery .mfp-content figure figcaption {
  margin: 0;
}

.mfp-gallery .mfp-content img.mfp-img {
  margin: 0 auto;
  padding: 0;
}

.mfp-gallery .mfp-bottom-bar {
  background: #fff;
  border-top: 4px solid #00B8B0;
  cursor: auto;
  left: auto;
  margin-top: 0px;
  padding: 15px 30px;
  padding-right: 85px;
  position: relative;
  top: auto;
  width: auto;
}

.mfp-gallery .mfp-title {
  color: #666;
  line-height: 15px;
}

.mfp-gallery .mfp-counter {
  color: #666;
  line-height: 15px;
  right: 90px;
  top: 15px;
}

.mfp-gallery .mfp-arrow {
  border-left: 1px solid #ccc;
  bottom: 0;
  color: #666;
  font-size: 16px;
  height: 45px;
  line-height: 45px;
  margin: 0;
  margin-top: 0;
  opacity: 1;
  filter: alpha(opacity=1);
  padding: 0;
  position: absolute;
  text-align: center;
  top: auto;
  width: 30px;
}

.mfp-arrow-left {
  left: auto;
  right: 30px;
}

.mfp-arrow:before {
  display: none;
}

.mfp-arrow-left:after {
  content: '\f053';
}

.mfp-arrow-right:after {
  content: '\f054';
}

.mfp-arrow:after {
  border: none;
  display: block;
  font-family: FontAwesome;
  height: 0;
  left: 0;
  margin-left: 8px;
  margin-top: 0px;
  position: absolute;
  top: 0;
  text-align: center;
  width: auto;
}

/* jQuery UI */
.date {
  position: relative;
}

.date .ui-datepicker-trigger {
  background: transparent;
  border: none;
  cursor: pointer;
  height: 20px;
  margin-top: -28px;
  opacity: 1;
  padding: 0;
  position: absolute;
  right: 5px;
  text-align: center;
  top: 100%;
  width: 20px;
  z-index: 10;
}

.date .ui-datepicker-trigger:after {
  content: "\f073";
  font-family: FontAwesome;
}

.ui-datepicker {
  background: #fff !important;
  border: 1px solid #d3d3d3;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
}

.ui-datepicker-header {
  border-bottom: 1px solid #d3d3d3;
  padding: 10px;
  text-align: center;
}

.ui-datepicker-calendar tbody td {
  padding: 5px;
  text-align: center;
}

.ui-datepicker-prev {
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  float: left;
}

.ui-datepicker-next {
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  float: right;
}

/* Scroll Tabs */
.scroll-tabs-wrap .tab {
  padding-top: 15px;
}

.scroll_tabs_container {
  background: #fff;
  border: 1px solid #e5e5e5;
  height: 36px;
  margin-bottom: -1px;
  overflow: hidden;
}

.scroll_tabs_container div.scroll_tab_inner {
  height: 36px;
}

.scroll_tabs_container div.scroll_tab_inner .scroll_tab_first {
  border-left: 0;
}

.scroll_tabs_container div.scroll_tab_inner span {
  background-color: #F7F7F7;
  border-left: 1px solid #e5e5e5;
  color: #8b8b8b;
  cursor: pointer;
  font-size: 14px;
  line-height: 36px;
  padding-left: 15px;
  padding-right: 15px;
  position: relative;
}

.scroll_tabs_container div.scroll_tab_inner span.on {
  background: #fff;
  color: #004593;
}

.scroll_tabs_container .scroll_tab_left_button {
  background: #fff;
  border-right: 1px solid #e5e5e5;
  height: 36px;
}

.scroll_tabs_container .scroll_tab_left_button:before {
  content: "\f053";
  font-family: FontAwesome;
  line-height: 36px;
}

.scroll_tabs_container .scroll_tab_right_button {
  background: #fff;
  border-left: 1px solid #e5e5e5;
  height: 36px;
}

.scroll_tabs_container .scroll_tab_right_button:before {
  content: "\f054";
  font-family: FontAwesome;
  line-height: 36px;
}

.scroll_tabs_container .scroll_tab_left_button_disabled {
  color: #e5e5e5;
}

/* Jquery UI Slider
===============================================================================*/
#range.ui-slider .ui-slider-handle {
  border: 2px solid #444444;
}

#range .ui-widget-header {
  background: #8b8b8b;
}

.ui-slider {
  position: relative;
  text-align: left;
}

.ui-slider .ui-slider-handle {
  background: #fff;
  border: 2px solid #004593;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.4);
  border-radius: 100%;
  cursor: default;
  height: 20px;
  outline: none;
  position: absolute;
  width: 20px;
  z-index: 2;
  -ms-touch-action: none;
  touch-action: none;
}

.ui-slider .ui-slider-range {
  background-position: 0 0;
  border: 0;
  background: #00b8b0;
  display: block;
  position: absolute;
  z-index: 1;
}

.ui-slider.ui-state-disabled .ui-slider-handle,
.ui-slider.ui-state-disabled .ui-slider-range {
  filter: inherit;
}

.ui-slider-horizontal {
  height: 18px;
}

.ui-slider-horizontal .ui-slider-handle {
  top: -2px;
  margin-left: -10px;
}

.ui-slider-horizontal .ui-slider-range {
  height: 100%;
  top: 0;
}

.ui-slider-horizontal .ui-slider-range-min {
  left: 0;
}

.ui-slider-horizontal .ui-slider-range-max {
  right: 0;
}

/* .ui-widget-content{border:1px solid #8b8b8b;background:#f7f7f7;color:#444;}
.ui-widget-content a{color:#444;} */
.range-slide {
  margin: 0 0 30px;
  position: relative;
}

.range-slide input {
  border: none;
  color: #004593;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  height: 20px;
  padding: 0;
  position: absolute;
  right: 0;
  text-align: right;
  top: 0;
  width: 80px;
}

.range-slide label {
  display: inline-block;
  margin: 0 0 5px;
}

.range-slide .markers {
  display: table;
  margin: 5px -1%;
  width: 103%;
}

.range-slide .markers span {
  display: table-cell;
  font-size: 11px;
  text-align: center;
  width: 4.166666666666%;
}

.step-bar {
  margin: 0 3px 30px;
  position: relative;
}

.step-bar .ui-widget-content {
  border: none;
  background: #e5e5e5;
  height: 14px;
  z-index: 1;
}

.step-bar .ui-slider .ui-slider-handle {
  background: #009988;
  border: 7px solid #00b8b0;
  box-shadow: none;
  height: 30px;
  width: 30px;
}

.step-bar .ui-slider-horizontal .ui-slider-handle {
  top: -8px;
  margin-left: -15px;
}

.step-bar label {
  display: inline-block;
  margin: 0 0 5px;
}

.step-bar .markers {
  margin: 0 0 15px;
}

.step-bar .markers span:first-child {
  display: none;
}

.step-bar .markers span:last-child {
  display: none;
}

.step-bar .markers span {
  background: #8b8b8b;
  display: block;
  height: 30px;
  left: 0;
  margin-left: -1px;
  position: absolute;
  top: -8px;
  width: 2px;
  z-index: 0;
}

.step-bar .markers.step-3 span:nth-child(2) {
  left: 50%;
}

.step-bar .markers.step-4 span:nth-child(2) {
  left: 33.3333333%;
}

.step-bar .markers.step-4 span:nth-child(3) {
  left: 66.6666666%;
}

.step-bar .markers.step-5 span:nth-child(2) {
  left: 25%;
}

.step-bar .markers.step-5 span:nth-child(3) {
  left: 50%;
}

.step-bar .markers.step-5 span:nth-child(4) {
  left: 75%;
}

.step-bar .markers.step-6 span:nth-child(2) {
  left: 20%;
}

.step-bar .markers.step-6 span:nth-child(3) {
  left: 40%;
}

.step-bar .markers.step-6 span:nth-child(4) {
  left: 60%;
}

.step-bar .markers.step-6 span:nth-child(5) {
  left: 80%;
}

.product-tiles .product-tile {
  background: #f7f7f7;
  margin: 0 0 30px;
  padding: 15px;
}

.product-tiles .product-tile.active img {
  opacity: 1;
}

.product-tiles .product-tile.active .text {
  opacity: 1;
}

.product-tiles .product-tile a {
  display: block;
  color: #6f6c6c;
  font-size: 16px;
}

.product-tiles .product-tile img {
  display: block;
  margin: 0 0 7px;
  max-width: 100%;
  opacity: 0.3;
}

.product-tiles .product-tile .text {
  display: block;
  opacity: 0.3;
}

.filter-columns {
  margin: 0 0 15px;
}

.filter-columns a.filter-toggle {
  color: #6f6c6c;
  display: block;
  margin: 0 0 5px;
  text-align: center;
  text-transform: uppercase;
}

.filter-columns a.filter-toggle:after {
  content: "\f106";
  font-family: FontAwesome;
}

.filter-columns a.filter-toggle.closed:after {
  content: "\f107";
}

.filter-columns .filters {
  border-bottom: 2px solid #00b8b0;
  border-top: 2px solid #00b8b0;
}

.filter-columns .filters .filter-columns-wrap {
  overflow: hidden;
  padding: 10px 0 10px 1px;
}

.filter-columns .filters .filter-column:nth-child(1) {
  border-left: none;
}

.filter-columns .filters .filter-column:nth-child(3) {
  border-left: none;
}

.filter-columns .filters .filter-column:nth-child(5) {
  border-left: none;
}

.filter-columns .filters .filter-column:nth-child(7) {
  border-left: none;
}

.filter-columns .filters .filter-column {
  border-left: 1px solid #e5e5e5;
  float: left;
  margin: 5px 0;
  padding: 0 10px;
  width: 50%;
}

.filter-columns .filters .filter-column h4 {
  color: #00b8b0;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  margin: 0 0 5px;
}

.filter-columns .filters .filter-column ul {
  font-size: 12px;
  line-height: 14px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.filter-columns .filters .filter-column ul li {
  margin: 0 0 3px;
}

.filter-columns .filters .filter-column ul li a {
  color: #6f6c6c;
  display: inline-block;
  padding: 3px 7px;
}

.filter-columns .filters .filter-column ul li a.active {
  background: #00b8b0;
  color: #fff;
}

.calculator {
  margin: 0 0 30px;
  text-align: center;
}

.calculator .total {
  margin: 0 0 30px;
}

.calculator .total input[type=text] {
  border: none;
  border-bottom: 2px solid #004593;
  color: #004593;
  font-size: 55px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  height: auto;
  line-height: 55px;
  margin: 0 auto 10px;
  max-width: 260px;
  text-align: center;
}

.calculator .total input[type=text]::placeholder {
  color: #004593;
  opacity: 1;
}

.calculator h2 {
  font-size: 55px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  line-height: 55px;
  margin: 0 0 10px;
}

.calculator h4 {
  font-weight: 600;
  text-transform: uppercase;
}

.calc-inputs {
  margin: 0 -1% 20px;
}

.calc-inputs>label {
  float: left;
  margin: 0 1%;
}

.calc-inputs>label.compare {
  margin: 0 0 15px;
  width: 98%;
}

.calc-inputs>label.procedures {
  width: 20%;
}

.calc-inputs>label.per {
  width: 18%;
}

.calc-inputs>label.cost {
  width: 38%;
}

.calc-inputs>label.success {
  width: 16%;
}

.calc-inputs>label span.label-text {
  font-size: 12px;
}

.calc-inputs input[type=text],
.calc-inputs select {
  height: 26px;
  padding: 5px;
}

.calc-inputs .SelectBox {
  padding: 2px 5px;
}

.calc-inputs .SumoSelect.open>.optWrapper {
  top: 30px;
}

/* Scrollbar plugin */
.scroll-list {
  max-height: 219px;
  overflow: hidden;
}

.scroll-list.tall {
  max-height: 325px;
}

.mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background: #8b8b8b !important;
}

.mCSB_scrollTools .mCSB_draggerRail {
  background: #e5e5e5;
}

.mCSB_scrollTools .mCSB_buttonUp::after,
.mCSB_scrollTools .mCSB_buttonDown::after {
  color: #b8b8b8;
  content: "\f077";
  font-family: FontAwesome;
  font-size: 16px;
  left: 50%;
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
}

.mCSB_scrollTools .mCSB_buttonDown::after {
  content: "\f078";
}

/* Scrollbar plugin */
.sumoStopScroll {
  overflow: auto;
}

.SelectBox {
  padding: 7px 8px;
}

.SumoSelect {
  font-size: 12px;
  display: block;
  width: 100%;
}

.SumoSelect>.CaptionCont {
  border: 1px solid #e5e5e5;
  border-radius: 0;
}

.SumoSelect>.optWrapper {
  border-radius: 0;
}

.SumoSelect>.optWrapper>.options>li {
  border-bottom: 0;
  margin: 0;
}

.SumoSelect:hover>.CaptionCont {
  box-shadow: none;
  border-color: #e5e5e5;
}

.SumoSelect:focus>.CaptionCont,
.SumoSelect.open>.CaptionCont {
  box-shadow: none;
  border-color: #004593;
}

.SumoSelect.open.sumo-error>.CaptionCont {
  box-shadow: none;
  border-color: #8D2A90;
}

.SumoSelect.sumo-error>.CaptionCont {
  box-shadow: none;
  border-color: #8D2A90;
}

.SumoSelect.open>.optWrapper {
  top: 40px;
}

.SumoSelect>.optWrapper>.options li.opt {
  border-bottom: 1px solid #e5e5e5;
}

.SumoSelect>.optWrapper>.options li.opt:hover {
  background-color: #f7f7f7;
}

.SumoSelect>.optWrapper.isFloating {
  bottom: auto;
  left: 0;
  margin: auto;
  max-height: none;
  position: absolute;
  right: auto;
  top: auto;
  width: 100%;
}

/* Marketo form */
.mktoForm {
  width: 100% !important;
}

.mktoForm .mktoFormRow {
  clear: both;
  margin: 0 -15px;
}

.mktoForm .mktoFormCol {
  float: left;
  margin: 0 0 15px !important;
  min-height: 0 !important;
  padding: 0 15px;
  position: relative;
  width: 50%;
}

.mktoForm .mktoOffset {
  display: none;
}

.mktoForm .mktoGutter {
  display: none;
}

.mktoForm .mktoFieldWrap {
  float: none !important;
}

.mktoForm label.mktoLabel {
  color: #8b8b8b;
  display: block;
  font-weight: 600;
  margin: 0 0 5px;
  width: 100% !important;
}

.mktoForm input[type=text],
.mktoForm input[type=url],
.mktoForm input[type=email],
.mktoForm input[type=tel],
.mktoForm input[type=number],
.mktoForm input[type=date],
.mktoForm textarea.mktoField,
.mktoForm select.mktoField {
  background: #fff;
  border: 1px solid #e5e5e5;
  color: #444444 !important;
  font-family: Arial, Helvetica, Verdana, sans-serif;
  display: block;
  font-size: 12px;
  height: 36px;
  line-height: 20px !important;
  outline: none;
  padding: 8px !important;
  width: 100% !important;
}

.mktoForm textarea.mktoField {
  height: 36px !important;
}

.mktoForm input[type=text]:focus,
.mktoForm input[type=url]:focus,
.mktoForm input[type=email]:focus,
.mktoForm input[type=tel]:focus,
.mktoForm input[type=number]:focus,
.mktoForm input[type=date]:focus,
.mktoForm textarea.mktoField:focus,
.mktoForm select.mktoField:focus {
  border-color: #004593;
}

.mktoForm .mktoError {
  left: 15px;
  right: auto !important;
}

.mktoButtonRow {
  display: block !important;
  position: relative;
}

.mktoButtonWrap {
  display: block;
  text-align: right !important;
}

.mktoForm .mktoButtonWrap.mktoSimple button.mktoButton {
  background: #004593 !important;
  border: none !important;
  color: #ffffff;
  display: inline-block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px !important;
  line-height: 12px;
  min-width: 100px;
  padding: 10px 15px !important;
  text-decoration: none;
  white-space: nowrap;
}

@media only screen and (max-width: 480px) {
  .mktoForm {
    padding: 0 !important;
  }

  .mktoForm .mktoFormRow {
    clear: both;
    margin: 0;
  }

  .mktoForm .mktoFormCol {
    padding: 0 !important;
    width: 100%;
  }

  .mktoForm input[type=text],
  .mktoForm input[type=url],
  .mktoForm input[type=email],
  .mktoForm input[type=tel],
  .mktoForm input[type=number],
  .mktoForm input[type=date],
  .mktoForm textarea.mktoField,
  .mktoForm select.mktoField {
    background: #fff;
    border: 1px solid #e5e5e5;
    color: #444444 !important;
    font-family: Arial, Helvetica, Verdana, sans-serif;
    display: block;
    font-size: 12px !important;
    height: 36px !important;
    line-height: 20px !important;
    outline: none;
    padding: 8px !important;
    width: 100% !important;
  }
}

/* Header
=======================================================================================================*/
header {
  background: #fff;
  position: relative;
  z-index: 3;
}

header>.container-fluid {
  height: 60px;
  position: relative;
  /* transition:height 500ms;
	-webkit-transition:height 500ms;
	-webkit-backface-visibility:hidden;
	-webkit-transform:translateZ(0); */
}

header .logo {
  left: 15px;
  margin-top: -16px;
  opacity: 1;
  position: absolute;
  top: 50%;
}

header nav.desktop {
  display: none;
  font-size: 16px;
  margin: 0 0 0 115px;
}

header nav.desktop ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

header nav.desktop ul li {
  float: left;
  margin: 0 25px 0 0;
}

header nav.desktop ul li a>span.go-to-link {
  display: none;
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
}

header nav.desktop ul li a {
  color: #444444;
  display: inline-block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  padding: 30px 10px;
  position: relative;
  /* transition:padding 500ms;
	-webkit-transition:padding 500ms;*/
}

header nav.desktop ul li a.opened:after {
  bottom: -4px;
  color: #e5e5e5;
  content: "\f0d8";
  height: 20px;
  font-family: FontAwesome;
  font-size: 30px;
  left: 50%;
  margin-left: -10px;
  position: absolute;
  text-align: center;
  width: 20px;
}

header .links {
  display: none;
  margin-top: -10px;
  position: absolute;
  right: 15px;
  top: 50%;
}

header .links a {
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
}

header .links a.language span.up {
  display: none;
}

header .links a.language.opened span.up {
  display: inline;
}

header .links a.language.opened span.down {
  display: none;
}

header .links a.user span.up {
  display: none;
}

header .links a.user.opened span.up {
  display: inline;
}

header .links a.user.opened span.down {
  display: none;
}

header .links a.bd-sites span.up {
  display: none;
}

header .links a.bd-sites.opened span.up {
  display: inline;
}

header .links a.bd-sites.opened span.down {
  display: none;
}

header .languages {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  border-top: 2px solid #e5e5e5;
  display: none;
  position: absolute;
  right: 15px;
  top: 100%;
  z-index: 2;
}

header .languages .pad {
  padding: 50px 50px 30px 50px;
}

header .languages ul {
  list-style: none;
  margin: 0 0 30px 0;
  min-width: 210px;
  padding: 0;
}

header .languages ul.scroll-list {
  max-height: 235px;
}

header .languages ul.scroll-list.tall {
  max-height: 325px;
}

header .languages ul li a {
  color: #8b8b8b;
  font-size: 12px;
  padding-left: 24px;
  position: relative;
  white-space: nowrap;
}

header .languages ul li a img {
  left: 0;
  position: absolute;
  top: 1px;
}

header .sites {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  border-top: 2px solid #e5e5e5;
  display: none;
  position: absolute;
  right: 15px;
  top: 100%;
  z-index: 2;
}

header .sites .pad {
  padding: 50px 50px 30px 50px;
}

header .sites ul {
  list-style: none;
  margin: 0 50px 30px 0;
  padding: 0;
}

header .sites ul li a {
  color: #8b8b8b;
  font-size: 12px;
  position: relative;
  white-space: nowrap;
}

header .user-options {
  background: #fff;
  border-top: 2px solid #e5e5e5;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  display: none;
  font-size: 15px;
  position: absolute;
  right: 15px;
  top: 100%;
  width: 320px;
  z-index: 2;
}

header .user-options .avatar-wrap {
  background: #f7f7f7;
  padding: 30px 10px;
  text-align: center;
}

header .user-options .avatar-wrap .avatar {
  border-radius: 100%;
  margin: 0 auto 5px;
  overflow: hidden;
  width: 64px;
}

header .user-options ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

header .user-options ul li {
  margin: 0;
}

header .user-options ul li a {
  border-top: 1px solid #e5e5e5;
  color: #444444;
  display: block;
  font-size: 16px;
  padding: 10px;
}

header a.close {
  color: #8b8b8b;
  font-size: 30px;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  position: absolute;
  top: 10px;
  right: 10px;
}

header form.search {
  display: none;
  margin: -18px 0 0 0;
  max-width: 330px;
  overflow: hidden;
  position: absolute;
  right: 15px;
  top: 50%;
  width: 0;
}

header .mobile-toggle {
  display: block;
  font-size: 26px;
  height: 36px;
  line-height: 36px;
  margin-top: -18px;
  position: absolute;
  right: 15px;
  text-align: center;
  top: 50%;
  width: 36px;
}

header .mobile-toggle span.close {
  display: none;
  font-size: 30px;
}

header .mobile-toggle.opened span.close {
  display: block;
}

header .mobile-toggle.opened span.open {
  display: none;
}

header.transparent a.logo:nth-child(1) {
  display: none;
}

header.transparent .links a {
  color: #fff;
}

header.transparent nav.desktop ul li a {
  color: #fff;
}

header.transparent .mobile-toggle {
  color: #fff;
}

header .pillar-text {
  color: #444444;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 16px;
  padding: 20px 10px;
  text-align: center;
}

header .pillar-text a {
  display: inline-block;
  margin: 0 5px;
  position: relative;
}

header .pillar-back {
  color: #8b8b8b;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-size: 60px;
  display: block;
  height: 40px;
  line-height: 32px;
  margin-top: -20px;
  position: absolute;
  right: 15px;
  text-align: center;
  top: 50%;
  width: 40px;
}

#scroll-to-position-1.scroll-to-section li:nth-child(1) a:after {
  background: #00b8b0;
  bottom: 8px;
  content: " ";
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

#scroll-to-position-2.scroll-to-section li:nth-child(2) a:after {
  background: #00b8b0;
  bottom: 8px;
  content: " ";
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

#scroll-to-position-3.scroll-to-section li:nth-child(3) a:after {
  background: #00b8b0;
  bottom: 8px;
  content: " ";
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

#scroll-to-position-4.scroll-to-section li:nth-child(4) a:after {
  background: #00b8b0;
  bottom: 8px;
  content: " ";
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

#scroll-to-position-5.scroll-to-section li:nth-child(5) a:after {
  background: #00b8b0;
  bottom: 8px;
  content: " ";
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

#scroll-to-position-6.scroll-to-section li:nth-child(6) a:after {
  background: #00b8b0;
  bottom: 8px;
  content: " ";
  height: 4px;
  left: 0;
  position: absolute;
  width: 100%;
}

nav.mega-menu {
  background: #f7f7f7;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  border-top: 6px solid #e5e5e5;
  display: none;
  position: absolute;
  top: 100%;
  width: 100%;
  z-index: 2;
}

nav.mega-menu .container-fluid {
  padding-bottom: 0;
  padding-top: 0;
}

nav.mega-menu .tab-nav {
  list-style: none;
  margin: 0 -30px 0 -15px;
  padding: 0;
}

nav.mega-menu .tab-nav li {
  margin: 0;
}

nav.mega-menu .tab-nav li a {
  color: #8b8b8b;
  display: block;
  padding: 25px;
  padding-left: 50px;
  position: relative;
}

nav.mega-menu .tab-nav li a:focus {
  opacity: 1;
}

nav.mega-menu .tab-nav li.on a {
  background: #fff;
  color: #444;
}

nav.mega-menu .tab-nav li a img {
  left: 15px;
  margin-top: -12px;
  position: absolute;
  top: 50%;
}

nav.mega-menu .mega-content {
  background: #fff;
  min-height: 430px;
  margin-right: -15px;
  padding: 30px;
}

nav.mega-menu .mega-content .flat-list {
  list-style: none;
  margin: 0 0 15px;
  padding: 0;
}

nav.mega-menu .mega-content .flat-list>li {
  margin: 0 0 10px;
  padding: 0;
  position: relative;
}

nav.mega-menu .mega-content .flat-list li a {
  color: #444;
}

nav.mega-menu .mega-content .accordion-list {
  list-style: none;
  margin: 0 0 15px;
  padding: 0;
}

nav.mega-menu .mega-content .accordion-list>li {
  margin: 0;
  padding: 0 0 10px 30px;
  position: relative;
}

nav.mega-menu .mega-content .accordion-list li a {
  color: #444;
}

nav.mega-menu .mega-content .accordion-list li a.trigger {
  left: 0;
  height: 25px;
  line-height: 25px;
  position: absolute;
  text-align: center;
  top: -3px;
  width: 25px;
}

nav.mega-menu .mega-content .accordion-list li a.trigger:after {
  content: "\f107";
  font-family: FontAwesome;
}

nav.mega-menu .mega-content .accordion-list li a.trigger.opened:after {
  content: "\f106";
}

nav.mega-menu .mega-content .accordion-list li ul {
  display: none;
  font-size: 12px;
  line-height: 18px;
  list-style: none;
  margin: 0;
  max-height: 200px;
  overflow: hidden;
  padding: 0 0;
}

nav.mega-menu .mega-content .accordion-list li ul li:first-child {
  padding-top: 10px;
}

nav.mega-menu .mega-content .accordion-list li ul li a {
  color: #8b8b8b;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
}

nav.mega-menu .mega-content .accordion-list .browse {
  color: #004593;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
}

nav.mega-menu .featured {
  padding: 30px 15px;
}

nav.mega-menu .featured figure {
  margin: 0 0 15px;
}

nav.mega-menu .featured h4 {
  font-size: 16px;
  line-height: 18px;
  margin: 0 0 5px;
}

nav.mega-menu .featured p {
  font-size: 12px;
  line-height: 18px;
  margin: 0 0 15px;
}

nav.mobile {
  background: #004593;
  border-top: 1px solid #023970;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  display: none;
  left: 0;
  position: absolute;
  top: 100%;
  width: 100%;
  z-index: 2;
}

nav.mobile a {
  color: #fff;
}

nav.mobile ul {
  list-style: none;
  margin: 0 -15px;
  padding: 0;
}

nav.mobile ul li {
  border-bottom: 1px solid #fff;
  margin: 0;
}

nav.mobile ul li strong.label {
  color: #fff;
  display: block;
  font-size: 20px;
  padding: 10px 15px;
}

nav.mobile ul li a {
  display: block;
  padding: 10px 15px;
}

nav.mobile .accordion-list li {
  position: relative;
}

nav.mobile .accordion-list li a.trigger:after {
  content: "\f107";
  font-family: FontAwesome;
  position: absolute;
  right: 15px;
  top: 7px;
}

nav.mobile .accordion-list li a.trigger.opened:after {
  content: "\f106";
}

nav.mobile .accordion-list li>ul {
  display: none;
  margin: 0 0 15px;
}

nav.mobile .accordion-list li>ul li {
  border: none;
  padding: 0;
}

nav.mobile .accordion-list li>ul li a {
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  padding: 5px 15px;
}

nav.mobile .accordion-list li>ul>li>ul {
  margin: 0;
  padding: 0 0 0 15px;
}

nav.mobile ul.links {
  display: block;
  margin: 0 -15px;
  padding: 0;
  position: static;
  right: auto;
  top: auto;
}

nav.mobile ul.links li a {
  color: #fff;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  padding: 10px 15px;
}

nav.mobile ul.links li a i {
  margin-right: 5px;
}

nav.mobile form.search {
  display: block;
  margin: 15px 0;
  max-width: 100%;
  position: static;
  right: auto;
  top: auto;
  width: 100%;
}

nav.mobile .accordion-list .scroll-language {
  display: none;
  height: 200px;
  margin: 0 10px 15px 0;
  overflow: auto;
}

nav.mobile .accordion-list .scroll-language h5 {
  color: #fff;
  margin: 0 15px;
}

nav.mobile .accordion-list .scroll-language ul {
  margin: 0 0 15px;
}

nav.mobile .accordion-list .scroll-language p:last-child {
  margin: 0;
}

nav.mobile .accordion-list .scroll-language ul li {
  border: none;
  padding: 0;
}

nav.mobile .accordion-list .scroll-language ul li a {
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  padding: 5px 15px 5px 45px;
  position: relative;
}

nav.mobile .accordion-list .scroll-language ul li a img {
  left: 20px;
  position: absolute;
  top: 9px;
}

nav.mobile .mCSB_scrollTools .mCSB_dragger .mCSB_dragger_bar {
  background: #fff !important;
}

nav.mobile .mCSB_scrollTools .mCSB_draggerRail {
  background: #8b8b8b;
}

/* Sub
=======================================================================================================*/
nav.sub {
  background: #fff;
  border-bottom: 2px solid #e5e5e5;
  display: none;
  margin-bottom: -2px;
  padding: 30px 0;
  position: relative;
  z-index: 3;
}

nav.sub .label {
  color: #444444;
  float: left;
  font-size: 24px;
  margin: -2px 30px 0 0;
}

nav.sub ul {
  font-size: 16px;
  list-style: none;
  margin: 0;
  padding: 0;
}

nav.sub ul li {
  display: inline;
  margin: 0 20px 0 0;
}

nav.sub ul li.on a {
  color: #444444;
}

nav.sub ul li a {
  color: #8b8b8b;
}

nav.sub a.show-cart {
  position: absolute;
  right: 65px;
  top: 30px;
}

nav.sub a.show-cart .count {
  font-size: 12px;
  position: absolute;
  right: -13px;
  top: -5px;
}

nav.sub a.show-search {
  position: absolute;
  right: 15px;
  top: 30px;
}

nav.sub form.search {
  display: none;
  max-width: 330px;
  overflow: hidden;
  position: absolute;
  right: 15px;
  top: 22px;
  width: 0;
}

nav.sub .mini-cart-wrap {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  border: 1px solid #e5e5e5;
  display: none;
  position: absolute;
  right: 15px;
  top: 80%;
  width: 400px;
}

nav.sub .mini-cart-wrap:before {
  content: "\A";
  border-style: solid;
  border-width: 0 10px 15px 10px;
  border-color: transparent transparent #e5e5e5 transparent;
  position: absolute;
  right: 44px;
  top: -15px;
}

nav.sub .mini-cart-wrap:after {
  content: "\A";
  border-style: solid;
  border-width: 0 10px 15px 10px;
  border-color: transparent transparent #fff transparent;
  position: absolute;
  right: 44px;
  top: -13px;
}

nav.sub .mini-cart {
  padding: 15px 0 0;
}

nav.sub ul.sub-menu {
  margin: -30px 0;
}

nav.sub ul.sub-menu>li {
  display: inline-block;
  margin: 0;
  padding: 30px 10px;
  position: relative;
}

nav.sub ul.sub-menu>li:after {
  background: #004593;
  content: " ";
  bottom: 0;
  display: none;
  height: 2px;
  left: 0;
  position: absolute;
  width: 100%;
}

nav.sub ul.sub-menu>li:hover a {
  color: #004593;
  opacity: 1;
}

nav.sub ul.sub-menu>li:hover:after {
  display: block;
}

nav.sub ul.sub-menu>li:hover .sub-menu-drop {
  display: block;
}

nav.sub ul.sub-menu>li .sub-menu-drop {
  background: #f7f7f7;
  border-top: 2px solid #e5e5e5;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  display: none;
  left: 0;
  position: absolute;
  top: 100%;
  width: 520px;
}

nav.sub ul.sub-menu>li .sub-menu-drop .tab-nav {
  float: left;
  font-size: 14px;
  width: 180px;
}

nav.sub ul.sub-menu>li .sub-menu-drop .tab-nav li {
  display: block;
  margin: 0;
}

nav.sub ul.sub-menu>li .sub-menu-drop .tab-nav li.on {
  background: #fff;
}

nav.sub ul.sub-menu>li .sub-menu-drop .tab-nav li a {
  color: #8b8b8b;
  display: block;
  padding: 15px;
}

nav.sub ul.sub-menu>li .sub-menu-drop .tab-nav li a:hover,
nav.sub ul.sub-menu>li .sub-menu-drop .tab-nav li a:focus {
  opacity: 0.6;
}

nav.sub ul.sub-menu>li .sub-menu-drop .tab-wrap {
  background: #fff;
  float: left;
  width: 340px;
}

nav.sub ul.sub-menu>li .sub-menu-drop .tab-wrap ul li {
  display: block;
  font-size: 14px;
}

nav.sub ul.sub-menu>li .sub-menu-drop .tab-wrap ul li a {
  color: #8b8b8b;
  display: block;
  padding: 10px 15px;
}

nav.sub ul.sub-menu>li .sub-menu-drop .tab-wrap ul li a:hover,
nav.sub ul.sub-menu>li .sub-menu-drop .tab-wrap ul li a:focus {
  opacity: 0.6;
}

nav.sub ul.sub-menu>li .sub-menu-drop.narrow {
  width: auto;
}

nav.sub ul.sub-menu>li .sub-menu-drop.narrow .tab-wrap {
  width: 200px;
}

/* Crumbs
=======================================================================================================*/
nav.crumbs>div>ul:first-child {
  display: none;
}

nav.crumbs.expand ul:first-child {
  display: block;
}

nav.crumbs {
  background: #f7f7f7;
  border-bottom: 2px solid #e5e5e5;
  border-top: 2px solid #e5e5e5;
  font-size: 12px;
  min-height: 40px;
  position: relative;
  z-index: 2;
}

nav.crumbs ul {
  float: left;
  list-style: none;
  margin: 0 20px 0 -15px;
  padding: 0;
}

nav.crumbs ul>li {
  float: left;
  margin: 0;
  max-width: 100%;
  position: relative;
  white-space: nowrap;
}

nav.crumbs ul>li>a {
  color: #8b8b8b;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  line-height: 20px;
  overflow: hidden;
  padding: 8px 20px;
}

nav.crumbs ul>li>a:after {
  background: -moz-linear-gradient(left, rgba(247, 247, 247, 0) 0%, rgba(247, 247, 247, 1) 100%);
  background: -webkit-linear-gradient(left, rgba(247, 247, 247, 0) 0%, rgba(247, 247, 247, 1) 100%);
  background: linear-gradient(to right, rgba(247, 247, 247, 0) 0%, rgba(247, 247, 247, 1) 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f7f7f7', endColorstr='#f7f7f7', GradientType=1);
  content: "";
  display: block;
  height: 30px;
  margin-top: -15px;
  right: 0;
  position: absolute;
  top: 50%;
  width: 20px;
}

nav.crumbs ul li.on {
  padding-right: 20px;
}

nav.crumbs ul li.on a {
  color: #444444;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  overflow: hidden;
  position: relative;
}

nav.crumbs ul li.on a.trigger {
  height: 30px;
  line-height: 30px;
  margin-top: -15px;
  padding: 0;
  position: absolute;
  right: 0;
  top: 50%;
  width: 30px;
}

nav.crumbs ul li.on a.trigger:after {
  background: transparent;
  content: "\f107";
  display: block;
  font-family: FontAwesome;
  text-align: center;
  width: 100%;
}

nav.crumbs ul li.on.opened a.trigger:after {
  content: "\f106";
}

nav.crumbs ul li.on ul {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  border-top: 2px solid #e5e5e5;
  display: none;
  left: 15px;
  max-width: 100%;
  min-width: 220px;
  position: absolute;
  top: 100%;
}

nav.crumbs ul li.on ul li {
  display: block;
  float: none;
  margin: 0;
  white-space: initial;
}

nav.crumbs ul li.on ul li a {
  color: #8b8b8b;
  font-size: 12px;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
  white-space: normal;
}

nav.crumbs ul li.on ul li a:after {
  display: none;
}

nav.crumbs span.pipe {
  background: #e5e5e5;
  display: block;
  float: left;
  height: 45px;
  margin: -5px 10px;
  width: 2px;
  -ms-transform: rotate(-35deg);
  -webkit-transform: rotate(-35deg);
  transform: rotate(-35deg);
}

/* Solution XS Nav
=======================================================================================================*/
nav.solution-indicator {
  background: rgba(0, 184, 176, 0.95);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  left: 0;
  display: none;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 2;
}

nav.solution-indicator .indicator-text {
  color: #fff;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 16px;
}

nav.solution-indicator .indicator-text .indicator-item {
  display: none;
  padding: 20px 50px 20px 65px;
  position: relative;
}

nav.solution-indicator .indicator-text .indicator-item .icon {
  border-radius: 100%;
  left: 15px;
  overflow: hidden;
  position: absolute;
  top: 10px;
}

nav.solution-indicator .section-list {
  background: #fff;
  display: none;
}

nav.solution-indicator .section-list h5 {
  background: #e5e5e5;
  margin: 0;
  padding: 5px 15px;
}

nav.solution-indicator .section-list>a {
  color: #00B8B0;
  display: block;
  padding:
    10px 15px 10px 55px;
  position: relative;
}

nav.solution-indicator .section-list>a .icon {
  border-radius: 100%;
  left: 15px;
  overflow: hidden;
  position: absolute;
  top: 5px;
}

nav.solution-indicator .indicator-toggle {
  color: #fff;
  display: block;
  font-size: 26px;
  height: 36px;
  line-height: 30px;
  position: absolute;
  right: 15px;
  text-align: center;
  top: 12px;
  width: 36px;
}

nav.solution-indicator .indicator-toggle span.close {
  display: none;
  font-size: 30px;
}

nav.solution-indicator .indicator-toggle.opened span.close {
  display: block;
}

nav.solution-indicator .indicator-toggle.opened span.open {
  display: none;
}

/* Scroll-To Nav
=======================================================================================================*/
section.scroll-to-wrap {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  bottom: 0;
  display: none;
  padding: 15px 0;
  position: fixed;
  width: 100%;
  z-index: 2;
}

.scroll-to-wrap .label-text {
  color: #444;
  font-size: 22px;
  float: left;
  margin: 4px 30px 0 0;
}

.scroll-to-wrap .button {
  position: absolute;
  right: 15px;
  top: 15px;
}

.scroll-to-wrap .button-drop {
  position: absolute;
  right: 15px;
  top: 10px;
  width: 200px;
}

.scroll-to-wrap .button-drop>a {
  color: #004593;
  border: 2px solid #004593;
  opacity: 1;
}

.scroll-to-wrap .button-drop>a:after {
  color: #004593;
}

.scroll-to-wrap .button-drop>a.opened {
  background: #004593;
  color: #fff;
}

.scroll-to-wrap .button-drop>a.opened:before {
  display: none;
}

.scroll-to-wrap .button-drop>a.opened:after {
  color: #fff;
}

.scroll-to-wrap .button-drop ul {
  border-top: 1px solid #e5e5e5;
  bottom: 100%;
  text-align: center;
  top: auto;
}

.scroll-to-wrap .button-drop ul li a {
  color: #004593;
  padding: 10px 15px;
}

.scroll-to-wrap .button-drop ul li a:hover,
.scroll-to-wrap .button-drop ul li a:focus {
  background: #004593;
  color: #fff;
}

.scroll-to-wrap .scroll-to-nav a:focus {
  opacity: 1;
}

.scroll-to-wrap .scroll-to-nav a {
  border-bottom: 4px solid #f7f7f7;
  color: #8b8b8b;
  display: inline-block;
  font-size: 13px;
  line-height: 18px;
  margin: 0 25px 0 0;
  padding: 5px 2px;
}

.scroll-to-wrap .scroll-to-nav a.on {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-position-1 .scroll-to-nav a:nth-child(1) {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-position-2 .scroll-to-nav a:nth-child(2) {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-position-3 .scroll-to-nav a:nth-child(3) {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-position-4 .scroll-to-nav a:nth-child(4) {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-position-5 .scroll-to-nav a:nth-child(5) {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-position-6 .scroll-to-nav a:nth-child(6) {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-position-7 .scroll-to-nav a:nth-child(7) {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-position-8 .scroll-to-nav a:nth-child(8) {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-position-9 .scroll-to-nav a:nth-child(9) {
  border-bottom: 4px solid #00b8b0;
  color: #444;
}

#scroll-to-stop {
  display: none !important;
}

/* Timeline
=======================================================================================================*/
#indicate-position-1-1 .indicator-item:nth-child(1) {
  display: block;
}

#indicate-position-1-2 .indicator-item:nth-child(2) {
  display: block;
}

#indicate-position-2-1 .indicator-item:nth-child(3) {
  display: block;
}

#indicate-position-2-2 .indicator-item:nth-child(4) {
  display: block;
}

#indicate-position-3-1 .indicator-item:nth-child(5) {
  display: block;
}

#indicate-position-3-2 .indicator-item:nth-child(6) {
  display: block;
}

#indicate-position-4-1 .indicator-item:nth-child(7) {
  display: block;
}

#indicate-position-4-2 .indicator-item:nth-child(8) {
  display: block;
}

#indicate-position-stop {
  display: none !important;
}

.timeline-section {
  background: #f7f7f7;
  border-top: 2px solid #e5e5e5;
  bottom: 0;
  box-shadow: 0 -3px 5px rgba(0, 0, 0, 0.2);
  left: 0;
  display: none;
  padding: 5px 0 50px;
  position: fixed;
  width: 100%;
  z-index: 2;
}

.timeline-wrap {
  padding: 0 20px;
}

.timeline {
  margin: 0;
  position: relative;
}

.timeline .section-wrap {
  margin: 0 0 15px;
  overflow: hidden;
}

.timeline .sections {
  margin: 0 -6% 15px;
  text-transform: uppercase;
}

.timeline .sections a {
  color: #8b8b8b;
  display: block;
  float: left;
  padding: 5px;
  text-align: center;
  width: 25%;
}

.timeline .sections a:nth-child(1) {
  text-indent: -15px;
}

.timeline .sections a:nth-child(2) {
  text-indent: -15px;
}

.timeline .sections a:nth-child(3) {
  text-indent: 15px;
}

.timeline .sections a:nth-child(4) {
  text-indent: 10px;
}

.timeline .line {
  background: #e5e5e5;
  height: 4px;
  position: relative;
}

.timeline .line .highlight {
  background: #00B8B0;
  display: block;
  height: 4px;
  left: 0;
  position: absolute;
  top: 0;
  transition: width 200ms linear;
  -webkit-transition: width 200ms linear;
}

.timeline .icons a {
  display: block;
  height: 40px;
  line-height: 40px;
  margin: -20px 0 0 -20px;
  opacity: 1;
  position: absolute;
  text-align: center;
  top: 100%;
  width: 40px;
}

.timeline .icons a:nth-child(1) {
  left: 0;
}

.timeline .icons a:nth-child(2) {
  left: 14%;
}

.timeline .icons a:nth-child(3) {
  left: 28%;
}

.timeline .icons a:nth-child(4) {
  left: 42%;
}

.timeline .icons a:nth-child(5) {
  left: 58%;
}

.timeline .icons a:nth-child(6) {
  left: 72%;
}

.timeline .icons a:nth-child(7) {
  left: 86%;
}

.timeline .icons a:nth-child(8) {
  left: 100%;
}

.timeline .icons a .icon {
  background: #e5e5e5;
  border-radius: 100%;
  display: block;
  height: 30px;
  line-height: 28px;
  margin: 3px 5px;
  overflow: hidden;
  width: 30px;
}

.timeline .icons a .icon img {
  max-width: 100%;
  opacity: 0.4;
}

.timeline .icons a .text {
  color: #8b8b8b;
  display: none;
  left: 50%;
  line-height: 18px;
  margin-left: -40px;
  font-size: 12px;
  position: absolute;
  top: 100%;
  white-space: nowrap;
  width: 80px;
}

.timeline .icons a:hover .text,
.timeline .icons a:focus .text {
  display: block;
}

#position-1-1 .line .highlight {
  width: 0;
}

#position-1-1 .sections a:nth-child(1) {
  color: #444444;
}

#position-1-1 .icons a:nth-child(1) .icon {
  background: #00B8B0;
}

#position-1-1 .icons a:nth-child(1) .icon {
  height: 40px;
  margin: -2px 0;
  width: 40px;
}

#position-1-1 .icons a:nth-child(1) .text {
  display: block;
  color: #00B8B0;
}

#position-1-2 .line .highlight {
  width: 14%;
}

#position-1-2 .sections a:nth-child(1) {
  color: #444444;
}

#position-1-2 .icons a:nth-child(1) .icon,
#position-1-2 .icons a:nth-child(2) .icon {
  background: #00B8B0;
}

#position-1-2 .icons a:nth-child(2) .icon {
  height: 40px;
  margin: -2px 0;
  width: 40px;
}

#position-1-2 .icons a:nth-child(2) .text {
  display: block;
  color: #00B8B0;
}

#position-2-1 .line .highlight {
  width: 28%;
}

#position-2-1 .sections a:nth-child(1),
#position-2-1 .sections a:nth-child(2) {
  color: #444444;
}

#position-2-1 .icons a:nth-child(1) .icon,
#position-2-1 .icons a:nth-child(2) .icon,
#position-2-1 .icons a:nth-child(3) .icon {
  background: #00B8B0;
}

#position-2-1 .icons a:nth-child(3) .icon {
  height: 40px;
  margin: -2px 0;
  width: 40px;
}

#position-2-1 .icons a:nth-child(3) .text {
  display: block;
  color: #00B8B0;
}

#position-2-2 .line .highlight {
  width: 42%;
}

#position-2-2 .sections a:nth-child(1),
#position-2-2 .sections a:nth-child(2) {
  color: #444444;
}

#position-2-2 .icons a:nth-child(1) .icon,
#position-2-2 .icons a:nth-child(2) .icon,
#position-2-2 .icons a:nth-child(3) .icon,
#position-2-2 .icons a:nth-child(4) .icon {
  background: #00B8B0;
}

#position-2-2 .icons a:nth-child(4) .icon {
  height: 40px;
  margin: -2px 0;
  width: 40px;
}

#position-2-2 .icons a:nth-child(4) .text {
  display: block;
  color: #00B8B0;
}

#position-3-1 .line .highlight {
  width: 58%;
}

#position-3-1 .sections a:nth-child(1),
#position-3-1 .sections a:nth-child(2),
#position-3-1 .sections a:nth-child(3) {
  color: #444444;
}

#position-3-1 .icons a:nth-child(1) .icon,
#position-3-1 .icons a:nth-child(2) .icon,
#position-3-1 .icons a:nth-child(3) .icon,
#position-3-1 .icons a:nth-child(4) .icon,
#position-3-1 .icons a:nth-child(5) .icon {
  background: #00B8B0;
}

#position-3-1 .icons a:nth-child(5) .icon {
  height: 40px;
  margin: -2px 0;
  width: 40px;
}

#position-3-1 .icons a:nth-child(5) .text {
  display: block;
  color: #00B8B0;
}

#position-3-2 .line .highlight {
  width: 72%;
}

#position-3-2 .sections a:nth-child(1),
#position-3-2 .sections a:nth-child(2),
#position-3-2 .sections a:nth-child(3) {
  color: #444444;
}

#position-3-2 .icons a:nth-child(1) .icon,
#position-3-2 .icons a:nth-child(2) .icon,
#position-3-2 .icons a:nth-child(3) .icon,
#position-3-2 .icons a:nth-child(4) .icon,
#position-3-2 .icons a:nth-child(5) .icon,
#position-3-2 .icons a:nth-child(6) .icon {
  background: #00B8B0;
}

#position-3-2 .icons a:nth-child(6) .icon {
  height: 40px;
  margin: -2px 0;
  width: 40px;
}

#position-3-2 .icons a:nth-child(6) .text {
  display: block;
  color: #00B8B0;
}

#position-4-1 .line .highlight {
  width: 86%;
}

#position-4-1 .sections a:nth-child(1),
#position-4-1 .sections a:nth-child(2),
#position-4-1 .sections a:nth-child(3),
#position-4-1 .sections a:nth-child(4) {
  color: #444444;
}

#position-4-1 .icons a:nth-child(1) .icon,
#position-4-1 .icons a:nth-child(2) .icon,
#position-4-1 .icons a:nth-child(3) .icon,
#position-4-1 .icons a:nth-child(4) .icon,
#position-4-1 .icons a:nth-child(5) .icon,
#position-4-1 .icons a:nth-child(6) .icon,
#position-4-1 .icons a:nth-child(7) .icon {
  background: #00B8B0;
}

#position-4-1 .icons a:nth-child(7) .icon {
  height: 40px;
  margin: -2px 0;
  width: 40px;
}

#position-4-1 .icons a:nth-child(7) .text {
  display: block;
  color: #00B8B0;
}

#position-4-2 .line .highlight {
  width: 100%;
}

#position-4-2 .sections a:nth-child(1),
#position-4-2 .sections a:nth-child(2),
#position-4-2 .sections a:nth-child(3),
#position-4-2 .sections a:nth-child(4) {
  color: #444444;
}

#position-4-2 .icons a:nth-child(1) .icon,
#position-4-2 .icons a:nth-child(2) .icon,
#position-4-2 .icons a:nth-child(3) .icon,
#position-4-2 .icons a:nth-child(4) .icon,
#position-4-2 .icons a:nth-child(5) .icon,
#position-4-2 .icons a:nth-child(6) .icon,
#position-4-2 .icons a:nth-child(7) .icon,
#position-4-2 .icons a:nth-child(8) .icon {
  background: #00B8B0;
}

#position-4-2 .icons a:nth-child(8) .icon {
  height: 40px;
  margin: -2px 0;
  width: 40px;
}

#position-4-2 .icons a:nth-child(8) .text {
  display: block;
  color: #00B8B0;
}

#position-stop {
  display: none !important;
}

/* Main
=======================================================================================================*/
main {
  background: #fff;
  padding: 30px 0;
}

main.slant {
  position: relative;
  z-index: 1;
}

main.slant:after {
  background: inherit;
  bottom: 0;
  content: " ";
  display: block;
  height: 50%;
  left: 0;
  position: absolute;
  right: 0;
  z-index: -1;
  -webkit-backface-visibility: hidden;
  -webkit-transform: skewY(1.3deg);
  transform: skewY(1.3deg);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
}

/* Section
=======================================================================================================*/
section {
  background: #fff;
  padding: 45px 0 30px;
  position: relative;
}

section.border-top {
  border-top: 2px solid #e5e5e5;
}

section.off-white {
  border-top: 2px solid #e5e5e5;
}

section.off-white.no-border {
  border-top: none;
}

section .section-triangle {
  font-size: 100px;
  line-height: 100px;
  margin-bottom: -90px;
  position: relative;
  text-align: center;
  top: -90px;
  z-index: 1;
}

section.callout {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: 0 0 -1px;
  padding: 15px;
}

section.results {
  background: #f7f7f7;
  border-bottom: 2px solid #e5e5e5;
  border-top: 2px solid #e5e5e5;
  padding: 10px 0;
}

section.background-image {
  background-color: inherit;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}

section.background-image .bg-img {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  bottom: 0;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
}

section.background-image .container {
  position: relative;
  z-index: 1;
}

section.background-image .content-box {
  margin: 0 0 15px;
}

section .half-background {
  background-color: inherit;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100%;
  position: absolute;
  top: 0;
  width: 50%;
}

section .half-background.right {
  background-position: left center;
  right: 0;
}

section .half-background.left {
  background-position: right center;
  left: 0;
}

section .center-button {
  left: 50%;
  margin: -50px 0 0 -65px;
  position: absolute;
  top: 50%;
}

section.ordered-accordion {
  background: #f7f7f7;
  border-bottom: 2px solid #e5e5e5;
  border-top: 2px solid #e5e5e5;
  margin-top: -2px;
  text-align: center;
}

section.ordered-accordion .top {
  min-height: 60px;
  padding: 0 0 30px;
  position: relative;
}

section.ordered-accordion .num {
  color: #000;
  display: block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 50px;
  margin: 0 0 10px;
  line-height: 50px;
}

section.ordered-accordion .plus-minus {
  background: #00B8B0;
  border-radius: 100%;
  bottom: 30px;
  color: #fff;
  cursor: pointer;
  display: block;
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-size: 30px;
  height: 30px;
  left: 50%;
  line-height: 26px;
  margin-left: -15px;
  position: absolute;
  text-align: center;
  width: 30px;
}

section.ordered-accordion .plus-minus .minus {
  display: none;
}

section.ordered-accordion .plus-minus.opened .plus {
  display: none;
}

section.ordered-accordion .plus-minus.opened .minus {
  display: inline;
}

.section-modal {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  display: none;
  left: 0;
  position: absolute;
  width: 100%;
  z-index: 2;
}

.section-modal .close-section-modal {
  font-family: "FS Albert Pro Light", Helvetica, Arial, Verdana, sans-serif;
  font-size: 60px;
  line-height: 60px;
  position: absolute;
  right: 0;
  top: -15px;
}

.section-modal .indicator {
  text-shadow: 0 -2px 1px rgba(0, 0, 0, 0.1);
  color: #fff;
  font-size: 30px;
  left: 15px;
  position: absolute;
  transform: scaleX(2);
  top: -20px;
}

/* Home Section
=======================================================================================================*/
section.hero-section {
  background: transparent;
  border-top: none;
  margin-top: -60px;
  padding: 230px 0 200px;
  position: relative;
  z-index: 1;
}

section.hero-section.no-slant:after {
  display: none;
}

section.hero-section.no-slant a.scroll-button {
  bottom: 30px;
}

section.hero-section:after {
  background: #fff;
  bottom: 0;
  content: " ";
  display: block;
  height: 50px;
  left: 0;
  position: absolute;
  right: 0;
  z-index: 0;
  -webkit-backface-visibility: hidden;
  -webkit-transform: skewY(-1.3deg);
  transform: skewY(-1.3deg);
  -webkit-transform-origin: 100% 0;
  transform-origin: 100% 0;
}

section.hero-section h1 {
  font-size: 50px;
}

section.hero-section a.scroll-button {
  background: #004593;
  border: 4px solid #fff;
  border-radius: 100%;
  bottom: 10px;
  color: #fff;
  font-size: 35px;
  line-height: 44px;
  left: 50%;
  height: 50px;
  margin: 0 0 0 -25px;
  position: absolute;
  text-align: center;
  width: 50px;
  z-index: 1;
}

section.hero-section a.scroll-button:hover,
section.hero-section a.scroll-button:focus {
  opacity: 1;
}

section.hero-section img.inline-background {
  display: block;
  margin: 0;
  vertical-align: top;
  width: 100%;
}

section.hero-section .content {
  padding: 60px 0 120px;
}

section.img-section {
  padding: 0;
}

section.img-section img.inline-background {
  display: block;
  margin: 0;
  width: 100%;
}

section.img-section .content {
  padding: 60px 0;
}

/* Section Nav
=======================================================================================================*/
section.section-links {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  overflow: auto;
  padding: 15px 0;
}

.section-links>.container {
  min-width: 850px;
  position: relative;
}

.section-links .button {
  position: absolute;
  right: 15px;
  top: -2px;
}

.section-links .label-text {
  color: #444;
  font-size: 22px;
  float: left;
  margin: 4px 20px 0 0;
}

.section-links .links {
  padding-right: 120px;
}

.section-links .links a:focus {
  opacity: 1;
}

.section-links .links a {
  border-bottom: 4px solid #f7f7f7;
  color: #8b8b8b;
  display: inline-block;
  font-size: 13px;
  line-height: 18px;
  margin: 0 20px 0 0;
  padding: 5px 2px;
  white-space: nowrap;
}

.section-links .links a.on {
  border-bottom: 4px solid #00b8b0;
  display: inline-block;
  color: #444;
}

/* Sub Section
=======================================================================================================*/
section.sub {
  background: #fff;
  border-top: 2px solid #e5e5e5;
  padding: 30px 0 15px;
  position: relative;
  text-align: center;
  z-index: 1;
}

section.sub a {
  color: #444444;
}

section.sub h5 {
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0;
  margin: 0 0 7px;
}

section.sub h5 a {
  color: #444444;
}

section.sub ul {
  list-style: none;
  margin: 0 0 30px;
  padding: 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  -ms-hyphens: auto;
  -moz-hyphens: auto;
  hyphens: auto;
}

section.sub ul li {
  margin: 0 0 5px;
}

section.sub ul li a {
  color: #8b8b8b;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
}

section.sub:after {
  background: inherit;
  bottom: 0;
  content: " ";
  display: block;
  height: 50%;
  left: 0;
  position: absolute;
  right: 0;
  z-index: -1;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: skewY(1.3deg);
  transform: skewY(1.3deg);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
}

/* Global Section
=======================================================================================================*/
section.global {
  border-top: 2px solid #E5E5E5;
  padding: 90px 0;
  min-height: 400px;
}

/* Footer
=======================================================================================================*/
footer {
  color: #fff;
  font-size: 11px;
  line-height: 15px;
  padding: 60px 0 30px;
  position: relative;
  text-align: center;
}

footer a {
  color: #fff;
}

footer a:hover,
footer a:focus {
  color: #fff;
}

footer .logo {
  display: inline-block;
  margin: 0 0 30px;
}

footer .social {
  margin: 0 0 15px;
}

footer .social a {
  display: inline-block;
  margin: 0 15px;
  font-size: 13px;
}

footer .copyright {
  font-size: 10px;
  margin: 0 0 15px;
}

/* Alert
=======================================================================================================*/
#alert {
  background: #00B8B0;
  border-top: 4px solid #fff;
  bottom: 0;
  box-shadow: 0 -3px 5px rgba(0, 0, 0, 0.2);
  color: #fff;
  left: 0;
  padding: 30px 0 0;
  position: fixed;
  width: 100%;
  z-index: 9;
}

#alert .options {
  text-align: center;
}

/* Chat Button
=======================================================================================================*/
#chat {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  overflow: hidden;
  padding-right: 44px;
  position: fixed;
  right: 0;
  top: 30%;
  z-index: 9;
}

#chat .content {
  height: 150px;
  margin-right: -300px;
  transition: margin-right 200ms;
  width: 190px;
}

#chat .content.opened {
  height: auto;
  margin-right: 0;
}

#chat .content h4 {
  margin: 10px;
  text-align: center;
}

#chat .content h4:after {
  background: #e5e5e5;
  content: " ";
  display: block;
  margin: 10px auto;
  height: 1px;
  width: 50px;
}

#chat .content ul {
  list-style: 0;
  margin: 0;
  padding: 0;
}

#chat .content ul li {
  margin: 0;
  padding: 0;
}

#chat .content ul li a {
  color: #6f6c6c;
  display: block;
  padding: 10px 10px 10px 35px;
  position: relative;
}

#chat .content ul li a img {
  left: 12px;
  position: absolute;
  top: 12px;
}

#chat .content ul li a img:nth-child(2) {
  opacity: 0;
}

#chat .content ul li a:hover,
#chat .content ul li a:focus {
  background: #00bce8;
  color: #fff;
  opacity: 1;
}

#chat .content ul li a:hover img:nth-child(2),
#chat .content ul li a:focus img:nth-child(2) {
  opacity: 1;
}

#chat .chat-toggle {
  background: #f7f7f7;
  border-left: 1px solid #e5e5e5;
  display: block;
  height: 100%;
  padding: 10px;
  position: absolute;
  text-align: center;
  top: 0;
  right: 0;
  width: 45px;
}

#chat .chat-toggle .text {
  color: #6f6c6c;
  display: block;
  font-size: 16px;
  height: 100px;
  position: relative;
  text-transform: uppercase;
  top: 15%;
  width: 100px;
  -ms-transform: rotate(-90deg);
  -webkit-transform: rotate(-90deg);
  transform: rotate(-90deg);
}

#chat .chat-toggle .text:after {
  content: "\f106";
  font-family: 'FontAwesome';
}

#chat .chat-toggle.opened .text {
  top: 30%;
}

#chat .chat-toggle.opened .text:after {
  content: "\f107";
}

#chat .chat-toggle:hover,
#chat .chat-toggle:focus {
  opacity: 1;
}

/* Top Button
=======================================================================================================*/
#top {
  background: rgba(0, 0, 0, .3);
  border-radius: 5px;
  bottom: 115px;
  color: #fff;
  display: none;
  font-size: 26px;
  height: 50px;
  line-height: 50px;
  position: fixed;
  right: 5px;
  text-align: center;
  width: 50px;
  z-index: 9;
}

#top.fixed {
  display: block;
}

/* Media Queries
=======================================================================================================*/
/* XS Classes */
@media (min-width:500px) {
  .arrow-list.n3 li {
    margin-right: 5%;
    width: 28.33333%;
  }

  .grid-block .buttons a {
    border-left: 1px solid #e5e5e5;
    float: left;
    width: 30%;
  }

  .grid-block .buttons a:first-child {
    border-left: none;
  }

  .grid-block .buttons a:last-child {
    width: 40%;
  }

  .grid-block .buttons.x2 a {
    border-left: 1px solid #e5e5e5;
    float: left;
    width: 50%;
  }

  .icon-section:after,
  .icon-section:before {
    display: block;
    width: 90px;
  }

  .icon-section:after {
    margin-left: 70px;
    -ms-transform: rotate(20deg);
    -webkit-transform: rotate(20deg);
    transform: rotate(20deg)
  }

  .icon-section:before {
    margin-left: -160px;
    -ms-transform: rotate(-20deg);
    -webkit-transform: rotate(-20deg);
    transform: rotate(-20deg)
  }

  .icon-section .row {
    margin-top: -50px;
  }

  .section-tabs .tab-nav a {
    width: 140px;
  }

  .orders-filter .label-text {
    display: inline-block;
    margin: 0;
    position: relative;
    top: -5px;
  }

  .orders-filter .select-wrap {
    display: inline-block;
    margin: 0 6px;
    min-width: 90px;
  }

  .orders-filter .select-wrap:last-child {
    margin-right: 0;
  }

  .track-orders .side-left {
    float: left;
    width: 50%;
  }

  .track-orders .side-right {
    float: right;
    width: 50%;
  }

  .track-orders .line {
    background: #e5e5e5;
    bottom: 0;
    display: block;
    left: 50%;
    position: absolute;
    top: 55px;
    width: 1px;
  }

  .cart .cart-item {
    padding: 20px 50px 20px 80px;
  }

  .cart .details {
    padding-right: 50px;
  }

  .listing.product-catalog .item {
    padding-right: 160px;
  }

  .listing.product-catalog .item .price {
    position: absolute;
    right: 80px;
    top: 10px;
  }

  .step-slider .slides .slide .row .h1.xl {
    font-size: 50px;
    line-height: 50px;
  }

  .step-slider .slides .slide .big {
    font-size: 14px;
    line-height: 20px;
  }

  .step-slider .slides .slide .times,
  .step-slider .slides .slide .equals {
    font-size: 60px;
    line-height: 60px;
  }

  .step-slider .slides .slide .times {
    left: 0;
  }

  .step-slider .slides .slide .equals {
    right: 0;
  }

  .step-slider-controls ol .flex-active .text {
    opacity: 1;
  }

  .white-popup .sales-support .content:first-child {
    border-bottom: none;
    border-right: 1px solid #e5e5e5;
  }

  .white-popup .sales-support .content {
    float: left;
    width: 50%;
  }

  section.ordered-accordion {
    text-align: left;
  }

  section.ordered-accordion .top {
    padding: 0 0 30px 80px;
  }

  section.ordered-accordion .num {
    left: 0;
    position: absolute;
    top: 0;
  }
}

/* SM Classes */
@media (min-width:768px) {
  .container {
    width: 100%;
  }

  .sm-align-right {
    text-align: right;
  }

  .padding-left-sm {
    padding-left: 30px;
  }

  .padding-right-sm {
    padding-right: 30px;
  }

  .xs-color-visible {
    background: transparent !important;
  }

  .float-img-wrap {
    min-height: 400px;
  }

  .float-img img {
    max-width: 600px;
  }

  .float-img.slide-left {
    margin-right: 15px;
    position: absolute;
    top: 0;
    right: 50%;
  }

  .float-img.side-right {
    left: 50%;
    margin-left: 15px;
    position: absolute;
    top: 0;
  }

  .arrow-list.n3 li {
    margin-right: 3%;
    width: 30.33333%;
  }

  .arrow-list.n4 li {
    margin-right: 3%;
    width: 22%;
  }

  .arrow-list li {
    margin-right: 3%;
    width: 17%;
  }

  .banner .caption {
    bottom: auto;
    margin-top: -57px;
    top: 50%;
    width: 280px;
  }

  .banner .caption h4 {
    margin: 0 0 10px;
  }

  .hero .caption {
    border-left: 1px solid #000;
  }

  .hero .caption {
    bottom: auto;
    left: auto;
    min-height: 250px;
    padding: 60px 20px;
    right: 0;
    width: 340px;
  }

  .banner-img .content {
    color: #fff;
    left: 45px;
    position: absolute;
    top: 45px;
  }

  .banner-img .content h2 {
    color: #fff;
  }

  .table-boxes.five-wide .table-box {
    width: 20%;
  }

  .table-boxes.five-wide .table-box:last-child {
    margin-left: 0;
  }

  .wheel-rotator {
    margin: 0 auto 60px;
    padding-left: 315px;
  }

  .wheel-wrap {
    left: 0;
    position: absolute;
    top: 0;
  }

  .wheel-rotator .wheel-content {
    min-height: 240px;
  }

  .wheel-rotator .line {
    display: block;
  }

  .table-list {
    overflow: hidden;
  }

  .table-list li {
    display: block;
    float: left;
    margin: 0.5%;
    position: relative;
  }

  .table-list li span.label {
    display: table;
    width: 100%;
  }

  .table-list li span.label .label-text {
    display: table-cell;
    vertical-align: middle;
  }

  .table-list li.wide-25 {
    width: 24%;
  }

  .table-list li.wide-50 {
    width: 49%;
  }

  .table-list li.wide-75 {
    width: 74%;
  }

  .table-list li.wide-100 {
    width: 99%;
  }

  .timeline-slider-controls {
    max-width: 738px;
  }

  .timeline-slider-controls .line {
    left: 93px;
    right: 93px;
  }

  .timeline-slider-controls ol li {
    display: inline-block;
    float: none;
    margin: 0 16px;
    width: 100px;
  }

  .timeline-slider-controls ol li .text {
    display: block;
  }

  .timeline-slider-controls.wide-4 ol li {
    margin: 0 40px;
    width: 100px;
  }

  .timeline-slider-controls.wide-3 ol li {
    margin: 0 71px;
    width: 100px;
  }

  .section-slider .flex-direction-nav {
    display: block;
  }

  .section-slider .flex-control-nav {
    display: none;
  }

  .flexslider.hero ul.flex-direction-nav li a {
    height: 40px;
    line-height: 40px;
    right: 340px;
    width: 30px;
  }

  .flexslider.hero ul.flex-direction-nav li a.flex-prev {
    bottom: 40px;
  }

  .full-slider .flex-nav a.flex-prev,
  .full-slider .flex-nav a.flex-next {
    display: block;
  }

  .excellence-slider .caption {
    background: rgba(0, 0, 0, 0.6);
    bottom: 10px;
    color: #fff;
    left: 10px;
    padding-right: 100px;
    position: absolute;
    right: 10px;
  }

  .excellence-slider .caption a {
    color: #fff;
    position: absolute;
    right: 10px;
    top: 10px;
  }

  .career-button {
    display: table;
    position: relative;
    width: 100%;
  }

  .career-button .copy {
    border-bottom: none;
    border-right: 1px solid #e5e5e5;
  }

  .career-button>a {
    display: table-cell;
    vertical-align: middle;
    width: 200px;
  }

  .careers-button {
    padding-right: 350px;
    position: relative;
  }

  .careers-button .copy {
    border-bottom: none;
    border-right: 1px solid #e5e5e5;
    font-size: 20px;
    line-height: 30px;
    min-height: 100px;
  }

  .careers-button .button-wrap {
    position: absolute;
    right: 0;
    top: 0;
    width: 350px;
  }

  .header-search-type {
    overflow: hidden;
    right: 15px;
    width: 660px;
  }

  .header-search-type .side-right {
    border-left: 1px solid #e5e5e5;
    float: right;
    overflow: hidden;
    width: 40%;
  }

  .header-search-type .side-left {
    float: left;
    overflow: hidden;
    width: 60%;
  }

  .support-button {
    padding-right: 250px;
    position: relative;
  }

  .support-button .copy {
    border-bottom: none;
    border-right: 1px solid #e5e5e5;
    font-size: 20px;
    line-height: 30px;
    min-height: 100px;
  }

  .support-button .button-wrap {
    position: absolute;
    right: 0;
    top: 0;
    width: 250px;
  }

  .slide-select-list {
    margin: 0 0 30px;
  }

  .background-tabs .tab-nav a {
    font-size: 18px;
    line-height: 24px;
    padding: 30px 10px;
  }

  .home-tabs .tab-wrap .tab {
    opacity: 0;
    position: absolute;
    top: -10000%;
    z-index: -1;
  }

  .home-tabs .tab-wrap .tab.show-tab {
    opacity: 1;
    position: static;
    top: auto;
    z-index: 0;
  }

  .tab-block .tab-wrap .tab {
    display: none;
    left: 200px;
    height: 100%;
    overflow: auto;
    padding: 30px 10px 30px 30px;
    position: absolute;
    right: 0;
    top: 0;
  }

  .tab-block .tab-wrap .tab:first-child {
    display: block;
  }

  .tab-explore .tab-wrap .tab {
    display: none;
    left: 160px;
    height: 100%;
    overflow: auto;
    padding: 30px 10px 30px 30px;
    position: absolute;
    right: 0;
    top: 0;
  }

  .tab-explore .tab-wrap .tab:first-child {
    display: block;
  }

  .content-tab-nav {
    max-width: 100%;
  }

  .content-tab-nav .toggle {
    display: none;
  }

  .content-tab-nav .toggle:after {
    display: none;
  }

  .content-tab-nav .options {
    background: transparent;
    box-shadow: none;
    border: none;
    display: block !important;
    font-size: 13px;
    height: auto !important;
    left: auto;
    line-height: 18px;
    position: static;
    text-align: center;
    top: auto;
  }

  .content-tab-nav .options a {
    border-bottom: 4px solid rgba(255, 255, 255, 0);
    color: #8b8b8b;
    display: inline-block;
    margin: 0 10px;
    padding: 5px 2px;
    text-align: center;
  }

  .content-tab-nav .options a.on {
    border: none;
    border-bottom: 4px solid #00b8b0;
    color: #444;
  }

  .content-tab-nav .options a:hover,
  .content-tab-nav .options a:focus {
    background: transparent;
    opacity: 0.6;
  }

  .background-tabs .content-tab-nav {
    margin: -15px 0 15px;
  }

  .background-tabs .content-tab-nav .options {
    display: table;
    border-spacing: 10px;
    overflow: visible !important;
  }

  .background-tabs .content-tab-nav .options a {
    background: rgba(256, 256, 256, 0.6);
    border-bottom: none;
    display: table-cell;
    padding: 45px 10px;
    position: relative;
    vertical-align: middle;
    width: 1%;
  }

  .background-tabs .content-tab-nav .options a:hover,
  .background-tabs .content-tab-nav .options a:focus {
    opacity: 1;
  }

  .background-tabs .content-tab-nav .options a.on:before {
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid rgba(256, 256, 256, 0.6);
    content: " ";
    display: block;
    height: 0;
    left: 50%;
    margin-left: -20px;
    position: absolute;
    top: 100%;
    width: 0;
  }

  .filter-columns.wide-3 .filter-column {
    width: 33.333%;
  }

  .filter-columns.wide-3 .filter-column:nth-child(3) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-4 .filter-column {
    width: 25%;
  }

  .filter-columns.wide-4 .filter-column:nth-child(3) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-5 .filter-column {
    width: 20%;
  }

  .filter-columns.wide-5 .filter-column:nth-child(3) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-5 .filter-column:nth-child(5) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-6 .filter-column {
    width: 16.666%;
  }

  .filter-columns.wide-6 .filter-column:nth-child(3) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-6 .filter-column:nth-child(5) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-7 .filter-column {
    width: 25%;
  }

  .filter-columns.wide-7 .filter-column:nth-child(3) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-7 .filter-column:nth-child(5) {
    border-left: none;
  }

  .filter-columns.wide-7 .filter-column:nth-child(7) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-8 .filter-column {
    width: 25%;
  }

  .filter-columns.wide-8 .filter-column:nth-child(3) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-8 .filter-column:nth-child(5) {
    border-left: none;
  }

  .filter-columns.wide-8 .filter-column:nth-child(7) {
    border-left: 1px solid #e5e5e5;
  }

  .hot-spots .hot-spot-content {
    left: 50%;
    margin-left: 30px;
    position: absolute;
    top: 0;
    z-index: 1;
  }

  .page-nav a {
    display: block;
  }

  .home-blocks aside {
    min-height: 440px;
  }

  .cart .cart-item .quantity {
    margin: 8px 0 5px;
  }

  .cart .cart-item .price p {
    margin: 15px 0 5px;
  }

  .cart .cart-item .remove {
    top: 31px;
  }

  .cart.saved .cart-item .add {
    right: 45px;
    top: 27px;
  }

  .checkout .checkout-item .quantity p {
    margin: 15px 0 5px;
  }

  .checkout .checkout-item .price p {
    margin: 15px 0 5px;
  }

  .checkout .details .totals {
    text-align: right;
  }

  .mobile-side-nav-toggle {
    display: none;
  }

  .side-nav {
    display: block;
  }

  #page {
    padding-top: 80px;
    /* transition:padding 500ms;
		-webkit-transition:padding 500ms;
		-webkit-backface-visibility:hidden;
		-webkit-transform:translateZ(0); */
  }

  #page.no-header {
    padding-top: 0 !important;
  }

  #page.scrolled {
    padding-top: 60px;
  }

  #page.scrolled header>.container-fluid {
    height: 60px;
  }

  #page.scrolled header {
    background: #fff;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  }

  #page.scrolled header a.logo:nth-child(1) {
    display: block;
  }

  #page.scrolled header a.logo:nth-child(2) {
    display: none;
  }

  #page.scrolled header nav.desktop ul li a {
    color: #444;
    padding: 20px 10px;
  }

  #page.scrolled header>div>.links a {
    color: #004593;
  }

  #page.scrolled header.transparent .mobile-toggle {
    color: #004593;
  }

  #page.scrolled header .pillar-text {
    padding: 20px 10px;
  }

  header {
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 10;
  }

  header>.container-fluid {
    height: 80px;
  }

  header .pillar-text {
    padding: 30px 20px;
  }

  header .pillar-text a {
    display: none;
  }

  nav.mobile .row>div {
    padding: 5px 30px;
  }

  nav.mobile .row>div:first-child ul li:last-child {
    border-bottom: none;
  }

  nav.mobile .accordion-list li a.trigger {
    right: 0;
  }

  nav.crumbs ul>li {
    max-width: 220px;
  }

  nav.solution-indicator {
    display: none;
  }

  main.slant .padding-left-sm {
    padding-left: 15px;
  }

  main.slant .padding-right-sm {
    padding-right: 15px;
  }

  section.hero-section {
    margin-top: -80px;
  }

  section.hero-section .content {
    left: 0;
    padding: 0;
    position: absolute;
    top: 40%;
    width: 100%;
  }

  section.img-section .content {
    left: 0;
    padding: 0;
    position: absolute;
    top: 40%;
    width: 100%;
  }

  section.img-section .content.tall {
    top: 20%;
  }

  section.scroll-to-wrap {
    display: block;
  }

  .scroll-to-buffer {
    height: 64px;
  }

  section.timeline-section {
    display: block;
  }

  .timeline-buffer {
    height: 106px;
  }

  section.sub {
    text-align: left;
  }
}

/* MD Classes */
@media (min-width:992px) {
  .h1.xl, h1.xl {
    font-size: 60px;
    line-height: 60px;
  }

  .banner-img .content {
    left: 60px;
    top: 60px;
  }

  .browse-block .text-wrap {
    min-height: 115px;
  }

  .background-tabs .tab {
    min-height: 400px;
  }

  .filter-columns.wide-7 .filter-column {
    width: 14.285%;
  }

  .filter-columns.wide-7 .filter-column:nth-child(5) {
    border-left: 1px solid #e5e5e5;
  }

  .filter-columns.wide-8 .filter-column {
    width: 12.5%;
  }

  .filter-columns.wide-8 .filter-column:nth-child(5) {
    border-left: 1px solid #e5e5e5;
  }

  .timeline-slider-wrap>.container {
    padding: 0 100px;
    position: relative;
  }

  .timeline-slider .flex-direction-nav {
    display: block;
  }

  .timeline-slider-controls {
    max-width: 962px;
  }

  .timeline-slider-controls .line {
    left: 145px;
    right: 145px;
  }

  .timeline-slider-controls ol li {
    margin: 0 32px;
  }

  .timeline-slider-controls.wide-4 .line {
    left: 205px;
    right: 205px;
  }

  .timeline-slider-controls.wide-3 .line {
    left: 235px;
    right: 235px;
  }

  .step-slider-wrap>.container {
    padding: 0 100px;
    position: static !important;
  }

  .step-slider .flex-direction-nav {
    display: block;
  }

  .step-slider-controls {
    max-width: 992px;
  }

  .step-slider-controls .line {
    left: 93px;
    right: 93px;
  }

  .step-slider-controls ol li {
    display: inline-block;
    float: none;
    margin: 0 14px;
    width: 100px;
  }

  .step-slider-controls ol li .text {
    display: block;
  }

  header nav.desktop {
    display: block;
  }

  header .links {
    display: block;
  }

  header .mobile-toggle {
    display: none;
  }

  nav.sub {
    display: block;
  }

  nav.sub ul.small li {
    font-size: 12px;
  }

  footer .logo {
    float: left;
    margin-right: 10px;
    margin-top: -3px;
  }

  footer .social {
    margin: 3px 0;
    text-align: left;
  }

  footer .social a {
    font-size: 20px;
    margin: 0 4px;
  }

  footer .links {
    text-align: right;
  }
}

/* LG Classes */
@media (min-width:1200px) {
  .step-slider-controls {
    max-width: 1200px;
  }

  .step-slider-controls ol li {
    margin: 0 34px;
  }

  nav.sub ul.small li {
    font-size: 17px;
  }

  footer .logo {
    margin-right: 15px;
  }

  footer .social a {
    margin: 0 10px;
  }
}

/* XL Classes */
@media (min-width:1400px) {
  .page-nav a {
    top: 340px;
  }

  footer .social {
    margin: 0;
  }

  footer .social a {
    font-size: 25px;
    margin: 0 12px;
  }
}

/* Solution Indicator Height Check */
@media (max-height:500px) {
  nav.solution-indicator {
    display: none !important;
  }
}

/* Image heights */
@media (min-width:768px) {
  .min-height-shim {
    padding: 30px 0;
  }
}

@media (min-width:992px) {
  .min-height-shim {
    padding: 50px 0;
  }
}

@media (min-width:1200px) {
  .min-height-shim {
    padding: 80px 0;
  }
}

@media (min-width:1500px) {
  .min-height-shim {
    padding: 110px 0;
  }
}

@media (min-width:1800px) {
  .min-height-shim {
    padding: 140px 0;
  }
}

/* Baynote/KIBO Styles */
p:not(.uppercase) #baynote div {
  font-size: 12px;
  line-height: 18px;
}

#baynote .rec_title {
  display: none;
}

#baynote .content {
  margin-bottom: 30px;
}

#baynote .thumb {
  max-width: 50px;
  min-height: 50px;
  float: left;
  margin-right: 10px;
}

#baynote .thumb img {
  max-width: 40px;
}

#baynote .rec_head, #baynote .rec_desc, #baynote .rec_size, #baynote .rec_clone {
  margin-left: 50px;
}

#baynote .rec_desc, #baynote .rec_size, #baynote .rec_clone {
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  color: #6f6c6c;
}
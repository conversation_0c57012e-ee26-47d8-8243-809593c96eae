@import url(circle.css);

html {
  height: 100%;
  overflow-y: scroll;
}

body {
  height: 100%;
}

.button.full-width {
  text-align: center;
  width: 100%;
}

.search-type {
  right: 40px;
}

form.search.icon-button {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  padding-right: 40px;
}

form.search.icon-button input[type=text] {
  background: transparent;
  color: #444;
  height: 40px;
}

form.search.icon-button input[type=text]:focus {
  background: #fff;
}

form.search.icon-button button {
  background: #004593;
  height: 100%;
  color: #fff;
  right: 0;
  top: 0;
  width: 40px;
}

.side-nav-toggle h5 {
  margin: 0 0 5px;
}

.side-nav-toggle h5 a:before {
  top: -2px;
}

.notice {
  background: #fef1e6;
  border: 1px solid #f27707;
  border-radius: 3px;
  padding: 10px 90px 10px 60px;
  position: relative;
}

.notice a {
  color: #f27707;
}

.notice h4 {
  margin: 0;
}

.notice .info {
  border-right: 1px solid #f27707;
  bottom: 10px;
  display: block;
  left: 15px;
  position: absolute;
  top: 10px;
  width: 30px;
}

.notice .info a {
  left: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.notice .dismiss {
  border-left: 1px solid #f27707;
  bottom: 10px;
  display: block;
  position: absolute;
  right: 15px;
  text-align: right;
  top: 10px;
  width: 60px;
}

.notice .dismiss a {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.notice.accent-9-style {
  background: #fff;
  border-color: #d10018;
  color: #444444;
}

.notice.accent-9-style .info {
  border-color: #d10018;
}

.notice.accent-9-style .dismiss {
  border-color: #d10018;
}

.notice.accent-9-style a {
  color: #d10018;
}

.avatar-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.avatar-list li {
  display: inline-block;
  margin: 0 10px 10px 0;
}

.avatar-list li a {
  color: #6f6c6c;
  display: inline-block;
  height: 38px;
  position: relative;
  width: 38px;
}

.avatar-list li a:hover,
.avatar-list li a:focus {
  opacity: 1;
}

.avatar-list li a:hover img,
.avatar-list li a:focus img {
  border-color: #004593;
}

.avatar-list li a:hover .tip,
.avatar-list li a:focus .tip {
  display: block;
}

.avatar-list li a img {
  border: 1px solid #e5e5e5;
  border-radius: 100%;
}

.avatar-list li a .tip {
  background: #fff;
  border: 1px solid #004593;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  display: none;
  font-size: 12px;
  left: 0;
  line-height: 14px;
  margin: -18px 0 0 18px;
  padding: 10px 15px;
  position: absolute;
  top: 100%;
  width: 220px;
  z-index: 1;
}

.avatar-list li a .tip .title {
  display: block;
}

.welcome {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 45px 0;
  text-align: center;
}

.welcome .thumb {
  border-radius: 100%;
  margin: 0 0 10px;
}

.welcome h1 {
  margin: 0;
}

.options-dropdown {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  display: none;
  margin: 0;
  padding: 7px 0;
  position: absolute;
  right: 0;
  text-align: left;
  top: 20px;
  width: 180px;
  z-index: 1;
}

.options-dropdown a {
  color: #6f6c6c;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
  line-height: 16px;
  padding: 7px 15px;
}

.options-dropdown a i {
  margin-right: 5px;
}

.card-wrap {
  left: 0;
  perspective: 1000px;
  position: absolute;
  text-align: left;
  top: 0;
  width: 100%;
  z-index: 1;
}

.card-wrap .card {
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
  transform-style: preserve-3d;
  transform-origin: center right;
  transition: transform 200ms;
}

.card-wrap .card.flipped {
  transform: translateX(-100%) rotateY(-180deg);
}

.card-wrap .card.flipped .card-face {
  display: block;
}

.card-wrap .card .card-face {
  backface-visibility: hidden;
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  display: none;
  padding: 15px;
  position: absolute;
  transform: rotateY(180deg);
  width: 100%;
}

.card-wrap .card .card-face .header {
  border-bottom: 1px solid #e5e5e5;
  font-size: 10px;
  margin: -10px -15px 15px;
  padding: 5px 15px;
  text-transform: uppercase;
}

.card-wrap .card .card-face .card-close {
  color: #6f6c6c;
  font-size: 30px;
  position: absolute;
  right: 7px;
  top: 7px;
}

.card-wrap .card .card-face .list ul {
  list-style: none;
  margin: -10px 0 10px;
}

.card-wrap .card .card-face .list ul li {
  border-bottom: 1px solid #e5e5e5;
  margin: 0;
}

.card-wrap .card .card-face .list ul li a {
  color: #6f6c6c;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
  overflow: hidden;
  padding: 8px 0;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-wrap .card .card-face .list ul.icons li a {
  padding-left: 18px;
}

.card-wrap .card .card-face .list ul.icons li a i {
  left: 0;
  position: absolute;
  top: 12px;
}

@media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {
  .card-wrap .card.flipped {
    opacity: 1;
    transform: none;
  }

  .card-wrap .card.flipped .card-face {
    display: block;
  }

  .card-wrap .card .card-face {
    display: none;
    transform: none;
  }
}

.box {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 15px;
  position: relative;
}

.box .header {
  border-bottom: 1px solid #e5e5e5;
  border-radius: 3px 3px 0 0;
  padding: 10px 15px;
  line-height: 20px;
  margin: -15px -15px 15px;
}

.box .header.toggle {
  margin-bottom: 0;
  padding: 0;
}

.box .header.toggle a {
  display: block;
  padding: 10px 15px;
}

.box .header.toggle a:after {
  content: "\f077";
  font-family: FontAwesome;
  float: right;
}

.box .header.toggle a.closed:after {
  content: "\f078";
}

.box .header h5 {
  margin: 0;
}

.box .header .h4 {
  line-height: 20px;
  margin: 0;
}

.box .header .filter {
  float: left;
  max-width: 80px;
  margin: -5px 10px -5px -10px;
}

.box .header .SelectBox {
  padding: 5px 8px;
}

.box .header .button {
  padding: 7px 10px;
  position: absolute;
  right: 5px;
  top: 5px;
}

.box .circle-wrap {
  margin: 0 auto;
}

.box .menu.white-text {
  border-color: #fff;
}

.box .menu {
  border: 1px solid #e5e5e5;
  border-radius: 100%;
  font-size: 18px;
  height: 28px;
  line-height: 15px;
  position: absolute;
  right: 6px;
  text-align: center;
  top: 6px;
  width: 28px;
}

.box .attachment-icon {
  font-size: 18px;
  height: 28px;
  line-height: 28px;
  position: absolute;
  right: 6px;
  text-align: center;
  top: 6px;
  width: 28px;
}

.box .offset {
  margin: -15px;
}

.box>.offset.img-wrap:first-child {
  border-radius: 3px 3px 0 0;
  margin-bottom: 10px;
}

.box .icon {
  border-radius: 100%;
  color: #fff;
  display: block;
  font-size: 20px;
  height: 40px;
  line-height: 40px;
  margin: 0 auto 10px;
  width: 40px;
}

.box .inline-links {
  margin: -5px 0;
  text-align: center;
}

.box .inline-links>span:first-child,
.box .inline-links a {
  display: block;
  font-size: 16px;
  margin: 0;
  padding: 5px;
  position: relative;
  white-space: nowrap;
}

.box .inline-links a.on {
  background: #004593;
  color: #fff;
}

.box .inline-links a.on:after {
  background: #004593;
  bottom: -15px;
  content: " ";
  display: none;
  height: 3px;
  left: 0;
  position: absolute;
  width: 100%;
}

.box .inline-links a .num {
  background: #004593;
  border-radius: 100%;
  color: #fff;
  display: inline-block;
  font-size: 14px;
  text-align: center;
  width: 20px;
}

.box>.link-list {
  list-style: none;
  margin: -15px;
}

.box>.link-list>li {
  margin: 0;
}

.box>.link-list>li:first-child>a {
  border-top: none;
}

.box>.link-list>li>a {
  border-top: 1px solid #e5e5e5;
  display: block;
  padding: 10px 15px;
}

.box .steps {
  line-height: 20px;
  margin: 10px 20px -10px;
  padding: 15px 0;
  position: relative;
}

.box .steps .bar {
  background: #8b8b8b;
  height: 4px;
}

.box .steps .bar .fill {
  background: #00bce8;
  height: 4px;
}

.box .steps .step.on {
  background: #00bce8;
}

.box .steps .step {
  background: #8b8b8b;
  border-radius: 100%;
  display: block;
  height: 20px;
  line-height: 20px;
  margin-left: -10px;
  position: absolute;
  text-align: center;
  top: 6px;
  width: 20px;
}

.box .steps .step:nth-child(1) {
  left: 0;
}

.box .steps .step:nth-child(2) {
  left: 25%;
}

.box .steps .step:nth-child(3) {
  left: 50%;
}

.box .steps .step:nth-child(4) {
  left: 75%;
}

.box .steps .step:nth-child(5) {
  left: 100%;
}

.box .steps .step .text {
  font-size: 12px;
  left: 50%;
  line-height: 12px;
  margin-left: -50px;
  opacity: 0;
  position: absolute;
  text-align: center;
  top: -25px;
  white-space: nowrap;
  width: 100px;
}

.box .steps .step.current .text {
  opacity: 1;
}

.box .docs-accordions {
  margin: -16px -15px -15px;
  padding-bottom: 40px;
}

.box .docs-accordions .accordion>a {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  display: block;
  line-height: 20px;
  overflow: hidden;
  padding: 10px 15px 10px 35px;
}

.box .docs-accordions .accordion>a:hover,
.box .docs-accordions .accordion>a:focus {
  opacity: 1;
}

.box .docs-accordions .accordion>a:before {
  content: "\f107";
  font-family: FontAwesome;
  left: 15px;
  position: absolute;
  top: 10px;
}

.box .docs-accordions .accordion>a.opened:before {
  content: "\f106";
}

.box .docs-accordions .accordion>a.opened {
  background: #004593;
  color: #fff;
}

.box .docs-accordions .accordion>a.opened .meta {
  color: #fff;
}

.box .docs-accordions .accordion>a .meta {
  color: #6f6c6c;
  float: right;
  line-height: 20px;
}

.box .docs-accordions .accordion>a .meta .updated {
  margin-left: 15px;
}

.box .docs-accordions .accordion>ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.box .docs-accordions .accordion>ul li {
  margin: 0;
}

.box .docs-accordions .accordion>ul li a {
  border-bottom: 1px solid #e5e5e5;
  display: block;
  font-size: 13px;
  padding: 10px 15px;
}

.box .docs-accordions .accordion>ul li a i {
  color: #6f6c6c;
  margin-right: 7px;
}

.box .my-install .steps {
  padding: 25px 0 15px;
}

.box .my-install .steps .step {
  top: 16px;
}

.box .my-install .steps .step .text {
  display: table;
  height: 24px;
  top: -30px;
}

.box .my-install .steps .step .text span {
  display: table-cell;
  vertical-align: middle;
}

.box .my-install .steps .step:nth-child(1) {
  left: 0;
}

.box .my-install .steps .step:nth-child(2) {
  left: 11.11111%;
}

.box .my-install .steps .step:nth-child(3) {
  left: 22.22222%;
}

.box .my-install .steps .step:nth-child(4) {
  left: 33.33333%;
}

.box .my-install .steps .step:nth-child(5) {
  left: 44.44444%;
}

.box .my-install .steps .step:nth-child(6) {
  left: 55.55555%;
}

.box .my-install .steps .step:nth-child(7) {
  left: 66.66666%;
}

.box .my-install .steps .step:nth-child(8) {
  left: 77.77777%;
}

.box .my-install .steps .step:nth-child(9) {
  left: 88.88888%;
}

.box .my-install .steps .step:nth-child(10) {
  left: 100%;
}

.box .status {
  border-radius: 3px;
  margin: 0 0 10px;
  overflow: hidden;
  padding: 5px 10px 5px 25px;
  position: relative;
}

.box .status .text {
  position: relative;
  z-index: 1;
}

.box .status .bg {
  display: block;
  height: 100%;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.box .status .bg span {
  background: #fff;
  display: block;
  height: 100%;
  left: 8px;
  opacity: 0.6;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 0;
}

.box .view-all-list {
  list-style: none;
  margin: -15px;
}

.box .view-all-list li {
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 15px;
  margin: 0;
}

.box .view-all-list li.first-of {
  border-bottom: none;
  font-size: 12px;
}

.box .view-all-list li.first-of a {
  float: right;
}

.box .thumb-list {
  list-style: none;
  margin: -15px;
  padding: 0;
}

.box .thumb-list li {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  line-height: 14px;
  margin: 0;
  padding: 15px 15px 15px 65px;
  position: relative;
}

.box .thumb-list li img {
  left: 15px;
  position: absolute;
  top: 11px;
}

.box .listing.border {
  margin: 0 -15px;
}

.box .listing.border .item {
  padding: 0 15px 15px 15px;
}

.box .products-affected {
  border-bottom: 1px solid #e5e5e5;
  font-size: 12px;
  line-height: 16px;
  margin: -15px -15px 15px;
  padding: 15px;
}

.box .products-affected p {
  margin: 0 0 10px;
}

.box .incident-details {
  margin: -15px;
  overflow: hidden;
}

.box .incident-details .details {
  float: left;
  font-size: 12px;
  line-height: 16px;
  padding: 15px;
  width: 50%;
}

.box .incident-details .details:first-child {
  border-right: 1px solid #e5e5e5;
}

.box .incident-details .details p {
  margin: 0 0 10px;
}

.box .community .item {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  margin: -1px -15px;
}

.box .community .item h4 {
  margin: 0;
}

.box .community .item>.meta {
  font-size: 12px;
  line-height: 16px;
  margin: 0 0 5px;
}

.box .community .item .member-wrap {
  font-size: 12px;
  line-height: 16px;
}

.box .community .item .member {
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  margin: 0 0 5px;
  overflow: hidden;
  padding: 5px 5px 5px 55px;
  position: relative;
  width: 200px;
}

.box .community .item .member span {
  display: block;
}

.box .community .item .member img {
  left: 0;
  position: absolute;
  top: 0;
}

.box .community>.pager:first-child {
  margin: -7px 0 7px;
}

.box .community>.pager:last-child {
  margin: 9px 0 -7px 0;
}

.box .blog>.pager:first-child {
  margin: -7px 0 7px;
}

.box .blog>.pager:last-child {
  margin: -7px 0 -7px;
}

.box .search-wrap {
  border-bottom: 1px solid #e5e5e5;
  margin: 0 -15px;
  padding: 0 15px 15px;
}

.box .conversation-wrap {
  border-bottom: 1px solid #e5e5e5;
  margin: 0 -15px;
  padding: 0 15px 15px;
}

.box .conversation {
  padding-right: 130px;
  position: relative;
}

.box .conversation button {
  padding: 12px 10px;
  position: absolute;
  right: 0;
  top: 0;
  width: 120px;
}

.box .comments .comment {
  border-top: 1px solid #e5e5e5;
  font-size: 12px;
  line-height: 16px;
  margin: -1px 0 0 0;
  padding: 15px 0 15px 50px;
  position: relative;
}

.box .comments .comment .thumb {
  left: 0;
  position: absolute;
  top: 17px;
}

.box .comments .comment p {
  margin: 0 0 3px;
}

.box .comments .comment:last-child {
  border-bottom: none;
}

.box .comments .highlight {
  border: 2px solid #004593;
  border-radius: 3px;
  padding: 0 15px;
}

.box .comments .highlight .comment {
  border: none;
}

.box .attachment {
  margin: 0 auto;
  max-width: 200px;
  text-align: center;
}

.box .attachment img {
  border: 1px solid #e5e5e5;
  margin: 0 0 7px;
}

.box .attachment p {
  margin: 0 0 10px;
}

.box .attachment-options {
  margin: 0 auto;
  max-width: 200px;
  text-align: center;
}

.box .attachment-options a:first-child {
  float: left;
}

.box .attachment-options a:last-child {
  float: right;
}

.box .incident-form hr {
  margin: 20px -15px;
}

.box .incident-form>p {
  margin: 0 0 20px;
}

.box .incident-form .small {
  opacity: 0.7;
}

.box .incident-form .overlay-wrap {
  position: relative;
}

.box .incident-form .overlay {
  background: #fff;
  bottom: 1px;
  left: 1px;
  padding: 0;
  position: absolute;
}

.box .incident-form .overlay .icon-wrap {
  background: #d3d3d3;
  color: #fff;
  display: inline-block;
  font-size: 18px;
  margin-right: 7px;
  padding: 7px 10px;
}

.box .incident-form .overlay .selection {
  background: #00b8b0;
  color: #fff;
  display: inline-block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  line-height: 20px;
  padding: 2px 30px 2px 10px;
  position: relative;
  top: -1px;
}

.box .incident-form .overlay .selection:after {
  content: "\00D7";
  font-size: 20px;
  position: absolute;
  right: 7px;
  top: 1px;
}

.box .selections .tags li {
  background: #00b8b0;
  color: #fff;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  margin: 2px 0;
  padding: 5px 30px 5px 10px;
  position: relative;
}

.box .selections .tags li a:after {
  display: none;
}

.box .selections .tags li a {
  background: transparent;
  color: #fff;
  font-size: 20px;
  margin: 0;
  padding: 0;
  position: absolute;
  right: 7px;
  top: 4px;
}

.box .copy h4 {
  margin: 0 0 10px;
}

.box .copy p {
  margin: 0 0 15px;
}

.box .copy hr {
  margin: 0 0 15px;
}

.box .copy .avatar-list {
  font-size: 12px;
  display: table;
  line-height: 16px;
  list-style: none;
  margin: 0 0 15px;
}

.box .copy .avatar-list li {
  display: table-cell;
  padding: 0 10px 0 0;
  vertical-align: middle;
}

.box .meta {
  font-size: 12px;
  line-height: 16px;
}

.box .meta-links {
  font-size: 12px;
  line-height: 16px;
}

.box .content {
  font-size: 12px;
  line-height: 16px;
}

.box .content h4 {
  margin: 0 0 10px;
}

.box .content hr {
  margin: 0 0 15px;
}

.box .content p {
  margin: 0 0 15px;
}

/* .box .content .video{margin:0 0 15px;}
.box .content .video p{margin:0 0 5px;} */
.box .content .avatar {
  display: inline-block;
  margin: 5px 0;
  vertical-align: middle;
}

.box .content .avatar img {
  margin: -2px 3px 0 0;
}

.box .mosaic {
  list-style: none;
  margin: -1%;
  overflow: hidden;
  padding: 0;
}

.box .mosaic li {
  float: left;
  margin: 1%;
  max-width: 60px;
  width: 23%;
}

.box .mosaic li img {
  max-width: 100%;
}

.box .product-img {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  margin: -15px -15px 10px;
  padding: 20px 15px;
  text-align: center;
}

.box .product-img img {
  max-width: 100%;
}

.box .table-wrap {
  border: none;
  border-top: 4px solid #d3d3d3;
  margin: 0 -15px -15px;
}

.box .side-profile .thumb-wrap {
  padding-left: 80px;
  position: relative;
}

.box .side-profile .thumb-wrap .thumb {
  border: 1px solid #e5e5e5;
  left: 0;
  position: absolute;
  top: 2px;
}

.box .side-profile .thumb-wrap .details {
  list-style: none;
  margin: 0 0 20px;
}

.box .side-profile .thumb-wrap .details li {
  margin: 0 0 5px;
}

.box .side-profile hr {
  margin: 0 -15px 20px;
}

.box .side-profile .subjects {
  list-style: none;
  margin: 0 0 10px;
}

.box .side-profile .subjects li:first-child {
  font-size: 12px;
}

.box .side-profile .subjects li:first-child .add {
  margin-left: 10px;
}

.box .side-user-profile {
  text-align: center;
}

.box .side-user-profile .thumb {
  border-radius: 100%;
  margin: 20px 0 10px;
}

.box .side-user-profile .logo {
  border: 1px solid #e5e5e5;
  margin: 0 0 10px;
}

.box .side-user-profile h4 {
  color: #000;
  margin: 0 0 5px;
}

.box .side-user-profile p {
  margin: 0 0 20px;
}

.box .side-user-profile hr {
  margin: 0 0 20px;
}

.box .side-user-profile .links {
  list-style: none;
  margin: 0 0 -10px;
}

.box .side-user-profile .links li {
  margin: -1px 0;
}

.box .side-user-profile .links li a {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  display: block;
  padding: 10px 0;
}

.box .side-user-profile .links li:last-child a {
  border-bottom: none;
}

.box .device-list {
  list-style: none;
  margin: 0 0 -10px;
}

.box .device-list li {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: -1px 0;
  min-height: 60px;
  padding: 15px 0 15px 65px;
  position: relative;
}

.box .device-list li:first-child {
  border-top: none;
  margin-top: -15px;
}

.box .device-list li:last-child {
  border-bottom: none;
}

.box .device-list li img {
  border: 1px solid #e5e5e5;
  left: 0;
  position: absolute;
  top: 9px;
}

.box .security p {
  margin: 0 0 10px;
}

.box .security p:last-child {
  margin: 0;
}

.box .side-message {
  padding: 20px 0;
}

.box .side-message .icon {
  font-size: 40px;
  line-height: 40px;
  margin: 0 0 5px;
  width: 100%;
}

.box .education>div>h4 {
  border-bottom: 1px solid #e5e5e5;
  margin: -15px -15px 15px;
  padding: 10px 15px;
}

.box .education>div {
  margin: 0 0 15px;
}

.box .education .courses ul {
  list-style: none;
  margin: 0;
}

.box .education .courses ul li {
  min-height: 50px;
  padding: 10px 0 10px 60px;
  position: relative;
}

.box .education .courses ul li img {
  left: 0;
  position: absolute;
  top: 5px;
}

.box .education .courses .all {
  margin: 0 -15px;
}

.box .education .badges ul {
  list-style: none;
  margin: 0 -10px;
}

.box .education .badges ul li {
  display: inline-block;
  margin: 0 9px 10px;
}

.box .app-store .app {
  font-size: 12px;
  line-height: 16px;
  margin: 0 -10px;
  text-align: center;
}

.box .app-store .app img {
  border: 1px solid #e5e5e5;
  margin: 0 0 5px;
}

.box .app-store .app p {
  margin: 0 0 15px;
}

.news-events {
  margin: 0 -15px -30px;
}

.news-events .listing {
  padding: 0 15px;
}

.my-install-banner {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 15px;
  position: relative;
}

.my-install-banner h3 {
  margin: 0 0 5px;
}

.my-install-banner p {
  margin: 0 0 20px;
}

.my-install-banner .img-wrap {
  border-radius: 3px 3px 0 0;
  margin: -15px -15px 20px;
}

.my-install-banner .circle-wrap {
  margin: 0 auto;
}

.tasks-view {
  margin: 10px 0;
  text-align: right;
  text-transform: uppercase;
}

.tasks-view>span {
  margin-right: 10px;
}

.tasks-view>.buttons {
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  float: right;
  margin: -6px 0;
  overflow: hidden;
}

.tasks-view>.buttons a {
  background: #fff;
  border-left: 1px solid #e5e5f5;
  border-right: 1px solid #e5e5f5;
  color: #004593;
  float: left;
  margin: 0 -1px;
  padding: 5px 12px;
}

.tasks-view>.buttons a.on {
  background: #004593;
  color: #fff;
}

.tasks-wrap {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  position: relative;
}

.tasks-wrap .header {
  border-bottom: 1px solid #e5e5e5;
  line-height: 20px;
  margin: 0;
  padding: 10px 15px;
}

.tasks-wrap .side .scroll-content .mCSB_container.mCS_no_scrollbar_y.mCS_y_hidden {
  margin-right: 0;
}

.tasks-wrap .side .scroll-content .mCSB_scrollTools {
  right: 3px;
}

.tasks-wrap .side .scroll-content .mCSB_inside>.mCSB_container {
  margin-right: 25px;
}

.tasks-wrap .side .button {
  padding: 7px 20px;
  position: absolute;
  right: 5px;
  top: 5px;
}

.tasks-wrap .side .task-accordions {
  font-size: 14px;
  line-height: 18px;
}

.tasks-wrap .side .task-accordions .accordion {
  border-bottom: 1px solid #e5e5e5;
  margin: 0;
}

.tasks-wrap .side .task-accordions .accordion .handel {
  position: absolute;
  right: 8px;
  top: 12px;
}

.tasks-wrap .side .task-accordions .accordion a.toggle {
  background: #f7f7f7;
  display: block;
  padding: 10px 15px 10px 40px;
}

.tasks-wrap .side .task-accordions .accordion a.toggle:after {
  content: "\f107";
  left: 15px;
  right: auto;
  top: 10px;
}

.tasks-wrap .side .task-accordions .accordion a.toggle:hover,
.tasks-wrap .side .task-accordions .accordion a.toggle:focus {
  opacity: 1;
}

.tasks-wrap .side .task-accordions .accordion a.toggle.closed {
  background: #004593;
  color: #fff;
}

.tasks-wrap .side .task-accordions .accordion a.toggle.closed:after {
  color: #fff;
  content: "\f106";
}

.tasks-wrap .side .task-accordions .accordion a.toggle.closed .meta {
  color: #fff;
}

.tasks-wrap .side .task-accordions .accordion a.handel {
  position: absolute;
  right: 8px;
  top: 12px;
}

.tasks-wrap .side .task-accordions .accordion a.toggle .meta {
  color: #6f6c6c;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
}

.tasks-wrap .side .task-accordions .accordion ul.content {
  display: none;
  list-style: none;
  margin: 0;
  padding: 0;
}

.tasks-wrap .side .task-accordions .accordion ul.content li {
  margin: 0;
}

.tasks-wrap .side .task-accordions .accordion ul.content li {
  border-top: 1px solid #e5e5e5;
  display: block;
  font-size: 12px;
  line-height: 16px;
  padding: 10px 20px;
  position: relative;
}

.tasks-wrap .side .task-accordions .accordion ul.content li a {
  font-size: 14px;
}

.tasks-wrap .side .task-accordions .accordion ul.content li .meta {
  display: block;
}

.tasks-wrap .side .task-accordions .accordion ul.content li .percent {
  position: absolute;
  right: 10px;
  top: 15px;
}

.tasks-wrap .main {
  border-top: 1px solid #e5e5e5;
}

.tasks-wrap .main .header-bar {
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 15px;
  position: relative;
}

.tasks-wrap .main .header-bar h4 {
  margin: 0;
}

.tasks-wrap .main .header-bar .buttons {
  margin: 10px 0 0;
  text-align: right;
}

.tasks-wrap .main .header-bar .buttons .button {
  margin-right: 10px;
  text-align: center;
  vertical-align: middle;
  width: 90px;
}

.tasks-wrap .main .header-bar .buttons .SumoSelect {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
  width: 140px;
}

.tasks-wrap .main .content {
  padding: 15px;
}

.tasks-wrap .main .content p {
  margin: 0 0 15px;
}

.tasks-wrap .main .content hr {
  margin: 15px -15px 30px;
}

.tasks-wrap .main .table-wrap {
  border: none;
  border-top: 1px solid #e5e5e5;
  margin: 0;
}

.milestones {
  list-style: none;
  margin: -10px 0;
  padding: 0 0 0 70px;
}

.milestones li {
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  margin: 24px 0;
  padding: 10px 15px;
  position: relative;
}

.milestones li .text a {
  display: block;
  font-size: 14px;
}

.milestones li .percent {
  background: #004593;
  border-radius: 3px;
  color: #fff;
  height: 52px;
  left: -70px;
  line-height: 52px;
  position: absolute;
  text-align: center;
  top: 0;
  width: 52px;
}

.dashboard {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
}

.dashboard .top {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  padding: 30px 0 0 0;
  position: relative;
}

.dashboard .top .tabs .tab-nav {
  margin-bottom: -1px;
}

.dashboard .top .tabs .tab-nav a {
  padding: 15px 5px;
}

.dashboard .top .tabs .tab-nav a.on {
  color: #004593;
}

.dashboard .top .tabs .tab-nav a.on:after {
  background: #004593;
}

.dashboard .top .side {
  display: none;
}

.dashboard .top .side {
  background: #fff;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  width: 260px;
}

.dashboard .top .side .feed {
  color: #6f6c6c;
  display: block;
  font-size: 18px;
  line-height: 20px;
  margin: 20px 0 0 0;
  text-align: center;
}

.dashboard .main {
  padding: 15px 0 0 0;
  position: relative;
}

.dashboard .main .content {
  margin: 0 auto;
  max-width: 1400px;
  padding: 15px;
}

.dashboard .main .tiles {
  background: #f7f7f7;
  border-top: 1px solid #e5e5e5;
  padding: 15px 20px;
}

.dashboard .main .tiles .tile-wrap {
  margin: 0 auto;
  max-width: 1400px;
  min-height: 300px;
}

.dashboard .main .tiles .tile-wrap>.row>div {
  padding-left: 10px;
  padding-right: 10px;
}

.dashboard .main .tiles .tile {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 10px auto;
  max-width: 400px;
  padding: 15px 15px 10px;
  position: relative;
  text-align: center;
}

.dashboard .main .tiles .tile .menu {
  border: 1px solid #e5e5e5;
  border-radius: 100%;
  font-size: 18px;
  height: 28px;
  line-height: 15px;
  position: absolute;
  right: 10px;
  top: 10px;
  width: 28px;
}

.dashboard .main .tiles .tile .icon {
  border-radius: 100%;
  color: #fff;
  display: block;
  font-size: 20px;
  height: 40px;
  line-height: 40px;
  margin: 0 auto 5px;
  width: 40px;
}

.dashboard .main .tiles .tile .links {
  border-top: 1px solid #e5e5e5;
  display: table;
  margin: 0;
  position: relative;
  width: 100%;
}

.dashboard .main .tiles .tile .links:after {
  background: #e5e5e5;
  content: " ";
  bottom: 5px;
  left: 50%;
  position: absolute;
  top: 5px;
  width: 1px;
}

.dashboard .main .tiles .tile .links a {
  color: #6f6c6c;
  display: table-cell;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
  height: 60px;
  line-height: 16px;
  padding: 5px 7px;
  vertical-align: middle;
  width: 50%;
}

.dashboard .main .tiles .tile .links.full:after {
  display: none;
}

.dashboard .main .tiles .tile .links.full a {
  width: 100%;
}

.dashboard .main .widgets {
  background: #f7f7f7;
  border-top: 1px solid #e5e5e5;
  padding: 25px 20px 5px;
}

.dashboard .main .widgets .widgets-wrap {
  margin: 0 auto;
  max-width: 1400px;
  min-height: 300px;
}

.dashboard .main .widgets .widgets-wrap>.row>div {
  padding-left: 10px;
  padding-right: 10px;
}

.dashboard .main .widgets .widget {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 auto 20px;
  max-width: 400px;
  padding: 15px;
  position: relative;
}

.dashboard .main .widgets .widget .header {
  border-bottom: 1px solid #e5e5e5;
  font-size: 10px;
  margin: -10px -15px 15px;
  padding: 5px 15px;
  text-transform: uppercase;
}

.dashboard .main .widgets .widget .account-rep {
  text-align: center;
}

.dashboard .main .widgets .widget .account-rep .avatar {
  border-radius: 100%;
  height: 40px;
  margin: 0 auto 10px;
  overflow: hidden;
  width: 40px;
}

.dashboard .main .widgets .widget .account-rep h4 {
  margin: 0 0 5px;
}

.dashboard .main .widgets .widget .account-rep p {
  margin: 0 0 15px;
}

.dashboard .main .widgets .widget .account-rep hr {
  margin: 0 -15px 15px;
}

.dashboard .main .widgets .widget .account-rep .button {
  min-width: 100px;
}

.dashboard .main .widgets .widget .list ul {
  list-style: none;
  margin: -10px 0 10px;
}

.dashboard .main .widgets .widget .list ul li {
  border-bottom: 1px solid #e5e5e5;
  margin: 0;
}

.dashboard .main .widgets .widget .list ul li a {
  color: #6f6c6c;
  display: block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
  overflow: hidden;
  padding: 8px 0;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dashboard .main .widgets .widget .list ul.icons li a {
  padding-left: 18px;
}

.dashboard .main .widgets .widget .list ul.icons li a i {
  left: 0;
  position: absolute;
  top: 12px;
}

.dashboard .main .widgets .widget .list ul.tasks li a {
  padding-left: 22px;
}

.dashboard .main .widgets .widget .list ul.tasks li a i {
  font-size: 18px;
  left: 0;
  position: absolute;
  top: 9px;
}

.dashboard .main .widgets .widget .datepicker {
  border-radius: 3px;
  margin: -15px;
  overflow: hidden;
}

.dashboard .main .widgets .widget .datepicker .ui-datepicker {
  border: none;
  box-shadow: none;
}

.dashboard .main .widgets .widget .datepicker .ui-datepicker-header {
  font-size: 12px;
  padding: 8px;
}

.dashboard .main .widgets .widget .datepicker .ui-datepicker table thead th {
  padding: 5px;
  text-align: center;
}

.dashboard .main .widgets .widget .datepicker .ui-datepicker-prev,
.dashboard .main .widgets .widget .datepicker .ui-datepicker-next {
  cursor: pointer;
  font-size: 16px;
  padding: 0 8px;
}

.dashboard .main .widgets .widget .datepicker .ui-datepicker-prev:before,
.dashboard .main .widgets .widget .datepicker .ui-datepicker-next:before {
  font-family: FontAwesome;
}

.dashboard .main .widgets .widget .datepicker .ui-datepicker-prev:before {
  content: "\f0d9";
}

.dashboard .main .widgets .widget .datepicker .ui-datepicker-next:before {
  content: "\f0da";
}

.dashboard .main .widgets .widget .datepicker .ui-datepicker-prev span,
.dashboard .main .widgets .widget .datepicker .ui-datepicker-next span {
  display: none;
}

.dashboard .main .notifications-table {
  margin: 0 0 15px;
}

.dashboard .main .notifications-table .table-row {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: 0 0 -1px;
  padding: 10px 15px;
}

.dashboard .main .notifications-table .table-row p {
  display: inline-block;
  font-size: 12px;
  margin: 0;
  padding: 5px 25px;
  position: relative;
}

.dashboard .main .notifications-table .table-row p.name {
  display: block;
  font-size: 16px;
}

.dashboard .main .notifications-table .table-row p.name .tag {
  border-radius: 5px;
  display: inline;
  margin: 0 5px;
  padding: 2px 5px;
  position: relative;
  top: -2px;
  white-space: nowrap;
}

.dashboard .main .notifications-table .table-row p.name a {
  color: #6f6c6c;
  display: block;
  transition: color .2s ease-in-out;
}

.dashboard .main .notifications-table .table-row p.name a:hover,
.dashboard .main .notifications-table .table-row p.name a:focus {
  opacity: 1;
}

.dashboard .main .notifications-table .table-row p.name a:before,
.dashboard .main .notifications-table .table-row p.name a:after {
  color: #8b8b8b;
  font-size: 24px;
  left: -10px;
  position: absolute;
  top: 5px;
  transition: opacity .2s ease-in-out;
}

.dashboard .main .notifications-table .table-row p.name a:before {
  content: "\f1db";
  font-family: FontAwesome;
}

.dashboard .main .notifications-table .table-row p.name a:after {
  color: #8fcf4f;
  content: "\f058";
  font-family: FontAwesome;
  opacity: 0;
}

.dashboard .main .notifications-table .table-row p.name.checked a {
  color: #b8b8b8;
  text-decoration: line-through;
}

.dashboard .main .notifications-table .table-row p.name.checked a:before {
  opacity: 0;
}

.dashboard .main .notifications-table .table-row p.name.checked a:after {
  opacity: 1;
}

.dashboard .main .notifications-table .table-row p.name.disable a {
  text-decoration: none;
}

.dashboard .main .notifications-table .table-row p.name.disable a:before {
  opacity: 1;
}

.dashboard .main .notifications-table .table-row p.name.disable a:after {
  opacity: 0;
}

.dashboard .main .phases {
  margin: 0 auto 30px;
  max-width: 900px;
  padding: 60px 25px;
}

.dashboard .main .phases .bar {
  background: #b8b8b8;
  height: 2px;
  position: relative;
}

.dashboard .main .phases .bar .line {
  display: block;
  height: 2px;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.dashboard .main .phases .bar .dot {
  background: #004593;
  border-radius: 100%;
  display: block;
  height: 10px;
  left: 0;
  margin-left: -5px;
  position: absolute;
  width: 10px;
  top: -4px;
}

.dashboard .main .phases .phase-wrap {
  position: relative;
}

.dashboard .main .phases .phase-wrap .phase {
  font-size: 12px;
  height: 100px;
  margin: -40px 0 0 -25px;
  left: 0;
  position: absolute;
  text-align: center;
  text-transform: uppercase;
  top: 50%;
  width: 50px;
}

.dashboard .main .phases .phase-wrap .phase span {
  display: block;
  white-space: nowrap;
}

.dashboard .main .phases .phase-wrap .phase .label {
  margin: 0 0 40px;
}

.dashboard .main .phases .phase-wrap .phase .day {
  font-size: 16px;
}

.dashboard .main .phases .phase-wrap .phase:nth-child(1) {
  left: 0;
}

.dashboard .main .phases .phase-wrap .phase:nth-child(2) {
  left: 20%;
}

.dashboard .main .phases .phase-wrap .phase:nth-child(3) {
  left: 40%;
}

.dashboard .main .phases .phase-wrap .phase:nth-child(4) {
  left: 60%;
}

.dashboard .main .phases .phase-wrap .phase:nth-child(5) {
  left: 80%;
}

.dashboard .main .phases .phase-wrap .phase:nth-child(6) {
  left: 100%;
}

.dashboard .main .phases .phase-wrap .phase:before {
  background: #fff;
  color: #8b8b8b;
  font-size: 24px;
  left: 50%;
  margin: -22px 0 0 -10px;
  position: absolute;
  top: 50%;
}

.dashboard .main .phases .phase-wrap .phase:before {
  content: "\f1db";
  font-family: FontAwesome;
}

.dashboard .main .phases .phase-wrap .phase.on:before {
  color: #004593;
}

.dashboard .main .phases .phase-wrap .phase.checked:before {
  color: #8fcf4f;
  content: "\f058";
  font-family: FontAwesome;
}

.dashboard .main .side {
  display: none;
}

.dashboard .main .side {
  background: #fff;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
  height: 100%;
  padding-bottom: 49px;
  position: absolute;
  right: 0;
  top: 0;
  width: 260px;
}

.dashboard .main .side .listing {
  bottom: 50px;
  left: 0;
  max-height: 100%;
  overflow: auto;
  padding: 5px 5px 5px 15px;
  position: absolute;
  right: 0;
  top: 0;
}

.dashboard .main .side .scroll-content.mCS_no_scrollbar {
  padding: 5px 15px;
}

.dashboard .main .side .listing .item {
  border-bottom: 1px solid #e5e5e5;
  margin: 0;
  padding: 10px 0;
}

.dashboard .main .side .listing .item h4 {
  line-height: 20px;
}

.dashboard .main .side .all {
  background: #fff;
  border-top: 1px solid #e5e5e5;
  border-bottom: 4px solid #f7f7f7;
  bottom: 0;
  display: block;
  left: 0;
  padding: 12px 10px;
  position: absolute;
  text-align: center;
  width: 100%;
}

.dashboard .main .side .all:hover,
.dashboard .main .side .all:focus {
  background: #004593;
  border-bottom: 4px solid #023970;
  color: #fff;
  opacity: 1;
}

.dashboard .main .tabs .tab-wrap {
  border: none;
  margin: none;
}

.dashboard .main .tabs .tab-wrap .tab {
  margin: 0;
  min-height: 300px;
  padding: 0;
}

.circle-wrap {
  height: 120px;
  margin: 0 auto 30px;
  max-width: 120px;
}

.circle-wrap .c100>span {
  top: -10px;
}

.circle-wrap .c100>span .fade {
  opacity: 0.5;
}

.circle-wrap .c100>span .label.tall {
  bottom: 18px;
}

.circle-wrap .c100>span .label {
  bottom: 30px;
  display: block;
  font-size: 12px;
  left: 0;
  line-height: 14px;
  position: absolute;
  text-align: center;
  text-transform: uppercase;
  width: 100%;
}

.back-bar {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 15px;
  position: relative;
}

.back-bar .button i {
  margin-right: 5px;
}

.back-bar .actions {
  border-top: 1px solid #e5e5e5;
  line-height: 16px;
  margin: 10px -15px -10px;
  padding: 10px 15px;
  position: relative;
  text-align: right;
}

.back-bar .actions a {
  color: #6f6c6c;
  display: inline-block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 12px;
  margin: 0 7px;
  text-transform: uppercase;
}

.back-bar .actions a i:first-child {
  margin-right: 3px;
}

.back-bar .actions .options-dropdown {
  top: 35px;
}

.back-bar .actions .options-dropdown a {
  margin: 0;
}

.content-library {
  position: relative;
  z-index: 2;
}

.content-library .wrap .main .content {
  background: #f7f7f7;
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  padding: 10px 15px 0;
}

.content-library .wrap .side {
  background: #f7f7f7;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
  display: none;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 260px;
}

.content-library .wrap .side .scroll-content {
  bottom: 0;
  left: 0;
  max-height: 100%;
  overflow: auto;
  padding-right: 5px;
  position: absolute;
  right: 0;
  top: 0;
}

.content-library .wrap .side .scroll-content.mCS_no_scrollbar {
  padding: 0;
}

.content-library .wrap .side .search-wrap {
  padding: 15px;
}

.content-library .wrap .side .tabs .tab-nav {
  margin-bottom: -1px;
  width: 100%;
}

.content-library .wrap .side .tabs .tab-nav a {
  font-size: 12px;
}

.content-library .wrap .side .tabs .tab-nav a.on {
  color: #004593;
}

.content-library .wrap .side .tabs .tab-nav a.on:after {
  background: #004593;
}

.content-library .product-wrap {
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: cover;
  position: relative;
}

.content-library .products {
  margin: 0 auto;
  padding: 30px 15px 15px;
  text-align: center;
}

.content-library .products .product {
  display: inline-block;
  margin: 0 -15px 45px;
  width: 100px;
}

.content-library .products .product .icon {
  border: 1px solid #004593;
  border-radius: 100%;
  display: inline-block;
  height: 60px;
  margin: 0 0 5px;
  overflow: hidden;
  width: 60px;
}

.content-library .products .product .text {
  color: #6f6c6c;
  display: block;
}

.content-library .products .product.active {
  position: relative;
}

.content-library .products .product.active .icon img:first-child {
  display: none;
}

.content-library .products .product.active .text {
  color: #004593;
}

.content-library .products .product.active .triangle {
  border-color: transparent transparent #e5e5e5 transparent;
  border-style: solid;
  border-width: 0 10px 15px 10px;
  bottom: -30px;
  height: 0;
  left: 50%;
  margin-left: -10px;
  position: absolute;
  width: 0;
}

.content-library .products .product.active .triangle:after {
  border-color: transparent transparent #fff transparent;
  border-style: solid;
  border-width: 0 10px 15px 10px;
  content: " ";
  height: 0;
  left: 50%;
  margin-left: -10px;
  position: absolute;
  top: 2px;
  width: 0;
}

.content-library .products form.search {
  margin: 0 auto;
  max-width: 460px;
}

.content-library .products .SumoSelect {
  margin: 15px auto;
  max-width: 160px;
  text-align: left;
}

.content-library .products .SumoSelect>.CaptionCont>span:before {
  content: "\f00a";
  font-family: FontAwesome;
  margin-right: 3px;
}

.content-library .products .button-bar {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: -15px -15px 15px;
  padding: 15px;
}

.content-library .products .button-bar .buttons {
  margin: 0 0 15px;
}

.content-library .products .button-bar .button {
  background: #fff;
  border: 1px solid #d3d3d3;
  border-radius: 3px;
  color: #6f6c6c;
  margin: 5px;
}

.content-library .products .button-bar .button.on {
  background: #004593;
  border: 1px solid #004593;
  color: #fff;
}

.content-library .widgets {
  margin: 0 auto;
  padding: 10px 10px 0;
}

.content-library .widgets .widgets-wrap {
  margin: 0 auto;
  max-width: 1400px;
}

.content-library .widgets .widgets-wrap>.row>div {
  padding-left: 10px;
  padding-right: 10px;
}

.content-library .widgets .widget {
  background: #fff url(../img/stripe.png);
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 auto 20px;
  max-width: 400px;
  position: relative;
}

.content-library .widgets .widget .header {
  border-radius: 3px 3px 0 0;
  color: #fff;
  padding: 20px 15px;
  text-align: center;
}

.content-library .widgets .widget .header .icon {
  font-size: 26px;
  margin: 0 0 5px;
}

.content-library .widgets .widget .header h4 {
  color: #fff;
  margin: 0 0 5px;
}

.content-library .widgets .widget .header p {
  margin: 0 0 10px;
}

.content-library .widgets .widget .item {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 15px;
}

.content-library .widgets .widget .item:last-child {
  border-radius: 0 0 3px 3px;
}

.content-library .widgets .widget .item h4 a {
  color: #6f6c6c;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 16px;
  line-height: 20px;
}

.content-library .widgets .widget .item .links {
  position: relative;
}

.content-library .widgets .widget .item .links:after {
  clear: both;
  content: " ";
  display: table;
}

.content-library .widgets .widget .item .links>a {
  color: #6f6c6c;
  display: block;
  float: left;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 10px;
  line-height: 16px;
  text-transform: uppercase;
  width: 50%;
}

.content-library .widgets .widget .item .links>a:nth-child(2) {
  text-align: right;
}

.content-library .widgets .widget .item .links>a i {
  margin: 0 2px 0 0;
}

.content-library .advanced-list {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 20px 15px 0;
}

.content-library .advanced-list .search-wrap {
  margin: 0 0 20px;
  padding: 0 0 0 40px;
  position: relative;
}

.content-library .advanced-list .search-wrap a.toggle {
  display: block;
  height: 36px;
  left: 0;
  position: absolute;
  top: 0;
  width: 36px;
}

.content-library .advanced-list .search-wrap a.toggle:hover,
.content-library .advanced-list .search-wrap a.toggle:focus {
  opacity: 1;
}

.content-library .advanced-list .search-wrap a.toggle:before,
.content-library .advanced-list .search-wrap a.toggle:after {
  color: #8b8b8b;
  font-size: 24px;
  left: 5px;
  position: absolute;
  top: 8px;
  transition: opacity .2s ease-in-out;
}

.content-library .advanced-list .search-wrap a.toggle:before {
  content: "\f096";
  font-family: FontAwesome;
}

.content-library .advanced-list .search-wrap a.toggle:after {
  color: #004593;
  content: "\f068";
  font-family: FontAwesome;
  font-size: 13px;
  left: 9px;
  opacity: 0;
  top: 8px;
}

.content-library .advanced-list .search-wrap a.toggle.checked:after {
  opacity: 1;
}

.content-library .advanced-list .search-wrap .SumoSelect {
  text-align: left;
}

.content-library .advanced-list .search-wrap .SumoSelect>.CaptionCont>span:before {
  content: "\f03a";
  font-family: FontAwesome;
  margin-right: 3px;
}

.content-library .advanced-list .search-wrap .select-options {
  margin: 10px 0;
}

.content-library .advanced-list .search-wrap .select-options .SumoSelect {
  right: 0;
  top: 0;
}

.content-library .advanced-list .search-wrap .select-options .SumoSelect>.CaptionCont {
  background: #004593;
  border-color: #004593;
  color: #fff;
}

.content-library .advanced-list .search-wrap .select-options .SumoSelect>.CaptionCont>span:before {
  content: "\f045";
}

.content-library .advanced-list .search-wrap .select-options .SumoSelect>.CaptionCont>label>i {
  background-image: url(../img/select-arrow.png);
  opacity: 1;
}

.content-library .advanced-list .list-table .table-row {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: 0 0 -1px;
  padding: 10px 15px;
}

.content-library .advanced-list .list-table .table-row p {
  display: inline-block;
  font-size: 12px;
  margin: 0;
  padding: 5px 25px;
  position: relative;
  vertical-align: top;
}

.content-library .advanced-list .list-table .table-row p.name {
  display: block;
  font-size: 16px;
}

.content-library .advanced-list .list-table .table-row p.name a {
  color: #6f6c6c;
  display: block;
  transition: color .2s ease-in-out;
}

.content-library .advanced-list .list-table .table-row p.name a:hover,
.content-library .advanced-list .list-table .table-row p.name a:focus {
  opacity: 1;
}

.content-library .advanced-list .list-table .table-row p.name a:before,
.content-library .advanced-list .list-table .table-row p.name a:after {
  color: #8b8b8b;
  font-size: 24px;
  left: -10px;
  position: absolute;
  top: 6px;
  transition: opacity .2s ease-in-out;
}

.content-library .advanced-list .list-table .table-row p.name a:before {
  content: "\f096";
  font-family: FontAwesome;
}

.content-library .advanced-list .list-table .table-row p.name a:after {
  color: #004593;
  content: "\f00c";
  font-family: FontAwesome;
  font-size: 20px;
  left: -8px;
  opacity: 0;
  top: 4px;
}

.content-library .advanced-list .list-table .table-row p.name.checked a:after {
  opacity: 1;
}

.content-library .advanced-list .list-table .table-row p.actions a {
  color: #6f6c6c;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  text-transform: uppercase;
}

.content-library .advanced-list .list-table .table-row p.actions a i:first-child {
  margin-right: 3px;
}

.content-library .advanced-list .list-table .table-row p.actions .pipe {
  margin: 0 10px;
}

.content-library .advanced-list .list-table .table-row p.actions .options-dropdown {
  top: 30px;
}

.content-library .detail {
  background: #f7f7f7;
  padding: 30px 15px;
}

.content-library .detail .detail-wrap {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 auto;
  max-width: 960px;
  min-height: 300px;
  padding: 30px 15px;
}

.content-library .detail .detail-wrap>hr {
  margin: 0 -15px 30px;
}

.content-library .detail .detail-wrap section {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: 0 -15px 30px;
}

.content-library .detail .detail-wrap .offset {
  margin: 0 -15px 30px;
}

.content-library .pager {
  background: #fff;
  padding: 10px 15px;
  margin: 0;
}

.knowledge-base {
  background: #fff;
  position: relative;
}

.knowledge-base .back {
  border-bottom: 1px solid #e5e5e5;
  margin: 0 0 -1px;
  padding: 15px;
  text-transform: uppercase;
}

.knowledge-base .tags {
  padding: 15px;
}

.knowledge-base .tags span {
  background: #00b8b0;
  color: #fff;
  display: inline-block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  margin: 2px 0;
  padding: 5px 30px 5px 10px;
  position: relative;
  white-space: nowrap;
}

.knowledge-base .tags a {
  color: #fff;
  font-size: 20px;
  position: absolute;
  right: 7px;
  top: 4px;
}

.knowledge-base .search-list {
  border-bottom: 1px solid #e5e5e5;
}

.knowledge-base .search-list .listing.border {
  margin: 0;
  padding: 0;
}

.knowledge-base .search-list .listing.border .item {
  margin: 0;
  padding: 15px;
}

.knowledge-base .search-list .listing.border .item:last-child {
  border-bottom: none;
  margin: 0;
}

.knowledge-base .search-list .listing.border .item .label-button {
  display: inline-block;
  margin: 0 0 5px;
}

.knowledge-base .search-list .listing.border .item h3 {
  margin: 0 0 5px;
}

.knowledge-base .search-list .listing.border .item p {
  font-size: 14px;
  line-height: 16px;
}

.knowledge-base .search-list .listing.border .item .meta {
  font-size: 12px;
  line-height: 16px;
}

.knowledge-base .detail {
  background: #f7f7f7;
  padding: 30px 15px;
}

.knowledge-base .detail .detail-wrap {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 auto;
  max-width: 960px;
  min-height: 300px;
  padding: 30px 15px;
}

.knowledge-base .detail .detail-wrap>hr {
  margin: 0 -15px 30px;
}

.knowledge-base .detail .detail-wrap section {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: 0 -15px 30px;
}

.knowledge-base .detail .detail-wrap .back-link {
  margin: -15px 0 15px;
  text-transform: uppercase;
}

.knowledge-base .detail .detail-wrap .info-bar {
  background: #f7f7f7;
  border-radius: 3px;
  margin: 0 0 30px;
  overflow: hidden;
  position: relative;
}

.knowledge-base .detail .detail-wrap .info-bar .icon {
  background: #00b8b0;
  display: block;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 50px;
}

.knowledge-base .detail .detail-wrap .info-bar .icon i {
  color: #fff;
  display: block;
  height: 30px;
  font-size: 20px;
  line-height: 30px;
  left: 50%;
  margin: -15px 0 0 -15px;
  position: absolute;
  top: 50%;
  text-align: center;
  width: 30px;
}

.knowledge-base .detail .detail-wrap .info-bar .inner {
  padding: 10px 15px 10px 65px;
}

.knowledge-base .detail .detail-wrap .offset {
  margin: 0 -15px 30px;
}

.knowledge-base .detail .detail-wrap ol li .img-wrap {
  display: block;
  margin: 5px 0;
}

.knowledge-base .detail .detail-wrap pre {
  background: #000;
  color: #fff;
  display: block;
  font-weight: 600;
  margin: 0 0 30px;
  overflow: auto;
  padding: 20px;
  word-wrap: normal;
  white-space: pre;
}

.knowledge-base .landing {
  background: #f7f7f7;
  padding: 30px 15px;
}

.knowledge-base .landing .landing-wrap {
  margin: 0 auto;
  max-width: 1200px;
}

.knowledge-base .landing .landing-wrap .search {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  overflow: hidden;
  padding-right: 50px;
  position: relative;
}

.knowledge-base .landing .landing-wrap .search input[type=text] {
  font-size: 14px;
  height: 50px;
  padding: 15px;
}

.knowledge-base .landing .landing-wrap .search button {
  background: #004593;
  color: #fff;
  height: 50px;
  line-height: 50px;
  right: 0;
  top: 0;
  width: 50px;
}

.knowledge-base .landing .landing-wrap .ticket-bar {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  font-size: 16px;
  margin: 0 0 30px;
  padding: 10px 15px;
  text-align: center;
}

.knowledge-base .landing .landing-wrap .ticket-bar .button {
  margin: 0 10px;
}

.knowledge-base .landing .landing-wrap .featured-list {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  overflow: hidden;
}

.knowledge-base .landing .landing-wrap .featured-list .square {
  color: #fff;
  padding: 30px 15px;
  text-align: center;
}

.knowledge-base .landing .landing-wrap .featured-list .square i {
  font-size: 30px;
  line-height: 30px;
}

.knowledge-base .landing .landing-wrap .featured-list .square h3 {
  color: #fff;
  margin: 0 0 10px;
}

.knowledge-base .landing .landing-wrap .featured-list .square p {
  margin: 0;
}

.knowledge-base .landing .landing-wrap .featured-list .item {
  display: table;
  width: 100%;
}

.knowledge-base .landing .landing-wrap .featured-list .item:nth-child(odd) {
  background: #f7f7f7;
}

.knowledge-base .landing .landing-wrap .featured-list .item a {
  display: table-cell;
  height: 60px;
  padding: 10px;
  vertical-align: middle;
}

.knowledge-base .landing .landing-wrap .featured-list .item.icon {
  display: block;
  min-height: 60px;
  padding: 10px 10px 10px 70px;
  position: relative;
}

.knowledge-base .landing .landing-wrap .featured-list .item.icon>img {
  left: 15px;
  position: absolute;
  top: 10px;
}

.knowledge-base .landing .landing-wrap .featured-list .item.icon a {
  display: block;
  height: auto;
  padding: 0;
}

/* .knowledge-base .landing .landing-wrap .popular-videos .video{
	background:#fff;
	border-radius:3px;
	box-shadow:0 0 5px rgba(0,0,0,0.2);
	margin:0 0 30px;
	overflow:hidden;
}
.knowledge-base .landing .landing-wrap .popular-videos .video p{margin:0;padding:15px;} */
.knowledge-base .landing .landing-wrap .popular-categories .category {
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
}

.knowledge-base .landing .landing-wrap .popular-categories .category a {
  color: #fff;
  display: block;
  font-size: 16px;
  padding: 20px 15px;
  text-align: center;
}

.knowledge-base .landing .landing-wrap .popular-categories .category a i {
  font-size: 24px;
  margin: 0 0 7px;
}

.knowledge-base .landing .landing-wrap .popular-categories .category a .text {
  display: block;
}

.knowledge-base .landing .landing-wrap .community {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  padding: 15px;
  margin: 0 0 30px;
}

.knowledge-base .landing .landing-wrap .community .pager {
  background: transparent;
  padding: 0;
}

.knowledge-base .landing .landing-wrap .community>.header h4 {
  margin: 0;
}

.knowledge-base .landing .landing-wrap .community>.header .pager {
  margin: 10px 0 15px;
}

.knowledge-base .landing .landing-wrap .community>.pager:last-child {
  margin: 15px 0 0;
}

.knowledge-base .landing .landing-wrap .community .item {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  margin: -1px -15px;
}

.knowledge-base .landing .landing-wrap .community .item h4 {
  margin: 0;
}

.knowledge-base .landing .landing-wrap .community .item .meta {
  font-size: 12px;
  line-height: 16px;
  margin: 0 0 5px;
}

.knowledge-base .landing .landing-wrap .community .item .member {
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  font-size: 12px;
  line-height: 16px;
  overflow: hidden;
  padding: 5px 5px 5px 55px;
  position: relative;
  width: 200px;
}

.knowledge-base .landing .landing-wrap .community .item .member span {
  display: block;
}

.knowledge-base .landing .landing-wrap .community .item .member img {
  left: 0;
  position: absolute;
  top: 0;
}

.knowledge-base .wrap .side {
  background: #f7f7f7;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
  display: none;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 260px;
}

.knowledge-base .wrap .side .content {
  font-size: 12px;
  line-height: 16px;
  padding: 15px;
}

.knowledge-base .wrap .side .content h4 {
  margin: 0 0 10px;
}

.knowledge-base .wrap .side .content hr {
  margin: 0 0 15px;
}

.knowledge-base .wrap .side .content p {
  margin: 0 0 15px;
}

/* .knowledge-base .wrap .side .content .video{margin:0 0 15px;}
.knowledge-base .wrap .side .content .video p{margin:0 0 5px;} */
.knowledge-base .wrap .side .scroll-content {
  bottom: 0;
  left: 0;
  max-height: 100%;
  overflow: auto;
  padding-right: 5px;
  position: absolute;
  right: 0;
  top: 0;
}

.knowledge-base .wrap .side .scroll-content.mCS_no_scrollbar {
  padding: 0;
}

.knowledge-base .pager {
  background: #fff;
  padding: 10px 15px;
  margin: 0;
}

.side .filters {
  padding-bottom: 1px;
}

.side .filters hr {
  margin: 15px 0;
}

.side .filters h4 {
  border-bottom: 1px solid #e5e5e5;
  font-size: 12px;
  line-height: 20px;
  margin: 0;
  padding: 7px 15px;
  text-transform: uppercase;
}

.side .filters h4 i:first-child {
  margin-right: 5px;
}

.side .filters h4 i:last-child {
  float: right;
  margin-top: 3px;
}

.side .filters {
  line-height: 18px;
}

.side .filters a {
  color: #6f6c6c;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
}

.side .filters .accordion {
  border-bottom: 1px solid #e5e5e5;
}

.side .filters .accordion .bookmark {
  opacity: 0.3;
  position: absolute;
  right: 15px;
  top: 0;
}

.side .filters .accordion h5 {
  margin: 10px 10px -3px 33px;
  opacity: 0.7;
  text-transform: uppercase;
}

.side .filters .accordion>a {
  color: #000;
  display: block;
  font-family: "FS Albert Pro Bold", Helvetica, Arial, Verdana, sans-serif;
  overflow: hidden;
  padding: 7px 15px 7px 33px;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.side .filters .accordion>a:before {
  content: "\f107";
  font-family: FontAwesome;
  left: 15px;
  position: absolute;
  top: 7px;
}

.side .filters .accordion>a.closed:before {
  content: "\f105";
}

.side .filters .accordion>a .dot {
  border-radius: 100%;
  display: block;
  height: 12px;
  position: absolute;
  right: 10px;
  top: 10px;
  width: 12px;
}

.side .filters .accordion ul.closed {
  display: none;
}

.side .filters .accordion ul {
  list-style: none;
  margin: 0;
  padding: 0 0 10px;
}

.side .filters .accordion ul li {
  margin: 0;
}

.side .filters .accordion ul.checklist li a {
  display: block;
  padding: 5px 15px 5px 33px;
  position: relative;
}

.side .filters .accordion ul.checklist li a:before {
  content: "\f096";
  font-family: FontAwesome;
  left: 15px;
  position: absolute;
  top: 5px;
}

.side .filters .accordion ul.checklist li a.checked:before {
  content: "\f046";
}

.side .filters .accordion ul.bookmark-list li a {
  display: block;
  padding: 5px 15px 5px 33px;
  position: relative;
}

.side .filters .accordion ul.bookmark-list li a:before {
  content: "\f02e";
  font-family: FontAwesome;
  left: 15px;
  position: absolute;
  top: 5px;
}

.side .filters .accordion ul.chapter-list {
  margin-bottom: -10px;
}

.side .filters .accordion ul.chapter-list li a {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  display: block;
  margin: 0 15px -1px 33px;
  overflow: hidden;
  padding: 7px 25px 7px 0;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.side .filters .accordion ul.chapter-list li a:after {
  content: "\f02e";
  font-family: FontAwesome;
  right: 0;
  opacity: 0.3;
  position: absolute;
  top: 7px;
}

.side .filters .accordion ul.link-list {
  margin-bottom: -10px;
}

.side .filters .accordion ul.link-list li a {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  display: block;
  margin: 0 15px -1px 33px;
  overflow: hidden;
  padding: 7px 7px 7px 0;
  position: relative;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 15px;
}

.message .header {
  border-radius: 3px 3px 0 0;
  margin: -15px -15px 15px;
  padding: 10px 15px;
  position: relative;
}

.message .header h5 {
  color: #fff;
  font-size: 14px;
  margin: 0;
}

.message .header .close {
  color: #fff;
  position: absolute;
  right: 15px;
  top: 10px;
}

.message .content {
  padding-right: 105px;
  position: relative;
}

.message .content h4 {
  margin: 0;
}

.message .content p {
  margin: 0 0 10px;
}

.message .content .button {
  position: absolute;
  right: 0;
  top: 28px;
}

.message .content .check-link {
  color: #6f6c6c;
}

.message .content .check-link i {
  margin-right: 3px;
}

.quick-search {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 15px;
}

.quick-search form.search {
  margin: 0 0 5px;
}

.quick-search p {
  margin: 0 0 15px;
}

.quick-search .button {
  display: block;
  text-align: center;
}

.community-help {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 15px;
}

.community-help h4 {
  margin: 0;
}

.community-help p {
  margin: 0 0 10px;
}

.community-help hr {
  margin: 15px -15px;
}

.pager {
  margin: 0;
  padding: 0;
  text-align: center;
}

.pager a {
  padding: 0 6px;
  margin: 0;
}

.pager a.on {
  border: 1px solid #e5e5e5;
  border-radius: 3px;
}

.pager .pages {
  display: inline-block;
  margin: -2px 5px 0;
  position: static;
  right: auto;
  top: auto;
  vertical-align: middle;
  width: auto;
}

.pager .info-text {
  display: block;
  font-size: 12px;
  margin: 10px 0 0;
  white-space: nowrap;
}

.survey {
  text-align: center;
}

.survey .buttons a {
  background: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  color: #6f6c6c;
  display: block;
  font-size: 16px;
  margin: 0 0 10px;
  padding: 10px;
}

.survey .buttons a.on {
  background: #004593;
  border-color: #004593;
  color: #fff;
}

.survey-results {
  font-size: 16px;
}

.survey-results ul {
  list-style: none;
  margin: 0 0 30px;
}

.survey-results ul li {
  margin: 0 0 30px;
}

.survey-results .bar {
  background: #e5e5e5;
  border-radius: 3px;
  display: block;
  height: 10px;
  margin: 20px 0 0;
  position: relative;
  text-align: center;
}

.survey-results .bar .text {
  display: block;
  font-size: 12px;
  line-height: 20px;
  position: relative;
  top: -20px;
}

.survey-results .percent {
  background: #004593;
  border-radius: 3px;
  display: block;
  height: 10px;
  left: 0;
  position: absolute;
  top: 0;
}

.section-slider .flex-direction-nav li a {
  border-width: 2px;
  font-size: 20px;
  height: 30px;
  line-height: 26px;
  width: 30px;
}

.section-slider .flex-control-nav {
  margin: -25px 0 20px;
}

.promo-slider {
  background: #fff;
}

.promo-slider .flex-control-nav {
  border-top: 1px solid #e5e5e5;
  padding: 10px;
  text-align: center;
}

.promo-slider .flex-control-nav li {
  display: inline;
  margin: 0;
  padding: 0;
}

.promo-slider .flex-control-nav li a.flex-active:after {
  background: #004593;
  border-radius: 100%;
  content: " ";
  display: block;
  height: 8px;
  left: 3px;
  position: absolute;
  top: 3px;
  width: 8px;
}

.promo-slider .flex-control-nav li a {
  background: #f7f7f7;
  border: 1px solid #e5e5e5;
  border-radius: 100%;
  display: inline-block;
  height: 16px;
  margin: 0 8px;
  overflow: hidden;
  position: relative;
  text-indent: -999px;
  width: 16px;
}

.promo-slider .flex-control-nav li a:hover,
.promo-slider .flex-control-nav li a:focus {
  opacity: 1;
}

.search-bar {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 10px 15px 5px;
  position: relative;
}

.search-bar .button {
  display: block;
  margin: 0 0 10px;
  text-align: center;
}

.search-bar .SumoSelect {
  margin: 0 0 10px;
}

.search-bar form.search {
  margin: 0 0 10px;
}

.inquiry-bar {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 10px 15px 5px;
  position: relative;
}

.inquiry-bar .button {
  display: block;
  margin: 0 0 10px;
  text-align: center;
}

.inquiry-bar form.search {
  margin: 0 0 10px;
}

.inquiry-bar h4 {
  margin: 0 0 5px;
}

.inquiry-bar .steps {
  padding: 15px 0;
  margin: 0 10px;
  position: relative;
}

.inquiry-bar .steps .bar {
  background: #8b8b8b;
  height: 4px;
}

.inquiry-bar .steps .bar .fill {
  background: #00bce8;
  height: 4px;
}

.inquiry-bar .steps .step.on {
  background: #00bce8;
}

.inquiry-bar .steps .step {
  background: #8b8b8b;
  border-radius: 100%;
  color: #fff;
  display: block;
  height: 20px;
  line-height: 20px;
  margin-left: -10px;
  position: absolute;
  text-align: center;
  top: 6px;
  width: 20px;
}

.inquiry-bar .steps .step:nth-child(1) {
  left: 0;
}

.inquiry-bar .steps .step:nth-child(2) {
  left: 50%;
}

.inquiry-bar .steps .step:nth-child(3) {
  left: 100%;
}

.inquiry-preview {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  min-height: 501px;
  overflow: hidden;
  padding: 10px 15px 50px;
  position: relative;
}

.inquiry-preview .inquiry {
  background: #f7f7f7;
  display: none;
  min-height: 441px;
  margin: 0 -15px;
  overflow: hidden;
  padding: 0 15px;
}

.inquiry-preview .inquiry ul {
  list-style: none;
  margin: 0;
}

.inquiry-preview .inquiry ul li {
  margin: 0;
  padding: 0 0 10px;
}

.inquiry-preview .inquiry .all {
  bottom: 0;
  left: 0;
  position: absolute;
  width: 100%;
}

.inquiry-preview .inquiry .header {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  margin: -10px -15px 15px;
  padding: 10px 15px;
}

.inquiry-text {
  padding: 0 30px;
  position: absolute;
  right: 0;
  text-align: center;
  top: 50%;
  transform: translateY(-50%);
}

.inquiry-entry {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  min-height: 490px;
  padding: 10px 15px;
  position: relative;
}

.inquiry-entry .header {
  border-bottom: 1px solid #e5e5e5;
  margin: -10px -15px 15px;
  padding: 10px 15px;
}

.inquiry-entry .buttons {
  margin: 0 0 10px;
  text-align: right;
}

.inquiry-entry .buttons .button {
  margin-left: 5px;
  padding: 8px 15px;
}

.inquiry-entry .buttons .button.disabled {
  background: rgba(0, 69, 147, 0.3);
}

.inquiry-entry .buttons .button.back {
  background: #fff;
  border: 2px solid #004593;
  color: #004593;
  padding: 6px 15px;
}

.inquiry-entry h5 {
  margin: 0 0 5px;
}

.inquiry-entry .tabs .tab-nav a {
  font-size: 12px;
  padding: 15px;
  width: auto;
}

.inquiry-entry .tabs .tab-nav a.on {
  color: #004593;
}

.inquiry-entry .tabs .tab-nav a.on:after {
  background: #004593;
}

.inquiry-entry .tabs .tab-wrap {
  margin: -1px -15px 0;
  padding: 0 15px;
}

.inquiry-entry .tabs .tab-wrap form.search {
  margin: 0 0 20px;
}

.inquiry-entry .tabs .tab-wrap hr {
  margin: 0 -15px 15px;
}

.inquiry-entry .tabs .tab-wrap .tab {
  margin: 0;
  padding: 20px 0 0;
}

.inquiry-entry .tabs .tab-wrap .tab p {
  margin: 0 0 20px;
}

.inquiry-entry .tabs .tab-wrap .tab .search-list {
  margin: -16px -15px 0;
}

.inquiry-entry .tabs .tab-wrap .tab .search-list .item.selected {
  background: rgba(140, 198, 236, 0.3);
}

.inquiry-entry .tabs .tab-wrap .tab .search-list .item {
  border-bottom: 1px solid #e5e5e5;
  border-top: 1px solid #e5e5e5;
  margin: -1px 0;
  min-height: 72px;
  padding: 15px 15px 15px 80px;
  position: relative;
}

.inquiry-entry .tabs .tab-wrap .tab .search-list .item:last-child {
  border-bottom: none;
}

.inquiry-entry .tabs .tab-wrap .tab .search-list .item img {
  border: 1px solid #e5e5e5;
  left: 15px;
  position: absolute;
  top: 9px;
}

.inquiry-entry form input:disabled {
  background: #f7f7f7;
  border-color: #f7f7f7;
}

.inquiry-confirmation {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 60px 15px 30px;
  position: relative;
}

.inquiry-confirmation .check {
  border-radius: 100%;
  color: #fff;
  display: block;
  font-size: 40px;
  height: 80px;
  line-height: 80px;
  margin: 0 auto 30px;
  width: 80px;
}

.inquiry-confirmation hr {
  margin: 60px -15px;
}

.inquiry-confirmation .large .text {
  display: block;
  vertical-align: middle;
}

.inquiry-confirmation .large .button {
  margin: 5px;
}

.inquiry-confirmation .large .button.back {
  background: #fff;
  border: 2px solid #004593;
  color: #004593;
  padding: 6px 15px;
}

.inquiry-summary {
  background: #f0f0f0;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  overflow: hidden;
  padding: 10px 15px;
  position: relative;
}

.inquiry-summary h4 {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  margin: -10px -15px 15px;
  padding: 10px 15px;
}

.inquiry-summary ul {
  list-style: none;
  margin: 0 0 30px 5px;
}

.quicklookup {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 10px 15px 5px;
  position: relative;
}

.quicklookup h4 {
  margin: 3px 0 10px;
}

.quicklookup form {
  margin: 0 0 5px;
  text-align: right;
}

.quicklookup form input {
  height: 32px;
  padding: 6px 8px;
  margin: 0 0 10px;
}

.quicklookup-details {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 10px 15px 0;
  position: relative;
}

.quicklookup-details p {
  margin: 0 0 15px;
}

.quicklookup-details .back {
  border-bottom: 1px solid #e5e5e5;
  margin: -10px -15px 15px;
  padding: 10px 15px;
  text-transform: uppercase;
}

.quicklookup-details .addresses {
  border-top: 1px solid #e5e5e5;
  margin: 0 -15px 15px;
  overflow: hidden;
}

.quicklookup-details .addresses .address {
  padding: 10px 15px;
}

.quicklookup-details .addresses .address:nth-child(1) {
  border-bottom: 1px solid #e5e5e5;
}

.repair-details {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  padding: 10px 15px 0;
  position: relative;
}

.repair-details p {
  margin: 0 0 15px;
}

.repair-details .back {
  border-bottom: 1px solid #e5e5e5;
  margin: -10px -15px 0;
  padding: 10px 15px;
  text-transform: uppercase;
}

.repair-details .details {
  margin: 0 -15px 15px;
  overflow: hidden;
}

.repair-details .details hr {
  margin: 0 0 15px;
}

.repair-details .details p {
  margin: 0 0 15px;
}

.repair-details .details .button {
  display: block;
  margin: 0 0 15px;
  text-align: center;
}

.repair-details .details .detail {
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 15px;
}

.repair-details .details .history {
  padding: 10px 15px;
}

.roadmap-tiles:after {
  clear: both;
  content: " ";
  display: table;
}

.roadmap-tiles .tile {
  float: left;
  padding: 0 15px;
  width: 100%;
}

.roadmap-tiles .tile a {
  background: #004593;
  color: #fff;
  display: block;
  min-height: 140px;
  margin: 15px 0;
  padding-top: 30px;
  position: relative;
  text-align: center;
}

.roadmap-tiles .tile a.accent-7 .number {
  background: #00a651;
  border-color: #00a651;
  color: #fff;
}

.roadmap-tiles .tile a.accent-2 .number {
  border-color: #00bce8;
}

.roadmap-tiles .tile a .number {
  background: #fff;
  color: #004593;
  border: 2px solid #004593;
  border-radius: 100%;
  left: -10px;
  height: 26px;
  line-height: 22px;
  position: absolute;
  text-align: center;
  top: -10px;
  width: 26px;
}

.roadmap-tiles .tile a .icon-wrap {
  display: block;
  font-size: 30px;
  line-height: 30px;
}

.roadmap-tiles .tile a .title-wrap {
  left: 0;
  margin-top: 20px;
  padding: 15px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
}

.roadmap-tiles .tile a:hover,
.roadmap-tiles .tile a:focus {
  opacity: 1;
}

.roadmap-tiles .tile a:hover .caption,
.roadmap-tiles .tile a:focus .caption {
  display: block;
}

.roadmap-tiles .tile a .caption {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  color: #6f6c6c;
  display: none;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  font-size: 13px;
  left: -10px;
  line-height: 18px;
  padding: 15px;
  position: absolute;
  right: -10px;
  text-align: left;
  top: 25px;
  z-index: 1;
}

.roadmap-tiles .tile a .caption .title {
  display: block;
  font-size: 14px;
  margin: 0 0 5px;
}

.roadmap-list {
  font-size: 16px;
  list-style: none;
  margin: 30px 0;
  padding: 0;
}

.roadmap-list li {
  margin: 0;
}

.roadmap-list li a {
  background: #004593;
  color: #fff;
  display: table;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  height: 70px;
  padding: 15px;
  width: 180px;
}

.roadmap-list li a span {
  display: table-cell;
  vertical-align: middle;
}

.roadmap-list li ul {
  margin: 15px 0 20px 30px;
}

.roadmap-list li ul li {
  display: inline-block;
  margin: 0 10px 10px 0;
}

.roadmap-list li ul li a {
  background: #00bce8;
}

.data-table-wrap {
  background: #fff;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  margin: 0 0 30px;
  position: relative;
}

.data-table-wrap h4 {
  margin: 0;
  padding: 10px 15px;
}

.data-table-wrap .menu {
  border: 1px solid #e5e5e5;
  border-radius: 100%;
  font-size: 18px;
  height: 28px;
  line-height: 15px;
  position: absolute;
  right: 8px;
  text-align: center;
  top: 8px;
  width: 28px;
}

.data-table-wrap .min-width {
  overflow: auto;
}

.data-table-wrap .min-width table.dataTable {
  min-width: 860px;
}

.data-table-wrap .min-width-narrow {
  overflow: auto;
}

.data-table-wrap .min-width-narrow table.dataTable {
  min-width: 480px;
}

.data-table-wrap table.dataTable {
  border-top: 4px solid #d3d3d3;
  width: 100%;
}

.data-table-wrap table.dataTable thead th {
  white-space: nowrap;
}

.data-table-wrap table.dataTable thead .sorting:after {
  font-family: FontAwesome;
  content: "\f0d7";
  margin-left: 5px;
}

.data-table-wrap table.dataTable thead .sorting_asc:after {
  font-family: FontAwesome;
  content: "\f0d7";
  margin-left: 5px;
}

.data-table-wrap table.dataTable thead .sorting_desc:after {
  font-family: FontAwesome;
  content: "\f0d8";
  margin-left: 5px;
}

.data-table-wrap .bottom {
  border-top: 1px solid #d3d3d3;
  padding: 10px 15px;
  position: relative;
  text-align: center;
}

.data-table-wrap .dataTables_paginate .paginate_button {
  color: #8b8b8b;
  cursor: pointer;
  display: inline-block;
  font-family: "FS Albert Pro", Helvetica, Arial, Verdana, sans-serif;
  padding: 0 7px;
  vertical-align: middle;
}

.data-table-wrap .dataTables_paginate .paginate_button.disabled {
  pointer-events: none;
  opacity: 0.5
}

.data-table-wrap .dataTables_paginate .paginate_button.first,
.data-table-wrap .dataTables_paginate .paginate_button.previous,
.data-table-wrap .dataTables_paginate .paginate_button.next,
.data-table-wrap .dataTables_paginate .paginate_button.last {
  overflow: hidden;
  position: relative;
  text-indent: -9999px;
  width: 20px;
}

.data-table-wrap .dataTables_paginate .paginate_button.first:after {
  content: "\f048";
  font-family: FontAwesome;
  float: left;
  text-indent: 0;
}

.data-table-wrap .dataTables_paginate .paginate_button.previous:after {
  content: "\f0d9";
  font-family: FontAwesome;
  float: left;
  text-indent: 0;
}

.data-table-wrap .dataTables_paginate .paginate_button.next:after {
  content: "\f0da";
  font-family: FontAwesome;
  float: left;
  text-indent: 0;
}

.data-table-wrap .dataTables_paginate .paginate_button.last:after {
  content: "\f051";
  font-family: FontAwesome;
  float: left;
  text-indent: 0;
}

.data-table-wrap .dataTables_paginate .paginate_button.current {
  border: 1px solid #e5e5e5;
  border-radius: 3px;
  color: #004593;
}

.data-table-wrap .dataTables_info {
  font-size: 12px;
  margin: 10px 0 0;
}

.white-popup .tasks-wrap {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding-left: 0;
  margin: 0;
}

header a.portal-nav-toggle {
  display: none;
  color: #fff;
  margin-top: -10px;
  position: absolute;
  left: 120px;
  top: 50%;
}

header a.portal-nav-toggle span.up {
  display: none;
}

header a.portal-nav-toggle.opened span.up {
  display: inline;
}

header a.portal-nav-toggle.opened span.down {
  display: none;
}

header .links a.notices {
  padding: 0 7px;
  position: relative;
}

header .links a.notices .num {
  background: #c9050f;
  border-radius: 100%;
  color: #fff;
  display: inline-block;
  font-size: 11px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  position: absolute;
  right: -7px;
  top: -7px;
  width: 16px;
}

header .portal-nav {
  background: #fff;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
  display: none;
  padding: 15px 0;
}

header .portal-nav ul {
  display: table;
  list-style: none;
  margin: 0 auto;
  max-width: 1400px;
  padding: 0;
  width: 100%;
}

header .portal-nav ul li {
  display: table-cell;
  margin: 0;
  text-align: center;
  width: 1%;
}

header .portal-nav ul li a {
  color: #6f6c6c;
  display: block;
}

header .portal-nav ul li a span {
  display: block;
}

nav.mobile ul.accordion-list li>a>i {
  margin-right: 5px;
}

.side-filters-wrap {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 10px 0;
  text-align: right;
  text-transform: uppercase;
}

.side-filters-wrap i {
  margin-left: 5px;
}

.behind {
  left: 0;
  min-height: 100%;
  opacity: 0.4;
  overflow: hidden;
  position: fixed;
  top: 0;
  width: 100%;
}

#side-filters {
  background: #fff;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  display: none;
  float: right;
  right: -100%;
  max-width: 360px;
  min-height: 100%;
  overflow: hidden;
  position: relative;
  width: 100%;
  z-index: 10;
}

#side-filters .close {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  font-size: 18px;
  padding: 7px 10px;
  text-align: right;
}

#side-filters .side .search-wrap {
  padding: 15px;
}

#side-filters .side .tabs .tab-nav {
  margin-bottom: -1px;
  width: 100%;
}

#side-filters .side .tabs .tab-nav a {
  font-size: 12px;
}

#side-filters .side .tabs .tab-nav a.on {
  color: #004593;
}

#side-filters .side .tabs .tab-nav a.on:after {
  background: #004593;
}

#side-filters .side .apply {
  margin: 15px;
}

#side-filters .side .apply .button {
  color: #fff;
  display: block;
  text-align: center;
  text-transform: uppercase;
  width: 100%;
}

#side-filters .side .content {
  font-size: 12px;
  line-height: 16px;
  padding: 15px;
}

#side-filters .side .content h4 {
  margin: 0 0 10px;
}

#side-filters .side .content hr {
  margin: 0 0 15px;
}

#side-filters .side .content p {
  margin: 0 0 15px;
}

/* #side-filters .side .content .video{margin:0 0 15px;}
#side-filters .side .content .video p{margin:0 0 5px;} */
#side-filters .side .blog-side {
  padding: 0 15px 15px;
}

#side-filters .side .blog-side h4 {
  margin: 0 0 10px;
}

/* Media Queries
=======================================================================================================*/
/* XS Classes */
@media (min-width:580px) {
  .visible-xxs {
    display: none;
  }

  .box .inline-links {
    text-align: left;
  }

  .box .inline-links>span:first-child,
  .box .inline-links a {
    display: inline-block;
    margin: 5px 30px 5px 0;
    padding: 0;
  }

  .box .inline-links a.on {
    background: transparent;
    color: #004593;
  }

  .box .inline-links a.on:after {
    display: block;
  }

  .box .steps .step .text {
    opacity: 1;
  }

  .box .my-install .steps .step .text {
    opacity: 0;
  }

  .box .my-install .steps .step.current .text {
    opacity: 1;
  }

  .box .community .item {
    min-height: 95px;
    padding-right: 235px;
    position: relative;
  }

  .box .community .item .member-wrap {
    position: absolute;
    right: 15px;
    top: 15px;
  }

  .box .header.latest {
    margin-bottom: 0;
  }

  .box .community>.pager:first-child {
    margin: 0;
    position: absolute;
    right: 15px;
    text-align: right;
    top: 9px;
  }

  .box .community>.pager:last-child {
    text-align: right;
  }

  .box .blog>.pager:first-child {
    margin: -47px 0 6px;
    text-align: right;
  }

  .box .blog>.pager:last-child {
    text-align: right;
  }

  .tasks-wrap .main .header-bar .buttons {
    margin: 0;
    position: absolute;
    right: 5px;
    top: 4px;
    width: 250px;
  }

  .dashboard .top {
    padding: 30px 260px 0 15px;
  }

  .dashboard .top .side {
    display: block;
  }

  .dashboard .main {
    padding: 15px 260px 0 0;
  }

  .dashboard .main .side {
    display: block;
  }

  .back-bar {
    padding-left: 277px;
  }

  .back-bar .button {
    left: 15px;
    position: absolute;
    top: 10px;
  }

  .back-bar .actions {
    border-top: none;
    margin: 0 auto;
    max-width: 960px;
    padding: 10px 0;
    text-align: center;
  }

  .back-bar .actions:after {
    clear: both;
    content: " ";
    display: table;
  }

  .back-bar .actions>a {
    margin: 0;
  }

  .back-bar .actions>a:nth-child(1) {
    float: left;
  }

  .back-bar .actions>a:nth-child(3) {
    float: right;
  }

  .content-library .advanced-list .search-wrap .search {
    max-width: 180px;
  }

  .content-library .advanced-list .search-wrap .select-options {
    left: 230px;
    margin: 0;
    position: absolute;
    top: 0;
  }

  .content-library .advanced-list .search-wrap .select-type {
    margin: 0;
    position: absolute;
    right: 0;
    top: 0;
  }

  .inquiry-bar .button {
    display: inline-block;
    margin: 0 0 5px;
  }

  .inquiry-bar form.search {
    max-width: 360px;
    position: absolute;
    right: 15px;
    top: 10px;
  }

  .inquiry-bar .steps-wrap {
    max-width: 680px;
    padding-left: 200px;
    position: absolute;
    right: 15px;
    top: 7px;
    width: 100%;
  }

  .inquiry-entry .buttons {
    position: absolute;
    right: 8px;
    top: 6px;
  }

  .knowledge-base .landing .landing-wrap .featured-list {
    padding-left: 200px;
    position: relative;
  }

  .knowledge-base .landing .landing-wrap .featured-list .square {
    bottom: 0;
    height: 100%;
    left: 0;
    min-height: 180px;
    position: absolute;
    top: 0;
    width: 200px;
  }

  .knowledge-base .landing .landing-wrap .featured-list .square .content {
    position: relative;
    top: 50%;
    transform: translateY(-50%);
  }

  .knowledge-base .landing .landing-wrap .community>.header h4 {
    float: left;
    margin: 0;
  }

  .knowledge-base .landing .landing-wrap .community>.header .pager {
    margin: 0 0 15px;
    text-align: right;
  }

  .knowledge-base .landing .landing-wrap .community>.pager:last-child {
    text-align: right;
  }

  .quicklookup form input {
    display: inline-block;
    margin: 0 10px 0 0;
  }

  .quicklookup form input:nth-child(1) {
    width: 220px;
  }

  .quicklookup form input:nth-child(2) {
    width: 140px;
  }

  .quicklookup-details .addresses .address {
    float: left;
    width: 50%;
  }

  .quicklookup-details .addresses .address:nth-child(1) {
    border-bottom: none;
    border-right: 1px solid #e5e5e5;
  }

  .repair-details .details .detail {
    border-bottom: none;
    border-right: 1px solid #e5e5e5;
    float: left;
    width: 60%;
  }

  .repair-details .details .history {
    float: left;
    width: 40%;
  }

  .roadmap-tiles .tile {
    width: 50%;
  }

  .pager {
    text-align: left;
  }

  .pager .info-text {
    display: inline-block;
    float: right;
    margin: 2px 0 0;
  }

  .data-table-wrap .bottom {
    text-align: left;
  }

  .data-table-wrap .dataTables_info {
    bottom: 10px;
    margin: 0;
    position: absolute;
    right: 15px;
  }
}

/* SM Classes */
@media (min-width:768px) {
  .box .education {
    margin: -15px;
    overflow: hidden;
  }

  .box .education>div {
    margin: 0;
  }

  .box .education>div>h4 {
    margin: 0;
  }

  .box .education .courses {
    border-right: 1px solid #e5e5e5;
    margin: ;
    float: left;
    width: 50%;
  }

  .box .education .courses ul {
    padding: 15px;
  }

  .box .education .courses .all {
    margin: 0;
  }

  .box .education .badges {
    float: left;
    width: 50%;
  }

  .box .education .badges ul {
    padding: 20px 15px;
  }

  .box .my-install .steps .step .text {
    opacity: 1;
  }

  .my-install-banner {
    padding-left: 187px;
    padding-right: 200px;
  }

  .my-install-banner h3 {
    margin: 0 0 5px;
  }

  .my-install-banner p {
    margin: 0 0 20px;
  }

  .my-install-banner .img-wrap {
    border-radius: 3px 0 0 3px;
    left: 0;
    margin: 0;
    position: absolute;
    top: 0;
    width: 167px;
  }

  .my-install-banner .SumoSelect {
    max-width: 300px;
  }

  .my-install-banner .circle-wrap {
    position: absolute;
    right: 15px;
    top: 25px;
  }

  .tasks-wrap {
    padding-left: 260px;
    position: relative;
  }

  .tasks-wrap .side {
    border-right: 1px solid #e5e5e5;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 260px;
  }

  .tasks-wrap .side .scroll-content {
    bottom: 0;
    left: 0;
    max-height: 100%;
    overflow: auto;
    position: absolute;
    right: 0;
    top: 41px;
  }

  .tasks-wrap .main {
    border-top: none;
  }

  .dashboard .main .notifications-table .table-row {
    display: table;
    width: 100%;
  }

  .dashboard .main .notifications-table .table-row p {
    display: table-cell;
    padding: 5px;
    white-space: nowrap;
  }

  .dashboard .main .notifications-table .table-row p:nth-child(2) {
    width: 170px;
  }

  .dashboard .main .notifications-table .table-row p:nth-child(3) {
    width: 90px;
    text-align: center;
  }

  .dashboard .main .notifications-table .table-row p.name {
    padding-left: 25px;
    white-space: normal;
  }

  .content-library .wrap {
    padding: 0 0 0 260px;
  }

  .content-library .wrap .side {
    display: block;
  }

  .content-library .products .product {
    margin: 0 2% 45px;
    width: 80px;
  }

  .content-library .products .SumoSelect {
    margin: 0;
    position: absolute;
    right: 15px;
    top: 30px;
  }

  .content-library .advanced-list .list-table .table-row {
    display: table;
    width: 100%;
  }

  .content-library .advanced-list .list-table .table-row p {
    display: table-cell;
    padding: 5px;
    white-space: nowrap;
  }

  .content-library .advanced-list .list-table .table-row p.name {
    padding-left: 25px;
    white-space: normal;
  }

  .content-library .advanced-list .list-table .table-row p.actions {
    text-align: right;
    width: 220px;
  }

  .content-library .detail {
    padding: 30px;
  }

  .content-library .detail .detail-wrap {
    padding: 45px;
  }

  .content-library .detail .detail-wrap>hr {
    margin: 0 -45px 30px;
  }

  .content-library .detail .detail-wrap section {
    margin: 0 -45px 30px;
  }

  .content-library .detail .detail-wrap .offset {
    margin: 0 -45px 30px;
  }

  .knowledge-base .wrap {
    padding: 0 0 0 260px;
  }

  .knowledge-base .wrap .side {
    display: block;
  }

  .knowledge-base .detail {
    padding: 30px;
  }

  .knowledge-base .detail .detail-wrap {
    padding: 45px;
  }

  .knowledge-base .detail .detail-wrap .back-link {
    margin: -30px 0 15px;
  }

  .knowledge-base .detail .detail-wrap>hr {
    margin: 0 -45px 30px;
  }

  .knowledge-base .detail .detail-wrap section {
    margin: 0 -45px 30px;
  }

  .knowledge-base .detail .detail-wrap .offset {
    margin: 0 -45px 30px;
  }

  .knowledge-base .landing {
    padding: 30px;
  }

  .knowledge-base .landing .landing-wrap .community .item {
    padding-right: 235px;
    position: relative;
  }

  .knowledge-base .landing .landing-wrap .community .item .member {
    position: absolute;
    right: 15px;
    top: 15px;
  }

  .search-bar .button {
    display: inline-block;
    margin: 0 0 5px;
  }

  .search-bar .SumoSelect {
    max-width: 120px;
    position: absolute;
    right: 405px;
    top: 10px;
  }

  .search-bar form.search {
    max-width: 240px;
    position: absolute;
    right: 150px;
    top: 10px;
  }

  .search-bar a.advanced {
    position: absolute;
    right: 15px;
    top: 18px;
  }

  .inquiry-confirmation .large .text {
    display: inline-block;
  }

  .survey-results ul li {
    padding-right: 430px;
    position: relative;
  }

  .survey-results .bar {
    position: absolute;
    right: 0;
    top: -12px;
    width: 400px;
  }

  .roadmap-tiles .tile {
    width: 33.3333333%;
  }

  #page.scrolled header>div>a.portal-nav-toggle {
    color: #004593;
  }

  #page.scrolled header .portal-nav {
    background: #004593;
    color: #fff;
  }

  #page.scrolled header .portal-nav a {
    color: #fff;
  }

  .side-filters-wrap {
    display: none;
  }
}

/* MD Classes */
@media (min-width:992px) {
  .content-library .advanced-list .search-wrap .search {
    max-width: 220px;
  }

  .content-library .advanced-list .search-wrap .select-options {
    left: 270px;
  }

  .content-library .products .product {
    margin: 0 5% 45px;
  }

  .roadmap-tiles .tile {
    width: 25%;
  }

  .data-table-wrap .cell-wrap {
    color: #6f6c6c;
    display: block;
    max-width: 440px;
    overflow: hidden;
    position: relative;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  header a.portal-nav-toggle {
    display: inline;
  }
}

/* LG Classes */
@media (min-width:1200px) {
  .container {
    max-width: 1170px
  }

  .milestones li {
    margin: 30px 0;
  }

  .roadmap-tiles .tile {
    width: 20%;
  }

  .data-table-wrap .cell-wrap {
    max-width: 540px;
  }
}
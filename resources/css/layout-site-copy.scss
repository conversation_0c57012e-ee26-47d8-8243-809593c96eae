body {
  font-family: "Open Sans Light", sans-serif;
  line-height: 18px;
  color: #333;
  background: #FFFFFF;
  margin: 0;
  padding: 0;
}

body#contact_map {
  background: url(../css/images/bkgrounds/contact_map.gif) center 240px no-repeat;
}

/*main layout*/
#wrapper {
  display: block;
}

#container {
  max-width: 1250px;
  margin: 0 auto;
  padding: 0;
}

#content_hldr {
  background: rgb(0, 140, 155);
  /* Old browsers */
  background: -moz-linear-gradient(left, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
  /* FF3.6+ */
  background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(186, 210, 52, 1)), color-stop(25%, rgba(77, 182, 74, 1)), color-stop(50%, rgba(56, 171, 115, 1)), color-stop(75%, rgba(43, 167, 152, 1)), color-stop(100%, rgba(0, 140, 155, 1)));
  /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(left, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
  /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(left, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
  /* Opera 11.10+ */
  background: -ms-linear-gradient(left, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
  /* IE10+ */
  background: linear-gradient(to right, rgba(186, 210, 52, 1) 0%, rgba(77, 182, 74, 1) 25%, rgba(56, 171, 115, 1) 50%, rgba(43, 167, 152, 1) 75%, rgba(0, 140, 155, 1) 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#bad234", endColorstr="#008c9b", GradientType=1);
  /* IE6-9 */
  position: relative;
  display: block;
  margin: 0;
  padding: 0 15px 15px 0;
  clear: both;
}

#content_hldr.fullpage #content {
  display: block;
  margin: 0;
  padding: 0;
}

#content_hldr.fullpage #content #pagecontent {
  margin: 0;
  padding: 50px;
}

#content_hldr.fullpage #chip {
  background: url(../img/bkgrds/column_chip.png) 0 0 no-repeat;
  top: -15px;
  left: 0px;
  position: absolute;
  float: left;
  z-index: 1;
  height: 40px;
  width: 40px;
}

#content_hldr.fullpage #col_hldr {
  display: none;
}

#content_hldr_btm {
  clear: both;
  float: none;
  display: block;
  line-height: 0;
  font-size: 0;
}

#content {
  background: rgb(187, 226, 231);
  /* Old browsers */
  background: -moz-linear-gradient(left, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
  /* FF3.6+ */
  background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(236, 242, 202, 1)), color-stop(25%, rgba(209, 236, 206, 1)), color-stop(50%, rgba(197, 229, 214, 1)), color-stop(75%, rgba(193, 229, 225, 1)), color-stop(99%, rgba(187, 226, 231, 1)));
  /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(left, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
  /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(left, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
  /* Opera 11.10+ */
  background: -ms-linear-gradient(left, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
  /* IE10+ */
  background: linear-gradient(to right, rgba(236, 242, 202, 1) 0%, rgba(209, 236, 206, 1) 25%, rgba(197, 229, 214, 1) 50%, rgba(193, 229, 225, 1) 75%, rgba(187, 226, 231, 1) 99%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#ecf2ca", endColorstr="#bbe2e7", GradientType=1);
  /* IE6-9 */
  position: relative;
  display: block;
  padding: 0;
  margin: 0;
  color: rgba(0, 0, 0, 0.65);
}

#homepage_chip {
  position: absolute;
  z-index: 1;
  top: 0px;
  left: 0px;
  width: 56%;
}

#homepage_chip img {
  width: 74%;
}

#header {
  height: 60px;
  display: block;
  padding: 0;
  position: relative;
}

#baslogo {
  padding: 0;
  margin: 0;
  display: inline-block;
  position: relative;
  top: 52px;
  left: 0;
}

#baslogo img {
  border: none;
  padding: 0;
}

#ct_login {
  background: url(../img/icons/accessu.png) left center no-repeat;
  height: 20px;
  padding: 0 0 0 30px;
  margin: 0;
  display: inline-block;
  position: relative;
  top: 20px;
  float: right;
  font-size: 12px;
}

#ct_login a {
  margin: 0px 7px 0px 7px;
  color: #007079;
}

#ct_login a:visited:hover {
  color: rgba(120, 57, 128, 1);
}

#ct_login a:visited {
  color: #007079;
}

#ct_login a:hover {
  color: rgba(120, 57, 128, 1);
}

ul.side-nav {
  padding: 0;
  margin: 0;
}

#col_hldr {
  background: url(../img/bkgrds/column_chip.png) 0 0 no-repeat;
  width: 250px;
  margin: -15px 0 0 0;
  position: relative;
  float: left;
  z-index: 1;
}

#col {
  font-size: 16px;
  position: relative;
  margin: 0;
  padding: 25px 15px 0 15px;
  letter-spacing: 0.02em;
  color: #4a5c58;
}

#col .title {
  margin: 0;
  font-size: 12px;
  padding: 15px 10px 7px 7px;
  font-size: 18px;
  color: rgba(120, 57, 128, 1);
  text-transform: uppercase;
}

#col a, #col a:link {
  padding: 4px 0 0 22px;
  line-height: 24px;
  text-decoration: none;
  color: rgba(120, 57, 128, 1);
}

#col a:visited:hover {
  color: rgba(120, 57, 128, 1);
}

#col a:visited {
  color: rgba(120, 57, 128, 1);
}

#col a:hover, #col a:hover {
  color: rgba(120, 57, 128, 1);
}

#col ul.select, #col ul.side-nav {
  margin: 5px 0 0 -25px;
  padding: 10px 0;
  list-style: none;
  text-transform: capitalize;
}

#col .side-nav li, #col .select li {
  list-style: none;
  margin: 0 0 0 10px;
  cursor: pointer;
}

#col .expanded li {
  list-style: none;
  margin: 0;
  cursor: pointer;
}

#col .select li .link {
  text-transform: lowercase;
}

#col li.tab.selected {
  color: #bd4f48;
  cursor: default;
}

#col li.ui-tabs-active a, #col li.ui-tabs-active a:link, #col li.ui-tabs-active a:visited {
  background: url(../img/bullets/large_white_arrow.png) 0 2px no-repeat;
  color: rgba(120, 57, 128, 1);
  cursor: default;
  font-weight: bold;
}

#col .service a {
  font-size: 10px;
}

/*Main content/tabbed pages */
a, a:link {
  color: #00F;
}

.slogan {
  margin: 10px;
}

#main a {
  color: #007079;
}

#main {
  position: relative;
  padding: 0;
}

#return {
  background: url(../img/bkgrds/return_chip.png) bottom right no-repeat;
  height: 40px;
  width: 40px;
  margin: 0;
  padding: 0;
  display: inline-block;
  position: absolute;
  bottom: -15px;
  right: -15px;
  display: none;
  z-index: 3;
  cursor: pointer;
}

#return a {
  font-size: 8px;
  color: #fff;
  text-decoration: none;
  position: absolute;
  bottom: 0px;
  right: 2px;
  display: none;
}

#footer_hldr {
  height: 150px;
  margin-top: 25px;
}

#footer {
  height: 150px;
  position: relative;
  display: block;
  margin: 0;
  padding: 0;
  clear: both;
  color: #4d4d4f;
  font-size: 12px;
}

#footer nav {
  position: relative;
  display: block;
  padding: 0;
}

#footer nav ul {
  position: relative;
  display: block;
  margin: 0;
  padding: 0;
}

#footer nav ul li {
  margin: 0 0 0 20px;
  padding: 0;
  list-style: none;
  display: inline-block;
}

#footer nav ul li:last-child {
  float: right;
  margin-right: 20px;
}

#footer img {
  border: none;
  vertical-align: bottom;
}

#footer a, #footer a:link {
  margin: 0;
  padding: 0;
  text-decoration: none;
  text-transform: capitalize;
  color: #007079;
  font-size: 12px;
}

#footer a:visited:hover, #footer a:visited, #footer a:hover {
  color: #007079;
}

.consult_statement {
  font-size: 13px;
  margin: 10px 0 -5px 0;
  color: #BF362F;
  display: block;
  text-align: center;
}

#nav_container #footer .consult_statement {
  display: none;
}

li.copyright {
  text-align: right;
}

#date_modified {
  display: none;
}

.tagline {
  font-size: 13px;
  color: #007079;
  text-align: center;
}

/*search form/page */
input, form {
  margin: 0;
  padding: 0;
}

input .input {
  border-radius: 3px;
  -webkit-border-radius: 3px;
  /* for Safari */
  -moz-border-radius: 3px;
  /* for Firefox */
}

#search {
  text-align: left;
  padding-right: 15px;
  padding-left: 7px;
}

#search label {
  color: #AC302B;
  font-size: 12px;
  font-weight: bold;
  display: inline-block;
}

#search_box {
  display: inline-block;
  border: 1px solid #c58b8b;
  background: #ecf3f7;
  margin: 0;
  padding: 0 0 0 3px;
  width: 150px;
  height: 15px;
  font-size: 11px;
  color: #000;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  /* for Safari */
  -moz-border-radius: 3px;
  /* for Firefox */
}

#input_active, #search_box:focus, #search_box:hover {
  border: 1px solid #c58b8b;
  background: #ecf3f7;
  margin: 0;
  padding: 0 0 0 3px;
  width: 150px;
  height: 15px;
  font-size: 11px;
  background: #fff;
  color: #000;
}

.search {
  margin: 0;
  padding: 0 0 0 20px;
  _padding-top: 1px;
  width: 80px;
  height: 16px;
  font-size: 11px;
  text-transform: uppercase;
  color: #bd4f48;
  font-weight: bold;
  cursor: pointer;
  border: none;
}

.go {
  display: inline-block;
  margin: 0 0 0 2px;
  padding: 0;
  width: 20px;
  height: 18px;
  cursor: pointer;
  background: url(../img/buttons/search_arrow.gif) 0px center no-repeat;
  color: #fff;
  border: none;
  position: relative;
  vertical-align: middle;
}

/*** Main Layout - END***/
/* Columns */
#trademarks .column {
  width: 25%;
  float: left;
  margin: 5px 60px 0 0;
  padding: 0;
  display: inline;
  clear: none;
}

#trademarks .last {
  margin-right: 0;
}

#trademarks .column img {
  border: 1px solid #4A5C58;
}

.col_l, .col_l_flush, .col_l_flush_nm, .fullpage .col_l_flush {
  float: left;
  position: relative;
}

.col_r, .col_r_flush, .col_r_flush_nm, .fullpage .col_r_flush {
  float: right;
  position: relative;
}

.col_full_flush, .fullpage .col_full_flush {
  position: relative;
}

.col_l {
  margin-right: 20px;
}

.col_r {
  margin-left: 20px;
}

.col_l_flush {
  margin-right: 20px;
  margin-left: -25px;
}

.col_r_flush {
  margin-right: -25px;
  margin-left: 20px;
}

.col_l_flush_nm {
  margin-left: -25px;
}

.col_r_flush_nm {
  margin-right: -25px;
}

.col_full_flush {
  margin-right: -25px;
  margin-left: 20px;
}

.col_l_full_flush_extend {
  margin-right: -50px;
  margin-left: -25px;
}

.col_r_full_flush_extend {
  margin-right: -25px;
  margin-left: -50px;
}

.fullpage .col_l {
  margin-right: 50px;
}

.fullpage .col_r {
  margin-left: 50px;
}

.fullpage .col_l_flush {
  margin-right: 50px;
  margin-left: -50px;
}

.fullpage .col_r_flush {
  margin-right: -50px;
  margin-left: 50px;
}

.fullpage .col_full_flush {
  margin-right: -50px;
  margin-left: -50px;
}

.gutter > [class*=col-] {
  padding-right: 10px;
  padding-left: 10px;
}

.top-band {
  background: url(../img/bkgrds/top_band_bkgrd.jpg) top center no-repeat;
  background-size: 100%;
  padding-top: 9px;
  margin-top: 40px;
}

.bottom-band {
  background: url(../img/bkgrds/bottom_band_bkgrd.jpg) bottom center no-repeat;
  background-size: 100%;
  padding-bottom: 13px;
  margin-bottom: 40px;
}

.top-band_full {
  background: url(../img/bkgrds/top_band_full.jpg) top center no-repeat;
  background-size: 100%;
  padding-top: 9px;
  margin-top: 40px;
}

.bottom-band_full {
  background: url(../img/bkgrds/bottom_band_full.jpg) bottom center no-repeat;
  background-size: 100%;
  padding-bottom: 9px;
  margin-bottom: 40px;
}

.bottom-line {
  background-image: url(../img/products/ultrasound/supporting/sapiens-line.png);
  background-size: 100%;
  background-repeat: no-repeat;
  margin-left: -15px;
  height: 10px;
  padding: 0;
}

/*General Table Formatting*/
.striped {
  width: 100%;
  border-bottom: 3px solid #e1f2e;
}

.striped th {
  text-align: left;
  width: auto;
  padding: 5px 10px;
  font-weight: bold;
  font-size: 15px;
  line-height: 22px;
  border-bottom: 1px solid #e1f2e2;
  vertical-align: middle;
  white-space: nowrap;
}

.striped th.col_100 {
  width: 100px;
}

.striped td {
  padding: 3px 10px;
  text-align: cen;
  width: auto;
  font-size: 14px;
  line-height: 22px;
  color: #444;
  border-bottom: 1px solid #e1f2e2;
}

.striped .font20 {
  font-size: 20px;
}

.striped tr:nth-of-type(odd) {
  background: #dff2e0;
}

.striped thead tr th {
  background: #FFF;
  border-bottom: 3px solid #e1f2e2;
}

.striped td.bigbullet {
  font-size: 40px;
  text-align: center;
  padding: 5px 0;
}

.color_two {
  background: #dff2e0;
}

.width_autofill {
  width: auto;
  _width: auto;
}

/*PDF thumbnail images and text*/
ul.thumbnails {
  margin: 40px 5px 20px 0;
  padding: 0;
  float: none;
  list-style-position: inside;
  list-style: none;
  display: block;
  position: relative;
  padding-left: 0;
}

ul.thumbnails li {
  margin: 0 25px 10px 0;
  float: left;
  position: relative;
  display: inline;
  zoom: 1;
  width: 116px;
  height: 250px;
  line-height: 105%;
  font-size: 10px;
  vertical-align: middle;
}

ul.thumbnails.med li {
  width: 175px;
}

ul.thumbnails.wide li {
  width: 225px;
}

#resources ul.thumbnails li {
  margin: 0 25px 10px 0;
  float: left;
  position: relative;
  display: inline;
  width: 116px;
  height: 250px;
  line-height: 105%;
  font-size: 10px;
  vertical-align: middle;
}

#main ul.thumbnails li img {
  margin-bottom: 10px;
  margin-right: 20px;
  position: relative;
  clear: both;
}

#main ul.thumbnails li a img {
  border: 1px solid #d6edf0;
  text-decoration: none;
}

#main ul.thumbnails li a:visited:hover img {
  border: 1px solid #007079;
  text-decoration: none;
}

#main ul.thumbnails li a:visited img {
  border: 1px solid #d6edf0;
  text-decoration: none;
}

#main ul.thumbnails li a:hover img {
  border: 1px solid #007079;
  text-decoration: none;
}

#main ul.thumbnails a {
  color: #007079;
  text-decoration: none;
}

#main ul.thumbnails a:visited:hover {
  color: rgba(120, 57, 128, 1);
  text-decoration: none;
}

#main ul.thumbnails a:visited {
  color: #007079;
  text-decoration: none;
}

#main ul.thumbnails a:hover {
  color: rgba(120, 57, 128, 1);
  text-decoration: none;
}

/*2columns*/
ul.info-2col {
  list-style: none;
  margin: 0;
  padding: 0;
  float: none;
  display: block;
  position: relative;
}

ul.info-2col li {
  margin: 0;
  padding: 5px 20px 5px 20px;
  width: 310px;
  height: 100px;
  float: left;
  display: inline;
  border-right: #a2bd88 dotted 1px;
}

ul.info-2col li img {
  margin: -10px 10px 30px 0;
  padding: 0;
  float: left;
  display: inline;
}

ul.info-2col li.last {
  border: none;
}

ul.info1-2col {
  list-style: none;
  margin: 0;
  padding: 0;
  float: none;
  display: block;
  position: relative;
}

ul.info1-2col li {
  margin: 0;
  padding: 5px 20px 5px 20px;
  width: 310px;
  float: left;
  display: inline;
}

ul.info1-2col li img {
  margin: -10px 10px 30px 0;
  padding: 0;
  float: left;
  display: inline;
}

ul.info1-2col li.last {
  border: none;
}

/*3columns*/
ul.info-3col {
  list-style: none;
  margin: 0;
  padding: 0;
  float: none;
  display: block;
  position: relative;
}

ul.info-3col li {
  margin: 0;
  padding: 5px 15px 5px 15px;
  width: 218px;
  height: 125px;
  float: left;
  display: inline;
  border-right: #a2bd88 dotted 1px;
}

ul.info-3col li img {
  margin: 0 10px 50px 0;
  padding: 0;
  float: left;
  display: inline;
  vertical-align: middle;
}

ul.info-3col li.last, .noborder li {
  border: none;
}

ul.text-3col {
  list-style: none;
  margin: 0 auto;
  padding: 0;
  display: inline-block;
  position: relative;
  -webkit-columns: 3 175px;
  -moz-columns: 3 175px;
  columns: 3 175px;
  -webkit-column-gap: 60px;
  -moz-column-gap: 60px;
  column-gap: 60px;
}

ul.text-3col li {
  margin: 0;
  padding: 5px 0px;
  min-width: 175px;
  display: inline-block;
  vertical-align: top;
}

ul.text-3col li img {
  margin: 0 10px 50px 0;
  padding: 0;
  display: inline;
  vertical-align: middle;
}

ul.text1-3col {
  list-style: none;
  margin: 20px 0 0 0;
  padding: 0;
  float: none;
  display: block;
  position: relative;
}

ul.text1-3col li {
  margin: 0;
  padding: 5px 10px 5px 10px;
  width: 213px;
  height: 100px;
  float: left;
  display: inline;
}

ul.text1-2col li img {
  margin: -10px 10px 30px 0;
  padding: 0;
  float: left;
  display: inline;
}

ol.text1-3col {
  margin: 20px 0 0 80px;
  padding: 0;
  float: none;
  display: block;
  position: relative;
}

ol.text1-3col li {
  margin: 0 0 20px 0;
  padding: 5px 10px 5px 10px;
  width: 30%;
  height: auto;
  float: left;
  display: inline;
}

ol.text1-3col li span {
  float: left;
  margin: 0 15px 50px 0;
}

.three-column-page {
  margin: 0;
  padding: 0;
  columns: 220px 3;
  -webkit-columns: 220px 3;
  /* Safari and Chrome */
  -moz-columns: 220px 3;
  /* Firefox */
  column-gap: 0;
  -moz-column-gap: 0;
  /* Firefox */
  -webkit-column-gap: 0;
  /* Safari and Chrome */
}

.three-column-page li {
  margin: 0;
  padding: 0 0 5px 0;
  width: 220px;
  list-style: none;
  display: -moz-inline-box;
  /* Firefox 2 and under*/
  display: inline-block;
  /* FF3, Opera, Safari */
  zoom: 1;
  *display: inline;
  /*IE 6 and 7*/
}

.three-column-full {
  margin: 0;
  padding: 0;
  columns: 250px 3;
  -webkit-columns: 250px 3;
  /* Safari and Chrome */
  -moz-columns: 250px 3;
  /* Firefox */
  column-gap: 0;
  -moz-column-gap: 0;
  /* Firefox */
  -webkit-column-gap: 0;
  /* Safari and Chrome */
}

.three-column-full li {
  margin: 0;
  padding: 0 0 5px 0;
  width: 250px;
  list-style: none;
  display: -moz-inline-box;
  /* Firefox 2 and under*/
  display: inline-block;
  /* FF3, Opera, Safari */
  zoom: 1;
  *display: inline;
  /*IE 6 and 7*/
}

.two-columns {
  columns: 225px 2;
  -webkit-columns: 225px 2;
  /* Safari and Chrome */
  -moz-columns: 225px 2;
  /* Firefox */
  column-gap: 0;
  -moz-column-gap: 0;
  /* Firefox */
  -webkit-column-gap: 0;
  /* Safari and Chrome */
}

.three-columns {
  columns: 225px 3;
  -webkit-columns: 225px 3;
  /* Safari and Chrome */
  -moz-columns: 225px 3;
  /* Firefox */
  column-gap: 0;
  -moz-column-gap: 0;
  /* Firefox */
  -webkit-column-gap: 0;
  /* Safari and Chrome */
}

/*# sourceMappingURL=layout-site-copy.scss.map */

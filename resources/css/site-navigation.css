@charset "utf-8";

#topnav_hldr {
  margin: 0;
  z-index: 100;
  position: relative;
}

#navigation {
  list-style: none;
  padding: 0;
  margin: 0;
  display: block;
  float: right;
}

#navigation li {
  display: inline;
  padding: 0 0 0 25px;
  margin: 0;
}

#navigation li a {
  margin: 0;
  padding: 0;
  text-decoration: none;
  color: #007079;
  font-size: 17px;
  text-transform: uppercase;
}

#navigation li a:hover {
  color: rgba(120, 57, 128, 1);
}

.navbar {
  display: block;
  min-height: 55px;
  margin: 0 0 -25px;
}

.navbar-default {
  background: none;
  border: none;
}

.navbar-brand {
  padding: 0;
  height: 50px;
  margin: -5px 0 0 0;
}

.navbar-brand>img {
  border: none;
}

.navbar-collapse {
  padding-right: 0;
}

#nav_container {
  background: url(../img/bkgrds/category_chip.png) 0 -50px no-repeat rgba(255, 255, 255, .5);
  margin: 0px -50px;
}

#product_nav {
  padding: 0;
  margin: 0 auto;
  display: block;
  text-align: center;
}

#product_nav ul {
  background: url(../img/nav/nav_arrow.png) left center repeat-x;
  padding: 0;
  margin: 50px auto;
  display: inline-block;
  text-decoration: none;
}

#product_nav li {
  background: url(../img/nav/placement_technologies.jpg) 10px 35px no-repeat rgba(36, 166, 128, 1);
  height: 240px;
  width: 220px;
  display: inline-block;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  position: relative;
  margin: 0 81px 0 0;
  padding: 5px 0 0 0;
}

#product_nav li:first-of-type {
  background: url(../img/nav/access_devices.jpg) 10px 35px no-repeat rgba(84, 183, 42, 1);
}

#product_nav li:last-of-type {
  background: url(../img/nav/care_and_maintenance.jpg) 10px 35px no-repeat rgba(6, 154, 175, 1);
  margin: 0
}

#product_nav li a {
  background: url(../img/nav/visit_chip.png) bottom right no-repeat rgba(36, 166, 128, .85);
  position: absolute;
  height: 198px;
  width: 195px;
  bottom: 0px;
  right: 0px;
  padding: 10px;
  color: #fff;
  font-size: 15px;
  font-weight: normal;
  text-align: left;
  line-height: 24px;
  text-decoration: none;
  transition: opacity .5s ease;
  opacity: 0;
}

#product_nav li:first-of-type a {
  background-color: rgba(84, 183, 42, .95);
}

#product_nav li:last-of-type a {
  background-color: rgba(6, 154, 175, .85);
}

#product_nav li:hover a {
  transition: opacity .5s ease;
  opacity: 1;
}

#resources_nav {
  padding: 0;
  margin: 0 auto;
  display: block;
  text-align: center;
}

#resources_nav ul {
  padding: 0;
  margin: 50px auto;
  display: inline-block;
  text-decoration: none;
}

#resources_nav li {
  background: url(../img/nav/health_economics.jpg) 10px 35px no-repeat rgba(36, 166, 128, 1);
  height: 240px;
  width: 220px;
  display: inline-block;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  position: relative;
  margin: 0 40px 0 0;
  padding: 5px 0 0 0;
}

#resources_nav li:first-of-type {
  background: url(../img/nav/literature.jpg) 10px 35px no-repeat rgba(84, 183, 42, 1);
}

#resources_nav li:nth-of-type(3) {
  background: url(../img/nav/programs.jpg) 10px 35px no-repeat rgba(6, 154, 175, 1);
}

#resources_nav li:last-of-type {
  background: url(../img/nav/mobile_apps.jpg) 10px 35px no-repeat rgba(0, 140, 155, 1);
  margin: 0
}

#resources_nav li a {
  background: url(../img/nav/visit_chip.png) bottom right no-repeat rgba(36, 166, 128, .85);
  position: absolute;
  height: 190px;
  width: 190px;
  bottom: 0px;
  right: 0px;
  padding: 10px;
  color: #fff;
  font-size: 16px;
  font-weight: normal;
  text-align: left;
  line-height: 20px;
  text-decoration: none;
  transition: opacity .5s ease;
  opacity: 0;
}

#resources_nav li:first-of-type a {
  background-color: rgba(84, 183, 42, .95);
}

#resources_nav li:nth-of-type(3) a {
  background-color: rgba(6, 154, 175, .85);
}

#resources_nav li:last-of-type a {
  background-color: rgba(0, 140, 155, .85);
}

#resources_nav li:hover a {
  transition: opacity .5s ease;
  opacity: 1;
}

#nav_container h3 {
  padding: 30px 0 10px 0px;
  letter-spacing: 2px;
  color: rgba(84, 183, 42, 1);
  margin-top: 0;
  font-weight: light;
  text-align: center;
  font-size: 48px;
}

#nav_container.ultrasound {
  background: none;
  height: auto;
}

#nav_container.access_products {
  background: none;
  height: auto;
}

#nav_container.care_products {
  background: none;
  height: auto;
}

#nav_container #header_wrapper h3 {
  background: url(../img/bkgrds/category_chip.png) 0 -50px no-repeat rgba(255, 255, 255, .5);
  margin: 0px;
  padding: 6px 0 8px 108px;
  text-align: left
}

#nav_container.access_products h3 {
  color: rgba(36, 166, 128, 1);
}

#nav_container.access_products a {
  color: rgba(36, 166, 128, 1);
}

#nav_container h4 {
  color: rgba(36, 166, 128, 1);
  margin: 20px 0 -15px 110px;
}

#nav_container.care_products h3 {
  color: rgba(6, 154, 175, 1);
}

#nav_container.care_products a {
  color: rgba(6, 154, 175, 1);
}

#nav_container.care_products h4 {
  color: rgba(6, 154, 175, 1);
}

.nav_col {
  float: left;
  display: inline-block;
  list-style: none;
  min-width: 385px;
}

#product_selection h3 {
  background: none;
  color: rgba(84, 183, 42, .85);
  padding: 6px 0 23px 55px;
  font-size: 46px;
  letter-spacing: 2px;
  border: none;
  font-weight: normal;
  margin: 0 0 10px 55px;
  text-align: left;
}

#product_selection h3.tip_confirmation_location {
  font-size: 42px;
}

#product_selection ul.col {
  margin: 15px 0 20px 90px;
  padding: 0;
  min-height: 40px;
  list-style: none;
  display: block;
  line-height: 28px;
}

#product_selection ul.col li {
  margin: 0;
  padding: 0 0 0 20px;
  font-size: 18px;
}

/*Font is slightly darker than title so nurses can read easily */
#product_selection a {
  color: rgba(73, 158, 36, 1);
  text-decoration: none;
}

#product_selection a:hover {
  color: rgba(120, 57, 128, 1);
  background: url(../img/bullets/small_purple_arrow.png) 0 center no-repeat;
  display: inline-block;
  position: relative;
  margin-left: -10px;
  padding-left: 10px;
}

#sub_nav_container {
  margin: 0px 0 55px 0
}

#sub_nav_container ul {
  padding: 0px;
  margin: 0 auto;
  display: block;
  text-decoration: none;
  text-align: center;
}

#sub_nav_container li {
  margin: 0 45px 30px;
  width: 166px;
  display: inline-block;
  color: #fff;
  font-size: 16px;
  position: relative;
  text-align: center;
  line-height: 20px;
  vertical-align: top;
}

#sub_nav_container li a {
  text-decoration: none
}

#sub_nav_container li img {
  border: solid 8px rgba(84, 183, 42, 1);
  margin: -5px 10px 10px 0px;
  position: relative;
}

#sub_nav_container li span {
  display: table-cell;
  vertical-align: middle;
  position: relative;
  width: 155px;
  margin: 0;
  padding: 1px 6px;
  color: #fff;
  line-height: 14px;
  background-color: rgba(84, 183, 42, 1);
  height: 35px;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: bold
}

#sub_nav_container p {
  text-align: left;
  width: 70%;
  margin-left: 15%;
  margin-right: auto;
  padding-bottom: 10px;
}

#sub_nav_container.access li img {
  border: solid 8px rgba(36, 166, 128, 1);
}

#sub_nav_container.access h3 {
  color: rgba(36, 166, 128, 1);
}

#sub_nav_container.access li span {
  background-color: rgba(36, 166, 128, 1);
}

#sub_nav_container.care_maintenance li img {
  border: solid 8px rgba(6, 154, 175, 1);
}

#sub_nav_container.care_maintenance h3 {
  color: rgba(6, 154, 175, 1);
}

#sub_nav_container.care_maintenance li span {
  background-color: rgba(6, 154, 175, 1);
}

#sub_nav_container.drk_blue li img {
  border: solid 8px rgba(0, 140, 155, 1);
}

#sub_nav_container.drk_blue h3 {
  color: rgba(0, 140, 155, 1);
}

#sub_nav_container.drk_blue li span {
  background-color: rgba(0, 140, 155, 1);
}

#secondary_nav {
  border-top: solid 1px rgba(120, 57, 128, 1.00);
  margin: 0px 50px 30px 60px;
}

#secondary_nav div {
  display: block;
  width: 100%;
}

#secondary_nav div:last-of-type {
  width: 100%;
}

#secondary_nav h4 {
  background: none;
  color: rgba(120, 57, 128, 1.00);
  text-transform: uppercase;
  padding: 0;
  font-size: 14px;
  letter-spacing: 4px;
  border: none;
  font-weight: bold;
  margin: 10px 0 0px 0px;
  width: 260px;
}

#secondary_nav .col {
  display: inline-block;
  float: left;
  list-style: none;
  padding: 0 0px 0 0px;
  color: rgba(89, 43, 95, 1);
  text-decoration: none;
  font-size: 14px;
  line-height: 18px;
  margin-top: -3px;
  width: 100%;
}

#secondary_nav a {
  color: rgba(120, 57, 128, 1.00);
  text-decoration: none;
}

#secondary_nav a:hover {
  color: rgba(173, 173, 173, 1);
  background: url(../img/bullets/small_purple_arrow.png) 0 center no-repeat;
  display: inline-block;
  position: relative;
  margin-left: -10px;
  padding-left: 10px;
}

#secondary_nav .col_2 {
  width: 385px;
  float: left;
}

#bpv {
  color: rgba(36, 166, 128, 1);
  margin: 0 0 0 60px;
}

#bpv p:first-of-type {
  font-size: 20px;
  font-style: normal;
  letter-spacing: 4px;
  margin-bottom: -10px;
  margin-top: 20px;
  text-transform: uppercase;
}

#bpv p {
  font-size: 15px;
  font-style: normal;
  line-height: 18px;
}

#bpv p:last-of-type {
  font-size: 14px;
  font-style: italic;
  margin-top: 20px;
}

#product_selection #bpv ul.col {
  display: block;
  line-height: 28px;
  list-style: outside none none;
  margin: 0 0 0 -20px;
  min-height: 40px;
  padding: 0;
}

#bpv a {
  color: rgba(36, 166, 128, 1);
}

#bpv a:hover {
  color: rgba(120, 57, 128, 1);
  display: inline-block;
  margin-left: -10px;
  padding-left: 10px;
  position: relative;
  text-decoration: none;
}

#bpv h4 {
  color: rgba(36, 166, 128, 1);
  font-weight: bold;
  margin-top: -5px;
}

#bpv span {
  color: rgba(36, 166, 128, 1);
  font-style: normal;
  font-weight: bold;
}
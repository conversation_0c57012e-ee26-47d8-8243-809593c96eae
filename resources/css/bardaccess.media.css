	@charset "utf-8";
	/* CSS Document */

	.quote-div {
		width: 47%;
	}

	.navbar-default .navbar-toggle {
		margin-right: 1px;
	}

	#searchBox {
		clear: both;
		width: 100%;
		border: 1px solid #67296F;
		margin-top: 25px
	}

	#search_input {
		padding: 13px;
		width: 96%;
		font-size: 15px;
		color: #333;
		text-transform: none;
		padding: 10px 13px 13px 13px;
		height: 21px;
	}

	.not-found-mobile {
		display: none;
	}

	/* iPhone 5*/

	@media (max-width: 320px) {
		#statlock_people {
			padding: 41px 47px 40px 47px;
		}
	}

	/* Dont move below 400px. Fonts will break*/

	@media (max-width: 846px) {
		#nav_container h3 {
			font-size: 40px;
		}
	}

	/* PHONE */

	@media (max-width: 400px) {
		#container {
			width: 99%;
		}

		#content_hldr {
			padding: 0 5px 5px 0;
		}

		#pagecontent {
			padding: 10px;
			margin: 0;
		}

		#content_hldr.fullpage #content #pagecontent {
			padding: 10px;
		}

		#content_hldr.category_page #pagecontent {
			margin: -5px 0 0;
			padding: 25px;
			background-size: 30%
		}

		#content_hldr.category_page #content {
			margin: 5px 0 0 0;
		}

		#content_hldr.fullpage #chip {
			top: -5px;
		}

		#content_hldr.jquery-tabs #main>div {
			margin: 0 0 50px;
			padding: 0px 0px 50px;
			clear: both;
		}

		#content_hldr.jquery-tabs #main>div.beginPage,
		#content_hldr.jquery-tabs #main>div.endPage {
			border: none;
			margin: 0px;
			padding: 0px;
		}

		#content_hldr.jquery-tabs #main h3 {
			border-bottom: 1px solid rgba(120, 57, 128, 1);
			font-weight: bold;
			padding: 0 0 2px;
		}

		#bbnone {
			border-bottom: none;
			font-weight: bold;
			padding: 0 0 2px;
		}

		#navigation li a {
			padding-right: 12px;
			padding-bottom: 10px;
			margin-left: -12px;
			border-bottom: 1px solid #ececec;
		}

		#related ul li.products h4 {
			background: rgba(161, 198, 38, 1);
		}

		.content_block_blue {
			margin-right: -15px;
		}

		h4 {
			font-size: 16px;
		}

		#cat_title_hldr {
			margin: 30px -5px 5px -2px;
		}

		#cat_title {
			height: 24px;
		}

		#cat_title h1 {
			font-size: 26px;
			line-height: 26px;
			letter-spacing: 2px;
			white-space: normal;
		}

		#overview img {
			margin: 0px
		}

		#overview h3 {
			font-size: 20px
		}

		#overview p.summary {
			font-size: 20px
		}

		#overview .site-rite-8-hero {
			margin-right: -15px;
		}

		#product_selection h3 {
			font-size: 24px;
			line-height: 26px;
		}

		#col_hldr {
			display: none;
		}

		#return {
			bottom: -5px;
			right: -5px;
		}

		#header {
			display: none;
		}

		#ct_login {
			display: none;
		}

		/* Overview */
		#responsiveLogo {
			display: block;
			margin: 10px 0 0px 0;
		}

		.pageSlogan {
			display: block;
			margin: 10px 0 20px 0;
		}

		#overview .overview p:first-of-type {
			font-size: 20px;
			padding: 0px 0 0 0px;
		}

		#overview ul.nobullet li {
			color: #783980
		}

		.background-purple {
			padding: 40px 0 0 0;
			margin: -25px 0 0 0;
			background: #FFFFFF;
		}

		.background-bluegreen {
			padding: 40px 0 0 0;
			margin: -25px 0 0 0;
			background: #FFFFFF;
		}

		#overview.background-bluegreen h3 {
			color: #783980;
		}

		/* A little bit of logo padding */
		.navbar-brand>img {
			padding-top: 15px;
			max-width: 90%
		}

		/*404 page*/
		.not-found-image {
			display: none;
		}

		.not-found-text h3 {
			display: none;
		}

		.not-found-text h4 {
			display: none;
		}

		.not-found-mobile {
			display: inline-block;
		}

		.not-found-mobile h3 {
			display: inline-block;
			font-size: 40px;
			line-height: 40px;
		}

		.not-found-mobile h4 {
			display: inline-block;
			margin-top: 10px
		}

		/*Related - homepage*/
		#related.home * {
			margin: 0;
			padding: 0;
		}

		#related.home {
			display: block;
			text-align: left
		}

		#related.home ul {
			position: relative;
			right: auto;
		}

		#related.home ul li {
			background: none;
			display: block;
			height: 35px;
			margin: 10px auto 0 auto;
			padding: 0;
			width: 100%;
		}

		#related.home ul li:first-of-type {
			display: block;
		}

		#related.home ul li:first-of-type h4 {
			margin: 0;
		}

		#related.home ul li h4 {
			box-sizing: border-box;
			border: none;
			height: 35px;
			line-height: 35px;
			margin: 0;
			padding: 0;
			width: 100%;
			font-size: 18px;
			display: block;
		}

		#related.home ul li p {
			display: none;
		}

		#related.home ul li a {
			display: block;
			padding: 0 0 0 15px
		}

		/*Footer*/
		#footer_hldr {
			margin-top: 15px;
		}

		#footer nav ul {
			text-align: right;
		}

		#footer nav ul {
			text-align: center;
		}

		#footer nav ul li {
			margin: 15px 10px 0px 10px;
			text-align: center;
		}

		#footer nav ul li:last-child {
			float: none;
			margin: 25px 10px 0px 10px;
		}

		#footer a,
		#footer a:link {
			font-size: 14px;
		}

		#instructions {
			margin: 0px 0px 15px;
			font-size: 14px;
			line-height: 22px !important;
		}

		#product_code {
			font-size: 16px;
			width: 230px !important;
			background-color: #FFF;
			height: 35px;
			padding: 6px 12px 6px 6px;
			line-height: 1.42857;
			background-image: none;
			border: 1px solid #CCC;
			border-radius: 4px;
			box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.075) inset;
			transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
		}

		#inst {
			width: 85%;
		}

		#language_selection {
			text-align: right;
			margin-top: -35px;
		}

		#lang {
			font-size: 19px;
		}

		.quote-div {
			width: 88%;
		}

		#transportable {
			margin-top: 0px;
		}

		/*category pages*/
		#nav_container {
			margin: 0px -25px;
			background-size: 30%;
			background-position: 0px -25px;
		}

		#nav_container #header_wrapper h3 {
			background-size: 30%;
			background-position: 0px -25px;
			padding: 6px 0 8px 70px;
		}

		#nav_container h3 {
			font-size: 30px;
			line-height: 30px;
			background-size: 30%;
			width: auto;
			margin-left: auto;
			margin-right: auto;
		}

		#nav_container h4 {
			margin: 20px 0 -15px 20px;
		}

		.nav_col {
			min-width: 250px;
		}

		#bpv {
			margin: 0
		}

		#secondary_nav {
			margin: 0;
		}

		#secondary_nav .col,
		#secondary_nav .col_2 {
			float: none;
		}

		#resource_container {
			margin: 0 0 0 15px;
		}

		/*Product Navigation*/
		#product_selection ul.col {
			margin: 15px 0 20px 10px;
		}

		#product_selection ul.col li {
			font-size: 16px;
		}

		#product_nav ul,
		#resources_nav ul {
			background: none;
		}

		#product_nav li,
		#resources_nav li {
			margin: 0 0 30px 0;
		}

		/*Catalog*/
		.catalog-ordering-info {
			cursor: hand;
			float: none;
			margin: 0 0 20px;
			cursor: pointer;
			color: #1F5B7F;
		}

		/*#related ul{margin:20px 0 0 0; padding:0; position:relative; right:5px;  }
	#related ul li{margin:0 0 20px 3px; }*/
		#sitemap h2:first-of-type {
			margin: 20px 0 0 0;
		}

		#sitemap h2 {
			margin: 45px 0 0;
		}

		#sitemap h3.hdr {
			margin: 40px 0 10px 0px;
			padding: 2px 0 2px 0px;
		}

		#sitemap .title {
			padding: 2px 0;
		}

		#sitemap ul {
			margin: 0;
			padding: 0;
		}

		#sitemap .row {
			padding-left: 15px;
		}

		#sitemap a,
		#sitemap a:link {
			white-space: normal;
		}

		.tm-10,
		.tm-20,
		.tm-30,
		.tm-40,
		.tm-50,
		.tm-60,
		.tm-70,
		.tm-80,
		.tm-85,
		.tm-90,
		.tm-100,
		.tm-110,
		.tm-120,
		.tm-130,
		.tm-140,
		.tm-150,
		.tm-160,
		.tm-170,
		.tm-180,
		.tm-190,
		.tm-200,
		.tm-235,
		.tm10,
		.tm20,
		.tm30,
		.tm40,
		.tm50,
		.tm215,
		.tm80 {
			margin-top: 0px;
		}

		.powergroshong {
			margin-top: 0px;
			width: 100%;
		}

		#picc_sv {
			margin-top: 0px;
		}

		#needle_body {
			margin: -5px -15px 20px 0px;
		}

		#training_man {
			margin: 0px -15px 0px 0px;
		}

		#guardiva_arm {
			margin: -20px -15px 20px 0px;
		}

		#site_scrub_devices {
			margin-left: -25px;
			max-width: 372px;
			align-content: center;
		}

		.right_arrow {
			display: none;
		}

		#statlock_people {
			padding: 60px 47px 40px 47px;
		}

		#tape_complications {
			padding: 0 0px 50px 0px;
			display: inline-block;
		}

		#cdc_guidelines {
			padding: 22px 0px 75px 0px;
		}

		#cdc_guidelines img {
			padding: 0px;
		}

		#max_barrier_arm_text {
			margin: 250px 0px 0px 0px;
		}

		#radstic_img {
			margin: -60px 15px 30px 15px;
		}

		#microez_img {
			margin: -60px 15px 30px 15px;
		}

		#excalibur {
			margin: 0px 20px 10px 0;
		}

		#self_blunt {
			margin: 0px 0px 30px 0px;
		}

		#optimal_performance {
			margin-top: 0px;
		}

		#lumen_design {
			margin: 0px 30px
		}

		#equistream {
			margin: -35px -39px 0px -50px;
		}

		.two_column,
		.three_column {
			-webkit-column-count: 1;
			/* Chrome, Safari, Opera */
			-moz-column-count: 1;
			/* Firefox */
			column-count: 1;
		}

		.values {
			font-size: 15px;
			letter-spacing: 0px;
		}

		.about_statement {
			padding: 15px;
			font-size: 18px;
			line-height: 26px
		}

		.split_tip {
			margin-top: 260px;
		}

		.power_trialysis_slim_lumen {
			margin: 20px 0 20px 30px;
		}

		#power_trialysis_slim_product {
			margin: 0 0 -225px -25px;
		}

		.powerglidepro_hand {
			margin: -50px -25px 0 0 !important;
		}

		.powerglidepro_blood_control {
			margin: -50px 0 -70px 20px;
		}

		#overview img.allpoints_dck_hero_m {
			margin: 40px 30px 25px 20px;
		}

		.dck_icons_m {
			margin: 0px auto;
		}

		.white {
			color: purple;
		}

		#nautilus_feature_hero {
			margin: 50px 0px
		}

		.visual {
			font-size: 35px;
			line-height: 30px;
		}

		.visualize_left {
			width: 85%;
			margin-top: 30px;
		}

		.visualize_right {
			width: 85%;
			margin-top: 30px;
		}

		#y_sensor_diamond {
			margin-top: 0px;
		}

		.pinpoint {
			font-size: 25px;
		}

		.sr8-pinpoint-needle {
			margin-top: 30px;
			float: left;
		}

		#diamond {
			float: none;
			margin: 0px 0 -10px -10px;
		}

		#icon_mobile {
			display: block;
			margin: 40px 0 0 -10px;
			float: none;
		}

		.powermidline_hero {
			margin: 0px -15px 0px 40px !important;
		}

		.powermidline_product {
			margin: -40px -26px -150px 40px !important;
		}

		.securis_hero {
			margin: 0px 30px 0 10px !important;
		}

		.comfort_pad {
			margin: -15px 50px 0 0px;
		}

		.allpoints_dck_kits {
			margin: 0px !important;
		}

		.sentrinex_m {
			margin-left: 50px;
		}

		.purell_m {
			margin-left: 60px;
		}

		blockquote .sub_box_blue {
			position: relative;
			background-color: rgba(0, 104, 167, 1.00);
			padding: 50px;
			margin: 50px -55px 20px -60px;
			height: auto;
		}

		blockquote .sub_box_green {
			position: relative;
			background-color: rgba(102, 184, 60, 1);
			padding: 50px;
			margin: 50px -55px 20px -60px;
			height: auto;
		}

		blockquote .sub_box_bluegreen {
			position: relative;
			background-color: rgba(0, 126, 146, 1);
			padding: 50px;
			margin: 50px -55px 20px -60px;
			height: auto;
		}

		.quote_after {
			display: none;
		}

		.securis_comparison_header h4 {
			color: rgba(102, 184, 60, 1) !important;
			background-color: rgba(255, 255, 255, 1) !important;
			font-size: 25px;
		}

		.securis_comparison_bkgrnd {
			background: none;
			height: auto;
		}

		.securis_comparison_bkgrnd p {
			margin: -10px 10px 20px 20px !important;
			color: rgba(102, 184, 60, 1) !important;
		}

		.securis_comparisons {
			display: none !important;
		}

		.securis_tape {
			display: block;
		}

		.securis_centurion {
			display: block;
		}

		.securis_tegaderm {
			display: block;
		}

		.bdr_l_r {
			border-left: none;
			border-right: none;
		}

		.securis_check {
			display: none !important;
		}

		.power_picc_provena_hero {
			margin: 0px -20px 0px 0px !important;
			border-bottom: solid 1px #878787;
		}

		.bm-50 {
			margin-bottom: 0px;
		}

		#overview img.nautilus_hero {
			margin: 0px 10px 30px 0px;
		}

		.nautilus_child {
			margin: 0px -20px 15px 0px
		}

		.tip_confirmation p {
			margin: 0px
		}

		#overview img.powerglide-st_hero {
			margin: 20px 0 60px 0px;
		}

		.coil-mobile {
			display: block;
		}

		.coil {
			display: none;
		}

		#overview img.struxure_hero {
			margin: 0px -15px 0 0px;
		}

		.halycon-3cg {
			margin: 25px 0 0px 0px;
		}

		.nautilus_trademark {
			font-size: 12px;
		}

		.halcyon_headline {
			font-size: 25px;
			margin: 0px 0 30px;
		}

		#access h4 {
			margin: 170px 0 30px;
		}

		#confirm h4 {
			margin: 45px 0 0px
		}

		.halycon_confirm {
			margin-bottom: -7px;
		}

		.halcyon_3cg {
			margin: 5px 0 0px 0px;
		}

		.halcyon_maxp {
			margin: 5px 0 0px 0px;
		}

		#overview img.halcyon_hero {
			margin: 40px -15px 0 0px;
		}
	}

	@media (min-width: 400px) and (max-width:725px) {

		/*404 page*/
		.not-found {
			max-height: 419px !important;
		}

		.not-found-image {
			display: none;
		}

		.not-found-text h3 {
			display: none;
		}

		.not-found-text h4 {
			display: none;
		}

		.not-found-mobile {
			display: inline-block;
		}

		.not-found-mobile h3 {
			display: inline-block;
			font-size: 40px;
			line-height: 40px;
		}

		.not-found-mobile h4 {
			display: inline-block;
			margin-top: 10px
		}
	}

	/* TABLET */

	@media (max-width: 846px) {
		#container {
			width: 98%;
		}

		#content_hldr.fullpage #content #pagecontent {
			padding: 20px;
		}

		#content_hldr.jquery-tabs #main>div {
			padding: 0px 0px 50px;
			clear: both;
			margin: 0 0 50px;
		}

		#content_hldr.jquery-tabs #main>div.beginPage,
		#content_hldr.jquery-tabs #main>div.endPage {
			border: none;
			margin: 0px;
			padding: 0px;
		}

		#content_hldr.jquery-tabs #main h3 {
			border-bottom: 1px solid rgba(120, 57, 128, 1);
			font-weight: bold;
			padding: 0 0 2px;
		}

		#content_hldr.jquery-tabs #main>div.statlock_hero {
			padding: 0px 0px 50px;
			clear: both;
			margin: 0 0 -40px;
		}

		#header {
			height: 0px;
		}

		#cat_title_hldr {
			padding: 20px 0 0 0;
		}

		#bbnone {
			border-bottom: none;
			font-weight: bold;
			padding: 0 0 2px;
		}

		#ct_login {
			display: none;
		}

		.feature_headline {
			color: rgba(120, 57, 128, 1);
			font-size: 25px;
			margin-left: -20px;
			margin-top: 0;
		}

		#features .headline_section {
			margin: 0px;
		}

		#features .headline_section p,
		#sr1 .headline_section p,
		#sr2 .headline_section p,
		#small_patients .headline_section p {
			color: rgba(120, 57, 128, 1);
			margin-left: -20px;
		}

		.accucath-full-color-icon {
			display: block;
		}

		.accucath-white-icon {
			display: none;
		}

		.hidden_sm {
			display: none;
		}

		/* Overview */
		.responsiveLogo {
			display: block;
			margin: 20px 30px;
		}

		.pageSlogan {
			display: block;
			margin: 0;
		}

		#overview p:first-of-type {
			color: #783980;
			padding: 25px 0 0 0px;
		}

		#overview ul.nobullet li {
			color: #783980
		}

		.background-purple,
		.background-blue,
		.background-bluegreen {
			padding: 40px 0 0 0;
			margin: -15px 0 0 0;
			background: #FFFFFF;
			min-height: auto;
		}

		.background-green {
			padding: 0 0 0 0;
			margin: 0px 0 0 0;
			background: #FFFFFF;
			min-height: auto;
		}

		#overview .bottom_box {
			opacity: 1;
			color: #783980;
			padding: 0px;
			margin: 0px;
		}

		#overview p.values {
			font-size: 22px;
			padding: 0px;
		}

		#overview p.about_statement {
			padding: 20px;
			font-size: 22px;
			color: white;
		}

		.white {
			color: purple;
		}

		.sub_box {
			margin-right: -15px;
			padding: 30px 25px;
		}

		.subtitle_block_blue,
		.subtitle_block_purple,
		.subtitle_block_drk_gray,
		.subtitle_block_bluegreen {
			color: rgba(255, 255, 255, 1.00)
		}

		/* Specifications and Kit Components */
		#specifications,
		#kit_components {
			overflow: scroll
		}

		/*HIDE LEFT COLUMN*/
		#pagecontent {
			padding: 15px;
			margin: 0;
		}

		#col_hldr {
			display: none;
		}

		/* MENU */
		#navigation {
			width: 100%;
		}

		#navigation li {
			width: 100%;
			text-align: right;
		}

		#navigation li a {
			padding-right: 12px;
			padding-bottom: 10px;
			margin-left: -12px;
			border-bottom: 1px solid #ececec;
		}

		#product_selection h3.tip_confirmation_location {
			font-size: 29px;
		}

		#footer nav ul li:last-child {
			float: none;
			margin: 25px 10px 0px 10px;
		}

		#footer .tagline {
			display: block;
		}

		/* A little bit of logo padding */
		.navbar-brand>img {
			padding-top: 15px;
			/* padding-top: 19px;padding-left: 12px;max-width: 80%; */
		}

		#footer a,
		#footer a:link {
			margin: 0 21px 0 0px;
		}

		/*404 page*/
		.not-found {
			max-height: 419px !important;
		}

		.not-found-image {}

		.not-found-image img {
			margin-right: -23px;
			max-width: 360px;
		}

		.not-found-text h3 {
			font-size: 30px !important;
			line-height: 40px !important;
		}

		.not-found-text h4 {}

		#instructions {
			margin: 0px 0px 15px;
			font-size: 14px;
			line-height: 24px !important;
		}

		#product_code {
			font-size: 16px;
			width: 340px;
			background-color: #FFF;
			height: 35px;
			padding: 6px 12px 6px 6px;
			line-height: 1.42857;
			background-image: none;
			border: 1px solid #CCC;
			border-radius: 4px;
			box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.075) inset;
			transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
		}

		#inst {
			width: 85%;
		}

		#lang {
			font-size: 22px;
		}

		#language_selection {
			text-align: right;
			margin-top: 0px;
			text-align: right !important
		}

		#topnav_hldr {
			margin-top: 20px;
			margin-left: 0px;
		}

		.quote-div {
			width: 88%;
		}

		/*Product Navigation*/
		#product_nav ul,
		#resources_nav ul {
			background: none;
			margin: 75px auto;
		}

		#product_nav li,
		#resources_nav li {
			margin: 0 0 25px 0
		}

		/*Catalog*/
		#product_catalog #selection {
			width: 100%;
		}

		#product_catalog #results {
			position: relative;
			top: inherit;
			width: 100%;
		}

		#product_catalog .full,
		#product_catalog .half {
			width: 100%;
		}

		#product_catalog #selection span {
			margin: 0 3px 10px 0
		}

		.tm-10,
		.tm-20,
		.tm-30,
		.tm-40,
		.tm-50,
		.tm-60,
		.tm-70,
		.tm-80,
		.tm-85,
		.tm-90,
		.tm-100,
		.tm-110,
		.tm-120,
		.tm-130,
		.tm-140,
		.tm-150,
		.tm-160,
		.tm-170,
		.tm-180,
		.tm-190,
		.tm-200,
		.tm-235,
		.tm10,
		.tm20,
		.tm30,
		.tm40,
		.tm50,
		.tm80 {
			margin-top: 0px;
		}

		.bm70 {
			margin-bottom: 0px;
		}

		.bm-50 {
			margin-bottom: 0px;
		}

		.lm25,
		.lm40 {
			margin-left: 0px;
		}

		/*COLUMNS*/
		.col_l_flush {
			margin-right: 20px;
			margin-left: -25px
		}

		.col_r_flush {
			margin-right: -15px;
			margin-left: 20px
		}

		.col_l_flush_nm {
			margin-left: -25px
		}

		.col_r_flush_nm {
			margin-right: -25px;
		}

		.col_full_flush {
			margin-right: -25px;
			margin-left: 20px
		}

		.col_l_full_flush_extend {
			margin-right: -50px;
			margin-left: -25px
		}

		.col_r_full_flush_extend {
			margin-right: -25px;
			margin-left: -50px
		}

		.fullpage .col_l {
			margin-right: 50px;
		}

		.fullpage .col_r {
			margin-left: 50px
		}

		.fullpage .col_l_flush {
			margin-right: 50px;
			margin-left: -50px
		}

		.fullpage .col_r_flush {
			margin-right: -50px;
			margin-left: 50px
		}

		.fullpage .col_full_flush {
			margin-right: -50px;
			margin-left: -50px
		}

		.sherlock_body {
			margin: 0px -15px 0 -20px;
		}

		.training_man {
			margin: 10px -15px 0px 0px;
		}

		.extension-set {
			margin-top: -110px;
		}

		.picc_sv {
			margin-top: -200px;
		}

		.powergroshong {
			margin-top: 0px;
			margin-right: -15px;
		}

		.needle_body {
			margin: 0px -15px 0 -270px;
		}

		.transportable {
			margin-top: -140px;
		}

		.guardiva_arm {
			margin: -20px -15px 0 -205px;
		}

		.site_scrub_devices {
			margin-left: -25px;
			max-width: 747px;
			align-content: center;
		}

		.statlock_hero {
			margin: 28px 0px -22px 40px;
		}

		.catheter_retention_block {
			padding: 0px 39px 0px 19px;
		}

		.catheter_retention {
			padding: 35px 3px 81px 0px;
		}

		.right_arrow {
			margin-left: -20px;
			margin-top: 240px;
			position: absolute;
		}

		.piv_select_hero {
			margin: 80px 0px 0px 20px
		}

		.statlock_people {
			padding: 60px 25px 40px 25px;
		}

		.tape_complications {
			padding: 0 0px 35px 60px;
		}

		.complication_therapy {
			padding: 30px;
		}

		.cdc_guidelines {
			padding: 62px 0px 75px 0px;
		}

		.arrow {
			margin: -220px 0 0 -0px;
		}

		.tape {
			margin-top: -30px;
		}

		.cdc_guidelines img {
			padding: 25px;
		}

		.graph_71 {
			margin-top: -30px;
			background-color: #dff2e0;
		}

		.tape_roll {
			padding: 15px;
		}

		.piv_premium_hero {
			margin: 0px 0px 0px 40px
		}

		.other_epidural_arrow_hero {
			margin-right: -35px;
		}

		.max_barrier_arm {
			margin: -20px -15px 0px -565px;
		}

		.max_barrier_arm_text {
			margin: 220px 0px 0px 0px;
		}

		.radstic_img {
			margin: -50px 15px -30px -10px;
		}

		.microez_img {
			margin: -50px 15px -30px -10px;
		}

		.stainless_tip {
			margin: 55px 0px 160px 0;
		}

		.kink_resistant {
			margin: 0px 0px 115px 0;
		}

		.peel_apart {
			margin-top: 50px
		}

		.excalibur {
			margin: -50px 0 -45px 0;
		}

		.power_trialysis_tri {
			margin: 100px -41px 0px 0;
		}

		.optimal_performance {
			margin-top: -450px;
		}

		.lumen_design {
			margin: 30px 0
		}

		.equistream {
			margin: -50px -54px -50px -100px;
		}

		.professional_sports {
			border: none;
			padding: 20px;
		}

		.recreation {
			border: none;
			padding: 45px 0px;
		}

		.working {
			padding: 20px 0px;
		}

		.cultural {
			padding: 20px 0px 20px 60px;
		}

		.power_trialysis_slim_lumen {
			margin: -45px 40px 20px 40px;
		}

		.power_trialysis_slim_product {
			margin: -400px -25px -360px 0px
		}

		.accucath_hero {
			margin: 0px -15px 30px 0px !important;
		}

		.allpoints_dck_hero {
			display: none;
		}

		.allpoints_dck_hero_m {
			display: block;
			margin: 0px 30px 0 40px;
		}

		.allpoints_dck_kits {
			margin-top: -50px;
		}

		.fade_swap {
			display: none;
		}

		.visual_guide_checklist {
			display: block;
		}

		#allpoints h3 {
			margin: 50px 0 -10px 0;
			line-height: 25px;
			color: #783980;
		}

		.nautilus_hero {
			margin: 100px 0px 65px 30px
		}

		.nautilus_feature_hero {
			margin: 100px 0px 65px 30px
		}

		.nautilus p {
			color: rgba(120, 57, 128, 1);
		}

		.consult_statement {
			margin: 0px;
		}

		.powerglidepro_hand {
			margin: -100px -25px 0 0;
		}

		.visual_tip_confirmation {
			margin-top: 30px;
		}

		.y_sensor_diamond {
			margin-top: -245px;
		}

		#site-rite-8 h3 {
			margin: 0px;
			color: #783980;
		}

		#content_hldr.jquery-tabs #main h3 {
			border-bottom: none;
		}

		.site-rite-8-hero {
			margin: -15px;
		}

		.powerglidepro_hero {
			margin: 0px;
		}

		.powermidline_hero {
			margin: 0px -15px 0px 40px;
		}

		.powermidline_product {
			margin: -40px -26px -85px 40px;
		}

		.powermidline .pl40,
		.powermidline .pl25 {
			padding: 0px;
		}

		.powermidline .tm-10,
		.powermidline .tm-20 {
			margin-top: -15px;
		}

		#features .headline_section.purple,
		#sr1 .headline_section.purple,
		#sr2 .headline_section.purple,
		#small_patients .headline_section.purple,
		#features .headline_section.blue,
		#features .headline_section.green,
		#features .headline_section.black {
			background-color: white;
			margin: 45px -15px 10px 0;
		}

		.site_rite_8_product {
			margin: 0px;
		}

		.site_rite_8_visualize {
			display: none;
		}

		.site_rite_8_virtual {
			display: none;
		}

		#site-rite-8 h3 {
			margin: 0px;
			color: purple;
		}

		.icon {
			display: none;
		}

		.icon_mobile {
			display: block;
			margin: -40px 0 0 -10px;
			float: right;
		}

		.diamond {
			float: right;
			margin: 0px 0 -10px -50px;
		}

		.securis_arm {
			margin: 0px;
			border-top: 1px #676767 solid;
			border-bottom: 1px #878787 solid;
		}

		.securis_comparison_header h4 {
			color: rgba(255, 255, 255, 1);
			background-color: rgba(102, 184, 60, 1);
			margin: -25px -15px 0 -23px;
		}

		.securis_comparison_bkgrnd {
			margin: 0 -15px 0 -23px;
		}

		.securis_comparison_bkgrnd p {
			margin: -15px 80px 0 260px;
			color: #fff;
		}

		.securis_overflow_container {
			height: auto;
		}

		.diamond_hero {
			margin: -20px -15px 0 40px !important;
		}

		.power_picc_provena_hero {
			margin: -120px -20px 0px 0px;
			border-bottom: solid 1px #878787;
		}

		.quote_begin {
			display: none;
		}

		.sub_box.right {
			padding: 30px 15px 30px 35px;
			margin: 15px -15px 15px 0px;
		}

		.powerglide-st_hero {
			margin: 80px 0px 160px 40px;
		}

		.powerglide-st-introducers {
			margin: 0;
		}

		.struxure_hero {
			margin: 0px -15px 30px 90px;
		}

		.coil-mobile {
			display: block;
			margin-left: 10px;
		}

		.coil {
			display: none;
		}

		.halcyon_confirm {
			margin-bottom: -3px;
		}
	}

	/* IN BETWEEN TABLET AND FULL SIZE */

	@media (min-width: 769px) {
		#container {
			width: 98%;
		}

		/*Navigation*/
		#resources_nav li {
			margin: 45px 80px 45px 0;
		}

		#instructions {
			margin: 0px 0px 15px;
			font-size: 16px;
			line-height: 24px !important;
		}

		#product_code {
			font-size: 16px;
			width: 500px;
			background-color: #FFF;
			height: 35px;
			padding: 6px 12px 6px 6px;
			line-height: 1.42857;
			background-image: none;
			border: 1px solid #CCC;
			border-radius: 4px;
			box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.075) inset;
			transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
		}

		#inst {
			width: 525px;
		}

		#language_selection {
			text-align: right;
			margin-top: -35px;
		}

		#lang {
			font-size: 29px;
		}

		.quote-div {
			width: 95%;
		}

		.responsiveLogo {
			display: none;
		}

		.pageSlogan {
			display: none;
		}

		/*Catalog*/
		#product_catalog #selection {
			width: 50%;
		}

		#product_catalog #results {
			width: 45%;
		}

		#product_catalog .full,
		#product_catalog .half {
			width: 100%;
		}

		.sherlock_body {
			margin: 0px -40px 0 -20px;
		}

		.slogan {
			margin: 0px;
		}

		.training_header {
			margin-top: -40px;
		}

		.training_man {
			margin: 120px -25px 0px 0px;
		}

		.extension-set {
			margin: -245px -25px 0 -100px;
		}

		.powergroshong {
			margin-top: -250px;
		}

		.needle_body {
			margin: 0px -25px 0 -270px;
		}

		.guardiva_arm {
			margin: -172px -25px 0 -110px;
		}

		.site_scrub_devices {
			margin-left: -25px;
			max-width: 735px;
			align-content: center;
		}

		.piv_select_hero {
			margin: 150px 0px 0px 20px;
		}

		.accucath_hero {
			margin: 0px -40px 30px 48px;
		}

		.diamond-heart {
			margin-top: -45px;
		}

		.power_picc_provena_hero {
			margin: -43px -40px -88px 0px;
		}

		.coil-mobile {
			display: none;
		}

		.halcyon_confirm {
			margin-bottom: -7px;
		}
	}

	@media (min-width: 769px) and (max-width:1260px) {

		/*404 page*/
		.not-found {
			max-height: 419px !important;
		}

		.not-found-image {}

		.not-found-image img {
			margin-right: -23px;
			max-width: 385px;
		}

		.not-found-text h3 {
			font-size: 40px !important;
			line-height: 40px !important;
		}

		.not-found-text h4 {}
	}

	@media (max-width:320px) {
		.navbar-default .navbar-brand {
			max-width: 220px;
		}
	}

	/* FULL SIZE */

	@media (min-width: 1000px) {
		#container {
			width: 1000px;
		}

		.elabel-background {

			min-height: 300px;
			cursor: default
		}

		#language_selection {
			text-align: right;
			margin-top: -35px;
		}

		#instructions {
			margin: 0px 0px 15px;
			font-size: 16px;
			line-height: 24px !important;
		}

		#product_code {
			font-size: 16px;
			width: 500px;
			background-color: #FFF;
			height: 35px;
			padding: 6px 12px 6px 6px;
			line-height: 1.42857;
			background-image: none;
			border: 1px solid #CCC;
			border-radius: 4px;
			box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.075) inset;
			transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
		}

		#inst {
			width: 525px;
		}

		#lang {
			font-size: 29px;
		}

		#about_us h2 {
			margin: 0px;
		}

		.quote-div {
			width: 42%;
		}

		.responsiveLogo {
			display: none;
		}

		.pageSlogan {
			display: none;
		}

		/*Catalog*/
		#product_catalog #selection {
			width: 60%;
		}

		#product_catalog #results {
			width: 35%;
		}

		#product_catalog .full {
			width: 100%;
		}

		#product_catalog .half {
			width: 49%;
		}

		.transportable {
			margin-top: -140px;
		}

		.basic_mode .right_side {
			margin-left: 90px;
		}

		.sherlock_body {
			margin: -165px -53px 0 -65px;
		}

		.slogan {
			margin: 0px;
		}

		.picc_sv {
			margin-top: -200px;
		}

		.site_scrub_devices {
			margin-left: -25px;
			max-width: 735px;
			align-content: center;
		}

		.statlock_hero {
			margin: 28px 0px -22px 40px;
		}

		.catheter_retention_block {
			padding: 0px 38px 0px 19px;
		}

		.catheter_retention {
			padding: 26px 0px 61px 0px;
		}

		.right_arrow {
			margin-left: -20px;
			margin-top: 240px;
			position: absolute;
		}

		.piv_select_hero {
			margin: 150px 0px 0px 20px
		}

		.statlock_people {
			padding: 60px 25px 40px 25px;
		}

		.tape_complications {
			padding: 0 0px 35px 30px;
		}

		.complication_therapy {
			padding: 30px;
		}

		.cdc_guidelines {
			padding: 37px 0px 84px 0px;
		}

		.cdc_guidelines img {
			padding: 25px;
		}

		.arrow {
			margin: -250px 0 0 0px;
		}

		.tape {
			margin-top: -30px;
		}

		.graph_71 {
			margin-top: -60px;
			background-color: #dff2e0;
		}

		.piv_premium_hero {
			margin: 114px 0px 0px 40px
		}

		.piv_plus_hero {
			margin: -0px 0px 0px 20px
		}

		.piv_ultra_hero {
			margin: 100px 0px 0px 40px
		}

		.piv_bd_hero {
			margin: 67px 0px 0px 40px
		}

		.pix_smiths_hero {
			margin: 128px 0px 0px 40px;
		}

		.piv_ultra_nt_hero {
			margin: 128px 0px 0px 40px
		}

		.pcl_picc_plus_hero {
			margin: -40px -30px 0px 50px
		}

		.ped_iv_ultra_hero {
			margin: 132px 0px 0px 0px
		}

		.ped_iv_ultra_nt_hero {
			margin: 60px 0px 0px 0px;
		}

		.ped_iv_premium_hero {
			margin: 134px 0px 0px 40px;
		}

		.ped_iv_select_hero {
			margin: 87px 0px 0px 35px
		}

		.ped_iv_smith_hero {
			margin: 106px 0px 0px 40px;
		}

		.ped_iv_arterial_hero {
			margin: 177px 0px 0px 20px;
		}

		.other_epidural_arrow_hero {
			margin-right: -45px;
		}

		.arterial_arrow_hero {
			margin: 10px 0px 0px 20px;
		}

		.arterial_plus_hero {
			margin: 70px 0px 0px 20px;
		}

		.arterial_select_hero {
			margin: 225px 0px 0px 20px;
		}

		.arterial_select_hero {
			margin: 112px 0px 0px 20px;
		}

		.neonate_iv_ultra_hero {
			margin: 81px 0px 0px 20px;
		}

		.neonate_ii_hero {
			margin: 25px 0px 0px 20px;
		}

		.neonate_picc_hero {
			margin: -41px 0px 0px 20px;
		}

		.max_barrier_arm {
			margin: -125px -25px 0px -555px;
		}

		.max_barrier_arm_text {
			margin: 120px 0px 0px 0px;
		}

		.radstic_img {
			margin: -40px 15px -30px -10px;
		}

		.microez_img {
			margin: -45px 15px -30px -10px;
		}

		.stainless_tip {
			margin: 55px 0px 160px 0;
		}

		.kink_resistant {
			margin: 0px 0px 99px 0;
		}

		.microez_img {
			margin: -50px 15px -30px -10px;
		}

		.peel_apart {
			margin-top: 50px
		}

		.power_trialysis_tri {
			margin: 100px -51px -60px 0;
		}

		.optimal_performance {
			margin-top: -450px;
		}

		.lumen_design {
			margin: 50px 0 30px 0
		}

		.equistream {
			margin: -50px -64px -50px -100px;
		}

		.professional_sports {
			border: none;
			padding: 60px 20px;
		}

		.recreation {
			border: none;
			padding: 45px 0px;
		}

		.cultural {
			padding: 20px 0px 20px 60px;
		}

		.working {
			padding: 20px 0px;
		}

		.solo_power {
			margin-left: -60px;
		}

		.duet_spiral {
			margin-top: -200px
		}

		.power_trialysis_slim_lumen {
			margin: -25px 40px 20px 40px;
		}

		.power_trialysis_slim_product {
			margin: -205px -25px -200px -323px;
		}
	}

	/* FULL SIZE */

	@media (min-width: 1260px) {
		#container {
			width: 1260px;
		}

		/*Search*/
		.elabel-background {

			min-height: 300px;
			cursor: default
		}

		#language_selection {
			text-align: right;
			margin-top: -35px;
		}

		#instructions {
			margin: 0px 0px 15px;
			font-size: 16px;
			line-height: 24px !important;
		}

		#product_code {
			font-size: 16px;
			width: 500px;
			background-color: #FFF;
			height: 35px;
			padding: 6px 12px 6px 6px;
			line-height: 1.42857;
			background-image: none;
			border: 1px solid #CCC;
			border-radius: 4px;
			box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.075) inset;
			transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
		}

		#inst {
			width: 525px;
		}

		#lang {
			font-size: 29px;
		}

		#about_us h2 {
			margin: 0px;
		}

		.responsiveLogo {
			display: none;
		}

		.pageSlogan {
			display: none;
		}

		.quote-div {
			width: 45%;
		}

		.transportable {
			margin-top: -140px;
		}

		.needle_body {
			margin: 0px -15px 0 -270px;
		}

		.basic_mode {
			margin-left: 115px;
		}

		.basic_mode .right_side {
			margin-left: 45px;
		}

		.sherlock_body {
			margin: -165px -73px 0 -40px;
		}

		.slogan {
			margin: 0px;
		}

		.training_man {
			margin: 20px -25px 0px 0px;
		}

		.extension-set {
			margin: -266px -25px 0 -100px;
		}

		.powergroshong {
			margin-top: -270px;
		}

		.site_scrub_devices {
			margin-left: -25px;
			max-width: 985px;
		}

		.statlock_hero {
			margin: 28px 0px -22px 40px;
		}

		.piv_select_hero {
			margin: 18px 0px 0px 20px
		}

		.tm215 {
			margin-top: 0px;
		}

		.right_arrow {
			margin-left: -40px;
			margin-top: 240px;
			position: absolute;
		}

		.catheter_retention_block {
			padding: 45px 38px 103px 39px;
		}

		.catheter_retention {
			background-color: #dff2e0;
			padding: 10px 18px 86px 18px;
		}

		.statlock_people {
			padding: 38px;
		}

		.tape_complications {
			padding: 0 30px 30px 30px;
		}

		.complication_therapy {
			padding: 38px;
		}

		.cdc_guidelines {
			padding: 15px 38px 30px 39px;
		}

		.cdc_guidelines img {
			padding: 25px;
		}

		.arrow {
			margin: -240px 0 0 -41px;
		}

		.tape {
			margin-top: -30px;
		}

		.tape_roll {
			padding: 35px;
		}

		.graph_71 {
			margin-top: -46px;
			background-color: #dff2e0;
		}

		.piv_premium_hero {
			margin: 114px 0px 0px 40px
		}

		.piv_plus_hero {
			margin: -75px 0px 0px 20px
		}

		.piv_plus {
			margin-bottom: 135px;
		}

		.piv_ultra_hero {
			margin: 79px 0px 0px 40px
		}

		.piv_bd_hero {
			margin: 22px 0px 0px 40px
		}

		.pix_smiths_hero {
			margin: 82px 0px 0px 40px;
		}

		.piv_ultra_nt_hero {
			margin: 66px 0px 0px 40px
		}

		.pcl_picc_plus_hero {
			margin: -85px -30px 0px 50px
		}

		.ped_iv_ultra_hero {
			margin: 132px 0px 0px 0px
		}

		.ped_iv_ultra_nt_hero {
			margin: 60px 0px 0px 0px;
		}

		.ped_iv_premium_hero {
			margin: 134px 0px 0px 40px;
		}

		.ped_iv_select_hero {
			margin: 87px 0px 0px 35px
		}

		.ped_iv_smith_hero {
			margin: 126px 0px 0px 40px;
		}

		.ped_iv_arterial_hero {
			margin: 60px 0px 0px 20px;
		}

		.other_epidural_arrow_hero {
			margin-right: -45px;
		}

		.arterial_arrow_hero {
			margin: 30px 0px 0px 20px;
		}

		.arterial_plus_hero {
			margin: -7px 0px 0px 20px;
		}

		.arterial_select_hero {
			margin: 30px 0px 0px 20px;
		}

		.arterial_select_hero {
			margin: 112px 0px 0px 20px;
		}

		.neonate_iv_ultra_hero {
			margin: 43px 0px 0px 20px;
		}

		.neonate_ii_hero {
			margin: 25px 0px 0px 20px;
		}

		.neonate_picc_hero {
			margin: -41px 0px 0px 20px;
		}

		.max_barrier_arm {
			margin: -125px -25px 0px -490px;
		}

		.max_barrier_arm_text {
			margin: 120px 0px 0px 0px;
		}

		.radstic_img {
			margin: -40px 50px -30px -10px;
		}

		.stainless_tip {
			margin: 55px 0px 160px 0;
		}

		.kink_resistant {
			margin: 0px 0px 155px 0;
		}

		.microez_img {
			margin: -50px 15px -30px -10px;
		}

		.peel_apart {
			margin-top: 60px
		}

		.power_trialysis_product {
			margin: -80px -25px 0px -50px;
		}

		.power_trialysis_slim_product {
			margin: -160px -25px -290px 30px
		}

		.power_trialysis_tri {
			margin: 10px -51px -60px 0;
		}

		.optimal_performance {
			margin-top: -450px;
		}

		.lumen_design {
			margin: 50px 0 30px 0
		}

		.equistream {
			margin: -50px -64px -50px -100px;
		}

		.professional_sports {
			border: none;
			padding: 20px;
		}

		.working {
			padding: 20px 0px;
		}

		.recreation {
			border: none;
			padding: 45px 0px;
		}

		.cultural {
			padding: 20px 0px 20px 60px;
		}

		.solo_power {
			margin-left: -60px;
		}

		.duet_spiral {
			margin-top: -150px
		}

		.rollstand {
			margin-bottom: 175px;
		}

		.nautilus_feature_hero {
			margin: 60px 10px 0 50px
		}

		.y_sensor_diamond {
			margin-top: 0px;
		}

		.visual_tip_confirmation {
			margin-top: 0px;
		}

		.visualize_left {
			width: 55%;
		}

		.visualize_right {
			width: 55%;
		}

		.sr8-pinpoint-needle {
			margin-top: 0px;
		}

		.securis_arm {
			margin: -104px 0 -210px
		}

		.nautilus_hero {
			margin: 100px -20px 90px 30px
		}

		.tip_confirmation p {
			margin: 120px 0px 0px 40px
		}

		.nautilus_child {
			margin: 18px -20px -13px 0
		}

		.powerglide-st-introducers {
			margin: -215px 0px -100px 0;
			position: relative;
		}

		.halcyon_confirm {
			margin-bottom: -8px;
		}
	}
@charset "utf-8";
#pagecontent {
  position: relative;
  display: block;
  background: #fff;
  font-size: 17px;
  line-height: 22px;
  padding: 25px;
  margin: 0 0 0 250px;
}

#pagecontent .purple-background {
  background: transparent radial-gradient(ellipse at 70% 40px, #7E53A3 0%, #4D1E75 100%) repeat scroll 0% 0%;
  padding: 0px;
}

#pagecontent.full {
  margin: 0;
  padding: 0;
}

/*Overview*/

#overview .tagline {
  margin: 0 0 20px 0;
  font-size: 14px;
  font-weight: bold;
  width: 200px;
  display: inline-block;
}

#overview img {
  float: right;
  position: relative;
}

#overview img.nologo {
  float: right;
  position: relative;
  margin-top: 0;
  margin-bottom: 10px;
}

#overview ul {
  margin: 0 20px 30px 0;
  padding: 0 0 0 40px;
  z-index: 1;
  position: relative;
  min-height: 305px;
}

#overview ul.nologo {
  margin: 0 20px 0 0;
  padding: 40px 0 0 0;
}

#overview ul li {
  list-style: none;
  margin: 0;
  padding: 0 0 15px 0;
  line-height: 20px;
}

#overview ul.nobullet li {
  line-height: 30px;
}

/*Overview P2 transition*/

#overview p.summary {
  font-size: 25px;
  font-style: italic;
  line-height: 30px;
  padding: 50px 0 0 40px;
  color: white;
}

#overview .sizes {
  margin: 0;
  padding: 7px 0 0 0;
  _padding: 3px 0 0 0;
  border-top: 1px solid #582c5f;
  clear: right;
}

#overview .bottom_box {
  margin: 10px -40px -40px;
  padding: 15px 80px;
  _padding: 15px 80px;
  clear: right;
  background-color: #FFFFFF;
  opacity: 0.3;
  color: black;
}

#overview .sizes span {
  margin-right: 8px;
}

#overview p.values {
  font-size: 24px;
  color: rgba(120, 57, 128, 1);
  letter-spacing: 3px;
  padding: 0px;
  text-align: center;
  width: 100%;
}

#overview h3 {
  font-size: 26px;
  color: white;
}

#overview p.about_statement {
  padding: 20px;
  font-size: 24px;
  color: white;
  font-weight: lighter;
  font-style: italic;
  text-align: center;
  line-height: 30px;
  background: rgb(226, 238, 184);
  /* Old browsers */
  background: -moz-linear-gradient(left, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
  /* FF3.6+ */
  background: -webkit-gradient(linear, left top, right top, color-stop(0%, rgba(226, 238, 184, 1)), color-stop(25%, rgba(136, 211, 154, 1)), color-stop(50%, rgba(142, 208, 176, 1)), color-stop(75%, rgba(133, 202, 191, 1)), color-stop(100%, rgba(110, 190, 196, 1)));
  /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(left, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
  /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(left, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
  /* Opera 11.10+ */
  background: -ms-linear-gradient(left, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
  /* IE10+ */
  background: linear-gradient(to right, rgba(226, 238, 184, 1) 0%, rgba(136, 211, 154, 1) 25%, rgba(142, 208, 176, 1) 50%, rgba(133, 202, 191, 1) 75%, rgba(110, 190, 196, 1) 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e2eeb8', endColorstr='#6ebec4', GradientType=1);
  /* IE6-8 */
}

#overview div.two_column p {
  font-size: 16px;
  color: #4d4d4f;
  font-style: normal;
  line-height: normal;
  padding: 0;
}

.background-purple {
  padding: 40px;
  margin: -25px;
  background: rgba(126, 83, 163, 1);
  /* Old Browsers */
  background: -moz-radial-gradient(70% 40px, ellipse cover, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
  /* FF3.6+ */
  background: -webkit-gradient(radial, 70% 40px, 0px, center center, 100%, color-stop(0%, rgba(126, 83, 163, 1)), color-stop(100%, rgba(77, 30, 117, 1)));
  /* Chrome, Safari4+ */
  background: -webkit-radial-gradient(70% 40px, ellipse cover, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
  /* Chrome10+,Safari5.1+ */
  background: -o-radial-gradient(70% 40px, ellipse cover, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
  /* Opera 11.10+ */
  background: -ms-radial-gradient(70% 40px, ellipse cover, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
  /* IE 10+ */
  background: radial-gradient(ellipse at 70% 40px, rgba(126, 83, 163, 1) 0%, rgba(77, 30, 117, 1) 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#7e53a3', endColorstr='#4d1e75', GradientType=1);
  /* IE6-9 fallback on horizontal gradient */
}

.background-blue {
  padding: 40px;
  margin: -25px;
  background: rgba(0, 104, 167, 1);
  /* Old Browsers */
  background: -moz-radial-gradient(70% 40px, ellipse cover, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
  /* FF3.6+ */
  background: -webkit-gradient(radial, 70% 40px, 0px, center center, 100%, color-stop(0%, rgba(55, 156, 219, 1)), color-stop(100%, rgba(0, 104, 167, 1)));
  /* Chrome, Safari4+ */
  background: -webkit-radial-gradient(70% 40px, ellipse cover, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
  /* Chrome10+,Safari5.1+ */
  background: -o-radial-gradient(70% 40px, ellipse cover, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
  /* Opera 11.10+ */
  background: -ms-radial-gradient(70% 40px, ellipse cover, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
  /* IE 10+ */
  background: radial-gradient(ellipse at 70% 40px, rgba(55, 156, 219, 1) 0%, rgba(0, 104, 167, 1) 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#379CDB', endColorstr='#0068a7', GradientType=1);
  /* IE6-9 fallback on horizontal gradient */
}

.background-orange {
  padding: 40px;
  margin: -25px;
  background: rgba(230, 106, 32, 1);
  /* Old Browsers */
  background: -moz-radial-gradient(70% 40px, ellipse cover, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
  /* FF3.6+ */
  background: -webkit-gradient(radial, 70% 40px, 0px, center center, 100%, color-stop(0%, rgba(255, 136, 45, 1)), color-stop(100%, rgba(230, 106, 32, 1)));
  /* Chrome, Safari4+ */
  background: -webkit-radial-gradient(70% 40px, ellipse cover, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
  /* Chrome10+,Safari5.1+ */
  background: -o-radial-gradient(70% 40px, ellipse cover, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
  /* Opera 11.10+ */
  background: -ms-radial-gradient(70% 40px, ellipse cover, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
  /* IE 10+ */
  background: radial-gradient(ellipse at 70% 40px, rgba(255, 136, 45, 1) 0%, rgba(230, 106, 32, 1) 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FF882D', endColorstr='#E66A20', GradientType=1);
  /* IE6-9 fallback on horizontal gradient */
}

.background-bluegreen {
  padding: 40px;
  margin: -25px;
  background: rgba(0, 126, 146, 1);
  /* Old Browsers */
  background: -moz-radial-gradient(70% 40px, ellipse cover, rgba(0, 168, 186, 1) 0%, rgba(0, 126, 146, 1) 100%);
  /* FF3.6+ */
  background: -webkit-gradient(radial, 70% 40px, 0px, center center, 100%, color-stop(0%, rgba(0, 168, 186, 1)), color-stop(100%, rgba(230, 106, 32, 1)));
  /* Chrome, Safari4+ */
  background: -webkit-radial-gradient(70% 40px, ellipse cover, rgba(0, 168, 186, 1) 0%, rgba(0, 126, 146, 1) 100%);
  /* Chrome10+,Safari5.1+ */
  background: -o-radial-gradient(70% 40px, ellipse cover, rgba(0, 168, 186, 1) 0%, rgba(0, 126, 146, 1) 100%);
  /* Opera 11.10+ */
  background: -ms-radial-gradient(70% 40px, ellipse cover, rgba(0, 168, 186, 1) 0%, rgba(0, 126, 146, 1) 100%);
  /* IE 10+ */
  background: radial-gradient(ellipse at 70% 40px, rgba(0, 168, 186, 1) 0%, rgba(0, 126, 146, 1) 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#FF882D', endColorstr='#E66A20', GradientType=1);
  /* IE6-9 fallback on horizontal gradient */
}

.background-green {
  padding: 40px;
  margin: -25px;
  background: rgb(134, 197, 63);
  /* Old browsers */
  background: -moz-linear-gradient(top, rgba(134, 197, 63, 1) 0%, rgba(73, 159, 36, 1) 100%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(top, rgba(134, 197, 63, 1) 0%, rgba(73, 159, 36, 1) 100%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, rgba(134, 197, 63, 1) 0%, rgba(73, 159, 36, 1) 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#86c53f', endColorstr='#499f24', GradientType=0);
  /* IE6-9 */
}

.background-blue {
  padding: 40px;
  margin: -25px;
  background: rgb(18, 50, 85, 1);
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#123255+0,316796+100 */
  background: rgb(18, 50, 85);
  /* Old browsers */
  background: -moz-linear-gradient(top, rgba(18, 50, 85, 1) 0%, rgba(49, 103, 150, 1) 100%);
  /* FF3.6-15 */
  background: -webkit-linear-gradient(top, rgba(18, 50, 85, 1) 0%, rgba(49, 103, 150, 1) 100%);
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(to bottom, rgba(18, 50, 85, 1) 0%, rgba(49, 103, 150, 1) 100%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#123255', endColorstr='#316796', GradientType=0);
  /* IE6-9 */
}

.subtitle_block_blue {
  padding: 20px 25px;
  margin-left: -25px;
  background-color: rgba(15, 118, 181, 1);
  clear: both;
  display: inline-block;
  float: left;
}

.subtitle_block_purple {
  padding: 20px 25px;
  margin-left: -25px;
  background-color: rgba(126, 83, 163, 1);
  clear: both;
  display: inline-block;
  float: left;
}

.subtitle_block_drk_gray {
  padding: 20px 25px;
  margin-left: -25px;
  background-color: rgba(54, 54, 54, 1);
  clear: both;
  display: inline-block;
  float: left;
}

.subtitle_block_bluegreen {
  padding: 20px 25px;
  margin-left: -25px;
  background-color: rgba(0, 126, 146, 1);
  clear: both;
  display: inline-block;
  float: left;
}

.subtitle_block_small_patient_orange {
  padding: 20px 25px;
  margin-left: -25px;
  background-color: rgba(241, 104, 83, 1);
  clear: both;
  display: inline-block;
  float: left;
}

.subtitle_block_small_patient_green {
  padding: 20px 25px;
  margin-left: -25px;
  background-color: rgba(0, 167, 157, 1);
  clear: both;
  display: inline-block;
  float: left;
}

.subtitle_block_small_patient_purple {
  padding: 20px 25px;
  margin-left: -25px;
  background-color: rgba(129, 61, 151, 1);
  clear: both;
  display: inline-block;
  float: left;
}

.content_block_blue {
  padding: 20px 30px;
  margin-right: -25px;
  width: 60%;
  background-color: rgba(15, 118, 181, 1);
  color: rgba(255, 255, 255, 1);
}

.content_block_blue h4 {
  color: rgba(255, 255, 255, 1);
}

.left_box_flush_green {
  background-color: #499f24;
  float: left;
  margin-left: -25px;
  color: #FFFFFF;
}

.left_box_flush_gray {
  background-color: #ebebeb;
  float: left;
  margin-left: -25px;
  padding: 50px;
}

.left_box_flush_dark_blue {
  background-color: rgb(18, 50, 85);
  float: left;
  margin-left: -25px;
  color: #FFFFFF;
}

/*Features, Training tab, Pinpoint*/

#features, #training, #pinpoint {
  margin: 0 0 25px 0;
}

#features ul.inline-list, #training ul.inline-list, #pinpoint ul.inline-list {
  margin: 0;
  padding: 0;
}

#features ul.inline-list li, #training ul.inline-list li, #pinpoint ul.inline-list li {
  display: inline;
  padding-right: 20px;
}

#features ul li, #training ul li, #pinpoint ul li, #references ol li {
  margin-bottom: 20px;
}

#features ul.single-space li, #training ul.single-space li, #pinpoint ul.single-space li {
  margin-bottom: 0;
}

#features .headline_section, #sr1 .headline_section, #sr2 .headline_section, #small_patients .headline_section, #port_stabilizer .headline_section {
  background-image: url(../img/bkgrds/feature_triangles.png) no-repeat;
  padding: 5px 25px;
  margin: 45px -25px 10px 0px;
  height: auto;
}

#features .headline_section p, #sr1 .headline_section p, #sr2 .headline_section p, #small_patients .headline_section p, #port_stabilizer .headline_section p {
  color: rgba(255, 255, 255, 1);
  font-size: 20px;
  line-height: 27px;
}

#features .headline_section.purple, #sr1 .headline_section.purple, #sr2 .headline_section.purple, #small_patients .headline_section.purple, #port_stabilizer .headline_section.purple {
  background-color: rgba(126, 83, 163, 1)
}

#features .headline_section.blue, #port_stabilizer .headline_section.blue {
  background-color: rgba(15, 118, 181, 1)
}

#features .headline_section.green {
  background-color: rgba(102, 184, 60, 1)
}

#features .headline_section.black {
  background-color: rgba(0, 0, 0, 1)
}

#features .headline_section.black p.text-reference {
  font-size: 14px;
  line-height: auto;
}

#features .headline_section p.quote {
  font-size: 16px;
  line-height: auto;
  font-style: italic;
}

.feature_headline {
  font-size: 30px;
  font-style: italic;
  line-height: 35px;
  margin-top: 40px;
  color: white;
  font-weight: lighter;
}

.feature_headline .securis {
  margin-top: -100px;
}

.sub_box {
  background-color: rgba(247, 247, 247, 1.00);
  padding: 15px 25px 30px 30px;
  margin: 30px -25px 0 0;
  height: auto;
}

.sub_box.left {
  padding: 15px 25px 30px 30px;
  margin: 30px 0 0 -25px;
}

.sub_box.right {
  padding: 15px 25px 30px 50px;
  margin: 30px -25px 0 0px;
}

.sub_box.green {
  background-color: rgba(102, 184, 60, 1.00);
}

.sub_box.full {
  padding: 15px 25px 30px 30px;
  margin: 0 -25px;
}

.sub_box_blue {
  position: relative;
  background-color: rgba(0, 104, 167, 1.00);
  padding: 75px 90px;
  margin: 30px 0;
  height: auto;
}

.sub_box_blue p {
  color: white;
  font-size: 22px;
  line-height: 28px;
}

.sub_box_blue span {
  color: white;
  font-size: 16px;
  font-style: italic;
}

.sub_box_green {
  position: relative;
  background-color: rgba(102, 184, 60, 1.00);
  padding: 75px 90px;
  margin: 30px 0;
  height: auto;
}

.sub_box_green p {
  color: white;
  font-size: 22px;
  line-height: 28px;
}

.sub_box_green span {
  color: white;
  font-size: 16px;
  font-style: italic;
}

.sub_box_bluegreen {
  position: relative;
  background-color: rgba(0, 126, 146, 1.00);
  padding: 75px 90px;
  margin: 30px 0;
  height: auto;
}

.sub_box_bluegreen p {
  color: white;
  font-size: 22px;
  line-height: 28px;
}

.sub_box_bluegreen span {
  color: white;
  font-size: 16px;
  font-style: italic;
}

.quote_begin {
  background: url(../img/products/accessories/supporting/AllPoints-Port-Access-System/quote-begin.png) top left no-repeat;
  height: 100px;
  display: block;
  margin: -30px 0px -60px -30px;
}

.quote_after {
  background: url(../img/products/accessories/supporting/AllPoints-Port-Access-System/quote-after.png) bottom right no-repeat;
  height: 100px;
  display: block;
  margin: -70px -20px -30px -30px;
}

.quote_small {
  padding: 15px;
}

.quote_small p {
  color: rgba(0, 104, 167, 1.00);
  font-size: 18px;
  font-style: italic;
  line-height: 22px;
}

.quote_small span {
  font-size: 14px;
}

.quote_small_begin {
  background: url(../img/products/accessories/supporting/AllPoints-Port-Access-System/quote-begin-small.png) top left no-repeat;
  height: 50px;
  display: block;
  margin: -15px 0 -55px -20px;
}

.quote_small_after {
  background: url(../img/products/accessories/supporting/AllPoints-Port-Access-System/quote-after-small.png) bottom right no-repeat;
  height: 50px;
  display: block;
  margin: -70px -15px 0px 0px;
}

/*Product Specs*/

#specs.tabcontent {
  padding: 0 0 40px 0
}

#specs h4 {
  margin: 30px 0 10px 0;
  padding: 0;
  color: #374f5e;
}

#specs .sub_header {
  margin: 0;
  padding: 0;
}

#specs .text-reference {
  margin-left: 20px;
}

#specs .note {
  margin-left: 5px;
  font-style: italic;
  font-size: 8pt
}

#product_specs .note {
  margin: -15px 0 30px 20px;
}

.jtubel {
  background-color: #FFFFFF;
  border-left: 3px solid #666666;
  vertical-align: middle;
}

.jtuber {
  background-color: #FFFFFF;
  vertical-align: middle;
}

#specs .wide {
  width: 420px;
}

/*Kit Components*/

#kit_components tr td {
  font-size: 40px;
  text-align: center;
  padding: 5px 0
}

#kit_components tr td:first-of-type {
  padding-left: 11px;
  font-size: 14px;
  text-align: left;
}

#kit_components .text tr td:nth-child(n+2) {
  font-size: 14px;
}

/*Comparison*/

#comparison {
  overflow: scroll
}

#comparison.tabcontent {
  padding: 0 0 40px 0
}

#comparison h4 {
  margin: 10px 0 0 0;
}

#comparison .text tr td {
  font-size: 14px;
  text-align: center;
}

#comparison tr td {
  font-size: 40px;
  text-align: center;
  padding: 5px 0
}

#comparison tr td:first-of-type {
  padding-left: 11px;
  font-size: 14px;
  text-align: left;
}

/*Resources tab*/

#literature ul#product_literature li {
  list-style: url(../img/blts/pdficon_small.gif);
  line-height: 32px;
}

#literature ul#product_literature li.video {
  list-style: url(../img/blts/media.gif);
}

#literature ul#product_literature li.plain {
  list-style: disc;
  list-style-image: none;
}

/*Videos tab*/

#video {}

#vid_player {
  margin: 20px 0 20px 5px;
  width: 711px;
  background: #d6d6d6;
  padding: 0 0 10px 0;
  line-height: 0;
  border: 1px solid #838282;
}

/*FAQ tab*/

#expandable_faq h4 {
  border: none;
  margin: 45px 0 5px 0;
}

#expandable_faq #expand {
  background: #dff2e0;
  border: 1px solid #dfdfdf;
  padding: 4px 7px;
  float: right;
}

#expandable_faq .faq_box {
  padding: 5px 20px 15px 20px;
  margin-bottom: -1px;
  background-color: #dff2e0;
  border: 1px solid #DFDFDF
}

#expandable_faq .faq, #expandable_faq .switchcontent {
  margin-left: 20px;
}

#expandable_faq .showstate {
  margin: 0 5px 0 -25px;
}

#expandable_faq .showstate img {
  padding: 3px 0 0 0;
}

#expandable_list #expand {
  padding: 4px 7px;
  float: right;
}
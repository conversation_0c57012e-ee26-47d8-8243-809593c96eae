<?php
$category_header = $title = 'Edit Device Matrix Entry';
$menu = 'device_matrix';
?>

@extends('layouts.admin.master')

@section('title', 'Edit Device Matrix Entry')

@section('styles')
<style>
    .form-section {
        margin-bottom: 30px;
    }
    .form-section h4 {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    .form-group {
        margin-bottom: 15px;
    }
    .form-group label {
        font-weight: bold;
    }
    .required:after {
        content: ' *';
        color: red;
    }
    .badge-environment {
        font-size: 14px;
        padding: 5px 10px;
        margin-left: 10px;
    }
    .badge-production {
        background-color: #dc3545;
        color: white;
    }
    .badge-uat {
        background-color: #28a745;
        color: white;
    }
    .btn-toolbar {
        margin-top: 20px;
    }
    .btn-toolbar .btn {
        margin-right: 10px;
    }
    .btn-primary, .btn-success, .btn-info, .btn-warning, .btn-danger {
        color: white !important;
    }
    .btn-sm {
        color: white !important;
    }
    
    /* Form highlight effect */
    @keyframes formHighlight {
        0% { background-color: rgba(40, 167, 69, 0.2); }
        50% { background-color: rgba(40, 167, 69, 0.3); }
        100% { background-color: transparent; }
    }
    
    .highlight-form {
        animation: formHighlight 2s ease;
        border-radius: 5px;
        padding: 15px;
    }
    
    /* Dropdown styling for form actions */
    .dropdown-item {
        cursor: pointer;
    }
    
    .dropdown-item i {
        margin-right: 5px;
        width: 16px;
        text-align: center;
    }
</style>
@endsection

@section('content')
<div id="content_hldr">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>
                    Edit Device Matrix Entry
                    @if($environment === 'production')
                    <span class="badge badge-environment badge-production">Production</span>
                    @else
                    <span class="badge badge-environment badge-uat">UAT/Staging</span>
                    @endif
                </h1>
            </div>
        </div>
    </div>
    
    <div id="content">
        @include('admin/software/includes/sidebar')
        
        <div id="pagecontent">
            <div id="main">
                @include('flash::message')
                
                <form action="{{ route('device.matrix.update', ['id' => $entry->id, 'environment' => $environment]) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <input type="hidden" name="environment" value="{{ $environment }}">
                    
                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h4>Basic Information</h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="system_id" class="required">System ID</label>
                                    <select name="system_id" id="system_id" class="form-control @error('system_id') is-invalid @enderror" required>
                                        <option value="">Select System ID</option>
                                        @foreach($systemIds as $system)
                                        <option value="{{ $system->system_id }}" {{ (old('system_id', $entry->system_id) == $system->system_id) ? 'selected' : '' }}>
                                            {{ $system->system_name }} ({{ $system->system_id }})
                                        </option>
                                        @endforeach
                                        <option value="new">+ Add New System ID</option>
                                    </select>
                                    @error('system_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="system_name" class="required">System Name</label>
                                    <input type="text" name="system_name" id="system_name" class="form-control @error('system_name') is-invalid @enderror" value="{{ old('system_name', $entry->system_name) }}" required>
                                    @error('system_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="country_code" class="required">Country Code</label>
                                    <select name="country_code" id="country_code" class="form-control @error('country_code') is-invalid @enderror" required>
                                        <option value="">Select Country</option>
                                        @foreach($countryCodes as $country)
                                        <option value="{{ $country->country_code }}" {{ (old('country_code', $entry->country_code) == $country->country_code) ? 'selected' : '' }}>
                                            {{ $country->country_name }} ({{ $country->country_code }})
                                        </option>
                                        @endforeach
                                        <option value="new">+ Add New Country</option>
                                    </select>
                                    @error('country_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="country_name" class="required">Country Name</label>
                                    <input type="text" name="country_name" id="country_name" class="form-control @error('country_name') is-invalid @enderror" value="{{ old('country_name', $entry->country_name) }}" required>
                                    @error('country_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Version Information Section -->
                    <div class="form-section">
                        <h4>Version Information</h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="ultrasound">Ultrasound Version</label>
                                    <input type="text" name="ultrasound" id="ultrasound" class="form-control @error('ultrasound') is-invalid @enderror" value="{{ old('ultrasound', $entry->ultrasound) }}">
                                    @error('ultrasound')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="sherlock">Sherlock Version</label>
                                    <input type="text" name="sherlock" id="sherlock" class="form-control @error('sherlock') is-invalid @enderror" value="{{ old('sherlock', $entry->sherlock) }}">
                                    @error('sherlock')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="shell">Shell Version</label>
                                    <input type="text" name="shell" id="shell" class="form-control @error('shell') is-invalid @enderror" value="{{ old('shell', $entry->shell) }}">
                                    @error('shell')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="dicom">DICOM Version</label>
                                    <input type="text" name="dicom" id="dicom" class="form-control @error('dicom') is-invalid @enderror" value="{{ old('dicom', $entry->dicom) }}">
                                    @error('dicom')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- File Information Section -->
                    <div class="form-section">
                        <h4>File Information</h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="source_filename">Source Filename</label>
                                    <input type="text" name="source_filename" id="source_filename" class="form-control @error('source_filename') is-invalid @enderror" value="{{ old('source_filename', $entry->source_filename) }}">
                                    @error('source_filename')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="download_filename">Download Filename</label>
                                    <input type="text" name="download_filename" id="download_filename" class="form-control @error('download_filename') is-invalid @enderror" value="{{ old('download_filename', $entry->download_filename) }}">
                                    @error('download_filename')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="product_image">Product Image</label>
                                    <input type="text" name="product_image" id="product_image" class="form-control @error('product_image') is-invalid @enderror" value="{{ old('product_image', $entry->product_image) }}">
                                    @error('product_image')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="installation_instructions">Installation Instructions</label>
                                    <input type="text" name="installation_instructions" id="installation_instructions" class="form-control @error('installation_instructions') is-invalid @enderror" value="{{ old('installation_instructions', $entry->installation_instructions) }}">
                                    @error('installation_instructions')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="btn-toolbar">
                        <div class="btn-group mr-2">
                            <button type="submit" name="submit_action" value="save_and_return" class="btn btn-primary">
                                <i class="fa fa-save"></i> Save & Return
                            </button>
                            <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="sr-only">Toggle Dropdown</span>
                            </button>
                            <div class="dropdown-menu">
                                <button type="submit" name="submit_action" value="save_and_continue" class="dropdown-item">
                                    <i class="fa fa-edit"></i> Save & Continue Editing
                                </button>
                                <button type="submit" name="submit_action" value="save_and_new" class="dropdown-item">
                                    <i class="fa fa-plus"></i> Save & Create New
                                </button>
                            </div>
                        </div>
                        <a href="{{ route('device.matrix.index', ['environment' => $environment]) }}" class="btn btn-secondary">
                            <i class="fa fa-times"></i> Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Handle system ID selection
        $('#system_id').change(function() {
            if ($(this).val() === 'new') {
                window.location.href = "{{ route('device.matrix.create-system', ['environment' => $environment]) }}";
            } else {
                // Auto-fill system name when system ID is selected
                var selectedOption = $(this).find('option:selected');
                if (selectedOption.val() !== '') {
                    var systemName = selectedOption.text().split(' (')[0];
                    $('#system_name').val(systemName);
                }
            }
        });
        
        // Handle country code selection
        $('#country_code').change(function() {
            if ($(this).val() === 'new') {
                window.location.href = "{{ route('device.matrix.create-country', ['environment' => $environment]) }}";
            } else {
                // Auto-fill country name when country code is selected
                var selectedOption = $(this).find('option:selected');
                if (selectedOption.val() !== '') {
                    var countryName = selectedOption.text().split(' (')[0];
                    $('#country_name').val(countryName);
                }
            }
        });
        
        // Highlight form if returning after save
        @if(session('highlight'))
        (function() {
            // Add a highlight effect to the form
            $('form').addClass('highlight-form');
            
            // Scroll to the form
            $('html, body').animate({
                scrollTop: $('form').offset().top - 100
            }, 500);
            
            // Remove the highlight after a delay
            setTimeout(function() {
                $('form').removeClass('highlight-form');
            }, 2000);
        })();
        @endif
        
        // Highlight any fields with errors
        @if(session('error_fields'))
            @foreach(session('error_fields') as $field)
                $('#{{ $field }}').addClass('is-invalid');
            @endforeach
        @endif
    });
</script>
@endsection
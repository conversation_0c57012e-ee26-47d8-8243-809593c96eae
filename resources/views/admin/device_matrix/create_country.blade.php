<?php
$category_header = $title = 'Add New Country';
$menu = 'device_matrix';
?>

@extends('layouts.admin.master')

@section('title', 'Add New Country')

@section('styles')
<style>
    .form-section {
        margin-bottom: 30px;
    }
    .form-section h4 {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    .form-group {
        margin-bottom: 15px;
    }
    .form-group label {
        font-weight: bold;
    }
    .required:after {
        content: ' *';
        color: red;
    }
    .badge-environment {
        font-size: 14px;
        padding: 5px 10px;
        margin-left: 10px;
    }
    .badge-production {
        background-color: #dc3545;
        color: white;
    }
    .badge-uat {
        background-color: #28a745;
        color: white;
    }
    .btn-toolbar {
        margin-top: 20px;
    }
    .btn-toolbar .btn {
        margin-right: 10px;
    }
    .btn-primary, .btn-success, .btn-info, .btn-warning, .btn-danger {
        color: white !important;
    }
    .btn-sm {
        color: white !important;
    }
</style>
@endsection

@section('content')
<div id="content_hldr">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>
                    Add New Country
                    @if($environment === 'production')
                    <span class="badge badge-environment badge-production">Production</span>
                    @else
                    <span class="badge badge-environment badge-uat">UAT/Staging</span>
                    @endif
                </h1>
            </div>
        </div>
    </div>
    
    <div id="content">
        @include('admin/software/includes/sidebar')
        
        <div id="pagecontent">
            <div id="main">
                @include('flash::message')
                
                <div class="alert alert-info">
                    <p><strong>Note:</strong> This will create a new country that can be used in the device matrix. After creating the country, you will be redirected to the device matrix entry form.</p>
                </div>
                
                <form action="{{ route('device.matrix.store-country') }}" method="POST">
                    @csrf
                    <input type="hidden" name="environment" value="{{ $environment }}">
                    
                    <!-- Country Information Section -->
                    <div class="form-section">
                        <h4>Country Information</h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="country_code" class="required">Country Code</label>
                                    <input type="text" name="country_code" id="country_code" class="form-control @error('country_code') is-invalid @enderror" value="{{ old('country_code') }}" required maxlength="2">
                                    <small class="form-text text-muted">Enter a 2-letter ISO country code (e.g., US, CA, GB).</small>
                                    @error('country_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="country_name" class="required">Country Name</label>
                                    <input type="text" name="country_name" id="country_name" class="form-control @error('country_name') is-invalid @enderror" value="{{ old('country_name') }}" required>
                                    <small class="form-text text-muted">Enter the full name of the country.</small>
                                    @error('country_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="region">Region</label>
                                    <select name="region" id="region" class="form-control @error('region') is-invalid @enderror">
                                        <option value="">Select Region</option>
                                        <option value="North America" {{ old('region') == 'North America' ? 'selected' : '' }}>North America</option>
                                        <option value="South America" {{ old('region') == 'South America' ? 'selected' : '' }}>South America</option>
                                        <option value="Europe" {{ old('region') == 'Europe' ? 'selected' : '' }}>Europe</option>
                                        <option value="Asia" {{ old('region') == 'Asia' ? 'selected' : '' }}>Asia</option>
                                        <option value="Africa" {{ old('region') == 'Africa' ? 'selected' : '' }}>Africa</option>
                                        <option value="Oceania" {{ old('region') == 'Oceania' ? 'selected' : '' }}>Oceania</option>
                                    </select>
                                    <small class="form-text text-muted">Optional: Select the region this country belongs to.</small>
                                    @error('region')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="btn-toolbar">
                        <button type="submit" class="btn btn-primary">Create Country</button>
                        <a href="{{ route('device.matrix.create', ['environment' => $environment]) }}" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>
@endsection
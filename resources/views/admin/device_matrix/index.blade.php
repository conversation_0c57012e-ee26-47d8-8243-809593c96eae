<?php
$category_header = $title = 'Device Matrix Management';
$menu = 'device_matrix';
?>

@extends('layouts.admin.master')

@section('title', 'Device Matrix Management')

@section('styles')
<style>
    .environment-toggle {
        margin-bottom: 20px;
    }
    .environment-toggle .btn {
        margin-right: 10px;
    }
    .search-form {
        margin-bottom: 20px;
    }
    .filter-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }
    .filter-row .form-group {
        flex: 1;
    }
    .action-buttons {
        margin-bottom: 20px;
    }
    .action-buttons .btn {
        margin-right: 10px;
    }
    .table-responsive {
        overflow-x: auto;
    }
    .table th, .table td {
        padding: 8px;
        vertical-align: middle;
    }
    .pagination {
        margin-top: 20px;
        justify-content: center;
    }
    .bulk-actions {
        margin-top: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
    }
    .bulk-actions .form-group {
        margin-bottom: 10px;
    }
    .badge-environment {
        font-size: 14px;
        padding: 5px 10px;
        margin-left: 10px;
    }
    .badge-production {
        background-color: #dc3545;
        color: white;
    }
    .badge-uat {
        background-color: #28a745;
        color: white;
    }
</style>
@endsection

@section('content')
<div id="content_hldr">
    <div id="cat_title_hldr">
        <div id="cat_title">
            <div id="end_cap">
                <h1>
                    Device Matrix Management
                    @if($environment === 'production')
                    <span class="badge badge-environment badge-production">Production</span>
                    @else
                    <span class="badge badge-environment badge-uat">UAT/Staging</span>
                    @endif
                </h1>
            </div>
        </div>
    </div>
    
    <div id="content">
        @include('admin/software/includes/sidebar')
        
        <div id="pagecontent">
            <div id="main">
                @include('flash::message')
                
                <!-- Environment Toggle -->
                <div class="environment-toggle">
                    <a href="{{ route('device.matrix.index', ['environment' => 'production']) }}" class="btn {{ $environment === 'production' ? 'btn-primary' : 'btn-outline-primary' }}">
                        Production Environment
                    </a>
                    <a href="{{ route('device.matrix.index', ['environment' => 'uat']) }}" class="btn {{ $environment === 'uat' ? 'btn-success' : 'btn-outline-success' }}">
                        UAT/Staging Environment
                    </a>
                </div>
                
                <!-- Search and Filter Form -->
                <form action="{{ route('device.matrix.index') }}" method="GET" class="search-form">
                    <input type="hidden" name="environment" value="{{ $environment }}">
                    
                    <div class="filter-row">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" value="{{ $search }}" placeholder="Search by ID, name, country...">
                        </div>
                        
                        <div class="form-group">
                            <label for="system_id">System</label>
                            <select name="system_id" id="system_id" class="form-control">
                                <option value="">All Systems</option>
                                @foreach($systemIds as $system)
                                <option value="{{ $system->system_id }}" {{ request('system_id') == $system->system_id ? 'selected' : '' }}>
                                    {{ $system->system_name }} ({{ $system->system_id }})
                                </option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="country_code">Country</label>
                            <select name="country_code" id="country_code" class="form-control">
                                <option value="">All Countries</option>
                                @foreach($countryCodes as $country)
                                <option value="{{ $country->country_code }}" {{ request('country_code') == $country->country_code ? 'selected' : '' }}>
                                    {{ $country->country_name }} ({{ $country->country_code }})
                                </option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="per_page">Per Page</label>
                            <select name="per_page" id="per_page" class="form-control">
                                <option value="15" {{ $perPage == 15 ? 'selected' : '' }}>15</option>
                                <option value="25" {{ $perPage == 25 ? 'selected' : '' }}>25</option>
                                <option value="50" {{ $perPage == 50 ? 'selected' : '' }}>50</option>
                                <option value="100" {{ $perPage == 100 ? 'selected' : '' }}>100</option>
                            </select>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Apply Filters</button>
                    <a href="{{ route('device.matrix.index', ['environment' => $environment]) }}" class="btn btn-secondary">Reset</a>
                </form>
                
                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{{ route('device.matrix.create', ['environment' => $environment]) }}" class="btn btn-success">
                        <i class="fa fa-plus"></i> Add New Entry
                    </a>
                    <a href="{{ route('device.matrix.export', ['environment' => $environment]) }}" class="btn btn-info">
                        <i class="fa fa-download"></i> Export to CSV
                    </a>
                    <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#copyEnvironmentModal">
                        <i class="fa fa-copy"></i> Copy Between Environments
                    </button>
                </div>
                
                <!-- Data Table -->
                <form action="{{ route('device.matrix.bulk-update') }}" method="POST" id="entries-form">
                    @csrf
                    <input type="hidden" name="environment" value="{{ $environment }}">
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead>
                                <tr>
                                    <th width="30"><input type="checkbox" id="select-all"></th>
                                    <th>System ID</th>
                                    <th>System Name</th>
                                    <th>Country</th>
                                    <th>Ultrasound</th>
                                    <th>Sherlock</th>
                                    <th>Shell</th>
                                    <th>DICOM</th>
                                    <th width="150">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if($entries->count() > 0)
                                    @foreach($entries as $entry)
                                    <tr>
                                        <td><input type="checkbox" name="ids[]" value="{{ $entry->id }}" class="entry-checkbox"></td>
                                        <td>{{ $entry->system_id }}</td>
                                        <td>{{ $entry->system_name }}</td>
                                        <td>{{ $entry->country_name }} ({{ $entry->country_code }})</td>
                                        <td>{{ $entry->ultrasound }}</td>
                                        <td>{{ $entry->sherlock }}</td>
                                        <td>{{ $entry->shell }}</td>
                                        <td>{{ $entry->dicom }}</td>
                                        <td>
                                            <a href="{{ route('device.matrix.edit', ['id' => $entry->id, 'environment' => $environment]) }}" class="btn btn-sm btn-primary">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="{{ $entry->id }}" data-toggle="modal" data-target="#deleteModal">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="9" class="text-center">No entries found.</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $entries->appends(request()->except('page'))->links() }}
                    </div>
                    
                    <!-- Bulk Actions -->
                    @if($entries->count() > 0)
                    <div class="bulk-actions">
                        <h5>Bulk Actions</h5>
                        <div class="form-group">
                            <label for="bulk_action">Action</label>
                            <select name="bulk_action" id="bulk_action" class="form-control">
                                <option value="">Select Action</option>
                                <option value="update">Update Field</option>
                                <option value="delete">Delete Selected</option>
                            </select>
                        </div>
                        
                        <div id="bulk-update-fields" style="display: none;">
                            <div class="form-group">
                                <label for="bulk_field">Field to Update</label>
                                <select name="bulk_field" id="bulk_field" class="form-control">
                                    <option value="">Select Field</option>
                                    <option value="ultrasound">Ultrasound</option>
                                    <option value="sherlock">Sherlock</option>
                                    <option value="shell">Shell</option>
                                    <option value="dicom">DICOM</option>
                                    <option value="source_filename">Source Filename</option>
                                    <option value="download_filename">Download Filename</option>
                                    <option value="product_image">Product Image</option>
                                    <option value="installation_instructions">Installation Instructions</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="bulk_value">New Value</label>
                                <input type="text" name="bulk_value" id="bulk_value" class="form-control">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" id="bulk-submit">Apply</button>
                    </div>
                    @endif
                </form>
            </div>
        </div>
        <div id="return"><a href="#top">top</a></div>
    </div>
</div>
<div id="content_hldr_btm"></div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to delete this device matrix entry? This action cannot be undone.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="delete-form" action="" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <input type="hidden" name="environment" value="{{ $environment }}">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Copy Environment Modal -->
<div class="modal fade" id="copyEnvironmentModal" tabindex="-1" role="dialog" aria-labelledby="copyEnvironmentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="copyEnvironmentModalLabel">Copy Between Environments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="{{ route('device.matrix.copy-environment') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-info">
                        <p>This feature allows you to copy device matrix entries between Production and UAT environments.</p>
                        <p><strong>Note:</strong> Existing entries in the target environment with the same system ID and country code will be updated.</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="source_environment">Source Environment</label>
                        <select name="source_environment" id="source_environment" class="form-control" required>
                            <option value="production" {{ $environment === 'production' ? 'selected' : '' }}>Production</option>
                            <option value="uat" {{ $environment === 'uat' ? 'selected' : '' }}>UAT/Staging</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="target_environment">Target Environment</label>
                        <select name="target_environment" id="target_environment" class="form-control" required>
                            <option value="production" {{ $environment !== 'production' ? 'selected' : '' }}>Production</option>
                            <option value="uat" {{ $environment !== 'uat' ? 'selected' : '' }}>UAT/Staging</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="copy_selected_only" name="copy_selected_only">
                            <label class="custom-control-label" for="copy_selected_only">Copy only selected entries</label>
                        </div>
                        <small class="form-text text-muted">If checked, only the entries you've selected in the table will be copied. Otherwise, all entries from the source environment will be copied.</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Copy Entries</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // Handle select all checkbox
        $('#select-all').change(function() {
            $('.entry-checkbox').prop('checked', $(this).prop('checked'));
        });
        
        // Handle bulk action display
        $('#bulk_action').change(function() {
            if ($(this).val() === 'update') {
                $('#bulk-update-fields').show();
            } else {
                $('#bulk-update-fields').hide();
            }
        });
        
        // Validate bulk action form before submit
        $('#bulk-submit').click(function(e) {
            e.preventDefault();
            
            // Check if any checkboxes are selected
            if ($('.entry-checkbox:checked').length === 0) {
                alert('Please select at least one entry.');
                return;
            }
            
            // Check if an action is selected
            if ($('#bulk_action').val() === '') {
                alert('Please select an action to perform.');
                return;
            }
            
            // If update action, validate fields
            if ($('#bulk_action').val() === 'update') {
                if ($('#bulk_field').val() === '') {
                    alert('Please select a field to update.');
                    return;
                }
            }
            
            // If delete action, confirm
            if ($('#bulk_action').val() === 'delete') {
                if (!confirm('Are you sure you want to delete the selected entries? This action cannot be undone.')) {
                    return;
                }
            }
            
            // Submit the form
            $('#entries-form').submit();
        });
        
        // Handle delete button click
        $('.delete-btn').click(function() {
            var id = $(this).data('id');
            var url = "{{ route('device.matrix.destroy', ['id' => ':id']) }}";
            url = url.replace(':id', id);
            $('#delete-form').attr('action', url);
        });
        
        // Auto-submit form when per_page changes
        $('#per_page').change(function() {
            $('.search-form').submit();
        });
        
        // Handle copy environment form
        $('#copy_selected_only').change(function() {
            if ($(this).is(':checked')) {
                // Check if any entries are selected
                if ($('.entry-checkbox:checked').length === 0) {
                    alert('Please select at least one entry to copy.');
                    $(this).prop('checked', false);
                    return;
                }
                
                // Add selected IDs to the form
                $('.entry-checkbox:checked').each(function() {
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'selected_ids[]',
                        value: $(this).val()
                    }).appendTo('#copyEnvironmentModal form');
                });
            } else {
                // Remove any previously added selected IDs
                $('#copyEnvironmentModal form input[name^="selected_ids"]').remove();
            }
        });
        
        // Prevent source and target from being the same
        $('#source_environment, #target_environment').change(function() {
            var source = $('#source_environment').val();
            var target = $('#target_environment').val();
            
            if (source === target) {
                alert('Source and target environments cannot be the same.');
                if (this.id === 'source_environment') {
                    $('#target_environment').val(source === 'production' ? 'uat' : 'production');
                } else {
                    $('#source_environment').val(target === 'production' ? 'uat' : 'production');
                }
            }
        });
    });
</script>
@endsection
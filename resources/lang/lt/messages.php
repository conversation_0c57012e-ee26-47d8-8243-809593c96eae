<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Language Strings
    |--------------------------------------------------------------------------
    */

    'elabeling' => [
        'language_label' => 'Language:',
        'instruction_for_use' => 'Instruction For Use',
        'elabeling_indicator' => 'eIFU indicator',
        'to_obtain' => 'To obtain an electronic version of an Instructions For Use (IFU):',
        'search_instructions' => 'Enter Lot Number or Product Code',
        'search' => 'Search',
        'request_copy_line_1' => 'Instrukcijų kopiją taip pat galima gauti paskambinus numeriu: 1-800-545-0890 (JAV).',
        'request_copy_line_2' => 'Visa su pacientais susijusi informacija pateikiama su rinkiniu.',
        'disclaimer_line_3' => 'Šioje svetainėje pateikta informacija skirta tik gydytojams / klinicistams.',
        'disclaimer_line_4' => 'Naudojimo instrukcijas būtina perskaityti prieš naudojant ir (arba) implantuojant įtaisą bei prieš atliekant tolesnius priežiūros veiksmus.',
        'disclaimer_line_5' => 'Šioje svetainėje gali būti pasiekiamos ne visų gaminių naudojimo instrukcijos. Jei nerandate reikiamos literatūros, kreipkitės į „Bard Access Systems“ numeriu 1-800-545-0890 (JAV).',
        'disclaimer_line_6' => 'Nors naudojimo instrukcijos gali keistis, šioje svetainėje visada pasiekiama naujausia versija.',
        'results_for' => 'Results for',
        'lot_number' => 'Lot Number:',
        'product_code' => 'Product Code:',
        'description' => 'Description:',
        'no_pdf_available' => 'No PDF available for this language',
        'no_results_found' => 'No results were found for',
        'copy_short' => 'A copy of the instructions can also be obtained by calling the manufacturer at: 1-801-522-COPY.',
        'secondary_ifu' => 'Secondary IFU',
        'additional_ifu' => 'Additional IFU',
        'maintenance_guide' => 'Maintenance Guide',
        'patient_guide' => 'Patient Guide',
        'implant_or_insertion_record' => 'Implant or Insertion Record',
        'identification_card' => 'Identification Card',
        'insert' => 'Insert',
        'nursing_guide' => 'Nursing Guide',
        'other' => 'Other',
    ],

];

<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Language Strings
    |--------------------------------------------------------------------------
    */

    'elabeling' => [
        'language_label' => 'Language:',
        'instruction_for_use' => 'Instruction For Use',
        'elabeling_indicator' => 'eIFU indicator',
        'to_obtain' => 'To obtain an electronic version of an Instructions For Use (IFU):',
        'search_instructions' => 'Enter Lot Number or Product Code',
        'search' => 'Search',
        'request_copy_line_1' => 'Lietošanas norādījumu kopiju var saņemt arī, zvanot pa tālruni +1 8005450890 (ASV).',
        'request_copy_line_2' => 'Visa pacientiem paredzētā informācija ir sniegta komplektā.',
        'disclaimer_line_3' => 'Informācija šajā vietnē ir paredzēta tikai ārstiem/klīnicistiem.',
        'disclaimer_line_4' => 'Izlasiet lietošanas norādījumus, pirms lietosiet un/vai implantēsiet ierīci vai veiksiet rehabilitācijas aprūpi.',
        'disclaimer_line_5' => 'Šajā vietnē, iespējams, nav pieejami lietošanas norādījumi visiem izstrādājumiem. Ja nevarat atrast nepieciešamo literatūru, lūdzu, sazinieties ar uzņēmumu Bard Access Systems pa tālruni +1 8005450890 (ASV).',
        'disclaimer_line_6' => 'Lietošanas norādījumi var tikt mainīti, taču šajā vietnē ir vienmēr pieejama to jaunākā versija.',
        'results_for' => 'Results for',
        'lot_number' => 'Lot Number:',
        'product_code' => 'Product Code:',
        'description' => 'Description:',
        'no_pdf_available' => 'No PDF available for this language',
        'no_results_found' => 'No results were found for',
        'copy_short' => 'A copy of the instructions can also be obtained by calling the manufacturer at: 1-801-522-COPY.',
        'secondary_ifu' => 'Secondary IFU',
        'additional_ifu' => 'Additional IFU',
        'maintenance_guide' => 'Maintenance Guide',
        'patient_guide' => 'Patient Guide',
        'implant_or_insertion_record' => 'Implant or Insertion Record',
        'identification_card' => 'Identification Card',
        'insert' => 'Insert',
        'nursing_guide' => 'Nursing Guide',
        'other' => 'Other',
    ],

];

<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Language Strings
    |--------------------------------------------------------------------------
    */

    'elabeling' => [
        'language_label' => 'Language:',
        'instruction_for_use' => 'Instruction For Use',
        'elabeling_indicator' => 'eIFU indicator',
        'to_obtain' => 'To obtain an electronic version of an Instructions For Use (IFU):',
        'search_instructions' => 'Enter Lot Number or Product Code',
        'search' => 'Search',
        'request_copy_line_1' => 'Копие на инструкциите може да бъде получено също и чрез обаждане на: 1-800-545-0890 (САЩ).',
        'request_copy_line_2' => 'Цялата специфична за пациента информация се доставя с комплекта.',
        'disclaimer_line_3' => 'Информацията на този уеб сайт е предназначена за употреба само от лекари.',
        'disclaimer_line_4' => 'Инструкциите за употреба трябва да бъдат прочетени преди използване и/или имплантиране на устройство, както и преди извършване на последващи грижи.',
        'disclaimer_line_5' => 'Възможно е на този сайт да не са достъпни инструкции за употреба за всички продукти.  Ако не намерите документите, които търсите, моля, свържете се с Bard Access Systems на тел. 1-800-545-0890 (САЩ).',
        'disclaimer_line_6' => 'Въпреки че инструкциите за употреба подлежат на промяна, най-актуалната версия винаги е достъпна на този сайт.',
        'results_for' => 'Results for',
        'lot_number' => 'Lot Number:',
        'product_code' => 'Product Code:',
        'description' => 'Description:',
        'no_pdf_available' => 'No PDF available for this language',
        'no_results_found' => 'No results were found for',
        'copy_short' => 'A copy of the instructions can also be obtained by calling the manufacturer at: 1-801-522-COPY.',
        'secondary_ifu' => 'Secondary IFU',
        'additional_ifu' => 'Additional IFU',
        'maintenance_guide' => 'Maintenance Guide',
        'patient_guide' => 'Patient Guide',
        'implant_or_insertion_record' => 'Implant or Insertion Record',
        'identification_card' => 'Identification Card',
        'insert' => 'Insert',
        'nursing_guide' => 'Nursing Guide',
        'other' => 'Other',
    ],

];

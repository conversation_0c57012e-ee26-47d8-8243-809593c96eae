<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Language Strings
    |--------------------------------------------------------------------------
    */

    'elabeling' => [
        'language_label' => 'Language:',
        'instruction_for_use' => 'Instruction For Use',
        'elabeling_indicator' => 'eIFU indicator',
        'to_obtain' => 'To obtain an electronic version of an Instructions For Use (IFU):',
        'search_instructions' => 'Enter Lot Number or Product Code',
        'search' => 'Search',
        'request_copy_line_1' => 'Juhiste koopia hankimiseks võite ka helistada telefoninumbril ************** (USA).',
        'request_copy_line_2' => 'Komplektiga on kaasas kogu patsienditeave.',
        'disclaimer_line_3' => 'Sellel veebisaidil olev teave on mõeldud üksnes raviarsti/klinitsisti jaoks.',
        'disclaimer_line_4' => 'Enne seadme kasutamist ja/või implanteerimist ja enne järelraviga alustamist tuleb lugeda seadme kasutusjuhiseid.',
        'disclaimer_line_5' => 'Kõikide toodete kasutusjuhised ei pruugi olla sellel saidil kättesaadavad. Kui te ei leia otsitavaid materjale, võtke ühendust Bard Access Systemsiga telefoninumbril ************** (USA).',
        'disclaimer_line_6' => 'Kuigi kasutusjuhiseid võidakse muuta, on nende uusim versioon alati saidil kättesaadav.',
        'results_for' => 'Results for',
        'lot_number' => 'Lot Number:',
        'product_code' => 'Product Code:',
        'description' => 'Description:',
        'no_pdf_available' => 'No PDF available for this language',
        'no_results_found' => 'No results were found for',
        'copy_short' => 'A copy of the instructions can also be obtained by calling the manufacturer at: 1-801-522-COPY.',
        'secondary_ifu' => 'Secondary IFU',
        'additional_ifu' => 'Additional IFU',
        'maintenance_guide' => 'Maintenance Guide',
        'patient_guide' => 'Patient Guide',
        'implant_or_insertion_record' => 'Implant or Insertion Record',
        'identification_card' => 'Identification Card',
        'insert' => 'Insert',
        'nursing_guide' => 'Nursing Guide',
        'other' => 'Other',
    ],

];

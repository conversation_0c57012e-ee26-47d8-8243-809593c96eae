// JavaScript Document
function pluploadQueueWidget(uploader_id, upload_url, upload_params, allowed_file_types, callback_output_target, callback_output_url) {

    $(document).ready(function () {

        $(function () {

            // Setup html5 version
            $(uploader_id).pluploadQueue({
                // General settings
                runtimes: 'html5',
                multi_selection: true,
                dragdrop: true,
                url: upload_url,
                chunk_size: '128kb',
                unique_names: true,
                rename: true,
                multipart: true,
                file_data_name: 'file',
                multiple_queues: true,
                filters: {
                    // Maximum file size
                    max_file_size: '1000mb',
                    // Specify what files to browse for
                    mime_types: [{
                        title: "Files",
                        extensions: allowed_file_types
                    }],
                    prevent_duplicates: true
                },

                // Post init events, bound after the internal events
                init: {

                    FilesAdded: function (up, files) {
                        // Called when files are added to queue
                        $.ajaxSetup({
                            timeout: 120000
                        });
                        plupload.each(files, function (file) {
                            $.ajax({
                                type: "POST",
                                url: upload_url,
                                method: 'POST',
                                data: $.extend({
                                    src_filename: file['name'],
                                    name: file['name'],
                                    filesize: file['size'],
                                    upload_check: 1
                                }, upload_params),
                                success: function (data, response, xhr) {

                                    if (typeof (data.error) != 'undefined' && data.error) {

                                        // remove file if there is a problem with the
                                        up.removeFile(file);

                                        showMessage("error", data.error);
                                    } else {

                                        console.log(data.msg);

                                    }

                                },
                                error: function (XMLHttpRequest, textStatus, errorThrown) {
                                    console.log("Your files have not been uploaded.");
                                },
                                cache: false,
                                dataType: "json"
                            });
                        });
                    },
                    BeforeUpload: function (up, file) {
                        // Called before uploading and set multipart parameters
                        // Merge default params object with upload_params object
                        up.settings.multipart_params = $.extend({
                            src_filename: file['name'],
                            filesize: file['size']
                        }, upload_params);
                    },
                    FileUploaded: function (up, file, info) {
                        // Called when a file has finished uploading
                        $.ajax({
                            type: "GET",
                            url: callback_output_url,
                            success: function (data) {
                                $(callback_output_target).html(data.output);
                            },
                            cache: false,
                            dataType: "json"
                        });
                    }
                } // end init
            });
        });
    }); // document ready end

}
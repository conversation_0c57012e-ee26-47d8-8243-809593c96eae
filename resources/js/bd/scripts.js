// Header navigation
$(document).ready(function() {

  // Header mega menu toggle
  $('header .desktop ul li.top-nav-container a').click(function(e) {
    e.preventDefault();
    $('header .links a.language').removeClass('opened');
    $('header .languages').removeAttr('style');
    $('header .links a.bd-sites').removeClass('opened');
    $('header .sites').removeAttr('style');
    $('header .mega-menu').slideToggle();
    $(this).toggleClass('opened');
  });
  $('header .mega-menu a.close').click(function(e) {
    e.preventDefault();
    clearNav();
  });
  $(document).mouseup(function(e) {
    var container = $('header .mega-menu, header .desktop ul li:first-child a');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      container.hide().removeAttr('style');
      $('header .desktop ul li:first-child a').removeClass('opened');
    }
  });

  // Header mega menu tabs
  $('.mega-menu .tab-content').hide();
  $('.mega-menu .tab-wrap > .tab-content:first-child').show();
  $('.mega-menu .tab-nav li:first-child').addClass('on');
  $('.mega-menu .tab-nav li').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $('.mega-menu .tab-wrap > .tab-content').hide();
    var activeTab = $(this).find('a').attr('href');
    $(activeTab).show();
  });

  // Header mega menu accordion list
  $('.mega-menu .accordion-list li a.trigger').click(function(e) {
    e.preventDefault();
    if ($(this).next().is(':visible')) {
      $(this).next('ul').slideToggle();
      $(this).toggleClass('opened');
    } else {
      $('.mega-menu .accordion-list li > ul').hide();
      $('.mega-menu .accordion-list li a.trigger').removeClass('opened');
      $(this).next('ul').slideToggle();
      $(this).toggleClass('opened');
    }
    // Custom Scrollbar
    $('.mega-menu .accordion-list li > ul').mCustomScrollbar({
      alwaysShowScrollbar: 1
    });
  });

  // Mobile nav menu accordion list
  $('.mobile .accordion-list li a.trigger').click(function(e) {
    e.preventDefault();
    $(this).toggleClass('opened');
    $(this).next().slideToggle();
  });

  // Header language toggle
  $('header .links a.language').click(function(e) {
    e.preventDefault();
    $('header .desktop ul li:first-child a').removeClass('opened');
    $('header .mega-menu').removeAttr('style');
    $('header .languages').slideToggle();
    $(this).toggleClass('opened');
  });
  $('header .languages a.close').click(function(e) {
    e.preventDefault();
    clearNav();
  });
  $(document).mouseup(function(e) {
    var container = $('header .languages, header .links a.language');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      container.hide().removeAttr('style');
      $('header .links a.language').removeClass('opened');
    }
  });

  // Header user toggle
  $('header .links a.user').click(function(e) {
    e.preventDefault();
    $('header .desktop ul li:first-child a').removeClass('opened');
    $('header .mega-menu').removeAttr('style');
    $('header .user-options').slideToggle();
    $(this).toggleClass('opened');
  });
  $('header .user-options a.close').click(function(e) {
    e.preventDefault();
    clearNav();
  });
  $(document).mouseup(function(e) {
    var container = $('header .user-options, header .links a.user');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      container.hide().removeAttr('style');
      $('header .links a.user').removeClass('opened');
    }
  });

  // Header sites toggle
  $('header .links a.bd-sites').click(function(e) {
    e.preventDefault();
    $('header .desktop ul li:first-child a').removeClass('opened');
    $('header .mega-menu').removeAttr('style');
    $('header .sites').slideToggle();
    $(this).toggleClass('opened');
  });
  $('header .sites a.close').click(function(e) {
    e.preventDefault();
    clearNav();
  });
  $(document).mouseup(function(e) {
    var container = $('header .sites, header .links a.bd-sites');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      container.hide().removeAttr('style');
      $('header .links a.bd-sites').removeClass('opened');
    }
  });

  // Toggle search box
  $('header a.show-search').click(function(e) {
    e.preventDefault();
    clearNav();
    $('header form.search').show().animate({
      width: '100%'
    });
    $('header nav.desktop.float-right').css({
      opacity: '0.2'
    });
  });
  $(document).mouseup(function(e) {
    var container = $('header form.search');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      container.hide().removeAttr('style');
      $('header nav.desktop.float-right').removeAttr('style');
    }
  });

  // Sub Menu Dropdown
  $('nav.sub .sub-menu-drop .tab-content').hide();
  $('nav.sub .sub-menu-drop .tab-wrap > .tab-content:first-child').show();
  $('nav.sub .sub-menu-drop .tab-nav li:first-child').addClass('on');
  $('nav.sub .sub-menu-drop .tab-nav li').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    var activeTab = $(this).find('a').attr('href');
    $(activeTab).siblings().hide();
    $(activeTab).show();
  });

  // Toggle search box for sub nav
  $('nav.sub a.show-search').click(function(e) {
    e.preventDefault();
    clearNav();
    $('nav.sub form.search').show().animate({
      width: '100%'
    });
    $('nav.sub ul, nav.sub .label').css({
      opacity: '0.2'
    });
  });
  $(document).mouseup(function(e) {
    var container = $('nav.sub form.search');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      container.hide().removeAttr('style');
      $('nav.sub ul, nav.sub .label').removeAttr('style');
    }
  });

  // Toggle mini cart for sub nav
  $('nav.sub a.show-cart').click(function(e) {
    e.preventDefault();
    clearNav();
    $('nav.sub .mini-cart-wrap').fadeIn(200);
  });
  $(document).mouseup(function(e) {
    var container = $('nav.sub .mini-cart-wrap');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      container.hide().removeAttr('style');
    }
  });

  // Open the mobile navigation
  $('header a.mobile-toggle').click(function(e) {
    e.preventDefault();
    $('nav.mobile').slideToggle();
    $(this).toggleClass('opened');
  });

  // Open the mobile navigation
  $('.crumbs li.on > a').click(function(e) {
    e.preventDefault();
    $(this).parent().find('ul').slideToggle();
    $(this).parent().toggleClass('opened');
  });
  $(document).mouseup(function(e) {
    var container = $('.crumbs li.on > ul, .crumbs li.on > a');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      container.hide().removeAttr('style');
      $('.crumbs li.on').removeClass('opened');
    }
  });

  // Open solution nav
  $('nav.solution-indicator a.indicator-toggle').click(function(e) {
    e.preventDefault();
    $('nav.solution-indicator .section-list').slideToggle();
    $(this).toggleClass('opened');
  });

  // Solution nav scroll to element
  $('nav.solution-indicator .section-list > a').click(function(e) {
    e.preventDefault();
    var sectionID = $(this).attr('href');

    $('html, body').animate({
      scrollTop: ($(sectionID).offset().top - 59)
    }, 500);

    $(this).parent().slideUp();
    $('nav.solution-indicator a.indicator-toggle').removeClass('opened');

  });

  // Timeline scroll to element
  $('.timeline .sections > a, .timeline .icons > a, a.scroll-to-link').click(function(e) {
    e.preventDefault();
    var sectionID = $(this).attr('href');
    $('html, body').animate({
      scrollTop: ($(sectionID).offset().top - 59)
    }, 500);

  });

  // Chat
  $('#chat a.chat-toggle').click(function(e) {
    e.preventDefault();
    $('#chat .content').toggleClass('opened');
    $(this).toggleClass('opened');
  });

  // Scroll to top
  $('#top').click(function(e) {
    e.preventDefault();
    $('html, body').animate({
      scrollTop: 0
    }, '500');
  });

  // Scroll to element
  $('a.scroll-button, a.scroll-link').click(function(e) {
    e.preventDefault();
    var scrollElement = $(this).attr('href');

    $('html, body').animate({
      scrollTop: ($(scrollElement).offset().top - 180)
    }, 500);
  });

  // Scroll to section
  $('.scroll-to-section li a').click(function(e) {
    e.preventDefault();
    $(this).parent().siblings().removeClass('on');
    var scrollElement = $(this).attr('href');
    $('html, body').animate({
      scrollTop: ($(scrollElement).offset().top - 59)
    }, 500);
  });

  // Mobile scroll to section
  $('.mobile-scroll-to-section li a').click(function(e) {
    e.preventDefault();
    $(this).parent().siblings().removeClass('on');
    var scrollElement = $(this).attr('href');
    $('html, body').animate({
      scrollTop: ($(scrollElement).offset().top - 0)
    }, 500);
    $('header a.mobile-toggle').removeClass('opened');
    $('nav.mobile').hide();
  });

  // Scroll to element
  $('.scroll-to-nav a').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    var scrollElement = $(this).attr('href');
    $('html, body').animate({
      scrollTop: ($(scrollElement).offset().top - 59)
    }, 500);
  });

  // Show Suggestions
  $('.search-suggestions a.close').click(function(e) {
    e.preventDefault();
    $('.search-suggestions').slideUp();
  });

  // Filter columns
  $('.filter-columns a.filter-toggle').click(function(e) {
    e.preventDefault();
    $(this).toggleClass('closed');
    $(this).next().children('.filter-columns-wrap').slideToggle(200);
  });

  // Way Arrow
  $('.way-arrow a.spot').click(function(e) {
    e.preventDefault();
    $('.way-arrow .spot-wrap').removeClass('opened');
    $(this).parent().toggleClass('opened');
  });

  // Wheel rotator
  var theAngle = 0;
  $('.turn-right').on('click', function(e) {
    e.preventDefault();
    theAngle += 120;
    $('.wheel').css({
      transform: 'rotate(' + theAngle + 'deg)'
    });
    $('.wheel .circle').css({
      transform: 'rotate(' + -theAngle + 'deg)'
    });
    $('.wheel .text').hide().delay(600).fadeIn();
    $('.hub .spoke').hide().delay(600).fadeIn();

    var onCircle = $('.wheel .circle.on');
    onCircle.removeClass('on');
    onCircle.prev().addClass('on');
    if (onCircle.prev().length == 0) {
      $('.wheel .circle').last().addClass('on');
    }

    var onCircleContent = $('.wheel .circle.on').attr('rel');
    $('.wheel-content .content').hide();
    $('#' + onCircleContent).fadeIn(1200);

    var reverseCircle = $('.wheel .circle.reverse');
    reverseCircle.removeClass('reverse');
    reverseCircle.prev().addClass('reverse');
    if (reverseCircle.prev().length == 0) {
      $('.wheel .circle').last().addClass('reverse');
    }
    $('.wheel-wrap').removeClass('center');
  });

  $('.turn-left').on('click', function(e) {
    e.preventDefault();
    theAngle -= 120;
    $('.wheel').css({
      transform: 'rotate(' + theAngle + 'deg)'
    });
    $('.wheel .circle').css({
      transform: 'rotate(' + -theAngle + 'deg)'
    });
    $('.wheel .text').hide().delay(600).fadeIn();
    $('.hub .spoke').hide().delay(600).fadeIn();

    var onCircle = $('.wheel .circle.on');
    onCircle.removeClass('on');
    onCircle.next().addClass('on');
    if (onCircle.next().length == 0) {
      $('.wheel .circle').first().addClass('on');
    }

    var onCircleContent = $('.wheel .circle.on').attr('rel');
    $('.wheel-content .content').hide();
    $('#' + onCircleContent).fadeIn(1200);

    var reverseCircle = $('.wheel .circle.reverse');
    reverseCircle.removeClass('reverse');
    reverseCircle.next().addClass('reverse');
    if (reverseCircle.next().length == 0) {
      $('.wheel .circle').first().addClass('reverse');
    }
    $('.wheel-wrap').removeClass('center');
  });

  $('.center-spot').on('click', function(e) {
    e.preventDefault();
    var onCircleContent = $(this).attr('rel');
    $('.wheel-wrap').addClass('center');
    $('.wheel-content .content').hide();
    $('#' + onCircleContent).fadeIn(1200);
  });

  $('.right-spot').on('click', function(e) {
    e.preventDefault();
    var onCircleContent = $('.wheel .circle.on').attr('rel');
    $('.wheel-content .content').hide();
    $('#' + onCircleContent).fadeIn(1200);
    $('.wheel-wrap').removeClass('center');
  });

  // Tool tip toggle
  $('.tip-toggle').on('click', function(e) {
    e.preventDefault();
    if ($(this).hasClass('opened')) {
      $(this).toggleClass('opened');
      $(this).parent().next().slideToggle(200);
    } else {
      $('.tip-toggle').removeClass('opened');
      $('.tip-toggle').parent().next().hide();
      $(this).toggleClass('opened');
      $(this).parent().next().slideToggle(200);
    }
  });

  // Stage bar toggle
  $('.star-toggle').on('click', function(e) {
    e.preventDefault();
    if ($(this).parent().hasClass('opened')) {
      $(this).parent().toggleClass('opened');
      $(this).next().fadeToggle(200);
    } else {
      $('.star-toggle').parent().removeClass('opened');
      $('.star-toggle').next().fadeOut(200);
      $(this).parent().toggleClass('opened');
      $(this).next().fadeToggle(200);
    }
  });

});

// Nav breakpoint checks
$(window).resize(function() {
  timelineBreakpointCheck();
  desktopNavBreakpointCheck();
  mobileNavBreakpointCheck();
});


function timelineBreakpointCheck() {
  if (window.matchMedia('(min-width:768px)').matches) {
    $('nav.solution-indicator a.indicator-toggle').removeClass('opened');
    $('nav.solution-indicator .section-list').removeAttr('style');
  }
}

function desktopNavBreakpointCheck() {
  if (!window.matchMedia('(min-width:992px)').matches) {
    clearNav();
  }
}

function mobileNavBreakpointCheck() {
  if (window.matchMedia('(min-width:992px)').matches) {
    $('header a.mobile-toggle').removeClass('opened');
    $('nav.mobile').removeAttr('style');
  }
}

// Clear nav function
function clearNav() {

  $('header .desktop ul li:first-child a').removeClass('opened');
  $('header .mega-menu').removeAttr('style');
  $('header .links a.language').removeClass('opened');
  $('header .languages').removeAttr('style');
  $('header .links a.user').removeClass('opened');
  $('header .user-options').removeAttr('style');
  $('header .links a.bd-sites').removeClass('opened');
  $('header .sites').removeAttr('style');
  $('header form.search').removeAttr('style');
  $('header nav.desktop.float-right').removeAttr('style');

}

// Fixed Nav
$(document).scroll(function() {
  var y = $(this).scrollTop();
  if (y > 1) {
    $('#page').addClass('scrolled');
  } else {
    $('#page').removeClass('scrolled');
  }
});

// Show scroll to top button
$(window).scroll(function() {

  // Toggle class for fixed menu
  if ($(this).scrollTop() > 100) {
    $('#top').addClass('fixed');
  } else {
    $('#top').removeClass('fixed');
  }

  // Fade in Solution nav
  if ($(this).scrollTop() > 60) {
    $('nav.solution-indicator').fadeIn();
  } else {
    $('nav.solution-indicator').fadeOut();
  }

  // Timeline scroll function / Scroll to function
  var scrollTop = $(this).scrollTop();
  $('.timeline-point').each(function() {
    var topDistance = $(this).offset().top - 60;
    var timelinePosition = $(this).attr('rel');

    if ((topDistance) <= scrollTop) {
      $('nav.solution-indicator').attr('id', 'indicate-' + timelinePosition);
      $('.timeline-section').attr('id', timelinePosition);
    }
  });
  // Scroll to function
  $('.scroll-to-point').each(function() {
    var topDistance = $(this).offset().top - 60;
    var scrollToPosition = $(this).attr('rel');

    if ((topDistance) <= scrollTop) {
      $('.scroll-to-section').attr('id', scrollToPosition);
      $('.scroll-to-wrap').attr('id', scrollToPosition);
    }
  });
  if ($(window).scrollTop() === 0) {
    $('.scroll-to-section').removeAttr('id');
    $('.scroll-to-wrap').removeAttr('id');
  }

});

// Non navigation
$(document).ready(function() {

  // Alert
  $('#alert a').click(function(e) {
    e.preventDefault();
    $('#alert').slideToggle();
  });

  // Table click
  $('table.hover tr').not('tr.divider').click(function(e) {
    e.preventDefault();
    $(this).toggleClass('on');
  });

  // Table load more rows
  $('.more-content').hide();
  $('.more-button').click(function(e) {
    e.preventDefault();
    var moreContent = $(this).attr('rel');
    $('.more-content.' + moreContent).slideToggle();
    $(this).toggleClass('less');
  });

  // Accordions
  $('.accordions .copy, .accordions .nested, .accordion .copy, .accordion .nested').hide();
  $(document.body).on('click', '.accordions .top.toggle, .accordion .top.toggle', function(e) {
    e.preventDefault();
    $(this).toggleClass('opened');
    $(this).next().slideToggle();
    $('.copy .flexslider').resize();
  });

  $(document.body).on('click', '.accordions .top .toggle, .accordion .top .toggle', function(e) {
    e.preventDefault();
    $(this).toggleClass('opened');
    $(this).parent().next().slideToggle();
  });

  $('.ordered-accordion-copy').hide();
  $(document.body).on('click', '.ordered-accordion .plus-minus', function(e) {
    e.preventDefault();
    $(this).toggleClass('opened');
    $(this).parent().next().slideToggle();
  });

  // FAQs
  $('.faqs .answer').hide();
  $('.faqs .question').css('cursor', 'pointer');
  $('.faqs .question').click(function(e) {
    e.preventDefault();
    $(this).toggleClass('opened');
    $(this).next().slideToggle();
  });

  // Link entire box as image
  $('.product-block, .person-block, .js-link').css('cursor', 'pointer');
  $('.product-block, .person-block, .js-link').click(function(e) {
    e.preventDefault();
    window.location = $(this).find('a.go-to-link').attr('href');
  });

  // Slide Select List
  $('.slide-select-list .toggle').click(function(e) {
    e.preventDefault();
    $(this).toggleClass('opened');
    $(this).next().slideToggle();
  });

  // Button Drop
  $('.button-drop .toggle').click(function(e) {
    e.preventDefault();
    $(this).toggleClass('opened');
    $(this).next().slideToggle();
  });

  // Mobile side nav toggle
  $('.mobile-side-nav-toggle span').click(function(e) {
    e.preventDefault();
    $(this).toggleClass('opened');
    $(this).parent().next().slideToggle();
  });

  // Side Nav Toggle
  $('.side-nav-toggle h5 a').click(function(e) {
    e.preventDefault();
    $(this).toggleClass('opened');
    $(this).parent().next().slideToggle();
  });

  // Promotion FadeOut
  $('.promotion a.close').click(function(e) {
    e.preventDefault();
    $(this).parent().fadeOut();
  });

  // Switches
  $('.switch a').click(function(e) {
    e.preventDefault();
    $(this).parent().children().removeClass('active');
    $(this).addClass('active');
  });

  // Scroll left
  if ($('.scroll-left .on').length) {
    var leftPosition = $('.scroll-left .on').position().left;
    $('.scroll-left').scrollLeft(leftPosition - 15);
  }

  // Select tabs
  $('.select-tabs .toggle').click(function(e) {
    e.preventDefault();
    $(this).next().slideToggle();
    $(this).toggleClass('opened');
  });

  $('.select-tabs .select-tab-wrap .tab').hide();
  $('.select-tabs .select-tab-wrap > .tab:first-child').show();

  $('.select-tab .options a').click(function(e) {
    e.preventDefault();
    var theText = $(this).text();
    $(this).parent().prev('.toggle').removeClass('opened');
    $(this).closest('.select-tab').find('.default').text(theText);

    $('.select-tabs .select-tab-wrap').children().hide();
    var activeTab = $(this).attr('href');
    $(activeTab).show();
  });
  $(document).mouseup(function(e) {
    var container = $('.select-tab .toggle.opened');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      $('.select-tab .toggle').removeClass('opened');
      $('.select-tab .options').slideUp();
    }
  });

  // Content tabs
  $('.content-tabs .toggle').click(function(e) {
    e.preventDefault();
    $(this).next().slideToggle();
    $(this).toggleClass('opened');
  });

  $('.content-tabs .content-tab-wrap .tab').hide();
  $('.content-tabs .content-tab-wrap > .tab:first-child').show();
  $('.content-tab-nav .options a:first-child').addClass('on');

  $('.content-tab-nav .options a').click(function(e) {
    e.preventDefault();
    var theText = $(this).text();
    $(this).parent().prev('.toggle').removeClass('opened');
    $(this).closest('.content-tab-nav').find('.default').text(theText);
    $(this).siblings().removeClass('on');
    $(this).addClass('on');

    $('.content-tabs .content-tab-wrap').children().hide();
    var activeTab = $(this).attr('href');
    $(activeTab).show();
    $('.content-tabs .flexslider').resize();
  });
  $(document).mouseup(function(e) {
    var container = $('.content-tab-nav .toggle.opened');
    if (!container.is(e.target) && container.has(e.target).length === 0) {
      $('.content-tab-nav .toggle').removeClass('opened');
      $('.content-tab-nav .options').slideUp();
    }
  });

  // Default Tabs & Product Detail Tabs
  $('.tabs .tab-wrap .tab').hide();
  $('.product-tabs .product-tab').hide();
  $(document.body).on('click', '.tabs .tab-nav a', function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $(this).parent().next().children().hide();
    var activetab = $(this).attr('href');
    $(activetab).show();
  });
  $('.product-tabs .links a').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $('.product-tab').hide();
    var activeTab = $(this).attr('href');
    $(activeTab).show();
  });
  if (!window.location.hash) {
    $('.tabs .tab-wrap > .tab:first-child').show();
    $('.tabs .tab-nav a:first-child').addClass('on');

    // Product Detail Tabs
    $('.product-tabs .tab-wrap > .product-tab:first-child').show();
    $('.product-tabs .links a:first-child').addClass('on');
  } else {
    if ($('.tabs')[0]) {
      $('.tabs .tab-nav a[href="' + window.location.hash + '"]').addClass('on');
      $('.tabs .tab-wrap ' + window.location.hash).show();
      $('html, body').animate({
        scrollTop: ($('.tabs .tab-nav').offset().top - 80)
      });
    }
    // Product Detail Tabs
    if ($('.product-tabs')[0]) {
      var top = $('.product-tabs .links').offset().top;
      $('.product-tabs .links a[href="' + window.location.hash + '"]').addClass('on');
      $('.product-tabs .tab-wrap ' + window.location.hash).show();
      $('html, body').animate({
        scrollTop: (top - 100)
      });
    }
  }

  // Home Tabs
  $('.home-tabs .tab-wrap > .tab:first-child').addClass('show-tab');
  $('.home-tabs .tab-nav a:first-child').addClass('on');
  $('.home-tabs .tab-nav a').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $(this).parent().next().children().removeClass('show-tab');
    var activeTab = $(this).attr('href');
    $(activeTab).addClass('show-tab');
  });

  // Background Color Tabs
  $('.background-color-tabs .tab-wrap > .tab').hide();
  $('.background-color-tabs .tab-wrap > .tab:first-child').show();
  $('.background-color-tabs .tab-nav a').click(function(e) {
    e.preventDefault();
    $('.background-color-tabs .tab-wrap').children().hide();
    var activeTab = $(this).attr('href');
    $(activeTab).show();
  });

  // Background Tabs
  $('.background-tabs .tab-wrap > .tab').hide();
  $('.background-tabs .tab-wrap > .tab:first-child').show();
  $('.background-tabs .tab-nav a:first-child').addClass('on');
  $('.background-tabs .tab-nav a').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $('.background-tabs .tab-wrap').children().hide();
    var activeTab = $(this).attr('href');
    $(activeTab).show();
  });

  // Section Tabs
  $('.section-tabs .tab-wrap .tab').hide();
  $('.section-tabs .tab-wrap > .tab.show-tab').show();
  $('.section-tabs .tab-nav a.show-tab').addClass('on');
  $('.section-tabs .tab-nav a').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $('.section-tabs .tab-wrap').children().hide();
    var activeTab = $(this).attr('href');
    $(activeTab).show();
  });

  // Tabs Block
  $('.tab-block .tab-nav li:first-child').addClass('on');
  $('.tab-block .tab-nav li').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $(this).parent().next().children().hide();
    var activeTab = $(this).find('a').attr('href');
    $(activeTab).show();
  });

  // Tabs Explore
  $('.tab-explore .tab-nav li:first-child').addClass('on');
  $('.tab-explore .tab-nav li').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $(this).parent().next().children().hide();
    var activeTab = $(this).find('a').attr('href');
    $(activeTab).show();

    var tabID = $('.tab-explore .tab-wrap .tab:visible').attr('id');
    $('.tab-explore .tab-nav .first').removeAttr('rel').attr('rel', tabID);
  });

  // Tour Tabs
  $('.tour-tabs .tour-tab-nav li:first-child').addClass('on');
  $('.tour-tabs .tour-tab-nav li').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $(this).parent().next().children().hide();
    var activeTab = $(this).find('a').attr('href');
    $(activeTab).show();
  });

  // Hot Spots
  $('.hot-spot-content .spot').first().show();
  $('.hot-spots a.spot-link').first().addClass('on');
  $('.hot-spots a.spot-link').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $('.hot-spot-content .spot').hide();
    var activeTab = $(this).attr('href');
    $(activeTab).show();
    $('html, body').animate({
      scrollTop: ($(activeTab).offset().top - 79)
    }, 500);
  });

  // Video swap
  $('.vid-wrap.swap').hide();
  $('a.vid-swap').click(function(e) {
    e.preventDefault();
    var activeVideo = $(this).attr('href');
    $(activeVideo).show();
    $(activeVideo).next().hide();
  });

  // Modal Tabs
  $('.modal-tabs .modal-tab-nav li:first-child').addClass('on');
  $('.modal-tabs .modal-tab-nav li').click(function(e) {
    e.preventDefault();
    $(this).siblings().removeClass('on');
    $(this).addClass('on');
    $(this).parent().next().find('.tab-wrap').children().hide();
    var activeTab = $(this).find('a').attr('href');
    $(activeTab).show();
  });

  // Section modal inline link
  $('.section-modal-link').click(function(e) {
    e.preventDefault();
    var sectionModal = $(this).attr('href');
    $(sectionModal).css({
      'top': $(this).offset().top + 40
    }).fadeIn();
    $(sectionModal + ' .indicator').css({
      'left': $(this).offset().left + $(this).width() / 2 - 18
    }).fadeIn();
  });
  // Section modal block
  $('.section-modal-box').click(function(e) {
    e.preventDefault();
    var sectionModal = $(this).attr('rel');
    $('.section-modal').hide();
    $('#' + sectionModal).css({
      'top': $(this).offset().top + $(this).height() + 60
    }).fadeIn();
    $('#' + sectionModal + ' .indicator').css({
      'left': $(this).offset().left + $(this).width() / 2 + 15
    }).fadeIn();
  });
  $('.close-section-modal').click(function(e) {
    e.preventDefault();
    $(this).parent().fadeOut();
  });
  $(window).resize(function() {
    $('.section-modal').hide();
  });

  // // Magnific Popup
  // $('.open-modal').magnificPopup({
  // 	type:'inline'
  // });
  //
  // $('.open-chat-modal').magnificPopup({
  // 	type:'inline',
  // 	callbacks:
  // 	{
  // 		close : function(){
  // 			location.reload();
  // 		}
  // 	}
  // });
  //
  // // Magnific Gallery Link
  // $('.gallery-link').magnificPopup({
  // 	type: 'image',
  // 	gallery: {
  // 		enabled: true,
  // 		navigateByImgClick: true,
  // 		tCounter: '%curr% / %total%'
  // 	},
  // 	callbacks: {
  // 		buildControls: function () {
  // 			// re-appends controls inside the main container
  // 			if (this.arrowLeft != null) {
  // 				this.contentContainer.append(this.arrowLeft.add(this.arrowRight));
  // 			}
  // 			this.contentContainer.prepend('<h4 class="header">Gallery Link Title</h4>');
  // 		}
  // 	}
  // });
  //
  // // Magnific Popup close button
  // $('.close-modal').click(function (e) {
  // 	e.preventDefault();
  // 	$.magnificPopup.close();
  // });

  // Scroll Tabs
  $('.tabs .scroll-tabs-wrap .tab').hide();
  $('.tabs .scroll-tabs-wrap > .tab:first-child').show();
  $('.scroll-tabs').scrollTabs({
    scroll_distance: 350,
    scroll_duration: 350,
    left_arrow_size: 20,
    right_arrow_size: 20,
    click_callback: function(e) {
      $(this).siblings().removeClass('on');
      $(this).addClass('on');
      $('.scroll-tabs-wrap').children().hide();
      var activeTab = $(this).attr('rel');
      $('#' + activeTab).show();
    }
  });
  $('.scroll_tab_first').addClass('on');

  // Jquery UI datepicker
  $('#to').datepicker({
    showOn: 'button',
    buttonText: ' '
  });
  $('#from').datepicker({
    showOn: 'button',
    buttonText: ' '
  });

  // Jquery UI ranger slider
  $('#time-frame').slider({
    range: true,
    min: 1,
    max: 24,
    values: [9, 13],
    step: 1,
    slide: function(event, ui) {
      $('#time-frame-amount').val(ui.values[0] + ' - ' + ui.values[1] + ' hrs');
    }
  });
  $('#time-frame-amount').val($('#time-frame').slider('values', 0) + ' - ' + $('#time-frame').slider('values', 1) + ' hrs');

  $('#range').slider({
    range: 'min',
    value: 6,
    min: 1,
    max: 24,
    step: 1,
    slide: function(event, ui) {
      $('#range-amount').val(ui.value + ' hrs');
    }
  });
  $('#range-amount').val($('#range').slider('value') + 'hrs');

  // Custom Scrollbar
  $('.scroll-filters, .scroll-quickshop, .scroll-cart, .scroll-list, .scroll-language, .scroll-content, .tab-block .tab-wrap .tab, .tab-explore .tab-wrap .tab').mCustomScrollbar({
    alwaysShowScrollbar: 1,
    scrollButtons: {
      enable: true
    }
  });

  // Custom Select
  $('select').SumoSelect();

  // Match height plugin
  $('.match-height').matchHeight();

  $('.track-height').matchHeight({
    byRow: false,
  });

});

$(window).on("load", function(e) {
  equalHeight();
  navHeightCheck();
  sourceOrder();
  crumbsCheck();
});
$(window).resize(function() {
  if (window.matchMedia('(min-width:768px)').matches) {
    $('.side-nav').removeAttr('style');
    $('.mobile-side-nav-toggle span').removeClass('opened');
  }
  $('div.equals div.equal').removeAttr('style');
  equalHeight();
  navHeightCheck();
  sourceOrder();
  crumbsCheck();
});
// Equal height columns
function equalHeight() {
  if (window.matchMedia('(min-width:768px)').matches) {
    $('div.equals').each(function(index) {
      var height = $(this).height();
      $(this).find('div.equal').css('height', height - 20);
    });
  }
}
// Change locations
function sourceOrder() {
  if (window.matchMedia('(min-width: 768px)').matches) {
    $('#move').insertBefore('#location-1');
  } else {
    $('#move').insertBefore('#location-2');
  }
}
// Nav height check
function navHeightCheck() {
  var heightCheck = $(window).height();
  if (heightCheck < 600) {
    $('header .desktop ul li:first-child span.go-to-link').show();
    $('header .desktop ul li:first-child span.go-to-link').click(function(e) {
      e.preventDefault();
      $('header .desktop ul li:first-child a').off();
      window.location = $(this).parent().attr('href');
    });
  } else {
    $('header .desktop ul li:first-child span.go-to-link').hide();
  }
}
// Crumbs check
function crumbsCheck() {
  var browserWidth = $(window).width();
  var parentCrumbsWidth = $('nav.crumbs > div > ul:first-child').width();
  var currentCrumbWidth = $('nav.crumbs > div > ul:last-child').width();
  var offsetWidth = 50;

  if (parentCrumbsWidth + currentCrumbWidth + offsetWidth < browserWidth) {
    $('nav.crumbs').addClass('expand');
  } else {
    $('nav.crumbs').removeClass('expand');
  }
}
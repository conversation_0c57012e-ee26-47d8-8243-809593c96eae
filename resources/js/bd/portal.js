﻿// Header navigation
$(document).ready(function(){

	// Mobile nav menu accordion list
	$('.mobile .accordion-list li a.trigger').click(function (e){
		e.preventDefault();
		$(this).toggleClass('opened');
		$(this).next().slideToggle();
	});

	// Header user toggle
	$('header .links a.user').click(function (e) {
		e.preventDefault();
		$('header .portal-nav').removeAttr('style');
		$('header .user-options').slideToggle();
		$(this).toggleClass('opened');
	});
	$('header .user-options a.close').click(function (e) {
		e.preventDefault();
		clearNav();
	});
	$(document).mouseup(function (e) {
		var container = $('header .user-options, header .links a.user');
		if (!container.is(e.target) && container.has(e.target).length === 0) {
			container.hide().removeAttr('style');
			$('header .links a.user').removeClass('opened');
		}
	});

	// Header portal nav toggle
	$('header a.portal-nav-toggle').click(function (e) {
		e.preventDefault();
		$(this).toggleClass('opened');
		$('header .user-options').removeAttr('style');
		$('header .portal-nav').slideToggle(200);
	});
	$(document).mouseup(function (e) {
		var container = $('header .portal-nav-toggle, header .portal-nav');
		if (!container.is(e.target) && container.has(e.target).length === 0) {
			container.hide().removeAttr('style');
			$('header a.portal-nav-toggle').removeClass('opened');
		}
	});
	$('header .portal-nav a').click(function (e) {
		e.preventDefault();
		var theText = $(this).text();
		$('header a.portal-nav-toggle').removeClass('opened');
		$('header a.portal-nav-toggle span.text').text(theText);
		$('header .portal-nav').slideToggle(200);
	});

	// Open the mobile navigation
	$('header a.mobile-toggle').click(function (e) {
		e.preventDefault();
		$('nav.mobile').slideToggle();
		$(this).toggleClass('opened');
	});

	// Open Side Filters
	$('.side-filters-wrap a').click(function (e){
		e.preventDefault();
		$('body').css('overflow', 'hidden');
		$('#page').addClass('behind');
		$('#side-filters').show().animate({
			right: '0'
		}, 200, function() {
			$('body').removeAttr('style');
		});
	});

	// Close Side Filters
	$('#side-filters .close a, #side-filters .filters a.apply').click(function (e) {
		e.preventDefault();
		$('body').css('overflow', 'hidden');
		$('#side-filters').animate({
			right: '-100%'
		}, 200, function() {
			$('body').removeAttr('style');
			$('#page').removeClass('behind');
			$('#side-filters').removeAttr('style');
		});
	});

	// Scroll to top
	$('#top').click(function (e) {
		e.preventDefault();
		$('html, body').animate({scrollTop:0}, '500');
	});

});

// Nav breakpoint checks
$(window).resize(function () {
	desktopNavBreakpointCheck();
	mobileNavBreakpointCheck();
});

function desktopNavBreakpointCheck() {
	if (!window.matchMedia('(min-width:992px)').matches) {
		clearNav();
	}
}
function mobileNavBreakpointCheck() {
	if (window.matchMedia('(min-width:992px)').matches) {
		$('header a.mobile-toggle').removeClass('opened');
		$('nav.mobile').removeAttr('style');
	}
}

// Clear nav function
function clearNav() {

	$('header .links a.user').removeClass('opened');
	$('header .user-options').removeAttr('style');
	$('header a.portal-nav-toggle').removeClass('opened');
	$('header .portal-nav').removeAttr('style');

}

// Fixed Nav
$(document).scroll(function () {
	var y = $(this).scrollTop();
	if (y > 1) {
		$('#page').addClass('scrolled');
	} else {
		$('#page').removeClass('scrolled');
	}
});

// Show scroll to top button
$(window).scroll(function(){

	// Toggle class for fixed menu
	if ($(this).scrollTop() > 100) {
		$('#top').addClass('fixed');
	} else {
		$('#top').removeClass('fixed');
	}

});

// Non navigation
$(document).ready(function(){

	// Default Tabs & Product Detail Tabs
	$('.tabs .tab-wrap .tab').hide();
	$(document.body).on('click', '.tabs .tab-nav a',function (e){
		e.preventDefault();
		$(this).siblings().removeClass('on');
		$(this).addClass('on');
		var activetab = $(this).attr('href');
		$(activetab).siblings().hide();
		$(activetab).show();
	});
	if (!window.location.hash)
	{
		$('.tabs .tab-wrap > .tab:first-child').show();
		$('.tabs .tab-nav a:first-child').addClass('on');
	}
	else{
		if ($('.tabs')[0])
		{
			$('.tabs .tab-nav a[href="' + window.location.hash + '"]').addClass('on');
			$('.tabs .tab-wrap ' + window.location.hash).show();
			$('html, body').animate({
				scrollTop: ($('.tabs .tab-nav').offset().top - 80)
			});
		}
	}

	// Dismiss Notice
	$('.notice .dismiss a').click(function (e) {
		e.preventDefault();
		$(this).parent().parent('.notice').fadeOut(200);
	});

	// Tags
	$('.tags > span > a, .box .selections .tags li a').click(function (e) {
		e.preventDefault();
		$(this).parent().fadeOut(200);
	});

	// Card Toggle
	$('a.card-toggle').click(function (e) {
		e.preventDefault();
		var activeCard = $(this).attr('href');
		$('.card-wrap .card.flipped').removeClass('flipped');
		$(activeCard).children('.card').addClass('flipped');
	});
	$('a.card-close').click(function (e) {
		e.preventDefault();
		$('.card-wrap .card.flipped').removeClass('flipped');
	});

	// Options Toggle
	$('a.options-toggle').click(function (e) {
		e.preventDefault();
		if ( $(this).hasClass('opened') ) {
			$(this).removeClass('opened');
			$(this).next('.options-dropdown').fadeOut(100);
		} else {
			$('a.options-toggle').removeClass('opened');
			$('.options-dropdown').fadeOut(100);
			$(this).addClass('opened');
			$(this).next('.options-dropdown').fadeIn(200);
		}
	});

	// Notifications Table
	$('.notifications-table .name a').click(function (e) {
		e.preventDefault();
		$(this).parent().toggleClass('checked');
	});

	// Advanced List Table
	$('.advanced-list .search-wrap a.toggle').click(function (e) {
		e.preventDefault();
		$(this).toggleClass('checked');
	});

	// Advanced List Table
	$('.advanced-list .list-table .name a').click(function (e) {
		e.preventDefault();
		$(this).parent().toggleClass('checked');
	});

	// Notifications Table
	$('.side .accordion > a').click(function (e) {
		e.preventDefault();
		$(this).toggleClass('closed');
		$(this).next().slideToggle(200);
	});

	// Docs Accordions
	$('.docs-accordions .accordion > ul:not(.opened)').hide();
	$('.docs-accordions .accordion > a').click(function (e) {
		e.preventDefault();
		$(this).toggleClass('opened');
		$(this).next().slideToggle(200);
	});

	// Message Close
	$('.message a.close, .message a.check-link').click(function (e) {
		e.preventDefault();
		$('.message').fadeOut(200);
	});

	// Inquiry Table Preview
	$('.inquiry-table tbody tr').hover(function(){
		var showInquiry = $(this).attr('rel');
		$(showInquiry).show();
		$('.inquiry-text').hide();
	},function(){
		$('.inquiry-preview .inquiry').hide();
		$('.inquiry-text').show();
	});

	// Box Header Toggle
	$('.box .header.toggle a').click(function (e) {
		e.preventDefault();
		$(this).toggleClass('closed');
		$(this).parent().next().slideToggle(200);
	});

	// More Content Toggle
	$('.more-content').hide();
	$('.more-button').click(function (e) {
		e.preventDefault();
		var moreContent = $(this).attr('rel');
		$('.more-content.'+ moreContent).slideToggle(200);
		$(this).toggleClass('less');
	});

	// Side Nav Toggle
	$('.side-nav-toggle h5 a').click(function (e) {
		e.preventDefault();
		$(this).toggleClass('opened');
		$(this).parent().next().slideToggle();
	});

	// Custom Scrollbar
	$('.scroll-content').mCustomScrollbar({
		alwaysShowScrollbar:0,
		scrollButtons:{
			enable:true
		}
	});

	// Custom Select
	$('select').SumoSelect();

	// Match height plugin
	$('.match-height').matchHeight();

	// Magnific Popup
	$('.open-modal').magnificPopup({
		type:'inline'
	});

});

// Flexslider resize
$(function() { 
	var resizeEnd;
	$(window).on('resize', function() {
		clearTimeout(resizeEnd);
		resizeEnd = setTimeout(function() {
			flexsliderResize();
		}, 250);
	});
});
function flexsliderResize(){ 
	if ($('.flexslider').length > 0) {
		$('.flexslider').data('flexslider').resize();
	}
}
var cartpopup = {
	
	bindAll: function()
	{
		this.bindCartPop();
	},
	bindCartPop: function()
	{
		
		$('#rollover_cart_popup').hide();
		
		$('#cart_content').hover(
				function() { $.data(this, 'hover', true); },
				function() { $.data(this, 'hover', false); }
		).data('hover', false);
		
		
		$('#rollover_cart_popup').hover(
				function() { $.data(this, 'hover', true); },
				function() { $.data(this, 'hover', false); }
		).data('hover', false);
		
		$('#cart_content').on('click keypress', function(e) {
			e.preventDefault();
			if (e.type === 'click' || e.which === 13)
			{
				$('#cart_popup').hide();
				$.ajax({
					url: '/us/cart/rollover/BDMiniCartComponent',
					cache: false,
					type: 'GET',
					success: function(result) {
					
						$('#rollover_cart_popup').html(result);
						$('#rollover_cart_popup').fadeIn();
						
					}
				});
			}
		});
		
		$('#show_minicart').on('click keypress', function(e) {
			if (typeof refreshMiniCart == 'function') {
				refreshMiniCart();
				sleep(500);
			}
			
			if (e.type === 'click' || e.which === 13)
			{
				$.ajax({
					url: '/us/cart/rollover/BDMiniCartComponent',
					cache: false,
					type: 'GET',
					success: function(result) {
						
						$('#rollover_cart_popup').html(result);
						//$('#rollover_cart_popup').fadeIn();
						//$('#rollover_cart_popup').delay(3000).fadeOut('fast');
					}
				});
				
				
			}
			
			$('#rollover_cart_popup').fadeIn();
			$('#rollover_cart_popup').delay(3000).fadeOut('fast');
			
		});

		$(document).on('click', '#ajax_cart_close', function(e) {
			e.preventDefault();
			$('#rollover_cart_popup').hide();
		});

		$('#cart_content').mouseleave(function() {
			setTimeout(function() {
				if (!$('#cart_content').data('hover') && !$('#rollover_cart_popup').data('hover')) {
					$('#rollover_cart_popup').fadeOut();
				}
			}, 100);
		});
		
		$('#rollover_cart_popup').mouseenter(function() {
			$('#rollover_cart_popup').show();
		});
		$('#rollover_cart_popup').mouseleave(function() {
			setTimeout(function() {
				if(!$('#cart_content').data('hover') && !$('#rollover_cart_popup').data('hover')) {
					$('#rollover_cart_popup').fadeOut();
				}
			}, 100);
		});
	
	}

};

$(document).ready(function() {
	cartpopup.bindAll();
});

function refreshMiniCart() {
	$.get(refreshMiniCartUrl + Math.floor(Math.random()*101) * (new Date().getTime()), function(result) {
		$.ajax({
			url: '/us/cart/rollover/BDMiniCartComponent',
			cache: false,
			type: 'GET',
			success: function(response) {
				$('#rollover_cart_popup').html(response);
				$('#minicart_data').html(result);
				$('#rollover_cart_popup').fadeIn();
				$('#rollover_cart_popup').delay(10000).fadeOut('fast');
			},
			error: function(response) {
				console.log('error in rolloverPopupUrl');
			}
		});
	});
}


function sleep(milliseconds) {
	  var start = new Date().getTime();
	  for (var i = 0; i < 1e7; i++) {
	    if ((new Date().getTime() - start) > milliseconds){
	      break;
	    }
	  }
	}
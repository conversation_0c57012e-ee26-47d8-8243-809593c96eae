$(document).ready(function(){

	// Sales/submit popup - select the right popup tab
	$(document.body).on('click', '.contact.modal',  function() {
		$("#modal .modal-tab-nav a").eq(0).click();
		setMarketoFormValues(); 
		setMarketoCookieValuesInForm();
		setInterval(setMarketoFormValues, 1000);
		$(this).magnificPopup({
			closeOnBgClick:false,
			overflowY:'scroll',
			type:'inline'
		});
		return false;
	});
	$(document.body).on('click', '.support.modal', function() {
		$("#modal .modal-tab-nav a").eq(1).click();
		setMarketoFormValues(); 
		setInterval(setMarketoFormValues, 1000);
		$(this).magnificPopup({
			closeOnBgClick:false,
			overflowY:'scroll',
			type:'inline'
		});
		return false;
	});

	setCapabilityOptions();
	$('#capabilitydropdown').change(function (e) {
		setCapabilityOptions();
	});

	$('#productline').change(function (e) {
		setMarketoFormValues();
	});

	$(document.body).on('click', '.open-modal:not(.loaded)', function(){
		var filterbox = $(".support-filter-wrapper");
		if (filterbox.data('url') != "")
		{
			filterbox.html('<p class="big"><i class="fa fa-spinner fa-spin" aria-hidden="true"></i></p>');
			$.get(filterbox.data('url'), function(data) {
				filterbox.html(data);
				refreshSupportCenter();
					//filterbox.find('select').selectBoxIt({
					//	autoWidth:false
					//});
				$(".region-select").val('');
				$("#modal .support-filters select").change(function() {
					if ($(this).hasClass('country-filter'))
					{
						var regionBlock = $(".region-parent");
						var hasRegion = $('.country-filter :selected').data('hasregions');
						if (hasRegion == "True")
						{
							regionBlock.find("select").hide().val('');
							regionBlock.find("select .country-" + $(this).val()).show();
							regionBlock.show();
						}
						else
						{
							regionBlock.hide();
						}
					}
					refreshSupportCenter();
				});
			}, 'text');
		}
		$('.open-modal').addClass('loaded');
	});
	$('.side-nav.checklist li ul li a').each(function()
	{
		var html = $(this).html();
		if (html){
			html = html.replace(/�/gi, '<sup>�</sup>');
			$(this).html(html);
		}
	});
	$('.selectboxit-text').css('max-width', '200px');
	$('#productline').change(function(){
		if ($('#productline option:selected').text() != 'Please Select') {
			$('#productline').parent('.SumoSelect').removeClass('sumo-error');
		}
	});
	
	$('input.reset-button').click(function(e){
		var switches = $('.switch');
		for (var i =0; i < switches.length; i++)
		{
			var s = $(switches[i]);
			if (s.children('.on').data('default') != "")
			{
				s.children().removeClass('active');
				s.children('.on').addClass('active');
			}
			else
			{
				s.children().removeClass('active');
				s.children('.off').addClass('active');				
			}
		}
		$.magnificPopup.close();
	});
	// Magnific Popup
	$('.open-modal-ia').magnificPopup({
		type:'inline',
		callbacks: {
			open: function() {
			  $.magnificPopup.instance.close = function() {
				var switches = $('.switch');
				for (var i =0; i < switches.length; i++)
				{
					var s = $(switches[i]);
					if (s.children('.on').data('default') != "")
					{
						s.children().removeClass('active');
						s.children('.on').addClass('active');
					}
					else
					{
						s.children().removeClass('active');
						s.children('.off').addClass('active');				
					}
				}
				// Call the original close method to close the popup
				$.magnificPopup.proto.close.call(this);
			  };
			}
		}	
	});	
});

function setCapabilityOptions()
{
	//get current capability
	var current = $("#capabilitydropdown").find(":selected").data("cap");
	var newOptions = $('#holder option.all, #holder option.cap-' + current).clone();
	var select = document.getElementById("productline");
	if (select != null)
	{
		var length = select.options.length;
		for (i = 0; i < length; i++) {
			select.options[i] = null;
		}
		var warning = "";
		if ($('#productline').parent('.SumoSelect').hasClass('sumo-error'))
		{
			warning = "sumo-error";
		}

		$('#productline').html(newOptions);
		//$('#productline').selectBoxIt('refresh');
		$('#productline').parent('.SumoSelect').addClass(warning);
		$('#capabilitydropdown').parent('.SumoSelect').removeClass('sumo-error')
		setMarketoFormValues();
		$('#productline')[0].sumo.reload();
	}
}

function setMarketoFormValues() {
	var cap = $("#productline").children('option:selected');
	var prod = $('#prodname');
	//var prod = $("#productline").children('option:selected');
	if (cap != null)
	{
		var value1 = cap.data('hidden1');
		var value2 = cap.data('hidden2');
		if (value1 != null)
		{
			$("input[name='Product_Line_of_Interest__c']").val(value1); 
			$("input[name='mPSProductInquiry']").val(value1);
		}
		if (value2 != null)
		{
			$("input[name='Business_Unit__c']").val(value2); 
		}
	}
	if (prod != null)
	{
		var prodname = prod.data("prodname");
		if (prodname != null)
		{
			$("input[name='Product_Name__c']").val(prodname); //TODO: change to real field
		}
	}
}

function setMarketoCookieValuesInForm(){
	$("form.mktoForm #FirstName").val($("#marketo_hidden_form #firstName").val());
	$("form.mktoForm #LastName").val($("#marketo_hidden_form #lastName").val());
	$("form.mktoForm #Address").val($("#marketo_hidden_form #address").val());
	$("form.mktoForm #City").val($("#marketo_hidden_form #city").val());
	$("form.mktoForm #Title__c").val($("#marketo_hidden_form #title").val());
	$("form.mktoForm #State").val($("#marketo_hidden_form #state").val());
	$("form.mktoForm #PostalCode").val($("#marketo_hidden_form #postalCode").val());
	$("form.mktoForm #Phone").val($("#marketo_hidden_form #phone").val());
	$("form.mktoForm #Country").val($("#marketo_hidden_form #country").val());
	$("form.mktoForm #facility").val($("#marketo_hidden_form #facility").val());
	$("form.mktoForm #Email").val($("#marketo_hidden_form #email").val());
}

function refreshSupportCenter()
{
	var capability = $("select.capability").val();
	var supportDiv = $(".support-content");
	var messageDiv = $(".support-default");
	if (capability != "")
	{
		var url = $('#supportselector').children('option:selected');
		if (url != null && url != "" && url.attr("data-url"))
		{
			url = url.data('url');
			supportDiv.html('<p class="big"><i class="fa fa-spinner fa-spin" aria-hidden="true"></i></p>');
			messageDiv.hide();
			url += supportDiv.data('url');

			if (url != null  && url != "")
			{
				url += url.indexOf("?") != -1 ? "&" : "?";
				url += url.indexOf("ajax=true") == -1 ? "ajax=true&" : ""
				url += url.indexOf("?") != -1 ? "&" : "?";
				url += url.indexOf("modalwindow=true") == -1 ? "modalwindow=true&" : ""
				$("#modal .support-filters select").each(function() {
					if ($(this).val() != "" && $(this).data('key') != "")
					{
						url+= $(this).data('key') + "=" + $(this).val() + "&";
					}
				});
				$.get(url, function(data) {
					supportDiv.html(data);
					supportDiv.find('div.accordions div.hide').hide();
					supportDiv.find('div.accordions div.show h2').css('cursor','pointer');
					supportDiv.find('div.accordions div.show h2').click(function(){
						$(this).parent().toggleClass('open');
						$(this).parent().next().slideToggle(100);
					});
					$('.scroll-list, .scroll-language, .scroll-content, .tab-block .tab-wrap .tab, .tab-explore .tab-wrap .tab').mCustomScrollbar({
						alwaysShowScrollbar: 1
					});

					messageDiv.hide();
				}, 'text');
			}
			
		}
	}
	else
	{
		supportDiv.html("");
		messageDiv.show();
	}
}

/*
// Magnific Popup show modal on window load
$(document).ready(function(){
	$.magnificPopup.open({
		items:{
			src: '#modal-1'
		},
		type:'inline'
	}, 0);
});
*/
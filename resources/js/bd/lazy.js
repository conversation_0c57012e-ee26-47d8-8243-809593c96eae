/**
 * Lazy typer class to invoke callback with delay and value checking
 * @param {*} inputNode 
 * @param {*} lazyHandler 
 * @param {*} threshold 
 * @param {*} scope 
 */
var LazyTyper = function (inputNode, lazyHandler, threshold, scope) {
	this.inputNode = inputNode;
	this.lazyHandler = lazyHandler;
	if (scope)
		this.handlerScope = scope;
	if (threshold)
		this.threshold = threshold;

	this.init();
}

LazyTyper.prototype.init = function () {
	var _t = this;
	this.inputNode.onkeyup= function(evt) {
		_t.typing();
	}
	//dojo.event.connect(this.inputNode, "onkeyup", this, "typing");
};

LazyTyper.prototype.destroy= function() {
	//dojo.event.disconnect(this.scrollingNode, "onkeyup", this, "typing");
	this.lazyHandler = null;
	delete this.lazyHandler;
};

LazyTyper.prototype.typing= function() {
	if (this._to) {
		clearTimeout(this._to);
	}

	this.text = this.inputNode.value;
	var _t = this;
	this._to = setTimeout(function() {
		_t.lazyType();
	}, this.threshold);
};

LazyTyper.prototype.lazyType= function() {
	var newText = this.inputNode.value;
	if (newText == this.text) {
		if (this.handlerScope)
			this.lazyHandler.call(this.handlerScope);
		else
			this.lazyHandler();
	}
};

var autocomplete = {

	bindAll: function ()
	{
		this.bindSearchAutocomplete();
	},

	bindSearchAutocomplete: function ()
	{
		var $search = $("#s_input");
		var option  = $search.data("options");
		var cache   = {};
		var currentRegion = "";		
		var perCategoryLimit =  5;
		var enableCaching = false; 
		var resultCount = 35;
		var typeDelay = 1000;
		var minChars = 2;
		//if (siteUId != null) {
		//	currentRegion = "/" + siteUId;
		//}

		if (option)
		{
			$.widget("custom.catcomplete", $.ui.autocomplete, {
			    _renderMenu: function(ul, items) {
			    	var that = this,
			        currentCategory = "";
			        perCategoryindex = 0;
			        $.each(items, function(index, item) {
			        	perCategoryindex++;
			        	if (item.category != currentCategory) {
				        	if (item.category == "Specificities") {
				        		var specifictyHtml = "<a class='browseAll' href='#'>Browse All&nbsp;-></a>";
							    ul.append("<li class='section'>" + item.category + "&nbsp;&nbsp;" + specifictyHtml + "</li>");
				        	} else {
				        		ul.append("<li class='section'>" + item.category + "</li>");
				        	}
						    currentCategory = item.category;
						    perCategoryindex = 0;
			        	} 
			        	if (perCategoryindex < perCategoryLimit) {
					      	that._renderItem(ul, item);
				        }
			        });
			    },
			    _renderItem: function(ul, item) {
					  ul.addClass('search_autocomplete');
					  ul.addClass('predict_search');
					  var terms = item.term.split(/[\s]+/);
					  if (typeof item.value !== 'undefined') {
						  var itemValue = item.value;
						  for (var i = 0; i < terms.length; i++) {
							  if(terms[i] != '')
							  {
							  itemValue = replaceAll(itemValue, terms[i], '<b>' + terms[i] + '</b>');
							  }
						  }
						  var renderHtml = "<a href='" + item.url + "'>" +  itemValue + "</a>";									
					  }
					  return $("<li></li>").data("item.autocomplete", item ).append(renderHtml).appendTo(ul);
			    }
			});
			$("#s_input").catcomplete({
				minLength: option.minCharactersBeforeRequest,
                delay:     option.waitTimeBeforeRequest,
				appendTo:  "#searchBoxFieldSet",
				select: function (event, ui)
                {
					if(typeof ui.item !== 'undefined'){
						window.location.href = ui.item.url;
					} else {
						window.location.href = currentRegion + '/specificities';
					}
                },
                focus: function (event, ui)
                {
                    return false;
                },
                open: function(event, ui) 
    			{
    				$(".ui-menu").css("z-index", 10000 );
    				$(".ui-menu").css("top", "30px" );
    				$(".ui-menu").css("width", "374px" );
    				$('#nav_main ul li a.ui-corner-all').css('width', '240px');
    			},
    			close: function(event, ui) 
    			{ 
    				$(".ui-menu").css("z-index", -1);
    				$(".ui-menu").css("top", "30px" );
    				$(".ui-menu").css("width", "374px" );
    				$('li.La').children('a').css('width', '159px');
    				$('div.menu_container').find('.headerSearch').css('width', '205px');
    				$('#nav_main ul li a.ui-corner-all').css('width', '140px');
    				$(this).css('width', '149px');
    			},
			    source: function(request, response) {

		            var term = request.term.toLowerCase();

		            if (term in cache) {
		                return response(cache[term]);
		            }

		            $.getJSON(option.autocompleteUrl, {term: request.term}, function(data) {
		            	var specificityData = [];
		                var autoSearchData = [];
		                if(data.specificityFacetValueList != null){
		                    $.each(data.specificityFacetValueList, function (i, obj)
		                    {
		                    	if(obj.name != null ) {
		                    		autoSearchData.push(
		                                    {   value: obj.name,
		                                        url: "/us/solrSearch/autocompleteSecure/" + "?q="+obj.query.query.value.replace(/[:]/g, '%3A'),
		                                        category:"Specificities",
		                                        term:term 
		                                    });                            		
		                    	}
		                    });
		                }
		                if(data.products != null){
		                    $.each(data.products, function (i, obj)
		                    {
		                    	if(obj.labelDescription != null ) {
		                            autoSearchData.push(
		                                    {   value: obj.labelDescription,
		                                        url: "/us/" + obj.url,
		                                        category:"Suggested Results",
		                                        term:term
		                                    });
		                    	}
		                    });
		                }
		                cache[term] = autoSearchData;
		                response(autoSearchData);
		            });
		        }
			});
		}
	}
};

$(document).ready(function ()
{
	autocomplete.bindAll();
	
	$('#s_input').focus(function() {
		$('li.La').children('a').css('width', '115px');
		$('div.menu_container').find('.headerSearch').css('width', '400px');
		$(this).css('width', '344px');
	});	
	$('#s_input').on('input', function() {
		$('ul.predict_search').css('opacity', '1').css('z-index', '10000').css('visibility', 'visible').css('width', '374px');
	});
/*	$('#s_input').blur(function() {
		$(".ui-menu").css("z-index", -1);
		$(".ui-menu").css("top", "30px" );
		$(".ui-menu").css("width", "374px" );
		$('li.La').children('a').css('width', '159px');
		$('#nav_main ul li a.ui-corner-all').css('width', '140px');
		$('div.menu_container').find('.headerSearch').css('width', '205px');
		$(this).css('width', '149px');
	});*/	
});

function replaceAll(str, find, replace) {
	return str.replace(new RegExp(escapeRegExp(find), 'ig'), replace);
}

function escapeRegExp(str) {
    return str.replace(/([.*+?^=!:${}()|\[\]\/\\])/ig, "\\$1");
}
videojs.addLanguage("tr",{
 "Play": "<PERSON><PERSON><PERSON>",
 "Pause": "<PERSON><PERSON><PERSON>",
 "Current Time": "<PERSON>üre",
 "Duration Time": "Toplam Süre",
 "Remaining Time": "Kalan Süre",
 "Stream Type": "<PERSON><PERSON><PERSON><PERSON> Tipi",
 "LIVE": "CANLI",
 "Loaded": "Yüklendi",
 "Progress": "Yükleniyor",
 "Fullscreen": "Tam Ekran",
 "Non-Fullscreen": "Küçük Ekran",
 "Mute": "Ses Kapa",
 "Unmuted": "Ses Aç",
 "Playback Rate": "Oynatma Hızı",
 "Subtitles": "<PERSON><PERSON>zı",
 "subtitles off": "Altyazı Kapat",
 "Captions": "Ek Açıklamalar",
 "captions off": "Ek Açıklamalar Kapalı",
 "Chapters": "<PERSON>ölümler",
 "You aborted the video playback": "Video oynatmayı iptal ettiniz",
 "A network error caused the video download to fail part-way.": "Video indirilirken bağlantı sorunu oluştu.",
 "The video could not be loaded, either because the server or network failed or because the format is not supported.": "Video oynatılamadı, <PERSON>ğ yada sunucu hattası veya belirtilen format desteklenmiyor.",
 "The video playback was aborted due to a corruption problem or because the video used features your browser did not support.": "Tarayıcınız desteklemediği için videoda hata oluştu.",
 "No compatible source was found for this video.": "Video için kaynak bulunamadı."
});
// initialize video.js
//note in the data-src's above that there are no file extensions, e.g., .m4v
videojs("#video-playlist", {"height":"auto", "width":"auto", "techOrder": ["html5", "flash"]}).ready(function(event){
    var myPlayer=this;
	
	this.volume(0.30);

    this.playlist({
        'continuous': true
    });
    
    function resizeVideoJS(){
      var width = document.getElementById(myPlayer.el().id).parentElement.offsetWidth;
      var aspectRatio=9/16;
      myPlayer.width(width).height( width * aspectRatio); 
    }

    resizeVideoJS(); // Initialize the function
    window.onresize = resizeVideoJS; // Call the function on resize   
});


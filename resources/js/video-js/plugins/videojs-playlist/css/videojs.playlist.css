.vjs-default-skin .vjs-big-play-button {
  left: 50%;
  margin: -2em 0 0 -2em;
  top: 50%;
}

/* .video-js, .vjs-control-bar {
} */
.video-js {
  border: 1px solid #E1E1E2;
}

.vjs-poster {
  background-repeat: repeat-x;
}

/* end video.css********************************************************/
.vjs-playlist {
  background-color: #2D2D2D;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

/* .vjs-track {
  color: #F7F9FC;
}

.vjs-track:hover {
  color: #315B7E;
} */
.vjs-playlist ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

.vjs-playlist ul li, #features .vjs-playlist ul li {
  background-color: #f9f9f9;
  -moz-transition-property: background-color;
  -webkit-transition-property: background-color;
  -o-transition-property: background-color;
  transition-property: background-color;
  -moz-transition-duration: .4s;
  -webkit-transition-duration: .4s;
  -o-transition-duration: .4s;
  transition-duration: .4s;
  font-size: 16px;
  /*margin:10px;*/
  border-bottom: 1px solid #E1E1E2;
  font-size: 12px;
  margin-bottom: 0px;
}

.vjs-playlist ul li:last-of-type, #features .vjs-playlist ul li:last-of-type {
  border-bottom: none;
}

.vjs-playlist ul li a {
  text-decoration: none;
  padding: 5px;
  display: block;
}

.vjs-playlist ul li img {
  margin: 0 10px 0 0;
  display: inline-block;
}

.vjs-text {
  position: relative;
  top: 10px;
  display: inline-block;
}

.vjs-playtime {
  position: relative;
  top: 10px;
  display: inline-block;
  float: right;
}

.currentTrack {
  color: gray;
}

.currentTrack {
  background-color: #BFD0E4;
}

.vjs-playlist ul li:hover {
  background-color: #fbfbfb;
  -moz-transition-property: background-color;
  -webkit-transition-property: background-color;
  -o-transition-property: background-color;
  transition-property: background-color;
  -moz-transition-duration: .1s;
  -webkit-transition-duration: .1s;
  -o-transition-duration: .1s;
  transition-duration: .1s;
}
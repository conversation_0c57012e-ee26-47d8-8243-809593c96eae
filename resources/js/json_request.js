//JSON Request - Requires YUI JSON files
// JavaScript Document
function stripslashes(str) {
str=str.replace(/\\'/g,'\'');
str=str.replace(/\\"/g,'"');
str=str.replace(/\\\\/g,'\\');
str=str.replace(/\\0/g,'\0');
return str;
}

function makeRequest(elementId, jsonConnection){
	    // Get the div element in which to report messages from the server
   var div = document.getElementById(elementId);

    // Define the callbacks for the asyncRequest
    var callbacks = {

        success : function (o) {
            YAHOO.log("RAW JSON DATA: " + o.responseText);

            // Process the JSON data returned from the server
            var messages = '';
            try {
                messages = YAHOO.lang.JSON.parse(o.responseText);
            }
            catch (x) {
                alert("JSON Parse failed! Please manually refresh the page to unlock the next section.");
                return;
            }

            YAHOO.log("PARSED DATA: " + YAHOO.lang.dump(messages));
				var m = messages;
				div.innerHTML = '';
				div.innerHTML += stripslashes(m.output);
        },

        failure : function (o) {
            if (!YAHOO.util.Connect.isCallInProgress(o)) {
                alert("Async call failed! Please manually refresh the page to unlock the next section.");
            }
        },

        timeout : 35000
    }

    YAHOO.util.Connect.asyncRequest('GET', jsonConnection, callbacks);
}

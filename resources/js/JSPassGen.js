// JavaScript Document

<!-- //hide

// JSPassGen v2.1
// Javascript password generator
// by <PERSON><PERSON> <j<PERSON><PERSON><PERSON><PERSON>@iname.com>
// heavily based on Jpass v1.1 (Kelv 2000 <EMAIL>)
// http://freshmeat.net/projects/jpass/

/*
//        complete simply working javascript password generator file. Use letter,
//vowels, consonants
//(uppercase and lowercase) arrays to create a really random and secure password.
//        improved security using time functions to initialize random number generator.
//        can be embedded in html and can be used in HTML event handler like onclick
//and onmouseover.
//        tested in Explorer, Netscape and Mozilla enviroment.

 //       This little script uses some arrays of consonants, vowels and digits to
//create a really random
//password
//        As javascript doesn't have a reliable random function, it use more layers of
//randomizers than
//normally required for such a script.

*/

function choice(arg){
//return random index number in valid range of arg array

  return Math.floor(Math.random()*arg.length);
}

function randstr(arg){
//return random argument of arg array

  var str = '';
  var seed = choice(arg);
		  str = arg[seed];
  return str;
}

function initialize(){
//use actual time to initialize random function as javascript doesn't provide an initialization function itself
//to get more random, use getMilliseconds() function
//don't use getTime() as it produces numbers larger than 1000 billions, eheh

  var count=new Date().getSeconds();
  for (c=0; c<count; c++)
		  Math.random();
}

function mkpass(){
  //use of initialize() can decrease speed of script. On really slow systems, disable it.

  initialize();

  //password length
  var pass_len=7;

  var cons_lo =['b','c','d','f','g','h','j','k','l','m','n','p','q','r','s','t','v','w','x','y','z'];
  var cons_up =['B','C','D','F','G','H','J','K','L','M','N','P','Q','R','S','T','V','W','X','Y','Z'];
  var hard_cons_lo = ['b','c','d','f','g','h','k','m','p','s','t','v','z'];
  var hard_cons_up = ['B','C','D','F','G','H','K','M','P','S','T','V','Z'];
  var link_cons_lo = ['h','l','r'];
  var link_cons_up = ['H','L','R'];
  var vowels_lo = ['a','e','i','u'];
  var vowels_up = ['A','E','I','U']; //O (letter o) and 0 (number zero) get confused
  var digits = ['1','2','3','4','5','6','7','8','9'];

  //change at will how many times digits appears in names array. Order doesn't matter
  var names = [cons_lo, cons_up, digits, hard_cons_lo, hard_cons_up, digits, link_cons_lo, link_cons_up, digits, vowels_lo, vowels_up, digits];

  var newpass= '';
  for(i=0; i<pass_len; i++)
   newpass = newpass + randstr(names[choice(names)]);

  return newpass;
}

function MkNumPass(pass_len){
  //use of initialize() can decrease speed of script. On really slow systems, disable it.

  initialize();

  var digits = ['0','1','2','3','4','5','6','7','8','9'];

  //change at will how many times digits appears in names array. Order doesn't matter
  var names = [digits];

  var newpass= '';
  for(i=0; i<pass_len; i++)
   newpass = newpass + randstr(names[choice(names)]);

  return newpass;
}


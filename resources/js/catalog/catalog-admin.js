function editProductDetail(prod_id, page_id){
	var win = window.open("product_catalog_admin.php?action=editProductDetail&product_id=" + prod_id + "&page_id=" + page_id, "popup", "width=855,height=1200,scrollbars=yes");
	win.focus();
}


function editProductSpecField(field_id, page_id){
	var win = window.open("product_catalog_admin.php?action=editProductSpecField&field_id=" + field_id + "&page_id=" + page_id, "popup", "width=855,height=1200,scrollbars=yes");
	win.focus();
}


function deleteProductsConfirmation(){
	return confirm("Are you sure you want to delete the selected product(s)?");
}


function DeleteProductSpecField(field_id, page_id){
	if (confirm('Are you sure you want to delete this field and all its associated options?'))
		location.href = 'product_catalog_admin.php?action=DeleteProductSpecField&field_id='+field_id+"&page_id="+page_id;
}


var saved_order = 0;
function orderSave(el){
	saved_order = parseInt(el.value);
}


function orderChanged(el){
	var val = parseInt(el.value)
	if (isNaN(val)){
		val = 0;
		el.value = "0";
	}

	if (val == saved_order)
		return;
	else
	{
		// Bump other values up if this value has gone down, otherwise bump values down
		var toadd = (val < saved_order) ? 1 : -1;
		reconcileOrder(el, val, toadd);
	}
}


function reconcileOrder(el, ord, toadd){
	var str = '';
	var replace = null;
	var name = (el == null) ? "" : el.name;

	var ordids = new Array();
	for (i=0; i<document.tform.elements.length; i++){
		if (document.tform.elements[i].type == 'text'){
			var x = document.tform.elements[i];
			if (x.id.substring(0, 5) == 'order'){
				if (parseInt(x.value) == ord && x.name != name)
					replace = x;
				ordids.push(x);
			}
		}
	}

	if (replace != null){ // Recurse
	
		replace.value = ord+toadd;
		reconcileOrder(replace, ord+toadd, toadd);
	}
	else{
	
		var pos = 1;
		// Make sure orders are consecutive numbers starting at 1
		while (ordids.length > 0){
			var lowestindex=-1, lowest = Number.MAX_VALUE;

			for(i=0; i<ordids.length; i++){
				if (parseInt(ordids[i].value) < lowest){
					lowestindex = i;
					lowest = parseInt(ordids[i].value);
				}
			}
			ordids[lowestindex].value = pos;
			ordids.splice(lowestindex,1);
			pos++;
		}
	}
}

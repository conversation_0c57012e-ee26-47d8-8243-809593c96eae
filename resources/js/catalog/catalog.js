function catalog_change() {
  var el,
    erase_flag = 0,
    arr = new Array(),
    options = new Array(),
    options_flag = 0,
    selected = new Array(),
    images = new Array();

  // Check first for any stand-alone products
  var add_arrays = '';
  for (i = 0; i < add_fields.length; i++) {
    eval("el = document.tform." + add_fields[i]);
    for (var j = 0; j < el.options.length; j++) {
      if (el.options[j].selected) {
        if (add_arrays != '') {
          add_arrays += ", ";
        }
        add_arrays += add_fields[i] + "[" + j + "]";
      }
    }
  }

  if (add_arrays != '') {
    erase_flag = 1;
    eval("selected = arr.concat(" + add_arrays + ")");
  }

  // Determine selected options, or erase if necessary
  for (i = 0; i < normal_fields.length; i++) {
    var arrays = '';
    eval("el = document.tform." + normal_fields[i]);

    for (var j = 0; j < el.options.length; j++) {
      if (erase_flag == 1) {
        el.options[j].selected = false;
      } else if (el.options[j].selected) {
        if (arrays != '') {
          arrays += ", ";
        }
        arrays += normal_fields[i] + "[" + j + "]";
      }
    }

    if (arrays != '') {
      options_flag = 1;
      eval("var theseoptions = arr.concat(" + arrays + ")");
      theseoptions.sort(numsort);
      if (options.length == 0) {
        options = theseoptions;
      } else {
        options = array_union(options, theseoptions);
      }
    }
  }


  if (erase_flag == 1) {
    el = document.tform.products;
    for (var j = 0; j < el.options.length; j++) {
      el.options[j].selected = false;
    }
    showproducts(selected);
    return;
  }

  // Merge selected options
  selected = new Array();
  if (options.length > 0) {
    selected[0] = options;
  }

  // No products selected, so grab them all
  if (selected.length == 0) {
    for (i = 0; i < product_codes.length; i++) {
      selected[i] = i;
    }
  }

  arrays = '';
  for (i = 0; i < selected.length; i++) {
    if (i > 0) {
      arrays += ", ";
    }
    arrays += "selected[" + i + "]";

    if (options.length == 0) {
      if (options_flag != 0) {
        selected[i] = new Array();
      }
    } else {
      selected[i] = array_union(selected[i], options);
    }
  }

  eval("selected = arr.concat(" + arrays + ")");

  // remove any duplicates from the selected array
  var unique = selected.sort().reduce(function(a, b) {
    if (b != a[0]) a.unshift(b);
    return a
  }, [])

  // Sort ASC
  unique.sort(numsort);

  showproducts(unique);
}

function showproducts(selected) {
  var html = '';
  var el = $("#results");
  if (el) {
    matches = (selected.length == 1) ? "1 match" : selected.length + " matches";
    html = '<div align="right" class="matches">' + matches + " found</div>";
    for (var i = 0; i < selected.length; i++) {
      imgstr = '';
      if (images[selected[i]] != '') {
        imgarray = images[selected[i]].split(',');
        for (j = 0; j < imgarray.length; j++) {
          num = (imgarray.length > 1) ? " #" + (j + 1) : "";
          if (imgstr != '') {
            imgstr += " | ";
          }
          imgstr += "<a href=\"javascript:image_popup('" + imgarray[j] + "')\">Kit Image" + num + "</a>";
        }
        imgstr += "<br>";
      }

      html += desc[selected[i]] + "<br>" + "<i>Product ID</i> - " + product_codes[selected[i]] + "<br>" + imgstr + "<br>";
    }
    $(el).html(html);
  }
}

// When the sort() method compares two values, it sends the values to the compare function, and sorts the values according to the returned (negative, zero, positive) value.
// Example: When comparing 40 and 100, the sort() method calls the compare function(40,100).
// The function calculates 40-100, and returns -60 (a negative value).
// The sort function will sort 40 as a value lower than 100.
function numsort(a, b) {
  return (a - b);
}

// Arrays must be sorted and unique
function array_union(ar1, ar2) {
  var ret = new Array();
  var c2 = 0;
  var count = 0;
  var length2 = ar2.length;
  for (var c1 = 0; c1 < ar1.length; c1++) {
    while (ar1[c1] > ar2[c2] && c2 < length2) {
      c2++;
    }
    if (c2 < length2 && ar1[c1] == ar2[c2]) {
      ret[count++] = ar1[c1];
    }
  }

  return ret;
}

function clear_form() {
  for (i = 0; i < normal_fields.length; i++) {
    eval("el = document.tform." + normal_fields[i]);
    for (var j = 0; j < el.options.length; j++) {
      el.options[j].selected = false;
    }
  }

  for (i = 0; i < add_fields.length; i++) {
    eval("el = document.tform." + add_fields[i]);
    for (var j = 0; j < el.options.length; j++) {
      el.options[j].selected = false;
    }
  }
}

function image_popup(img) {
  /*//var a = window.open("assets/images/kit_config/"+img, 'popup', 'width=800,height=710,resizable=yes,scrollbars=yes');
  //a.focus();*/
  var a = openShadowbox("assets/images/kit_config/" + img, 'img', 'Kit Image');
}
// JavaScript Document

	function writeCookie(name, value, expires){ // expires in days
		var now = new Date();
		now.setTime(now.getTime() + 1000 * 60 * 60 * 24 * expires)
		document.cookie = name + '=' + value + ";expires=" + now.toGMTString();
	}

	writeJsDetectCookie();
	
	function writeJsDetectCookie(){ //see if Javascript is enabled
		writeCookie('jsDetect', 'enabled', 1825);
	}

	var playerVersion = swfobject.getFlashPlayerVersion(); // returns a JavaScript object
	var majorVersion = playerVersion.major; // access the major, minor and release version numbers via their respective properties
		
	if (swfobject.hasFlashPlayerVersion("9.0.115")) { 
		var value = playerVersion.major + "." + playerVersion.minor + "." + playerVersion.release;
		writeCookie('flashDetect', value, 1825);
	}

	if (Plugin.isInstalled(Plugin.getInfo('Acrobat').name) == true) { 
		writeCookie('acrobatDetect', 'enabled', 1825);
	}
	
	function ignore_acrobat() { // set cookie to ignore checking for this plug in. 
		writeCookie('acrobatDetect', 'ignore', 365);
	}

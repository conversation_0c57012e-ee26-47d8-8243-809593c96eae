function confirmDelete(elementId, jsonConnection) {
    var agree = confirm("Delete selected item?");
    if (agree) {
        getRequest(elementId, jsonConnection);

        $(elementId).ajaxSuccess(function() {

            $(this).remove();

        });
    } else
        return false;
}

function removeElement(element) {
    $(document).ready(function() {
        $(element).fadeOut('slow', function() {
            $(element).remove();
        });
    });
}

function reloadFileList() {
    $(document).ready(function() {
        fadeGetRequest('#file_list', 'jsonActions.php?action=file_list', 'loading');
    });
}

function setupFileList() {
    $(document).ready(function() {
        $('#file_list li').hover(
            function() {
                var item_index = $('#file_list li').index(this);
                $('#file_list li span.icon_hldr').eq(item_index).fadeIn(100);
            },
            function() {
                var item_index = $('#file_list li').index(this);
                $('#file_list li span.icon_hldr').eq(item_index).fadeOut(500);
            }
        );
    });

}

function makeListSortable(list_id, list_item, formId, elementId, jsonConnection) {
    $(document).ready(function() {
        $(list_id).sortable({
            items: list_item, //the div which we want to make sortable
            scroll: true, //If set to true, the page scrolls when coming to an edge.
            update: function(event, ui) { //This event is triggered when the user stopped sorting and the DOM position has changed.
                $(list_id + ' input').each(function(index, value) { // reindex the order and reset the value of all the form inputs to the new order
                    $(this).val(index + 1);
                });

                postRequest(formId, elementId, jsonConnection);
            }

        });
        $(list_item).addClass('grab'); // add the grab class to list items

        $(list_item).mousedown(function() { // on mouse click add grabbing class to list item
            $(this).addClass('grabbing');
        });
        $(list_item).mouseup(function() { // on mouse release remove grabbing class to list item
            $(this).removeClass('grabbing');
        });

    });
}


// Scroll to Top
$(document).ready(function() {
    $('#return').click(function() {
        $('html, body').animate({
            scrollTop: 0
        }, 'slow');
        return false;
    });
});
//Scroll to Element
// usage: ScrollTo('#id');
function ScrollTo(element) {
    $('html,body').animate({
        scrollTop: $(element).offset().top
    }, 'slow');
}


// Update the Status of a record
function recordStatus(action, route, upload_params) {
    switch (action) {

        case 'update':
            $.ajax({
                async: false,
                type: "POST",
                url: route,
                dataType: "json",
                data: upload_params,
                success: function(response) {
                    if (response.status === 'success') {
                        $('#tr' + response.record_id).toggleClass('inactive');
                        $('#statusMessage').empty().append('The following record was updated: ' + response.record_title);
                        $('#statusMessage').toggle('slow').delay(2400).toggle('slow');
                    } else if (response.status === 'fail') {
                        $('#statusMessage').empty().append('There was a problem updating: ' + response.record_title + '. ' + response.message);
                        $('#statusMessage').toggle('slow').delay(2400).toggle('slow');
                    }
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    console.log(xhr.responseText)
                }
            });
            break;

            // deletes a record
        case 'delete':
            var title = $('#delete-record-' + upload_params.id).data("record-title");
            BootstrapDialog.show({
                message: 'Are you sure you want to delete ' + title,
                buttons: [{
                    label: 'Cancel',
                    action: function(dialogItself) {
                        dialogItself.close();
                    }
                }, {
                    icon: 'fa fa-trash-o',
                    label: 'Yes, delete this record',
                    cssClass: 'btn-danger',
                    action: function(dialogItself) {
                        $.ajax({
                            async: false,
                            type: "POST",
                            url: route,
                            dataType: "json",
                            data: upload_params,
                            success: function(response) {
                                if (response.status === 'success') {
                                    $('#tr' + response.record_id).toggle('slow');
                                    $('#statusMessage').empty().append('The following record was deleted: ' + response.record_title);
                                    $('#statusMessage').toggle('slow').delay(2400).toggle('slow');

                                } else if (response.status === 'fail') {
                                    $('#statusMessage').empty().append('There was a problem deleting: ' + response.record_title + '. ' + response.message);
                                    $('#statusMessage').toggle('slow').delay(2400).toggle('slow');
                                }
                            },
                            error: function(xhr, ajaxOptions, thrownError) {
                                console.log(xhr.responseText)
                            }
                        });
                        // CLOSE THE DIALOG
                        dialogItself.close();
                    }
                }]
            });
            break;

    }
}


function showMessage(messageClass, message) {

    if ('#message:hidden') {
        $('#message').fadeIn("fast", function() {
            $(this).append('<li>' + message + '</li>');
        });
    } else {
        $('#message').html(message);
    }

    $("#message").toggleClass(messageClass, (messageClass === 'error'));
}


/**
 * Make AJAX API Request
 *
 * @param string request_url
 * @param string output_target
 * @param string token
 * @return response
 */
function ajaxRequest(request_url, token, output_target) {

    $.get(request_url, {
            _token: token
        })

        .done(function(response) {

            if (typeof(response.error) != 'undefined' && response.error) {

                showMessage("error", response.error);

            } else if (typeof(response.output) != 'undefined' && response.output) {

                // Set the HTML contents of each element in the set of matched elements.
                $(output_target).html(response.output);

                // output the message too if one is set.
                if (typeof(response.msg) != 'undefined' && response.msg) {
                    showMessage("message", response.msg);
                }

            }

        }, "json").fail(function() {
            console.log("There was a problem completing the request");
        });

}

/**
 * Make AJAX API Request
 *
 * @param string request_url
 * @param string output_target
 * @param string token
 * @return response
 */
function postRequest(route, params, output_target) {

    $.ajax({
        async: false,
        type: "POST",
        url: route,
        dataType: "json",
        data: params,
        success: function(response) {
            if (response.status === 'success') {
                $(output_target).html(response.output);
                $('#statusMessage').empty().append('The following record was updated: ' + response.record_title);
                $('#statusMessage').toggle('slow').delay(2400).toggle('slow');
            } else if (response.status === 'fail') {
                $('#statusMessage').empty().append('There was a problem updating: ' + response.record_title + '. ' + response.message);
                $('#statusMessage').toggle('slow').delay(2400).toggle('slow');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(xhr.responseText)
        }
    });

}
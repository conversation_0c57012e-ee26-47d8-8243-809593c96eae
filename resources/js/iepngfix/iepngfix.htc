<public:component>
<public:attach event="onpropertychange" onevent="doFix()" />

<script type="text/javascript">

// IE5.5+ PNG Alpha Fix v1.0RC4
// (c) 2004-2005 <PERSON> http://www.twinhelix.com

// This is licensed under the CC-GNU LGPL, version 2.1 or later.
// For details, see: http://creativecommons.org/licenses/LGPL/2.1/


// This must be a path to a blank image. That's all the configuration you need.
if (typeof blankImg == 'undefined') var blankImg = '/js/iepngfix/blank.gif';


var f = 'DXImageTransform.Microsoft.AlphaImageLoader';

function filt(s, m)
{
 if (filters[f])
 {
  filters[f].enabled = s ? true : false;
  if (s) with (filters[f]) { src = s; sizingMethod = m }
 }
 else if (s) style.filter = 'progid:'+f+'(src="'+s+'",sizingMethod="'+m+'")';
}

function doFix()
{
 // Assume IE7 is OK.
 if (!/MSIE (5\.5|6\.)/.test(navigator.userAgent) ||
  (event && !/(background|src)/.test(event.propertyName))) return;

 var bgImg = currentStyle.backgroundImage || style.backgroundImage;

 if (tagName == 'IMG')
 {
  if ((/\.png$/i).test(src))
  {
   if (currentStyle.width == 'auto' && currentStyle.height == 'auto')
    style.width = offsetWidth + 'px';
   filt(src, 'scale');
   src = blankImg;
  }
  else if (src.indexOf(blankImg) < 0) filt();
 }
 else if (bgImg && bgImg != 'none')
 {
  if (bgImg.match(/^url[("']+(.*\.png)[)"']+$/i))
  {
   var s = RegExp.$1;
   if (currentStyle.width == 'auto' && currentStyle.height == 'auto')
    style.width = offsetWidth + 'px';
   style.backgroundImage = 'none';
   filt(s, 'crop');
   // IE link fix.
   for (var n = 0; n < childNodes.length; n++)
    if (childNodes[n].style) childNodes[n].style.position = 'relative';
  }
  else filt();
 }
}

doFix();

</script>
</public:component>
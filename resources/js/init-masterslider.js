         
var slider = new MasterSlider();

// if container width is mobile size
if( $("#container").width() < 460 ){
	
	// foreach slider image
	$(".ms-slide img").each(function(){

		// add path to mobile folder to pull the mobile slider
		$(this).attr('src', $(this).attr('src').replace('/sliders', '/sliders/mobile'));

	});	
	
	// than swap homepage chip to use the category chip 
	$("#homepage_chip img").attr('src', $("#homepage_chip img").attr('src').replace('homepage_chip', 'category_chip'));

}
else{
	
	// if screen size is larger than mobile show arrow controls and bullets
	slider.control("arrows", {autohide:true});
	slider.control("bullets", {autohide:false});
	
}
 
slider.setup("masterslider", {
	width           : 1235,
	height          : 494,
	space           : 0,
	start           : 1,
	grabCursor      : true,
	swipe           : true,
	mouse           : true,
	keyboard        : false,
	fullwidth		: true,
	wheel           : false,
	autoplay        : true,
	instantStartLayers:false,
	loop            : true,
	shuffle         : true,
	preload         : 0,
	heightLimit     : true,
	autoHeight      : true,
	smoothHeight    : true,
	endPause        : false,
	overPause       : true,
	fillMode        : "fill", 
	centerControls  : true,
	startOnAppear   : false,
	layersMode      : "center", 
	hideLayers      : false, 
	fullscreenMargin: 0,
	speed           : 20, 
	dir             : "h", 
	view            : "basic"
});

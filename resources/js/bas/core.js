//Scroll to Element
// usage: ScrollTo('#id');
function ScrollTo(element) {
  $('html,body').animate({
    scrollTop: $(element).offset().top
  }, 'slow');
}


function writeCookie(name, value, expires) { // expires in days
  var now = new Date();
  now.setTime(now.getTime() + 1000 * 60 * 60 * 24 * expires)
  document.cookie = name + '=' + value + ";expires=" + now.toGMTString();
}


// reveal return to top if #content_hldr has a height greater than 1000px
// function returnToTopVisibility() {
//   if ($("body").height() > $(window).height()) {
//     $("#return").fadeIn("slow");
//   } else {
//     $("#return").fadeOut(0);
//   }
// }


// Auto fix page height for pages with column heights taller than the main pagecontent.
function AutoFixPageHeight() {
  if ($("#col")) {
    var column_height = $("#col").height(),
      page_element = "#pagecontent",
      page_height = $(page_element).height();

    $(page_element).css("min-height", column_height + 40);
  }
}


// Initiate the site's core functionality on ready.
$(document).ready(function() {

  // Enable jQuery tabs for screens larger than 768px.
  if ($("#content_hldr").width() > 768 || $('meta[name=x-bardaccess-app]').attr("content") == 1) {

    // initiate jquery tabs functionality.
    $(".jquery-tabs").tabs({
      activate: function(event, ui) {
        var $activeTab = $(this).tabs('option', 'active');
        if ($activeTab >= 0) {

          // recalculate and only show return to top on longer sections
          // returnToTopVisibility();
        }
      },
      create: function() {
        //An array containing the zero-based indexes of the tabs that should be disabled
        var disabled_tabs = [];
        //find tab with href data attribute
        $(this).find('[data-href]').each(function() { // reindex the order and reset the value of all the form inputs to the new order

          if ($(this)) {
            var tab = $(this);

            disabled_tabs.push(tab.index());

            // on click goto href link location
            $(this).click(function() {
              window.location = tab.data("href");
              return false;
            });

          }

        });
        if (disabled_tabs.length > 0) {
          // disable that tab's normal functionality by index using disabled_tabs array
          // so we can use the location as a link to another page without the tab switching and flashing first.
          $(this).tabs({
            disabled: disabled_tabs
          });
        }
      }
    });
    $('.open-tab').click(function() {
      // get the tab to open from the data-open-tab attribute.
      // ex: <a class="open-tab" data-open-tab="sr2">
      // get the index of the matching tab item
      var tab_to_open = $(this).data('open-tab'),
        index = $(".jquery-tabs").find("[data-tab-name='" + tab_to_open + "']").index();

      // change active tab by index value
      $(".jquery-tabs").tabs("option", "active", index);

    });
  }

  // Search box controls
  $("#searchBtn").click(function() {
    $('#searchBox').toggle('slow');
    $('#searchIcon').toggleClass('fa-search-minus');
  });

  // show return to top on longer pages automatically
  // returnToTopVisibility();
  // Auto fix page height for pages with column heights taller than the main pagecontent.
  AutoFixPageHeight();

  // Alternating table row colors
  if ($(".striped").length > 0) {
    $(".striped tr:nth-child(odd)").addClass("color_two");
  }

  // Scroll to Top
  $('#return').click(function() {
    $('html, body').animate({
      scrollTop: 0
    }, 'slow');
    return false;
  });

});
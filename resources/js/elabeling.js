// JavaScript Document
changeLanguage = function (){
  $(document).ready(function(){
	$("#language_selection").change(function() {
	  $('.default-value').each(function(){
		var defaultVal = $(this).attr('title');
		if ($(this).val() == defaultVal){
		  $(this).val('');
		}
	  });
		postRequest('#form', '#pagecontent', 'jsonElabelingConnect.php')		
	});	
  });		
}

defaultInputText = function (){
	$('.default-value').each(function(){
	  var defaultVal = $(this).attr('title');
	  $(this).focus(function(){
		if ($(this).val() == defaultVal){
		  $(this).removeClass('active').val('');
		}
	  })
	  .blur(function(){
		if ($(this).val() == ''){
		  $(this).addClass('active').val(defaultVal);
		}
	  })
	  .blur().addClass('active');
	});
	$('form').submit(function(){
	  $('.default-value').each(function(){
		var defaultVal = $(this).attr('title');
		if ($(this).val() == defaultVal){
		  $(this).val('');
		}
	  });
	});
}
$(document).ready(function(){
  defaultInputText();
});
